#!/usr/bin/env python3
"""
完整产品数据存储服务
将解析后的产品数据存储到数据库
"""

import json
import psycopg2
from datetime import datetime
from typing import Dict, Any, List, Optional
from decimal import Decimal

from utils.database import DatabaseManager
from utils.logging_config import get_logger
from services.complete_product_parser import CompleteProductParser

logger = get_logger()


class CompleteProductStorage:
    """完整产品数据存储服务"""

    def __init__(self):
        """初始化存储服务"""
        self.db_manager = DatabaseManager()
        self.parser = CompleteProductParser()
        self._ensure_table_exists()
        logger.info("完整产品数据存储服务初始化成功")

    def store_products(self, products: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        批量存储产品数据

        Args:
            products: 产品数据列表

        Returns:
            存储结果统计
        """
        try:
            total_count = len(products)
            success_count = 0
            error_count = 0
            errors = []

            logger.info(f"开始存储 {total_count} 个产品")

            for i, product in enumerate(products, 1):
                try:
                    # 解析产品数据
                    parsed_product = self.parser.parse_product(product)

                    # 存储到数据库
                    self._store_single_product(parsed_product)

                    success_count += 1

                    if i % 100 == 0:
                        logger.info(f"已存储 {i}/{total_count} 个产品")

                except Exception as e:
                    error_count += 1
                    error_msg = f"存储产品失败 (索引 {i}): {e}"
                    logger.error(error_msg)
                    errors.append(error_msg)

            result = {
                "total_count": total_count,
                "success_count": success_count,
                "error_count": error_count,
                "errors": errors,
                "success_rate": success_count / total_count if total_count > 0 else 0,
            }

            logger.info(
                f"产品存储完成: 总数={total_count}, 成功={success_count}, 失败={error_count}"
            )
            return result

        except Exception as e:
            logger.error(f"批量存储产品失败: {e}", exc_info=True)
            raise

    def _store_single_product(self, parsed_product: Dict[str, Any]) -> None:
        """
        存储单个产品数据

        Args:
            parsed_product: 解析后的产品数据
        """
        try:
            # 构建插入SQL
            insert_sql = """
            INSERT INTO products_complete (
                product_id, name, spec, introduction, details,
                small_img, banner, size_img,
                category_id, category_name, label_id, label_name, label_list,
                brand_id, brand_name,
                attribute, status, price, unit, unit_name,
                param_info, param_info_list, new_param, spec_list, spec_search_list,
                use_to, show_for, show_for_company, show_for_company_name, show_for_company_list,
                qualifications, instructions, instructions_list, other, guide,
                common_problem, common_problem_list, question_list,
                video_explanation, video_installation, video_troubleshooting,
                accessory, accessory_list,
                is_suggest, is_hot, is_new,
                count, like_count, favorite_count,
                product_gen_map, product_gen_list_handle, product_gen_list_install,
                product_gen_list_fault, product_gen_list_learning,
                all_product_gen_list_handle, all_product_gen_list_install,
                all_product_gen_list_fault, all_product_gen_list_learning,
                show_for_company_filter, third_type_search, is_filter_show_for,
                company_id, site_id,
                show_time, up_time, sort,
                create_time, update_time, created_at, updated_at,
                create_by, update_by,
                remark, params,
                raw_data, is_processed, processed_at, sync_status
            ) VALUES (
                %(product_id)s, %(name)s, %(spec)s, %(introduction)s, %(details)s,
                %(small_img)s, %(banner)s, %(size_img)s,
                %(category_id)s, %(category_name)s, %(label_id)s, %(label_name)s, %(label_list)s,
                %(brand_id)s, %(brand_name)s,
                %(attribute)s, %(status)s, %(price)s, %(unit)s, %(unit_name)s,
                %(param_info)s, %(param_info_list)s, %(new_param)s, %(spec_list)s, %(spec_search_list)s,
                %(use_to)s, %(show_for)s, %(show_for_company)s, %(show_for_company_name)s, %(show_for_company_list)s,
                %(qualifications)s, %(instructions)s, %(instructions_list)s, %(other)s, %(guide)s,
                %(common_problem)s, %(common_problem_list)s, %(question_list)s,
                %(video_explanation)s, %(video_installation)s, %(video_troubleshooting)s,
                %(accessory)s, %(accessory_list)s,
                %(is_suggest)s, %(is_hot)s, %(is_new)s,
                %(count)s, %(like_count)s, %(favorite_count)s,
                %(product_gen_map)s, %(product_gen_list_handle)s, %(product_gen_list_install)s,
                %(product_gen_list_fault)s, %(product_gen_list_learning)s,
                %(all_product_gen_list_handle)s, %(all_product_gen_list_install)s,
                %(all_product_gen_list_fault)s, %(all_product_gen_list_learning)s,
                %(show_for_company_filter)s, %(third_type_search)s, %(is_filter_show_for)s,
                %(company_id)s, %(site_id)s,
                %(show_time)s, %(up_time)s, %(sort)s,
                %(create_time)s, %(update_time)s, %(created_at)s, %(updated_at)s,
                %(create_by)s, %(update_by)s,
                %(remark)s, %(params)s,
                %(raw_data)s, %(is_processed)s, %(processed_at)s, %(sync_status)s
            )
            ON CONFLICT (product_id) DO UPDATE SET
                name = EXCLUDED.name,
                spec = EXCLUDED.spec,
                introduction = EXCLUDED.introduction,
                details = EXCLUDED.details,
                small_img = EXCLUDED.small_img,
                banner = EXCLUDED.banner,
                size_img = EXCLUDED.size_img,
                category_id = EXCLUDED.category_id,
                category_name = EXCLUDED.category_name,
                label_id = EXCLUDED.label_id,
                label_name = EXCLUDED.label_name,
                label_list = EXCLUDED.label_list,
                brand_id = EXCLUDED.brand_id,
                brand_name = EXCLUDED.brand_name,
                attribute = EXCLUDED.attribute,
                status = EXCLUDED.status,
                price = EXCLUDED.price,
                unit = EXCLUDED.unit,
                unit_name = EXCLUDED.unit_name,
                param_info = EXCLUDED.param_info,
                param_info_list = EXCLUDED.param_info_list,
                new_param = EXCLUDED.new_param,
                spec_list = EXCLUDED.spec_list,
                spec_search_list = EXCLUDED.spec_search_list,
                use_to = EXCLUDED.use_to,
                show_for = EXCLUDED.show_for,
                show_for_company = EXCLUDED.show_for_company,
                show_for_company_name = EXCLUDED.show_for_company_name,
                show_for_company_list = EXCLUDED.show_for_company_list,
                qualifications = EXCLUDED.qualifications,
                instructions = EXCLUDED.instructions,
                instructions_list = EXCLUDED.instructions_list,
                other = EXCLUDED.other,
                guide = EXCLUDED.guide,
                common_problem = EXCLUDED.common_problem,
                common_problem_list = EXCLUDED.common_problem_list,
                question_list = EXCLUDED.question_list,
                video_explanation = EXCLUDED.video_explanation,
                video_installation = EXCLUDED.video_installation,
                video_troubleshooting = EXCLUDED.video_troubleshooting,
                accessory = EXCLUDED.accessory,
                accessory_list = EXCLUDED.accessory_list,
                is_suggest = EXCLUDED.is_suggest,
                is_hot = EXCLUDED.is_hot,
                is_new = EXCLUDED.is_new,
                count = EXCLUDED.count,
                like_count = EXCLUDED.like_count,
                favorite_count = EXCLUDED.favorite_count,
                product_gen_map = EXCLUDED.product_gen_map,
                product_gen_list_handle = EXCLUDED.product_gen_list_handle,
                product_gen_list_install = EXCLUDED.product_gen_list_install,
                product_gen_list_fault = EXCLUDED.product_gen_list_fault,
                product_gen_list_learning = EXCLUDED.product_gen_list_learning,
                all_product_gen_list_handle = EXCLUDED.all_product_gen_list_handle,
                all_product_gen_list_install = EXCLUDED.all_product_gen_list_install,
                all_product_gen_list_fault = EXCLUDED.all_product_gen_list_fault,
                all_product_gen_list_learning = EXCLUDED.all_product_gen_list_learning,
                show_for_company_filter = EXCLUDED.show_for_company_filter,
                third_type_search = EXCLUDED.third_type_search,
                is_filter_show_for = EXCLUDED.is_filter_show_for,
                company_id = EXCLUDED.company_id,
                site_id = EXCLUDED.site_id,
                show_time = EXCLUDED.show_time,
                up_time = EXCLUDED.up_time,
                sort = EXCLUDED.sort,
                create_time = EXCLUDED.create_time,
                update_time = EXCLUDED.update_time,
                updated_at = EXCLUDED.updated_at,
                create_by = EXCLUDED.create_by,
                update_by = EXCLUDED.update_by,
                remark = EXCLUDED.remark,
                params = EXCLUDED.params,
                raw_data = EXCLUDED.raw_data,
                is_processed = EXCLUDED.is_processed,
                processed_at = EXCLUDED.processed_at,
                sync_status = EXCLUDED.sync_status
            """

            # 准备参数
            params = self._prepare_insert_params(parsed_product)

            # 执行插入
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(insert_sql, params)
                    conn.commit()

            logger.debug(f"成功存储产品: {params.get('product_id')}")

        except Exception as e:
            logger.error(f"存储单个产品失败: {e}", exc_info=True)
            raise

    def _prepare_insert_params(self, parsed_product: Dict[str, Any]) -> Dict[str, Any]:
        """
        准备插入参数

        Args:
            parsed_product: 解析后的产品数据

        Returns:
            准备好的插入参数
        """
        params = {}

        # 基本信息
        params["product_id"] = parsed_product.get("id") or parsed_product.get(
            "product_id"
        )
        params["name"] = parsed_product.get("name") or parsed_product.get(
            "product_name"
        )
        params["spec"] = parsed_product.get("spec")
        params["introduction"] = parsed_product.get("introduction")
        params["details"] = parsed_product.get("details")

        # 图片信息
        params["small_img"] = parsed_product.get("small_img")
        params["banner"] = parsed_product.get("banner")
        params["size_img"] = parsed_product.get("size_img")

        # 分类和标签
        params["category_id"] = parsed_product.get("category_id")
        params["category_name"] = parsed_product.get("category_name")
        params["label_id"] = parsed_product.get("label_id")
        params["label_name"] = parsed_product.get("label_name")
        params["label_list"] = parsed_product.get("label_list")

        # 品牌信息
        params["brand_id"] = parsed_product.get("brand_id")
        params["brand_name"] = parsed_product.get("brand_name")

        # 产品属性
        params["attribute"] = parsed_product.get("attribute") or "0"
        params["status"] = parsed_product.get("status") or "0"
        params["price"] = self._safe_decimal(parsed_product.get("price"), 0.00)
        params["unit"] = parsed_product.get("unit") or 2
        params["unit_name"] = parsed_product.get("unit_name")

        # 参数信息
        params["param_info"] = parsed_product.get("param_info")
        params["param_info_list"] = parsed_product.get("param_info_list")
        params["new_param"] = parsed_product.get("new_param")
        params["spec_list"] = parsed_product.get("spec_list")
        params["spec_search_list"] = parsed_product.get("spec_search_list")

        # 使用场景
        params["use_to"] = parsed_product.get("use_to")
        params["show_for"] = parsed_product.get("show_for")
        params["show_for_company"] = parsed_product.get("show_for_company")
        params["show_for_company_name"] = parsed_product.get("show_for_company_name")
        params["show_for_company_list"] = parsed_product.get("show_for_company_list")

        # 文档和资源
        params["qualifications"] = parsed_product.get("qualifications")
        params["instructions"] = parsed_product.get("instructions")
        params["instructions_list"] = parsed_product.get("instructions_list")
        params["other"] = parsed_product.get("other")
        params["guide"] = parsed_product.get("guide")

        # 问题和支持
        params["common_problem"] = parsed_product.get("common_problem")
        params["common_problem_list"] = parsed_product.get("common_problem_list")
        params["question_list"] = parsed_product.get("question_list")

        # 视频资源
        params["video_explanation"] = parsed_product.get("video_explanation")
        params["video_installation"] = parsed_product.get("video_installation")
        params["video_troubleshooting"] = parsed_product.get("video_troubleshooting")

        # 配件信息
        params["accessory"] = parsed_product.get("accessory")
        params["accessory_list"] = parsed_product.get("accessory_list")

        # 推荐和热门标识
        params["is_suggest"] = parsed_product.get("is_suggest") or "0"
        params["is_hot"] = parsed_product.get("is_hot") or "0"
        params["is_new"] = parsed_product.get("is_new") or 1

        # 统计信息
        params["count"] = parsed_product.get("count") or 0
        params["like_count"] = parsed_product.get("like_count") or 0
        params["favorite_count"] = parsed_product.get("favorite_count") or 0

        # 产品生成内容
        params["product_gen_map"] = parsed_product.get("product_gen_map")
        params["product_gen_list_handle"] = parsed_product.get(
            "product_gen_list_handle"
        )
        params["product_gen_list_install"] = parsed_product.get(
            "product_gen_list_install"
        )
        params["product_gen_list_fault"] = parsed_product.get("product_gen_list_fault")
        params["product_gen_list_learning"] = parsed_product.get(
            "product_gen_list_learning"
        )

        # 全量生成内容
        params["all_product_gen_list_handle"] = parsed_product.get(
            "all_product_gen_list_handle"
        )
        params["all_product_gen_list_install"] = parsed_product.get(
            "all_product_gen_list_install"
        )
        params["all_product_gen_list_fault"] = parsed_product.get(
            "all_product_gen_list_fault"
        )
        params["all_product_gen_list_learning"] = parsed_product.get(
            "all_product_gen_list_learning"
        )

        # 过滤和搜索
        params["show_for_company_filter"] = (
            parsed_product.get("show_for_company_filter") or False
        )
        params["third_type_search"] = parsed_product.get("third_type_search") or False
        params["is_filter_show_for"] = parsed_product.get("is_filter_show_for")

        # 公司和站点信息
        params["company_id"] = parsed_product.get("company_id")
        params["site_id"] = parsed_product.get("site_id") or 999

        # 时间字段
        params["show_time"] = self._safe_datetime(parsed_product.get("show_time"))
        params["up_time"] = self._safe_datetime(parsed_product.get("up_time"))
        params["sort"] = parsed_product.get("sort") or 0

        # 系统字段
        params["create_time"] = self._safe_datetime(parsed_product.get("create_time"))
        params["update_time"] = self._safe_datetime(parsed_product.get("update_time"))
        params["created_at"] = self._safe_datetime(parsed_product.get("created_at"))
        params["updated_at"] = self._safe_datetime(parsed_product.get("updated_at"))
        params["create_by"] = parsed_product.get("create_by")
        params["update_by"] = parsed_product.get("update_by")

        # 扩展字段
        params["remark"] = parsed_product.get("remark")
        params["params"] = parsed_product.get("params")

        # 原始数据和处理标识
        params["raw_data"] = parsed_product.get("all_fields")
        params["is_processed"] = True
        params["processed_at"] = datetime.now()
        params["sync_status"] = "completed"

        return params

    def _safe_decimal(self, value: Any, default: float = 0.0) -> Decimal:
        """安全转换为Decimal"""
        if value is None or value == "":
            return Decimal(str(default))
        try:
            return Decimal(str(value))
        except (ValueError, TypeError):
            logger.warning(f"无法转换为Decimal: {value}")
            return Decimal(str(default))

    def _safe_datetime(self, value: Any) -> Optional[datetime]:
        """安全转换为datetime"""
        if not value:
            return None

        try:
            if isinstance(value, str):
                # 尝试多种日期格式
                formats = [
                    "%Y-%m-%d %H:%M:%S",
                    "%Y-%m-%dT%H:%M:%S",
                    "%Y-%m-%dT%H:%M:%S.%fZ",
                    "%Y-%m-%d",
                    "%Y/%m/%d %H:%M:%S",
                    "%Y/%m/%d",
                ]

                for fmt in formats:
                    try:
                        return datetime.strptime(value, fmt)
                    except ValueError:
                        continue

            elif isinstance(value, (int, float)):
                # 处理时间戳
                return datetime.fromtimestamp(value / 1000 if value > 1e10 else value)
            elif isinstance(value, datetime):
                return value

        except Exception as e:
            logger.debug(f"日期解析失败: {value}, 错误: {e}")

        return None

    def _ensure_table_exists(self):
        """确保产品表存在"""
        try:
            # 读取建表SQL
            sql_file_path = "sql/create_complete_products_table.sql"

            try:
                with open(sql_file_path, "r", encoding="utf-8") as f:
                    create_sql = f.read()

                with self.db_manager.get_connection() as conn:
                    with conn.cursor() as cursor:
                        cursor.execute(create_sql)
                        conn.commit()

                logger.info("产品表创建/验证成功")

            except FileNotFoundError:
                logger.warning(f"未找到SQL文件: {sql_file_path}，使用内置SQL创建表")
                self._create_table_with_builtin_sql()

        except Exception as e:
            logger.error(f"确保表存在失败: {e}", exc_info=True)
            raise

    def _create_table_with_builtin_sql(self):
        """使用内置SQL创建表"""
        create_sql = """
        CREATE TABLE IF NOT EXISTS products_complete (
            id SERIAL PRIMARY KEY,
            product_id INTEGER UNIQUE NOT NULL,
            name VARCHAR(500) NOT NULL,
            spec VARCHAR(200),
            introduction TEXT,
            details TEXT,
            small_img TEXT,
            banner TEXT,
            size_img TEXT,
            category_id INTEGER,
            category_name VARCHAR(255),
            label_id INTEGER,
            label_name VARCHAR(255),
            label_list JSONB,
            brand_id INTEGER,
            brand_name VARCHAR(255),
            attribute VARCHAR(20) DEFAULT '0',
            status VARCHAR(20) DEFAULT '0',
            price DECIMAL(12,2) DEFAULT 0.00,
            unit INTEGER DEFAULT 2,
            unit_name VARCHAR(100),
            param_info TEXT,
            param_info_list JSONB,
            new_param TEXT,
            spec_list JSONB,
            spec_search_list JSONB,
            use_to TEXT,
            show_for TEXT,
            show_for_company TEXT,
            show_for_company_name VARCHAR(255),
            show_for_company_list JSONB,
            qualifications TEXT,
            instructions TEXT,
            instructions_list JSONB,
            other TEXT,
            guide TEXT,
            common_problem TEXT,
            common_problem_list JSONB,
            question_list JSONB,
            video_explanation TEXT,
            video_installation TEXT,
            video_troubleshooting TEXT,
            accessory TEXT,
            accessory_list JSONB,
            is_suggest VARCHAR(10) DEFAULT '0',
            is_hot VARCHAR(10) DEFAULT '0',
            is_new INTEGER DEFAULT 1,
            count INTEGER DEFAULT 0,
            like_count INTEGER DEFAULT 0,
            favorite_count INTEGER DEFAULT 0,
            product_gen_map JSONB,
            product_gen_list_handle JSONB,
            product_gen_list_install JSONB,
            product_gen_list_fault JSONB,
            product_gen_list_learning JSONB,
            all_product_gen_list_handle JSONB,
            all_product_gen_list_install JSONB,
            all_product_gen_list_fault JSONB,
            all_product_gen_list_learning JSONB,
            show_for_company_filter BOOLEAN DEFAULT FALSE,
            third_type_search BOOLEAN DEFAULT FALSE,
            is_filter_show_for BOOLEAN,
            company_id INTEGER,
            site_id INTEGER DEFAULT 999,
            show_time TIMESTAMP,
            up_time TIMESTAMP,
            sort INTEGER DEFAULT 0,
            create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            create_by VARCHAR(100),
            update_by VARCHAR(100),
            remark TEXT,
            params JSONB,
            raw_data JSONB,
            is_processed BOOLEAN DEFAULT FALSE,
            processed_at TIMESTAMP,
            sync_status VARCHAR(20) DEFAULT 'pending'
        );

        CREATE INDEX IF NOT EXISTS idx_products_complete_product_id ON products_complete(product_id);
        CREATE INDEX IF NOT EXISTS idx_products_complete_name ON products_complete(name);
        CREATE INDEX IF NOT EXISTS idx_products_complete_spec ON products_complete(spec);
        CREATE INDEX IF NOT EXISTS idx_products_complete_category_id ON products_complete(category_id);
        CREATE INDEX IF NOT EXISTS idx_products_complete_sync_status ON products_complete(sync_status);
        """

        with self.db_manager.get_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute(create_sql)
                conn.commit()

        logger.info("使用内置SQL创建产品表成功")

    def get_storage_stats(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        try:
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    # 总数统计
                    cursor.execute("SELECT COUNT(*) FROM products_complete")
                    result = cursor.fetchone()
                    total_count = result[0] if result else 0

                    # 状态统计
                    cursor.execute(
                        """
                        SELECT sync_status, COUNT(*)
                        FROM products_complete
                        GROUP BY sync_status
                    """
                    )
                    status_stats = dict(cursor.fetchall())

                    # 推荐产品统计
                    cursor.execute(
                        """
                        SELECT
                            COUNT(*) FILTER (WHERE is_suggest = '1') as suggest_count,
                            COUNT(*) FILTER (WHERE is_hot = '1') as hot_count,
                            COUNT(*) FILTER (WHERE is_new = 1) as new_count
                        FROM products_complete
                    """
                    )
                    feature_result = cursor.fetchone()
                    feature_stats = feature_result if feature_result else (0, 0, 0)

                    return {
                        "total_count": total_count,
                        "status_stats": status_stats,
                        "suggest_count": feature_stats[0],
                        "hot_count": feature_stats[1],
                        "new_count": feature_stats[2],
                    }

        except Exception as e:
            logger.error(f"获取存储统计失败: {e}", exc_info=True)
            return {}
