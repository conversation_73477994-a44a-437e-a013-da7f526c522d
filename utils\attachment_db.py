"""
附件数据库操作模块

该模块负责管理附件的数据库操作，包括附件信息的存储、查询、更新等功能。
使用统一的连接池管理数据库连接。
"""

import os
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from dataclasses import dataclass
from utils.database import get_db_connection, return_db_connection

logger = logging.getLogger(__name__)


@dataclass
class AttachmentInfo:
    """附件信息数据类"""

    original_url: str
    local_path: str
    filename: str
    file_size: int
    file_type: str
    mime_type: str
    md5_hash: str
    download_time: str


class AttachmentDB:
    """附件数据库操作类"""

    def __init__(self, database_url: str = None):
        """
        初始化附件数据库操作类

        Args:
            database_url: 数据库连接URL（为了兼容性保留，但实际使用连接池）
        """
        # 保留参数但不使用，统一使用连接池
        logger.info("AttachmentDB初始化完成，使用连接池管理连接")

    def _execute_with_connection(self, operation_func, *args, **kwargs):
        """
        使用连接池执行数据库操作的通用方法

        Args:
            operation_func: 要执行的操作函数
            *args: 传递给操作函数的参数
            **kwargs: 传递给操作函数的关键字参数

        Returns:
            操作函数的返回值
        """
        conn = None
        try:
            conn = get_db_connection()
            if not conn:
                raise Exception("无法从连接池获取数据库连接")

            logger.debug("从连接池获取数据库连接成功")
            result = operation_func(conn, *args, **kwargs)
            conn.commit()
            logger.debug("数据库操作成功提交")
            return result

        except Exception as e:
            if conn:
                conn.rollback()
                logger.error(f"数据库操作失败，已回滚: {e}")
            raise
        finally:
            if conn:
                return_db_connection(conn)
                logger.debug("数据库连接已归还到连接池")

    def connect(self) -> bool:
        """
        建立数据库连接（兼容性方法，实际使用连接池）

        Returns:
            连接是否成功
        """
        try:
            # 测试连接池是否可用
            conn = get_db_connection()
            if conn:
                return_db_connection(conn)
                logger.info("附件数据库连接测试成功")
                return True
            else:
                logger.error("附件数据库连接测试失败")
                return False
        except Exception as e:
            logger.error(f"附件数据库连接失败: {e}")
            return False

    def disconnect(self):
        """关闭数据库连接（兼容性方法，连接池自动管理）"""
        logger.info("附件数据库连接已关闭（连接池自动管理）")

    def ensure_connection(self) -> bool:
        """确保数据库连接可用（兼容性方法，连接池自动管理）"""
        return self.connect()

    def save_attachment(
        self,
        attachment_info: AttachmentInfo,
        business_module: str,
        business_id: str,
        field_name: str,
    ) -> Optional[int]:
        """
        保存附件信息到数据库

        Args:
            attachment_info: 附件信息对象
            business_module: 业务模块
            business_id: 业务ID
            field_name: 字段名

        Returns:
            附件ID，失败返回None
        """

        def _save_operation(
            conn, attachment_info, business_module, business_id, field_name
        ):
            with conn.cursor() as cursor:
                # 准备插入数据
                insert_sql = """
                    INSERT INTO attachments (
                        business_module, business_id, field_name,
                        original_url, local_path, local_filename,
                        file_size, file_type, mime_type, file_extension,
                        md5_hash, download_status, download_time
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                    ) ON CONFLICT (business_module, business_id, field_name, original_url)
                    DO UPDATE SET
                        local_path = EXCLUDED.local_path,
                        local_filename = EXCLUDED.local_filename,
                        file_size = EXCLUDED.file_size,
                        file_type = EXCLUDED.file_type,
                        mime_type = EXCLUDED.mime_type,
                        file_extension = EXCLUDED.file_extension,
                        md5_hash = EXCLUDED.md5_hash,
                        download_status = EXCLUDED.download_status,
                        download_time = EXCLUDED.download_time,
                        updated_at = CURRENT_TIMESTAMP
                    RETURNING id
                """

                # 从local_path提取文件扩展名
                file_extension = (
                    "." + attachment_info.local_path.split(".")[-1]
                    if "." in attachment_info.local_path
                    else ""
                )

                cursor.execute(
                    insert_sql,
                    (
                        business_module,
                        business_id,
                        field_name,
                        attachment_info.original_url,
                        attachment_info.local_path,
                        attachment_info.filename,
                        attachment_info.file_size,
                        attachment_info.file_type,
                        attachment_info.mime_type,
                        file_extension,
                        attachment_info.md5_hash,
                        "completed",
                        datetime.strptime(
                            attachment_info.download_time, "%Y-%m-%d %H:%M:%S"
                        ),
                    ),
                )

                result = cursor.fetchone()
                attachment_id = result[0] if result else None

                logger.info(
                    f"附件信息保存成功: {attachment_info.filename} (ID: {attachment_id})"
                )
                return attachment_id

        try:
            return self._execute_with_connection(
                _save_operation,
                attachment_info,
                business_module,
                business_id,
                field_name,
            )
        except Exception as e:
            logger.error(f"保存附件信息失败: {e}")
            return None

    def batch_save_attachments(
        self,
        attachments_data: Dict[str, Dict[str, AttachmentInfo]],
        business_module: str,
    ) -> int:
        """
        批量保存附件信息

        Args:
            attachments_data: 附件数据字典 {business_id: {field_name: AttachmentInfo}}
            business_module: 业务模块

        Returns:
            成功保存的附件数量
        """
        saved_count = 0

        for business_id, field_attachments in attachments_data.items():
            for field_name, attachment_info in field_attachments.items():
                if self.save_attachment(
                    attachment_info, business_module, business_id, field_name
                ):
                    saved_count += 1

        logger.info(f"批量保存完成: {saved_count} 个附件")
        return saved_count

    def get_attachments_by_business(
        self, business_module: str, business_id: str
    ) -> List[Dict[str, Any]]:
        """
        根据业务信息获取附件列表

        Args:
            business_module: 业务模块
            business_id: 业务ID

        Returns:
            附件信息列表
        """
        if not self.ensure_connection():
            return []

        try:
            cursor = self.connection.cursor()

            query_sql = """
                SELECT id, field_name, original_url, local_path, local_filename,
                       file_size, file_type, mime_type, file_extension, md5_hash,
                       download_status, download_time, created_at, updated_at
                FROM attachments 
                WHERE business_module = %s AND business_id = %s
                ORDER BY created_at DESC
            """

            cursor.execute(query_sql, (business_module, business_id))
            rows = cursor.fetchall()

            columns = [desc[0] for desc in cursor.description]
            attachments = [dict(zip(columns, row)) for row in rows]

            cursor.close()
            return attachments

        except Exception as e:
            logger.error(f"查询附件信息失败: {e}")
            return []

    def get_attachment_by_field(
        self, business_module: str, business_id: str, field_name: str
    ) -> Optional[Dict[str, Any]]:
        """
        根据字段名获取特定附件

        Args:
            business_module: 业务模块
            business_id: 业务ID
            field_name: 字段名

        Returns:
            附件信息，不存在返回None
        """
        if not self.ensure_connection():
            return None

        try:
            cursor = self.connection.cursor()

            query_sql = """
                SELECT id, field_name, original_url, local_path, local_filename,
                       file_size, file_type, mime_type, file_extension, md5_hash,
                       download_status, download_time, created_at, updated_at
                FROM attachments 
                WHERE business_module = %s AND business_id = %s AND field_name = %s
                ORDER BY created_at DESC
                LIMIT 1
            """

            cursor.execute(query_sql, (business_module, business_id, field_name))
            row = cursor.fetchone()

            if row:
                columns = [desc[0] for desc in cursor.description]
                attachment = dict(zip(columns, row))
                cursor.close()
                return attachment

            cursor.close()
            return None

        except Exception as e:
            logger.error(f"查询附件信息失败: {e}")
            return None

    def update_attachment_status(
        self, attachment_id: int, status: str, error_message: str = None
    ) -> bool:
        """
        更新附件状态

        Args:
            attachment_id: 附件ID
            status: 新状态
            error_message: 错误信息（可选）

        Returns:
            更新是否成功
        """
        if not self.ensure_connection():
            return False

        try:
            cursor = self.connection.cursor()

            update_sql = """
                UPDATE attachments 
                SET download_status = %s, error_message = %s, updated_at = CURRENT_TIMESTAMP
                WHERE id = %s
            """

            cursor.execute(update_sql, (status, error_message, attachment_id))
            cursor.close()

            logger.info(f"附件状态更新成功: ID {attachment_id} -> {status}")
            return True

        except Exception as e:
            logger.error(f"更新附件状态失败: {e}")
            return False

    def verify_attachment_integrity(self, attachment_id: int, current_md5: str) -> bool:
        """
        验证附件完整性

        Args:
            attachment_id: 附件ID
            current_md5: 当前文件的MD5值

        Returns:
            验证是否通过
        """
        if not self.ensure_connection():
            return False

        try:
            cursor = self.connection.cursor()

            # 获取存储的MD5值
            query_sql = "SELECT md5_hash FROM attachments WHERE id = %s"
            cursor.execute(query_sql, (attachment_id,))
            row = cursor.fetchone()

            if not row:
                cursor.close()
                return False

            stored_md5 = row[0]
            is_valid = stored_md5 == current_md5

            # 更新验证时间和状态
            status = "verified" if is_valid else "corrupted"
            update_sql = """
                UPDATE attachments 
                SET download_status = %s, last_verified = CURRENT_TIMESTAMP
                WHERE id = %s
            """
            cursor.execute(update_sql, (status, attachment_id))
            cursor.close()

            logger.info(
                f"附件完整性验证: ID {attachment_id} - {'通过' if is_valid else '失败'}"
            )
            return is_valid

        except Exception as e:
            logger.error(f"验证附件完整性失败: {e}")
            return False

    def get_module_statistics(self, business_module: str = None) -> Dict[str, Any]:
        """
        获取模块统计信息

        Args:
            business_module: 业务模块，None表示获取所有模块

        Returns:
            统计信息
        """
        if not self.ensure_connection():
            return {}

        try:
            cursor = self.connection.cursor()

            if business_module:
                query_sql = """
                    SELECT business_module, total_files, total_size, completed_files,
                           failed_files, pending_files, avg_file_size, last_download_time
                    FROM attachment_stats 
                    WHERE business_module = %s
                """
                cursor.execute(query_sql, (business_module,))
            else:
                query_sql = """
                    SELECT business_module, total_files, total_size, completed_files,
                           failed_files, pending_files, avg_file_size, last_download_time
                    FROM attachment_stats
                """
                cursor.execute(query_sql)

            rows = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]

            if business_module:
                result = dict(zip(columns, rows[0])) if rows else {}
            else:
                result = [dict(zip(columns, row)) for row in rows]

            cursor.close()
            return result

        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}

    def get_type_statistics(self) -> List[Dict[str, Any]]:
        """
        获取文件类型统计信息

        Returns:
            文件类型统计列表
        """
        if not self.ensure_connection():
            return []

        try:
            cursor = self.connection.cursor()

            query_sql = """
                SELECT file_type, file_count, total_size, avg_size, min_size, max_size
                FROM attachment_type_stats
            """
            cursor.execute(query_sql)

            rows = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]
            result = [dict(zip(columns, row)) for row in rows]

            cursor.close()
            return result

        except Exception as e:
            logger.error(f"获取文件类型统计失败: {e}")
            return []

    def cleanup_failed_downloads(self, max_attempts: int = 3) -> int:
        """
        清理下载失败的附件记录

        Args:
            max_attempts: 最大尝试次数

        Returns:
            清理的记录数
        """
        if not self.ensure_connection():
            return 0

        try:
            cursor = self.connection.cursor()

            delete_sql = """
                DELETE FROM attachments 
                WHERE download_status = 'failed' AND download_attempts >= %s
            """
            cursor.execute(delete_sql, (max_attempts,))

            deleted_count = cursor.rowcount
            cursor.close()

            logger.info(f"清理失败下载记录: {deleted_count} 条")
            return deleted_count

        except Exception as e:
            logger.error(f"清理失败下载记录出错: {e}")
            return 0

    def get_pending_downloads(self, limit: int = 100) -> List[Dict[str, Any]]:
        """
        获取待下载的附件列表

        Args:
            limit: 返回数量限制

        Returns:
            待下载附件列表
        """
        if not self.ensure_connection():
            return []

        try:
            cursor = self.connection.cursor()

            query_sql = """
                SELECT id, business_module, business_id, field_name, original_url
                FROM attachments 
                WHERE download_status = 'pending'
                ORDER BY created_at ASC
                LIMIT %s
            """
            cursor.execute(query_sql, (limit,))

            rows = cursor.fetchall()
            columns = [desc[0] for desc in cursor.description]
            result = [dict(zip(columns, row)) for row in rows]

            cursor.close()
            return result

        except Exception as e:
            logger.error(f"获取待下载列表失败: {e}")
            return []

    def mark_attachment_pending(
        self, business_module: str, business_id: str, field_name: str, original_url: str
    ) -> Optional[int]:
        """
        标记附件为待下载状态

        Args:
            business_module: 业务模块
            business_id: 业务ID
            field_name: 字段名
            original_url: 原始URL

        Returns:
            附件ID，失败返回None
        """
        if not self.ensure_connection():
            return None

        try:
            cursor = self.connection.cursor()

            insert_sql = """
                INSERT INTO attachments (
                    business_module, business_id, field_name, original_url,
                    local_path, local_filename, download_status
                ) VALUES (
                    %s, %s, %s, %s, '', '', 'pending'
                ) ON CONFLICT (business_module, business_id, field_name, original_url)
                DO UPDATE SET
                    download_status = 'pending',
                    updated_at = CURRENT_TIMESTAMP
                RETURNING id
            """

            cursor.execute(
                insert_sql, (business_module, business_id, field_name, original_url)
            )
            result = cursor.fetchone()
            attachment_id = result[0] if result else None

            cursor.close()
            return attachment_id

        except Exception as e:
            logger.error(f"标记附件为待下载失败: {e}")
            return None

    def get_all_attachments(self) -> List[Dict[str, Any]]:
        """
        获取所有附件记录

        Returns:
            所有附件信息列表
        """
        if not self.ensure_connection():
            return []

        try:
            cursor = self.connection.cursor()

            query_sql = """
                SELECT id, business_module, business_id, field_name,
                       original_url, local_path, local_filename,
                       file_size, file_type, mime_type, file_extension, 
                       md5_hash, download_status, download_time,
                       created_at, updated_at
                FROM attachments 
                ORDER BY created_at DESC
            """

            cursor.execute(query_sql)
            rows = cursor.fetchall()
            cursor.close()

            # 转换为字典列表
            attachments = []
            for row in rows:
                attachment = {
                    "id": row[0],
                    "business_module": row[1],
                    "business_id": row[2],
                    "field_name": row[3],
                    "original_url": row[4],
                    "local_path": row[5],
                    "local_filename": row[6],
                    "file_size": row[7],
                    "file_type": row[8],
                    "mime_type": row[9],
                    "file_extension": row[10],
                    "md5_hash": row[11],
                    "download_status": row[12],
                    "download_time": row[13].isoformat() if row[13] else None,
                    "created_at": row[14].isoformat() if row[14] else None,
                    "updated_at": row[15].isoformat() if row[15] else None,
                }
                attachments.append(attachment)

            logger.info(f"获取所有附件记录: {len(attachments)} 条")
            return attachments

        except Exception as e:
            logger.error(f"获取所有附件记录失败: {e}")
            return []

    def get_attachment_statistics(self) -> Dict[str, Any]:
        """
        获取附件统计信息

        Returns:
            统计信息字典
        """
        if not self.ensure_connection():
            return {}

        try:
            cursor = self.connection.cursor()

            # 总体统计
            cursor.execute(
                """
                SELECT 
                    COUNT(*) as total_count,
                    SUM(file_size) as total_size,
                    COUNT(CASE WHEN download_status = 'completed' THEN 1 END) as completed_count,
                    COUNT(CASE WHEN download_status = 'failed' THEN 1 END) as failed_count
                FROM attachments
            """
            )

            total_stats = cursor.fetchone()

            stats = {
                "total_count": total_stats[0] or 0,
                "total_size": total_stats[1] or 0,
                "completed_count": total_stats[2] or 0,
                "failed_count": total_stats[3] or 0,
            }

            # 计算成功率
            if stats["total_count"] > 0:
                stats["success_rate"] = (
                    stats["completed_count"] / stats["total_count"]
                ) * 100
            else:
                stats["success_rate"] = 0

            # 按模块统计
            cursor.execute(
                """
                SELECT 
                    business_module,
                    COUNT(*) as count,
                    SUM(file_size) as size,
                    COUNT(CASE WHEN download_status = 'completed' THEN 1 END) as completed,
                    COUNT(CASE WHEN download_status = 'failed' THEN 1 END) as failed
                FROM attachments 
                GROUP BY business_module
                ORDER BY count DESC
            """
            )

            module_stats = {}
            for row in cursor.fetchall():
                module_stats[row[0]] = {
                    "count": row[1],
                    "size": row[2] or 0,
                    "completed": row[3],
                    "failed": row[4],
                }

            stats["module_stats"] = module_stats

            # 按文件类型统计
            cursor.execute(
                """
                SELECT 
                    file_type,
                    COUNT(*) as count,
                    SUM(file_size) as size,
                    AVG(file_size) as avg_size
                FROM attachments 
                WHERE file_type IS NOT NULL
                GROUP BY file_type
                ORDER BY count DESC
            """
            )

            type_stats = {}
            for row in cursor.fetchall():
                type_stats[row[0]] = {
                    "count": row[1],
                    "size": row[2] or 0,
                    "avg_size": row[3] or 0,
                }

            stats["type_stats"] = type_stats

            cursor.close()

            logger.info("获取附件统计信息成功")
            return stats

        except Exception as e:
            logger.error(f"获取附件统计信息失败: {e}")
            return {}

    def get_attachments_by_item(
        self, business_module: str, business_id: str
    ) -> List[Dict[str, Any]]:
        """
        获取指定业务项目的附件列表（别名方法，与get_attachments_by_business相同）

        Args:
            business_module: 业务模块
            business_id: 业务ID

        Returns:
            附件信息列表
        """
        return self.get_attachments_by_business(business_module, business_id)

    def record_attachment(
        self,
        business_module: str,
        business_id: str,
        field_name: str,
        original_url: str,
        download_status: str = "pending",
    ) -> Optional[int]:
        """
        记录附件信息（用于下载前的记录）

        Args:
            business_module: 业务模块
            business_id: 业务ID
            field_name: 字段名
            original_url: 原始URL
            download_status: 下载状态

        Returns:
            附件ID，失败返回None
        """
        if not self.ensure_connection():
            return None

        try:
            cursor = self.connection.cursor()

            # 检查是否已存在记录
            cursor.execute(
                """
                SELECT id FROM attachments 
                WHERE business_module = %s AND business_id = %s 
                AND field_name = %s AND original_url = %s
            """,
                (business_module, business_id, field_name, original_url),
            )

            existing = cursor.fetchone()
            if existing:
                # 更新现有记录的状态
                cursor.execute(
                    """
                    UPDATE attachments 
                    SET download_status = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE id = %s
                    RETURNING id
                """,
                    (download_status, existing[0]),
                )

                result = cursor.fetchone()
                attachment_id = result[0] if result else existing[0]
            else:
                # 插入新记录
                cursor.execute(
                    """
                    INSERT INTO attachments (
                        business_module, business_id, field_name, original_url, download_status
                    ) VALUES (%s, %s, %s, %s, %s)
                    RETURNING id
                """,
                    (
                        business_module,
                        business_id,
                        field_name,
                        original_url,
                        download_status,
                    ),
                )

                result = cursor.fetchone()
                attachment_id = result[0] if result else None

            cursor.close()

            logger.debug(
                f"记录附件信息: {business_module}/{business_id}/{field_name} (ID: {attachment_id})"
            )
            return attachment_id

        except Exception as e:
            logger.error(f"记录附件信息失败: {e}")
            return None

    def get_attachment(
        self, business_module: str, business_id: str, field_name: str, original_url: str
    ) -> Optional[Dict[str, Any]]:
        """
        获取指定的附件记录

        Args:
            business_module: 业务模块
            business_id: 业务ID
            field_name: 字段名
            original_url: 原始URL

        Returns:
            附件信息字典或None
        """
        if not self.ensure_connection():
            return None

        try:
            cursor = self.connection.cursor()

            cursor.execute(
                """
                SELECT id, business_module, business_id, field_name,
                       original_url, local_path, local_filename,
                       file_size, file_type, mime_type, file_extension,
                       md5_hash, download_status, download_time,
                       created_at, updated_at
                FROM attachments 
                WHERE business_module = %s AND business_id = %s 
                AND field_name = %s AND original_url = %s
            """,
                (business_module, business_id, field_name, original_url),
            )

            row = cursor.fetchone()
            cursor.close()

            if row:
                return {
                    "id": row[0],
                    "business_module": row[1],
                    "business_id": row[2],
                    "field_name": row[3],
                    "original_url": row[4],
                    "local_path": row[5],
                    "local_filename": row[6],
                    "file_size": row[7],
                    "file_type": row[8],
                    "mime_type": row[9],
                    "file_extension": row[10],
                    "md5_hash": row[11],
                    "download_status": row[12],
                    "download_time": row[13].isoformat() if row[13] else None,
                    "created_at": row[14].isoformat() if row[14] else None,
                    "updated_at": row[15].isoformat() if row[15] else None,
                }

            return None

        except Exception as e:
            logger.error(f"获取附件记录失败: {e}")
            return None

    def update_attachment_status(
        self,
        business_module: str,
        business_id: str,
        field_name: str,
        original_url: str,
        status: str,
        local_path: str = None,
        local_filename: str = None,
        file_size: int = None,
        file_type: str = None,
        mime_type: str = None,
        md5_hash: str = None,
        download_time: str = None,
        error_message: str = None,
    ) -> bool:
        """
        更新附件状态和信息

        Args:
            business_module: 业务模块
            business_id: 业务ID
            field_name: 字段名
            original_url: 原始URL
            status: 新状态
            local_path: 本地路径
            local_filename: 本地文件名
            file_size: 文件大小
            file_type: 文件类型
            mime_type: MIME类型
            md5_hash: MD5哈希
            download_time: 下载时间
            error_message: 错误信息

        Returns:
            更新是否成功
        """
        if not self.ensure_connection():
            return False

        try:
            cursor = self.connection.cursor()

            # 构建更新SQL
            update_fields = ["download_status = %s", "updated_at = CURRENT_TIMESTAMP"]
            params = [status]

            if local_path is not None:
                update_fields.append("local_path = %s")
                params.append(local_path)

            if local_filename is not None:
                update_fields.append("local_filename = %s")
                params.append(local_filename)

            if file_size is not None:
                update_fields.append("file_size = %s")
                params.append(file_size)

            if file_type is not None:
                update_fields.append("file_type = %s")
                params.append(file_type)

            if mime_type is not None:
                update_fields.append("mime_type = %s")
                params.append(mime_type)

            if md5_hash is not None:
                update_fields.append("md5_hash = %s")
                params.append(md5_hash)

            if download_time is not None:
                update_fields.append("download_time = %s")
                params.append(download_time)

            # 添加WHERE条件参数
            params.extend([business_module, business_id, field_name, original_url])

            update_sql = f"""
                UPDATE attachments 
                SET {', '.join(update_fields)}
                WHERE business_module = %s AND business_id = %s 
                AND field_name = %s AND original_url = %s
            """

            cursor.execute(update_sql, params)

            rows_affected = cursor.rowcount
            cursor.close()

            if rows_affected > 0:
                logger.debug(
                    f"更新附件状态成功: {business_module}/{business_id}/{field_name} -> {status}"
                )
                return True
            else:
                logger.warning(
                    f"未找到要更新的附件记录: {business_module}/{business_id}/{field_name}"
                )
                return False

        except Exception as e:
            logger.error(f"更新附件状态失败: {e}")
            return False

    def __enter__(self):
        """上下文管理器入口"""
        self.connect()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.disconnect()


# 使用示例
def main():
    """使用示例"""

    # 使用上下文管理器
    with AttachmentDB() as db:
        # 获取模块统计
        stats = db.get_module_statistics()
        print(f"模块统计: {stats}")

        # 获取文件类型统计
        type_stats = db.get_type_statistics()
        print(f"文件类型统计: {type_stats}")

        # 获取待下载列表
        pending = db.get_pending_downloads(10)
        print(f"待下载: {len(pending)} 个文件")


if __name__ == "__main__":
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )

    main()
