# HTML到Markdown转换功能指南

## 📋 概述

本文档介绍了云商系统中HTML到Markdown转换功能的实现和使用方法。该功能可以自动将从云商接口获取的HTML内容（特别是表格数据）转换为Markdown格式，以便在Streamlit应用中更好地展示。

## 🎯 解决的问题

- ✅ 自动转换API返回的HTML表格为Markdown表格
- ✅ 处理富文本HTML内容，保持格式
- ✅ 在Streamlit中优雅展示数据
- ✅ 减少前端处理HTML的复杂度

## 🏗️ 架构设计

### 核心组件

```
utils/
├── html_processor.py      # HTML处理核心模块
├── data_processing.py     # 数据处理集成模块
└── ...

examples/
└── html_conversion_example.py  # 使用示例
```

### 处理流程

```mermaid
graph TD
    A[云商API响应] --> B[数据处理器]
    B --> C[HTML检测]
    C --> D{包含HTML?}
    D -->|是| E[HTML处理器]
    D -->|否| F[直接返回]
    E --> G[类型判断]
    G --> H[表格转换]
    G --> I[混合内容转换]
    G --> J[纯文本转换]
    H --> K[Markdown输出]
    I --> K
    J --> K
    F --> K
```

## 🔧 核心功能

### 1. HTML处理器 (`HTMLProcessor`)

```python
from utils.html_processor import HTMLProcessor

processor = HTMLProcessor()

# 自动转换
result = processor.process_html_content(html_content, 'auto')

# 专门处理表格
result = processor.process_html_content(html_content, 'table')

# 处理混合内容
result = processor.process_html_content(html_content, 'mixed')
```

### 2. 转换类型

| 类型 | 适用场景 | 说明 |
|------|----------|------|
| `auto` | 自动检测 | 根据内容自动选择最佳转换方式 |
| `table` | 表格为主 | 专门优化表格转换，适合参数信息 |
| `mixed` | 富文本 | 处理包含标题、列表、表格的混合内容 |
| `text` | 纯文本 | 简单去除HTML标签，保留文本 |

### 3. 智能字段识别

系统会根据字段名自动选择处理策略：

```python
# 参数信息字段 - 使用表格转换
'param_info', 'specifications', 'parameters'

# 详细内容字段 - 使用混合转换  
'details', 'description', 'content', 'introduction'

# 其他字段 - 自动检测
```

## 📝 使用方法

### 1. 在服务层集成

```python
# services/product_service.py
def _validate_data(self, data: List[Dict]) -> List[Dict]:
    from utils.data_processing import process_api_data
    
    # 自动进行HTML处理
    processed_data = process_api_data(data, 'product')
    
    # 继续验证流程
    validated = []
    for item in processed_data:
        if self._validate_product_item(item):
            validated.append(item)
    return validated
```

### 2. 在页面组件中使用

```python
# components/product.py
def display_product_details(product):
    """展示产品详情"""
    
    # 技术参数（已转换为Markdown表格）
    if product.get('param_info'):
        st.subheader("📊 技术参数")
        st.markdown(product['param_info'])
    
    # 产品详情（已转换为Markdown）
    if product.get('details'):
        st.subheader("📝 产品详情") 
        st.markdown(product['details'])
    
    # 使用指南（已转换为Markdown）
    if product.get('guide'):
        st.subheader("📖 使用指南")
        st.markdown(product['guide'])
```

### 3. 直接使用API

```python
from utils.data_processing import process_api_data

# 处理API响应
api_response = {...}  # 云商API响应
processed_data = process_api_data(api_response, 'product')

# 或直接处理HTML内容
from utils.html_processor import HTMLProcessor
processor = HTMLProcessor()
markdown_result = processor.process_html_content(html_content)
```

## 🔄 转换示例

### HTML表格转换

**输入:**
```html
<table>
    <tr><th>参数</th><th>值</th><th>说明</th></tr>
    <tr><td>工作电压</td><td>DC 12V</td><td>稳定供电</td></tr>
    <tr><td>工作温度</td><td>-20℃~60℃</td><td>宽温范围</td></tr>
</table>
```

**输出:**
```markdown
| 参数 | 值 | 说明 |
| --- | --- | --- |
| 工作电压 | DC 12V | 稳定供电 |
| 工作温度 | -20℃~60℃ | 宽温范围 |
```

### 混合内容转换

**输入:**
```html
<h3>产品特点</h3>
<ul>
    <li><strong>高精度</strong>：识别率99.9%</li>
    <li><strong>快速响应</strong>：&lt;0.5秒</li>
</ul>
<p>更多信息请参考<a href="/manual">使用手册</a>。</p>
```

**输出:**
```markdown
### 产品特点

- **高精度**：识别率99.9%
- **快速响应**：<0.5秒

更多信息请参考[使用手册](/manual)。
```

## 🚀 部署和安装

### 1. 安装依赖

```bash
# 使用pip安装
pip install -r requirements_html_processing.txt

# 或使用提供的安装脚本
python install_html_dependencies.py
```

### 2. 验证安装

```bash
# 运行示例测试
python examples/html_conversion_example.py
```

### 3. 集成到现有项目

1. 复制 `utils/html_processor.py` 到项目中
2. 更新 `utils/data_processing.py` 集成HTML处理
3. 在服务层的 `_validate_data` 方法中启用HTML处理
4. 在页面组件中使用 `st.markdown()` 渲染转换后的内容

## ⚙️ 配置选项

### HTML2Text配置

```python
# 在HTMLProcessor.__init__()中配置
self.html2text_handler.ignore_links = False      # 保留链接
self.html2text_handler.ignore_images = False     # 保留图片
self.html2text_handler.body_width = 0           # 不限制宽度
self.html2text_handler.single_line_break = True  # 单行换行
```

### 字段映射配置

```python
# 在DataProcessor._get_html_fields_by_type()中配置
field_mapping = {
    'product': ['details', 'param_info', 'specifications'],
    'case': ['content', 'details', 'introduction'],
    # 添加更多数据类型...
}
```

## 🐛 故障排除

### 常见问题

1. **转换失败**
   - 检查HTML格式是否正确
   - 确认依赖包已正确安装
   - 查看日志中的错误信息

2. **表格格式不正确**
   - 检查原始HTML表格结构
   - 验证表头和表体标签
   - 确认单元格内容不包含管道符 `|`

3. **性能问题**
   - 对于大量数据，考虑异步处理
   - 使用缓存避免重复转换
   - 限制单次处理的数据量

### 调试方法

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# 启用详细日志
from utils.html_processor import HTMLProcessor
processor = HTMLProcessor()
result = processor.process_html_content(html_content, 'auto')
```

## 📈 性能优化

1. **缓存转换结果** - 避免重复处理相同内容
2. **批量处理** - 一次处理多个字段
3. **异步转换** - 对于大数据集使用异步处理
4. **内容预检** - 跳过不包含HTML的字段

## 🔮 未来扩展

- [ ] 支持更多HTML标签转换
- [ ] 自定义转换规则
- [ ] 支持图片和媒体内容
- [ ] 添加转换质量评估
- [ ] 支持其他输出格式（如reStructuredText）

---

**维护者**: 开发团队  
**最后更新**: 2024-12-19  
**版本**: v1.0 