import streamlit as st
import logging
import pandas as pd
from typing import Optional, Dict, Any, List
from utils.session import SessionManager
from utils.logging_config import get_logger
from utils.api_client import ZKMallClient

# 配置日志
logger = get_logger()


def main():
    """案例管理页面主函数"""
    # 页面配置
    st.set_page_config(
        page_title="案例管理 - 云商系统",
        page_icon="📋",
        layout="wide",
        initial_sidebar_state="expanded",
    )

    # 初始化session
    initialize_session()

    # 渲染页面
    render_header()
    render_sidebar()
    render_content()
    render_footer()


def initialize_session():
    """初始化页面session状态"""
    defaults = {
        "current_case": None,
        "search_term": "",
        "filter_status": "all",
        "current_page": 1,
        "page_size": 20,
    }

    for key, value in defaults.items():
        if f"case_management_{key}" not in st.session_state:
            SessionManager.set(f"case_management_{key}", value)


@st.cache_data(ttl=300)
def load_cases_from_api(
    search_term: str = "",
    current_page: int = 1,
    page_size: int = 20,
    product_id: Optional[int] = None,
) -> Dict[str, Any]:
    """
    从API加载案例数据

    Args:
        search_term: 搜索关键词
        current_page: 当前页码
        page_size: 每页数量
        product_id: 产品ID（可选）

    Returns:
        包含案例数据和分页信息的字典
    """
    try:
        client = ZKMallClient()

        # 构建查询参数
        params = {"current": current_page, "pageSize": page_size}

        if product_id:
            params["productId"] = product_id

        # 调用API获取案例数据
        cases = client.get_cases(**params)

        logger.info(f"成功获取案例数据，数量: {len(cases)}")

        # 如果有搜索关键词，进行本地过滤
        if search_term:
            filtered_cases = []
            for case in cases:
                if (
                    search_term.lower() in case.get("name", "").lower()
                    or search_term.lower() in case.get("keywords", "").lower()
                    or search_term.lower() in case.get("introduction", "").lower()
                ):
                    filtered_cases.append(case)
            cases = filtered_cases

        return {
            "cases": cases,
            "total": len(cases),
            "current_page": current_page,
            "page_size": page_size,
        }

    except Exception as e:
        logger.error(f"获取案例数据失败: {e}")
        st.error(f"获取案例数据失败: {e}")
        return {
            "cases": [],
            "total": 0,
            "current_page": current_page,
            "page_size": page_size,
        }


def render_header():
    """渲染页面头部"""
    st.title("📋 案例管理")
    st.markdown("管理和查看云商系统中的案例信息")

    # 导航面包屑
    st.markdown("🏢 [首页](/) > 📋 案例管理")


def render_sidebar():
    """渲染侧边栏"""
    with st.sidebar:
        st.subheader("📋 案例管理")

        # 搜索功能
        search_term = st.text_input(
            "搜索案例",
            value=SessionManager.get("case_management_search_term", ""),
            placeholder="输入案例名称或关键词",
        )
        SessionManager.set("case_management_search_term", search_term)

        # 状态筛选
        filter_status = st.selectbox(
            "案例状态",
            options=["all", "active", "inactive", "draft"],
            format_func=lambda x: {
                "all": "全部状态",
                "active": "激活",
                "inactive": "未激活",
                "draft": "草稿",
            }[x],
            index=["all", "active", "inactive", "draft"].index(
                SessionManager.get("case_management_filter_status", "all")
            ),
        )
        SessionManager.set("case_management_filter_status", filter_status)

        # 分页设置
        page_size = st.selectbox(
            "每页显示数量",
            options=[10, 20, 50, 100],
            index=[10, 20, 50, 100].index(
                SessionManager.get("case_management_page_size", 20)
            ),
        )
        SessionManager.set("case_management_page_size", page_size)

        if st.button("🔄 刷新数据", use_container_width=True):
            st.cache_data.clear()
            st.rerun()

        if st.button("🏠 返回首页", use_container_width=True):
            st.switch_page("main.py")


def render_content():
    """渲染主要内容"""
    tab1, tab2, tab3 = st.tabs(["案例列表", "案例详情", "操作记录"])

    with tab1:
        render_case_list()

    with tab2:
        render_case_detail()

    with tab3:
        render_operation_log()


def render_case_list():
    """渲染案例列表"""
    st.subheader("案例列表")

    # 操作按钮
    col1, col2, col3 = st.columns([1, 1, 2])
    with col1:
        if st.button("➕ 新增案例"):
            st.info("新增案例功能开发中...")

    with col2:
        if st.button("📤 导出数据"):
            st.info("导出功能开发中...")

    # 获取搜索参数
    search_term = SessionManager.get("case_management_search_term", "")
    # 确保页码和页大小是整数类型
    current_page = int(SessionManager.get("case_management_current_page", 1))
    page_size = int(SessionManager.get("case_management_page_size", 20))

    # 数据展示区域
    with st.container(border=True):
        with st.spinner("正在加载案例数据..."):
            # 从API获取真实数据
            data = load_cases_from_api(
                search_term=search_term, current_page=current_page, page_size=page_size
            )

            cases = data["cases"]
            total = data["total"]

            if cases:
                # 显示数据统计
                st.info(f"共找到 {total} 个案例")

                # 转换为DataFrame显示
                df_data = []
                for case in cases:
                    df_data.append(
                        {
                            "ID": case.get("id", ""),
                            "案例名称": case.get("name", ""),
                            "分类": case.get("categoryName", ""),
                            "关键词": case.get("keywords", ""),
                            "简介": (
                                case.get("introduction", "")[:100] + "..."
                                if len(case.get("introduction", "")) > 100
                                else case.get("introduction", "")
                            ),
                            "状态": "激活" if case.get("status") == "0" else "未激活",
                            "浏览量": case.get("count", 0),
                            "点赞数": case.get("likeCount", 0),
                            "收藏数": case.get("favoriteCount", 0),
                            "发布者": case.get("publishName", ""),
                            "公司": case.get("companyName", ""),
                        }
                    )

                if df_data:
                    df = pd.DataFrame(df_data)

                    # 显示数据表格
                    selected_rows = st.dataframe(
                        df,
                        use_container_width=True,
                        hide_index=True,
                        selection_mode="single-row",
                        on_select="rerun",
                    )

                    # 处理行选择
                    if selected_rows.selection.rows:
                        selected_index = selected_rows.selection.rows[0]
                        selected_case = cases[selected_index]
                        SessionManager.set(
                            "case_management_current_case", selected_case
                        )
                        st.success(f"已选择案例: {selected_case.get('name', '')}")

                        # 显示快速操作按钮
                        col1, col2, col3 = st.columns(3)
                        with col1:
                            if st.button("查看详情"):
                                # 切换到详情标签页的逻辑可以通过session state实现
                                st.info("请切换到'案例详情'标签页查看完整信息")
                        with col2:
                            if st.button("编辑案例"):
                                st.info("编辑功能开发中...")
                        with col3:
                            if st.button("删除案例"):
                                st.warning("删除功能需要管理员权限")
                else:
                    st.warning("暂无案例数据")

                # 分页控制
                if total > page_size:
                    # 确保分页计算使用整数
                    total_pages = (int(total) + int(page_size) - 1) // int(page_size)

                    col1, col2, col3, col4, col5 = st.columns([1, 1, 2, 1, 1])

                    with col1:
                        if st.button("⬅️ 上一页", disabled=current_page <= 1):
                            SessionManager.set(
                                "case_management_current_page", current_page - 1
                            )
                            st.rerun()

                    with col2:
                        st.write(f"第 {current_page} 页")

                    with col3:
                        new_page = st.number_input(
                            "跳转到页面",
                            min_value=1,
                            max_value=total_pages,
                            value=current_page,
                            key="case_page_input",
                        )
                        if int(new_page) != current_page:
                            SessionManager.set(
                                "case_management_current_page", int(new_page)
                            )
                            st.rerun()

                    with col4:
                        st.write(f"共 {total_pages} 页")

                    with col5:
                        if st.button("下一页 ➡️", disabled=current_page >= total_pages):
                            SessionManager.set(
                                "case_management_current_page", current_page + 1
                            )
                            st.rerun()
            else:
                if search_term:
                    st.warning(f"未找到包含'{search_term}'的案例")
                else:
                    st.warning("暂无案例数据")


def render_case_detail():
    """渲染案例详情"""
    st.subheader("案例详情")

    current_case = SessionManager.get("case_management_current_case")

    if current_case:
        # 显示案例详情
        col1, col2 = st.columns([2, 1])

        with col1:
            st.markdown(f"### {current_case.get('name', 'N/A')}")
            st.markdown(f"**分类**: {current_case.get('categoryName', 'N/A')}")
            st.markdown(f"**关键词**: {current_case.get('keywords', 'N/A')}")
            st.markdown(f"**简介**: {current_case.get('introduction', 'N/A')}")

            # 显示详细内容
            if current_case.get("content"):
                st.markdown("**详细内容**:")
                st.markdown(current_case["content"], unsafe_allow_html=True)

        with col2:
            st.markdown("**基本信息**")
            st.write(f"案例ID: {current_case.get('id', 'N/A')}")
            st.write(
                f"状态: {'激活' if current_case.get('status') == '0' else '未激活'}"
            )
            st.write(f"浏览量: {current_case.get('count', 0)}")
            st.write(f"点赞数: {current_case.get('likeCount', 0)}")
            st.write(f"收藏数: {current_case.get('favoriteCount', 0)}")
            st.write(f"发布者: {current_case.get('publishName', 'N/A')}")
            st.write(f"公司: {current_case.get('companyName', 'N/A')}")

            # 显示图片
            if current_case.get("img"):
                st.image(
                    current_case["img"], caption="案例图片", use_container_width=True
                )

            if current_case.get("banner"):
                st.image(
                    current_case["banner"], caption="案例横幅", use_container_width=True
                )

            # 显示视频链接
            if current_case.get("video"):
                st.markdown(f"**视频链接**: [查看视频]({current_case['video']})")
    else:
        st.info("请从案例列表中选择一个案例查看详情")


def render_operation_log():
    """渲染操作记录"""
    st.subheader("操作记录")
    st.info("操作记录功能开发中...")


def render_footer():
    """渲染页面脚部"""
    st.markdown("---")
    st.markdown("© 2024 云商系统 - 案例管理模块")


if __name__ == "__main__":
    main()
