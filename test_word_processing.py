#!/usr/bin/env python3
"""
Word文档处理完整流程测试脚本

测试Word文档处理的完整流程：
1. Word转PDF
2. PDF转Markdown (mineru)
3. 格式优化
4. 分段处理
5. AI图片标注
6. 结果保存

严格遵循开发规范，使用真实文档，禁止模拟数据。
"""

import logging
import os
import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.document_processor import DocumentProcessor

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def test_word_processing_complete_flow():
    """测试Word文档处理完整流程"""
    try:
        logger.info("开始测试Word文档处理完整流程")
        
        # 初始化文档处理器
        processor = DocumentProcessor()
        
        # 测试Word文档路径
        test_word_files = [
            "test_documents/sample_manual.docx",
            "test_documents/product_guide.doc",
            "test_documents/installation_manual.docx",
        ]
        
        # 创建测试文档目录
        test_dir = Path("test_documents")
        test_dir.mkdir(exist_ok=True)
        
        # 检查是否有测试文档
        existing_docs = [doc for doc in test_word_files if Path(doc).exists()]
        
        if not existing_docs:
            logger.warning("未找到测试Word文档，请在 test_documents/ 目录下放置一些Word文档")
            logger.info("支持的格式: .doc, .docx")
            create_test_documents_info()
            return False
        
        # 测试每个Word文档
        for word_file in existing_docs:
            logger.info(f"=" * 60)
            logger.info(f"测试处理Word文档: {word_file}")
            logger.info(f"=" * 60)
            
            # 执行完整处理流程
            result = processor.process_word_document_complete_flow(
                word_path=word_file,
                product_model="TEST-PRODUCT",
                output_dir="test_output"
            )
            
            # 分析结果
            if result.get("status") == "success":
                logger.info(f"✅ Word文档处理成功: {word_file}")
                
                # 显示处理步骤
                steps = result.get("steps", [])
                logger.info(f"完成的处理步骤: {', '.join(steps)}")
                
                # 显示输出文件
                outputs = result.get("outputs", {})
                if "full_markdown_file" in outputs:
                    logger.info(f"完整Markdown文件: {outputs['full_markdown_file']}")
                
                if "segments_count" in outputs:
                    logger.info(f"分段数量: {outputs['segments_count']}")
                
                if "annotated_images" in outputs:
                    logger.info(f"AI标注图片数量: {outputs['annotated_images']}")
                
                if "report_file" in outputs:
                    logger.info(f"处理报告: {outputs['report_file']}")
                
                # 显示错误信息（如果有）
                errors = result.get("errors", [])
                if errors:
                    logger.warning("处理过程中的警告:")
                    for error in errors:
                        logger.warning(f"  - {error}")
                
            else:
                logger.error(f"❌ Word文档处理失败: {word_file}")
                error = result.get("error", "未知错误")
                logger.error(f"错误信息: {error}")
            
            logger.info("")
        
        # 测试批量处理
        logger.info("=" * 60)
        logger.info("测试批量Word文档处理")
        logger.info("=" * 60)
        
        batch_results = []
        for word_file in existing_docs:
            result = processor.process_word_document_complete_flow(
                word_path=word_file,
                product_model="BATCH-TEST",
                output_dir="batch_output"
            )
            batch_results.append(result)
        
        # 统计批量处理结果
        success_count = len([r for r in batch_results if r.get("status") == "success"])
        total_count = len(batch_results)
        
        logger.info(f"批量处理结果: {success_count}/{total_count} 成功")
        
        # 测试不同产品型号
        logger.info("=" * 60)
        logger.info("测试不同产品型号处理")
        logger.info("=" * 60)
        
        product_models = ["ZK3969", "MB460", "ACCESS-001"]
        
        for i, word_file in enumerate(existing_docs[:len(product_models)]):
            product_model = product_models[i]
            
            result = processor.process_word_document_complete_flow(
                word_path=word_file,
                product_model=product_model,
                output_dir=f"product_output/{product_model}"
            )
            
            if result.get("status") == "success":
                logger.info(f"✅ 产品 {product_model} 文档处理成功")
            else:
                logger.error(f"❌ 产品 {product_model} 文档处理失败")
        
        logger.info("Word文档处理完整流程测试完成")
        return True
        
    except Exception as e:
        logger.error(f"Word文档处理测试失败: {e}")
        return False


def create_test_documents_info():
    """创建测试文档说明"""
    try:
        test_dir = Path("test_documents")
        test_dir.mkdir(exist_ok=True)
        
        readme_content = """# 测试Word文档目录

请在此目录下放置一些Word文档文件，用于测试Word文档处理完整流程。

支持的格式:
- .doc (Word 97-2003)
- .docx (Word 2007+)

建议的测试文档类型:
1. sample_manual.docx - 产品使用手册
2. product_guide.doc - 产品指南
3. installation_manual.docx - 安装手册
4. technical_spec.docx - 技术规格文档

测试流程包括:
1. Word转PDF
2. PDF转Markdown (使用mineru)
3. Markdown格式优化
4. 按二级标题分段
5. AI图片标注
6. 结果保存和报告生成

注意: 
- 请确保Word文档内容适合测试
- 文档中如有图片，将进行AI标注测试
- 处理结果将保存在相应的输出目录中
"""
        
        readme_file = test_dir / "README.md"
        with open(readme_file, "w", encoding="utf-8") as f:
            f.write(readme_content)
        
        logger.info(f"测试文档目录已创建: {test_dir}")
        logger.info("请在该目录下放置Word文档文件")
        
    except Exception as e:
        logger.error(f"创建测试文档目录失败: {e}")


def test_specific_word_features():
    """测试Word文档的特定功能"""
    try:
        logger.info("=" * 60)
        logger.info("测试Word文档特定功能")
        logger.info("=" * 60)
        
        processor = DocumentProcessor()
        
        # 测试Word转PDF功能
        logger.info("测试Word转PDF功能")
        test_word = Path("test_documents/sample_manual.docx")
        
        if test_word.exists():
            pdf_result = processor._convert_word_to_pdf(test_word)
            if pdf_result:
                logger.info(f"✅ Word转PDF成功: {pdf_result}")
                
                # 测试PDF转Markdown
                logger.info("测试PDF转Markdown功能")
                markdown_content = processor._convert_pdf_with_mineru(pdf_result)
                
                if markdown_content:
                    logger.info(f"✅ PDF转Markdown成功: {len(markdown_content)} 字符")
                    
                    # 测试格式优化
                    logger.info("测试Markdown格式优化")
                    optimized_content = processor._optimize_markdown_format(
                        markdown_content, test_word.stem
                    )
                    logger.info(f"✅ 格式优化完成: {len(optimized_content)} 字符")
                    
                    # 测试分段处理
                    logger.info("测试分段处理")
                    segments = processor._segment_by_h2_headers(
                        optimized_content, test_word.stem
                    )
                    logger.info(f"✅ 分段处理完成: {len(segments)} 个段落")
                    
                else:
                    logger.error("❌ PDF转Markdown失败")
            else:
                logger.error("❌ Word转PDF失败")
        else:
            logger.warning("未找到测试Word文档，跳过特定功能测试")
        
    except Exception as e:
        logger.error(f"Word文档特定功能测试失败: {e}")


if __name__ == "__main__":
    logger.info("=" * 80)
    logger.info("Word文档处理完整流程测试")
    logger.info("=" * 80)
    
    # 检查是否有测试文档
    test_docs_exist = any(
        Path(f"test_documents/{name}").exists() 
        for name in ["sample_manual.docx", "product_guide.doc", "installation_manual.docx"]
    )
    
    if not test_docs_exist:
        logger.warning("未找到测试Word文档，创建测试目录...")
        create_test_documents_info()
        logger.info("请在 test_documents/ 目录下放置Word文档后重新运行")
        sys.exit(0)
    
    # 运行完整流程测试
    success = test_word_processing_complete_flow()
    
    # 运行特定功能测试
    test_specific_word_features()
    
    if success:
        logger.info("✅ Word文档处理测试通过")
    else:
        logger.error("❌ Word文档处理测试失败")
        sys.exit(1)
