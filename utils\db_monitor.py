"""
数据库监控服务

提供数据库连接池的监控、健康检查和性能分析功能
"""

import logging
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from utils.database import (
    get_database_health,
    get_database_stats,
    check_connection_leaks,
    log_connection_usage,
    detect_connection_leaks,
    perform_periodic_cleanup,
    get_connection_usage_log,
)

logger = logging.getLogger(__name__)


class DatabaseMonitorService:
    """数据库监控服务"""

    def __init__(self):
        self.monitoring_enabled = True
        self.monitoring_interval = 300  # 5分钟
        self.cleanup_interval = 3600  # 1小时
        self.last_cleanup_time = datetime.now()
        self.monitoring_thread = None
        self.cleanup_thread = None
        self._stop_event = threading.Event()

    def start_monitoring(self):
        """启动监控服务"""
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            logger.info("数据库监控服务已在运行")
            return

        self.monitoring_enabled = True
        self._stop_event.clear()

        # 启动监控线程
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop, name="DatabaseMonitor", daemon=True
        )
        self.monitoring_thread.start()

        # 启动清理线程
        self.cleanup_thread = threading.Thread(
            target=self._cleanup_loop, name="DatabaseCleanup", daemon=True
        )
        self.cleanup_thread.start()

        logger.info("数据库监控服务已启动")

    def stop_monitoring(self):
        """停止监控服务"""
        self.monitoring_enabled = False
        self._stop_event.set()

        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        if self.cleanup_thread:
            self.cleanup_thread.join(timeout=5)

        logger.info("数据库监控服务已停止")

    def _monitoring_loop(self):
        """监控循环"""
        while self.monitoring_enabled and not self._stop_event.is_set():
            try:
                # 执行健康检查
                health_result = get_database_health()

                # 获取连接池统计
                pool_stats = get_database_stats()

                # 检查连接泄漏
                connection_leaks = check_connection_leaks()

                # 记录监控结果
                self._log_monitoring_results(
                    health_result, pool_stats, connection_leaks
                )

                # 检查是否需要告警
                self._check_alerts(health_result, pool_stats, connection_leaks)

            except Exception as e:
                logger.error(f"数据库监控循环异常: {e}")

            # 等待下一次监控
            self._stop_event.wait(self.monitoring_interval)

    def _cleanup_loop(self):
        """清理循环"""
        while self.monitoring_enabled and not self._stop_event.is_set():
            try:
                current_time = datetime.now()

                # 检查是否需要执行清理
                if (
                    current_time - self.last_cleanup_time
                ).total_seconds() >= self.cleanup_interval:
                    cleanup_result = perform_periodic_cleanup()
                    self.last_cleanup_time = current_time

                    logger.info(f"定期清理完成: {cleanup_result}")

            except Exception as e:
                logger.error(f"数据库清理循环异常: {e}")

            # 等待下一次清理检查
            self._stop_event.wait(60)  # 每分钟检查一次

    def _log_monitoring_results(self, health: Dict, stats: Dict, leaks: List):
        """记录监控结果"""
        # 记录基本状态
        if not health.get("healthy", False):
            logger.warning(f"数据库健康检查失败: {health.get('errors', [])}")

        # 记录连接池使用情况
        active_connections = stats.get("active_connections", 0)
        max_connections = stats.get("max_connections", 20)
        usage_rate = (
            (active_connections / max_connections) * 100 if max_connections > 0 else 0
        )

        logger.debug(
            f"连接池使用率: {usage_rate:.1f}% ({active_connections}/{max_connections})"
        )

        # 记录连接泄漏
        if leaks:
            logger.warning(f"发现 {len(leaks)} 个可能的连接泄漏")

    def _check_alerts(self, health: Dict, stats: Dict, leaks: List):
        """检查是否需要告警"""
        alerts = []

        # 健康检查告警
        if not health.get("healthy", False):
            alerts.append(
                {
                    "type": "health_check_failed",
                    "message": f"数据库健康检查失败: {health.get('errors', [])}",
                    "severity": "critical",
                }
            )

        # 连接池使用率告警
        active_connections = stats.get("active_connections", 0)
        max_connections = stats.get("max_connections", 20)
        usage_rate = (
            (active_connections / max_connections) * 100 if max_connections > 0 else 0
        )

        if usage_rate > 90:
            alerts.append(
                {
                    "type": "high_connection_usage",
                    "message": f"连接池使用率过高: {usage_rate:.1f}%",
                    "severity": "critical",
                }
            )
        elif usage_rate > 80:
            alerts.append(
                {
                    "type": "medium_connection_usage",
                    "message": f"连接池使用率较高: {usage_rate:.1f}%",
                    "severity": "warning",
                }
            )

        # 连接失败率告警
        success_rate = stats.get("connection_success_rate", 100)
        if success_rate < 95:
            alerts.append(
                {
                    "type": "low_success_rate",
                    "message": f"连接成功率过低: {success_rate:.1f}%",
                    "severity": "warning",
                }
            )

        # 连接泄漏告警
        if leaks:
            alerts.append(
                {
                    "type": "connection_leaks",
                    "message": f"发现 {len(leaks)} 个可能的连接泄漏",
                    "severity": "warning",
                }
            )

        # 处理告警
        for alert in alerts:
            self._handle_alert(alert)

    def _handle_alert(self, alert: Dict):
        """处理告警"""
        severity = alert.get("severity", "info")
        message = alert.get("message", "未知告警")

        if severity == "critical":
            logger.error(f"[CRITICAL] {message}")
        elif severity == "warning":
            logger.warning(f"[WARNING] {message}")
        else:
            logger.info(f"[INFO] {message}")

    def get_monitoring_report(self) -> Dict[str, Any]:
        """获取监控报告"""
        try:
            # 获取当前状态
            health = get_database_health()
            stats = get_database_stats()
            leaks = detect_connection_leaks(60)  # 1小时阈值
            usage_log = get_connection_usage_log(20)  # 最近20条记录

            # 计算一些统计指标
            active_connections = stats.get("active_connections", 0)
            max_connections = stats.get("max_connections", 20)
            usage_rate = (
                (active_connections / max_connections) * 100
                if max_connections > 0
                else 0
            )

            report = {
                "timestamp": datetime.now().isoformat(),
                "summary": {
                    "healthy": health.get("healthy", False),
                    "connection_usage_rate": round(usage_rate, 2),
                    "active_connections": active_connections,
                    "max_connections": max_connections,
                    "potential_leaks": len(leaks.get("potential_leaks", [])),
                    "success_rate": stats.get("connection_success_rate", 100),
                },
                "health_check": health,
                "pool_statistics": stats,
                "connection_leaks": leaks,
                "recent_usage": usage_log,
                "monitoring_status": {
                    "enabled": self.monitoring_enabled,
                    "monitoring_interval": self.monitoring_interval,
                    "last_cleanup": self.last_cleanup_time.isoformat(),
                },
            }

            return report

        except Exception as e:
            logger.error(f"生成监控报告失败: {e}")
            return {"error": str(e), "timestamp": datetime.now().isoformat()}

    def get_performance_metrics(self, hours: int = 24) -> Dict[str, Any]:
        """获取性能指标"""
        try:
            # 获取统计信息
            stats = get_database_stats()
            usage_log = get_connection_usage_log(1000)  # 获取更多日志用于分析

            # 分析连接使用模式
            if usage_log:
                # 计算平均连接使用时长
                durations = [
                    log.get("duration_seconds", 0)
                    for log in usage_log
                    if log.get("action") == "returned" and log.get("duration_seconds")
                ]

                avg_duration = sum(durations) / len(durations) if durations else 0
                max_duration = max(durations) if durations else 0
                min_duration = min(durations) if durations else 0

                # 统计连接获取和归还次数
                acquired_count = len(
                    [log for log in usage_log if log.get("action") == "acquired"]
                )
                returned_count = len(
                    [log for log in usage_log if log.get("action") == "returned"]
                )
            else:
                avg_duration = max_duration = min_duration = 0
                acquired_count = returned_count = 0

            metrics = {
                "timestamp": datetime.now().isoformat(),
                "period_hours": hours,
                "connection_metrics": {
                    "total_created": stats.get("total_connections_created", 0),
                    "total_returned": stats.get("total_connections_returned", 0),
                    "currently_active": stats.get("active_connections", 0),
                    "failed_connections": stats.get("failed_connections", 0),
                    "connection_timeouts": stats.get("connection_timeouts", 0),
                    "success_rate": stats.get("connection_success_rate", 100),
                },
                "performance_metrics": {
                    "avg_connection_duration": round(avg_duration, 2),
                    "max_connection_duration": round(max_duration, 2),
                    "min_connection_duration": round(min_duration, 2),
                    "recent_acquired": acquired_count,
                    "recent_returned": returned_count,
                },
                "pool_efficiency": {
                    "pool_utilization": round(
                        (
                            stats.get("active_connections", 0)
                            / stats.get("max_connections", 20)
                        )
                        * 100,
                        2,
                    ),
                    "connection_turnover": round(
                        stats.get("total_connections_returned", 0)
                        / max(stats.get("total_connections_created", 1), 1),
                        2,
                    ),
                },
            }

            return metrics

        except Exception as e:
            logger.error(f"获取性能指标失败: {e}")
            return {"error": str(e), "timestamp": datetime.now().isoformat()}


# 全局监控服务实例
monitor_service = DatabaseMonitorService()


def start_database_monitoring():
    """启动数据库监控"""
    monitor_service.start_monitoring()


def stop_database_monitoring():
    """停止数据库监控"""
    monitor_service.stop_monitoring()


def get_database_monitoring_report() -> Dict[str, Any]:
    """获取数据库监控报告"""
    return monitor_service.get_monitoring_report()


def get_database_performance_metrics(hours: int = 24) -> Dict[str, Any]:
    """获取数据库性能指标"""
    return monitor_service.get_performance_metrics(hours)
