#!/usr/bin/env python3
"""
数据完整性验证工具

用于验证API响应中的所有字段是否都被正确解析和存储到数据库中
"""

import json
import logging
from typing import Dict, List, Any, Set, Tuple
from utils.api_client import ZKMallClient
from utils.api_response_normalizer import ApiResponseNormalizer
from services.sync_service import SyncService

logger = logging.getLogger(__name__)


class DataCompletenessValidator:
    """数据完整性验证器"""

    def __init__(self):
        """初始化验证器"""
        self.api_client = ZKMallClient()
        self.normalizer = ApiResponseNormalizer()
        self.sync_service = SyncService()

        # 定义需要验证的数据类型
        self.entity_types = [
            "products",
            "cases",
            "programmes",
            "information",
            "distribution_orders",
            "categories",
            "labels",
            "brands",
        ]

    def validate_all_entities(self) -> Dict[str, Any]:
        """
        验证所有实体类型的数据完整性

        Returns:
            Dict[str, Any]: 验证结果报告
        """
        logger.info("开始验证所有实体数据完整性")

        validation_report = {
            "overall_status": "unknown",
            "total_entities": len(self.entity_types),
            "validated_entities": 0,
            "entities_with_issues": 0,
            "entity_reports": {},
            "summary": {
                "total_fields_found": 0,
                "total_fields_mapped": 0,
                "total_unmapped_fields": 0,
                "mapping_coverage": 0.0,
            },
        }

        for entity_type in self.entity_types:
            try:
                logger.info(f"验证 {entity_type} 数据完整性")
                entity_report = self.validate_entity_completeness(entity_type)
                validation_report["entity_reports"][entity_type] = entity_report
                validation_report["validated_entities"] += 1

                if entity_report.get("has_issues", False):
                    validation_report["entities_with_issues"] += 1

                # 累计统计
                validation_report["summary"]["total_fields_found"] += entity_report.get(
                    "total_fields", 0
                )
                validation_report["summary"][
                    "total_fields_mapped"
                ] += entity_report.get("mapped_fields", 0)
                validation_report["summary"]["total_unmapped_fields"] += len(
                    entity_report.get("unmapped_fields", [])
                )

            except Exception as e:
                logger.error(f"验证 {entity_type} 时出错: {e}")
                validation_report["entity_reports"][entity_type] = {
                    "status": "error",
                    "error": str(e),
                }

        # 计算总体覆盖率
        total_found = validation_report["summary"]["total_fields_found"]
        total_mapped = validation_report["summary"]["total_fields_mapped"]
        if total_found > 0:
            validation_report["summary"]["mapping_coverage"] = (
                total_mapped / total_found
            ) * 100

        # 确定总体状态
        if validation_report["entities_with_issues"] == 0:
            validation_report["overall_status"] = "excellent"
        elif (
            validation_report["entities_with_issues"]
            <= validation_report["validated_entities"] / 2
        ):
            validation_report["overall_status"] = "good"
        else:
            validation_report["overall_status"] = "needs_improvement"

        logger.info(
            f"数据完整性验证完成，总体状态: {validation_report['overall_status']}"
        )
        return validation_report

    def validate_entity_completeness(self, entity_type: str) -> Dict[str, Any]:
        """
        验证单个实体类型的数据完整性

        Args:
            entity_type: 实体类型

        Returns:
            Dict[str, Any]: 验证结果
        """
        try:
            # 获取API数据样本
            api_data = self._get_api_sample_data(entity_type)
            if not api_data:
                return {
                    "status": "no_data",
                    "message": f"未获取到 {entity_type} 的API数据",
                }

            # 分析字段
            all_fields = self._extract_all_fields(api_data)
            mapped_fields, unmapped_fields = self._check_field_mapping(
                entity_type, all_fields
            )

            # 检查数据库存储
            storage_issues = self._check_database_storage(
                entity_type, api_data[0] if api_data else {}
            )

            has_issues = len(unmapped_fields) > 0 or len(storage_issues) > 0
            mapping_coverage = (
                (len(mapped_fields) / len(all_fields)) * 100 if all_fields else 0
            )

            return {
                "status": "completed",
                "entity_type": entity_type,
                "total_fields": len(all_fields),
                "mapped_fields": len(mapped_fields),
                "unmapped_fields": list(unmapped_fields),
                "mapping_coverage": mapping_coverage,
                "storage_issues": storage_issues,
                "has_issues": has_issues,
                "sample_data_count": len(api_data),
                "recommendations": self._generate_recommendations(
                    entity_type, unmapped_fields, storage_issues
                ),
            }

        except Exception as e:
            logger.error(f"验证 {entity_type} 完整性时出错: {e}")
            return {"status": "error", "entity_type": entity_type, "error": str(e)}

    def _get_api_sample_data(self, entity_type: str) -> List[Dict[str, Any]]:
        """获取API样本数据"""
        try:
            if entity_type == "products":
                return self.api_client.get_products()[:5]  # 获取前5条数据作为样本
            elif entity_type == "cases":
                return self.api_client.get_cases()[:5]
            elif entity_type == "programmes":
                return self.api_client.get_programmes()[:5]
            elif entity_type == "information":
                return self.api_client.get_information()[:5]
            elif entity_type == "distribution_orders":
                return self.api_client.get_distribution_orders()[:5]
            elif entity_type == "categories":
                return self.api_client.get_categories()[:5]
            elif entity_type == "labels":
                return self.api_client.get_labels()[:5]
            elif entity_type == "brands":
                return self.api_client.get_brands()[:5]
            else:
                logger.warning(f"不支持的实体类型: {entity_type}")
                return []
        except Exception as e:
            logger.error(f"获取 {entity_type} API数据失败: {e}")
            return []

    def _extract_all_fields(self, data_list: List[Dict[str, Any]]) -> Set[str]:
        """从数据列表中提取所有字段名"""
        all_fields = set()
        for item in data_list:
            all_fields.update(self._extract_nested_fields(item))
        return all_fields

    def _extract_nested_fields(self, data: Any, prefix: str = "") -> Set[str]:
        """递归提取嵌套字段"""
        fields = set()

        if isinstance(data, dict):
            for key, value in data.items():
                field_name = f"{prefix}.{key}" if prefix else key
                fields.add(field_name)

                # 递归处理嵌套对象
                if isinstance(value, (dict, list)):
                    fields.update(self._extract_nested_fields(value, field_name))

        elif isinstance(data, list) and data:
            # 对于列表，分析第一个元素的结构
            fields.update(self._extract_nested_fields(data[0], prefix))

        return fields

    def _check_field_mapping(
        self, entity_type: str, all_fields: Set[str]
    ) -> Tuple[Set[str], Set[str]]:
        """检查字段映射情况"""
        # 获取映射配置
        field_mappings = self.normalizer.FIELD_MAPPINGS.get(entity_type.rstrip("s"), {})

        mapped_fields = set()
        unmapped_fields = set()

        for field in all_fields:
            # 检查是否为嵌套字段
            base_field = field.split(".")[0]
            if base_field in field_mappings or field in field_mappings:
                mapped_fields.add(field)
            else:
                unmapped_fields.add(field)

        return mapped_fields, unmapped_fields

    def _check_database_storage(
        self, entity_type: str, sample_data: Dict[str, Any]
    ) -> List[str]:
        """检查数据库存储问题"""
        issues = []

        try:
            # 标准化数据
            normalized_data = self.normalizer.normalize_response(
                sample_data, entity_type.rstrip("s")
            )

            # 检查关键字段是否存在
            required_fields = ["id"]
            for field in required_fields:
                if field not in normalized_data or normalized_data[field] is None:
                    issues.append(f"缺少必需字段: {field}")

            # 检查数据类型
            if "id" in normalized_data:
                try:
                    int(normalized_data["id"])
                except (ValueError, TypeError):
                    issues.append("ID字段不是有效的整数")

        except Exception as e:
            issues.append(f"数据标准化失败: {str(e)}")

        return issues

    def _generate_recommendations(
        self, entity_type: str, unmapped_fields: Set[str], storage_issues: List[str]
    ) -> List[str]:
        """生成改进建议"""
        recommendations = []

        if unmapped_fields:
            recommendations.append(
                f"需要为 {len(unmapped_fields)} 个未映射字段添加映射配置"
            )
            recommendations.append(
                f"未映射字段: {', '.join(list(unmapped_fields)[:5])}{'...' if len(unmapped_fields) > 5 else ''}"
            )

        if storage_issues:
            recommendations.append("需要修复数据库存储问题")
            recommendations.extend(storage_issues)

        if not unmapped_fields and not storage_issues:
            recommendations.append("数据映射和存储完整，无需改进")

        return recommendations

    def generate_field_mapping_template(self, entity_type: str) -> Dict[str, str]:
        """为未映射字段生成映射模板"""
        try:
            # 获取样本数据
            sample_data = self._get_api_sample_data(entity_type)
            if not sample_data:
                return {}

            # 提取所有字段
            all_fields = self._extract_all_fields(sample_data)

            # 检查映射情况
            mapped_fields, unmapped_fields = self._check_field_mapping(
                entity_type, all_fields
            )

            # 生成映射模板
            template = {}
            for field in unmapped_fields:
                # 生成建议的目标字段名（转换为snake_case）
                suggested_name = self._camel_to_snake(field)
                template[field] = suggested_name

            return template

        except Exception as e:
            logger.error(f"生成 {entity_type} 字段映射模板失败: {e}")
            return {}

    def _camel_to_snake(self, name: str) -> str:
        """将驼峰命名转换为下划线命名"""
        import re

        # 处理嵌套字段
        if "." in name:
            parts = name.split(".")
            return ".".join([self._camel_to_snake(part) for part in parts])

        # 转换驼峰命名
        s1 = re.sub("(.)([A-Z][a-z]+)", r"\1_\2", name)
        return re.sub("([a-z0-9])([A-Z])", r"\1_\2", s1).lower()

    def export_validation_report(self, report: Dict[str, Any], filepath: str) -> bool:
        """导出验证报告到文件"""
        try:
            with open(filepath, "w", encoding="utf-8") as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            logger.info(f"验证报告已导出到: {filepath}")
            return True
        except Exception as e:
            logger.error(f"导出验证报告失败: {e}")
            return False


def main():
    """主函数，用于命令行执行"""
    logging.basicConfig(level=logging.INFO)

    validator = DataCompletenessValidator()

    # 执行完整性验证
    report = validator.validate_all_entities()

    # 打印简要报告
    print("\n" + "=" * 50)
    print("数据完整性验证报告")
    print("=" * 50)
    print(f"总体状态: {report['overall_status']}")
    print(f"验证实体数: {report['validated_entities']}/{report['total_entities']}")
    print(f"有问题的实体: {report['entities_with_issues']}")
    print(f"字段映射覆盖率: {report['summary']['mapping_coverage']:.1f}%")

    # 导出详细报告
    validator.export_validation_report(report, "data_completeness_report.json")

    return report


if __name__ == "__main__":
    main()
