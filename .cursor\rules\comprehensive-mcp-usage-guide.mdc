---
description: 
globs: 
alwaysApply: true
---
# 全面MCP工具使用指南

## 🚨 强制使用声明

**本项目严格要求在所有开发活动中充分使用MCP工具！**

### 📋 强制使用原则
1. **任何开发任务开始前必须先检查相关MCP工具**
2. **禁止在有MCP工具可用时使用传统方法**
3. **每个开发步骤都要考虑如何利用MCP工具提升效率**
4. **必须记录MCP工具的使用过程和效果**
5. **违反MCP使用规范的代码不允许合并**
6. **必须使用OpenMemory记录重要决策和解决方案**
7. **必须使用Shrimp Task Manager进行任务规划和管理**

### ⚡ 开发流程MCP工具强制检查点
- **需求分析**: 必须使用思考工具、推理工具和Shrimp Task Manager规划任务
- **技术设计**: 必须使用文档检索工具、推理工具和OpenMemory搜索相关经验
- **编码实现**: 必须使用UI生成工具、项目管理工具和Shrimp Task Manager执行任务
- **测试验证**: 必须使用浏览器自动化工具和Shrimp Task Manager验证任务
- **代码审查**: 必须使用GitHub集成工具和OpenMemory记录审查结果
- **部署发布**: 必须使用反馈收集工具和OpenMemory记录部署经验
- **问题解决**: 必须使用OpenMemory搜索类似问题的解决方案
- **经验总结**: 必须使用OpenMemory记录重要的技术决策和最佳实践

## 📋 概述

本指南详细介绍了项目中所有可用的MCP（Model Context Protocol）工具，提供完整的使用方法、最佳实践和实际应用场景。**强制要求优先使用MCP工具**来提升开发效率和代码质量。

## 🎯 核心原则

1. **MCP优先**: 任何任务都要首先考虑是否有对应的MCP工具
2. **工具组合**: 多个MCP工具配合使用以实现最佳效果
3. **持续反馈**: 使用反馈工具收集用户意见并持续改进
4. **文档同步**: 使用项目管理工具保持文档与代码同步

## 🛠️ 完整MCP工具清单

### 1. 🌐 浏览器自动化工具套件 (Playwright)

#### 核心导航功能
```javascript
// 页面导航
mcp_playwright_playwright_navigate({
    "url": "https://example.com",
    "browserType": "chromium", // chromium, firefox, webkit
    "headless": false,
    "width": 1280,
    "height": 720,
    "timeout": 30000
})

// 历史导航
mcp_playwright_playwright_go_back()
mcp_playwright_playwright_go_forward()
```

#### 元素交互功能
```javascript
// 点击元素
mcp_playwright_playwright_click({
    "selector": "#submit-button"
})

// iframe内点击
mcp_playwright_playwright_iframe_click({
    "iframeSelector": "#main-iframe",
    "selector": ".inner-button"
})

// 填写表单
mcp_playwright_playwright_fill({
    "selector": "input[name='username']",
    "value": "testuser"
})

// 选择下拉框
mcp_playwright_playwright_select({
    "selector": "select[name='country']",
    "value": "China"
})

// 悬停元素
mcp_playwright_playwright_hover({
    "selector": ".menu-item"
})

// 拖拽操作
mcp_playwright_playwright_drag({
    "sourceSelector": ".draggable-item",
    "targetSelector": ".drop-zone"
})

// 键盘操作
mcp_playwright_playwright_press_key({
    "key": "Enter",
    "selector": "input[type='text']"
})
```

#### 内容获取功能
```javascript
// 截图功能
mcp_playwright_playwright_screenshot({
    "name": "page_screenshot",
    "savePng": true,
    "fullPage": true,
    "width": 1920,
    "height": 1080,
    "downloadsDir": "/path/to/screenshots"
})

// 获取页面文本
mcp_playwright_playwright_get_visible_text()

// 获取页面HTML
mcp_playwright_playwright_get_visible_html()

// 保存为PDF
mcp_playwright_playwright_save_as_pdf({
    "filename": "report.pdf",
    "format": "A4",
    "printBackground": true,
    "outputPath": "/path/to/pdfs"
})
```

#### 脚本执行功能
```javascript
// 执行JavaScript
mcp_playwright_playwright_evaluate({
    "script": `
        // 获取页面性能数据
        const performance = window.performance;
        const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
        return { loadTime, url: window.location.href };
    `
})

// 获取控制台日志
mcp_playwright_playwright_console_logs({
    "type": "error", // all, error, warning, log, info, debug
    "limit": 50,
    "search": "API error",
    "clear": false
})
```

#### HTTP请求功能
```javascript
// GET请求
mcp_playwright_playwright_get({
    "url": "https://api.example.com/users"
})

// POST请求
mcp_playwright_playwright_post({
    "url": "https://api.example.com/login",
    "value": JSON.stringify({
        "username": "admin",
        "password": "password123"
    }),
    "headers": {
        "Content-Type": "application/json",
        "Authorization": "Bearer token"
    },
    "token": "your-bearer-token"
})

// PUT请求
mcp_playwright_playwright_put({
    "url": "https://api.example.com/users/123",
    "value": JSON.stringify({ "name": "Updated Name" })
})

// PATCH请求
mcp_playwright_playwright_patch({
    "url": "https://api.example.com/users/123",
    "value": JSON.stringify({ "status": "active" })
})

// DELETE请求
mcp_playwright_playwright_delete({
    "url": "https://api.example.com/users/123"
})
```

#### 高级功能
```javascript
// 等待响应
mcp_playwright_playwright_expect_response({
    "id": "api_response_1",
    "url": "https://api.example.com/data"
})

// 验证响应
mcp_playwright_playwright_assert_response({
    "id": "api_response_1",
    "value": "expected_data"
})

// 设置用户代理
mcp_playwright_playwright_custom_user_agent({
    "userAgent": "Mozilla/5.0 (Custom Browser) WebKit/537.36"
})

// 关闭浏览器
mcp_playwright_playwright_close()
```

#### 代码生成功能
```javascript
// 开始录制会话
mcp_playwright_start_codegen_session({
    "options": {
        "outputPath": "/path/to/tests",
        "testNamePrefix": "AutoGenerated",
        "includeComments": true
    }
})

// 获取会话信息
mcp_playwright_get_codegen_session({
    "sessionId": "session_123"
})

// 结束录制并生成代码
mcp_playwright_end_codegen_session({
    "sessionId": "session_123"
})

// 清理会话
mcp_playwright_clear_codegen_session({
    "sessionId": "session_123"
})
```

### 2. 🎨 UI组件生成工具套件 (21st DevMagic)

#### 组件构建器
```javascript
// 创建新组件
mcp_21st-devmagic_21st_magic_component_builder({
    "message": "创建一个响应式的产品卡片组件，包含图片、标题、价格、描述和购买按钮",
    "searchQuery": "product card",
    "absolutePathToCurrentFile": "/d:/syncdb-nnnnn/yunshang/components/product_card.py",
    "absolutePathToProjectDirectory": "/d:/syncdb-nnnnn/yunshang",
    "standaloneRequestQuery": "产品卡片组件，支持图片展示、标题显示、价格格式化、描述文本、购买按钮交互"
})

// 复杂表格组件
mcp_21st-devmagic_21st_magic_component_builder({
    "message": "创建一个数据表格组件，支持排序、筛选、分页、搜索功能",
    "searchQuery": "data table",
    "absolutePathToCurrentFile": "/d:/syncdb-nnnnn/yunshang/components/data_table.py",
    "absolutePathToProjectDirectory": "/d:/syncdb-nnnnn/yunshang",
    "standaloneRequestQuery": "数据表格组件，包含排序、筛选、分页、搜索、导出功能"
})

// 表单组件
mcp_21st-devmagic_21st_magic_component_builder({
    "message": "创建一个用户注册表单，包含验证、错误提示、提交处理",
    "searchQuery": "form validation",
    "absolutePathToCurrentFile": "/d:/syncdb-nnnnn/yunshang/components/user_form.py",
    "absolutePathToProjectDirectory": "/d:/syncdb-nnnnn/yunshang",
    "standaloneRequestQuery": "用户注册表单，包含字段验证、错误提示、异步提交、成功反馈"
})
```

#### 组件优化器
```javascript
// 性能优化
mcp_21st-devmagic_21st_magic_component_refiner({
    "userMessage": "优化产品列表组件的性能，实现虚拟滚动和懒加载",
    "absolutePathToRefiningFile": "/d:/syncdb-nnnnn/yunshang/components/product_list.py",
    "context": "当前组件在显示大量产品时性能较差，需要优化渲染性能，减少内存占用，提升用户体验"
})

// UI/UX改进
mcp_21st-devmagic_21st_magic_component_refiner({
    "userMessage": "改进登录表单的用户体验，添加动画效果和更好的错误提示",
    "absolutePathToRefiningFile": "/d:/syncdb-nnnnn/yunshang/components/login_form.py",
    "context": "用户反馈登录界面过于简陋，错误提示不够友好，需要提升视觉效果和交互体验"
})

// 响应式设计
mcp_21st-devmagic_21st_magic_component_refiner({
    "userMessage": "使组件适配移动端设备，优化触摸交互",
    "absolutePathToRefiningFile": "/d:/syncdb-nnnnn/yunshang/components/navigation.py",
    "context": "导航组件在移动设备上显示不佳，需要实现响应式设计，优化触摸操作"
})
```

#### Logo搜索功能
```javascript
// 单个Logo搜索
mcp_21st-devmagic_logo_search({
    "queries": ["github"],
    "format": "TSX" // TSX, JSX, SVG
})

// 批量Logo搜索
mcp_21st-devmagic_logo_search({
    "queries": ["discord", "github", "slack", "microsoft", "google"],
    "format": "JSX"
})

// SVG格式Logo
mcp_21st-devmagic_logo_search({
    "queries": ["apple", "amazon", "facebook"],
    "format": "SVG"
})
```

#### 组件灵感获取
```javascript
// 获取设计灵感
mcp_21st-devmagic_21st_magic_component_inspiration({
    "message": "我需要一些现代化的仪表板组件设计灵感",
    "searchQuery": "dashboard widgets"
})

// 查看最新趋势
mcp_21st-devmagic_21st_magic_component_inspiration({
    "message": "查看最新的电商产品卡片设计趋势",
    "searchQuery": "ecommerce cards"
})
```

### 3. 📊 项目管理工具套件 (CodeLF)

#### 项目信息管理
```javascript
// 获取完整项目信息
mcp_codelf_get-project-info({
    "rootPath": "/d:/syncdb-nnnnn/yunshang"
})

// 更新项目文档
mcp_codelf_update-project-info({
    "rootPath": "/d:/syncdb-nnnnn/yunshang"
})

// 初始化CodeLF系统
mcp_codelf_init-codelf({
    "rootPath": "/d:/syncdb-nnnnn/yunshang"
})
```

#### 使用时机
- **每次重大功能开发前**: 获取项目信息了解现状
- **代码修改后**: 立即更新项目信息保持同步
- **新项目开始**: 初始化CodeLF系统建立文档结构
- **版本发布前**: 确保所有文档都是最新状态

### 4. 💭 思考工具套件 (Think Tool)

#### 思考记录
```javascript
// 问题分析
mcp_think-tool_think({
    "thought": "分析用户登录流程的安全性问题：1. 密码强度验证不足 2. 缺少多因素认证 3. 会话管理存在漏洞 4. 需要实现账户锁定机制"
})

// 架构设计思考
mcp_think-tool_think({
    "thought": "设计微服务架构时的考虑因素：服务拆分粒度、数据一致性、服务间通信、容错机制、监控体系、部署策略"
})

// 性能优化思考
mcp_think-tool_think({
    "thought": "数据库查询优化策略：1. 添加适当索引 2. 优化SQL语句 3. 实现查询缓存 4. 考虑读写分离 5. 分库分表方案"
})

// 用户体验思考
mcp_think-tool_think({
    "thought": "提升用户体验的关键点：页面加载速度、操作流程简化、错误提示友好、视觉设计统一、响应式适配"
})
```

#### 思考管理
```javascript
// 获取所有思考记录
mcp_think-tool_get_thoughts()

// 获取思考统计
mcp_think-tool_get_thought_stats()

// 清理思考记录
mcp_think-tool_clear_thoughts()
```

### 5. 📝 反馈收集工具套件 (MCP Feedback Enhanced)

#### 交互式反馈收集
```javascript
// 功能完成反馈
mcp_mcp-feedback-enhanced_interactive_feedback({
    "project_directory": "/d:/syncdb-nnnnn/yunshang",
    "summary": "产品管理模块开发完成，包含产品列表、详情页、搜索筛选、分页功能。请测试以下功能：1. 产品列表加载 2. 搜索功能 3. 筛选器 4. 分页导航 5. 产品详情页",
    "timeout": 600
})

// 测试结果收集
mcp_mcp-feedback-enhanced_interactive_feedback({
    "project_directory": "/d:/syncdb-nnnnn/yunshang",
    "summary": "自动化测试已完成，测试结果如下：API接口测试通过率95%，UI组件测试通过率98%，性能测试达标。请查看测试报告并提供意见。",
    "timeout": 300
})

// 部署前确认
mcp_mcp-feedback-enhanced_interactive_feedback({
    "project_directory": "/d:/syncdb-nnnnn/yunshang",
    "summary": "准备部署到生产环境，请确认：1. 代码审查完成 2. 测试全部通过 3. 文档已更新 4. 备份已完成。确认无误后将开始部署。",
    "timeout": 900
})
```

#### 系统信息获取
```javascript
// 获取系统环境信息
mcp_mcp-feedback-enhanced_get_system_info()
```

### 6. 🧠 深度推理工具套件 (DeepSeek Claude)

#### 复杂问题推理
```javascript
// 技术架构决策
mcp_deepseek-claude_reason({
    "query": {
        "context": "云商系统需要支持高并发访问，预计日活用户10万+，数据量TB级别",
        "question": "如何设计一个可扩展的系统架构，包括数据库选型、缓存策略、负载均衡、微服务拆分？"
    }
})

// 性能优化方案
mcp_deepseek-claude_reason({
    "query": {
        "context": "当前系统在高峰期响应时间超过3秒，数据库CPU使用率达到90%",
        "question": "分析性能瓶颈并提出具体的优化方案，包括短期和长期解决方案？"
    }
})

// 安全风险评估
mcp_deepseek-claude_reason({
    "query": {
        "context": "电商系统涉及用户个人信息、支付数据、商品信息等敏感数据",
        "question": "评估系统的安全风险点，制定全面的安全防护策略？"
    }
})

// 业务逻辑设计
mcp_deepseek-claude_reason({
    "query": {
        "context": "需要设计一个灵活的促销系统，支持满减、折扣、买赠等多种促销方式",
        "question": "如何设计促销系统的数据模型和业务逻辑，确保扩展性和性能？"
    }
})
```

### 7. 📚 文档检索工具套件 (Context7)

#### 库文档检索
```javascript
// 解析库ID
mcp_context7_resolve-library-id({
    "libraryName": "streamlit"
})

mcp_context7_resolve-library-id({
    "libraryName": "fastapi"
})

mcp_context7_resolve-library-id({
    "libraryName": "postgresql"
})

// 获取详细文档
mcp_context7_get-library-docs({
    "context7CompatibleLibraryID": "/streamlit/streamlit",
    "tokens": 15000,
    "topic": "caching and performance"
})

mcp_context7_get-library-docs({
    "context7CompatibleLibraryID": "/fastapi/fastapi",
    "tokens": 12000,
    "topic": "authentication and security"
})

mcp_context7_get-library-docs({
    "context7CompatibleLibraryID": "/postgresql/postgresql",
    "tokens": 10000,
    "topic": "indexing and optimization"
})
```

### 8. 🐙 GitHub集成工具套件

#### 仓库管理
```javascript
// 创建新仓库
mcp_github_create_repository({
    "name": "yunshang-frontend",
    "description": "云商系统前端应用",
    "private": false,
    "autoInit": true
})

// Fork仓库
mcp_github_fork_repository({
    "owner": "original-owner",
    "repo": "awesome-project",
    "organization": "my-org"
})

// 创建分支
mcp_github_create_branch({
    "owner": "my-org",
    "repo": "yunshang",
    "branch": "feature/product-management",
    "from_branch": "main"
})
```

#### 文件操作
```javascript
// 创建/更新单个文件
mcp_github_create_or_update_file({
    "owner": "my-org",
    "repo": "yunshang",
    "path": "components/product_card.py",
    "content": "# 产品卡片组件\nimport streamlit as st\n...",
    "message": "feat: 添加产品卡片组件",
    "branch": "feature/product-management"
})

// 批量推送文件
mcp_github_push_files({
    "owner": "my-org",
    "repo": "yunshang",
    "branch": "feature/ui-components",
    "files": [
        {
            "path": "components/header.py",
            "content": "# 页面头部组件..."
        },
        {
            "path": "components/footer.py",
            "content": "# 页面脚部组件..."
        }
    ],
    "message": "feat: 添加页面布局组件"
})

// 获取文件内容
mcp_github_get_file_contents({
    "owner": "my-org",
    "repo": "yunshang",
    "path": "README.md",
    "branch": "main"
})
```

#### Issue管理
```javascript
// 创建Issue
mcp_github_create_issue({
    "owner": "my-org",
    "repo": "yunshang",
    "title": "优化产品列表加载性能",
    "body": "当前产品列表在数据量大时加载缓慢，需要实现：\n1. 虚拟滚动\n2. 懒加载\n3. 缓存优化",
    "labels": ["enhancement", "performance"],
    "assignees": ["developer1", "developer2"]
})

// 更新Issue
mcp_github_update_issue({
    "owner": "my-org",
    "repo": "yunshang",
    "issue_number": 123,
    "title": "优化产品列表加载性能 [已完成]",
    "state": "closed",
    "labels": ["enhancement", "performance", "completed"]
})

// 添加Issue评论
mcp_github_add_issue_comment({
    "owner": "my-org",
    "repo": "yunshang",
    "issue_number": 123,
    "body": "性能优化已完成，加载时间从3秒降至0.5秒，测试通过。"
})

// 获取Issue详情
mcp_github_get_issue({
    "owner": "my-org",
    "repo": "yunshang",
    "issue_number": 123
})

// 获取Issue评论
mcp_github_get_issue_comments({
    "owner": "my-org",
    "repo": "yunshang",
    "issue_number": 123,
    "page": 1,
    "per_page": 20
})
```

#### Pull Request管理
```javascript
// 创建PR
mcp_github_create_pull_request({
    "owner": "my-org",
    "repo": "yunshang",
    "title": "feat: 实现产品管理模块",
    "head": "feature/product-management",
    "base": "main",
    "body": "## 功能描述\n- 产品列表展示\n- 产品搜索筛选\n- 产品详情页\n\n## 测试说明\n- 单元测试通过\n- 集成测试通过",
    "draft": false
})

// 更新PR
mcp_github_update_pull_request({
    "owner": "my-org",
    "repo": "yunshang",
    "pullNumber": 456,
    "title": "feat: 实现产品管理模块 [已完成测试]",
    "body": "## 功能描述\n- 产品列表展示 ✅\n- 产品搜索筛选 ✅\n- 产品详情页 ✅\n\n## 测试结果\n- 单元测试: 100%通过\n- 集成测试: 100%通过\n- 性能测试: 通过"
})

// 合并PR
mcp_github_merge_pull_request({
    "owner": "my-org",
    "repo": "yunshang",
    "pullNumber": 456,
    "merge_method": "squash",
    "commit_title": "feat: 产品管理模块",
    "commit_message": "实现完整的产品管理功能，包括列表、搜索、详情页面"
})

// 创建PR审查
mcp_github_create_pull_request_review({
    "owner": "my-org",
    "repo": "yunshang",
    "pullNumber": 456,
    "event": "APPROVE",
    "body": "代码质量良好，功能测试通过，同意合并。",
    "comments": [
        {
            "path": "components/product_list.py",
            "body": "建议添加错误处理机制",
            "line": 45,
            "side": "RIGHT"
        }
    ]
})
```

#### 搜索功能
```javascript
// 搜索代码
mcp_github_search_code({
    "q": "streamlit cache_data repo:my-org/yunshang",
    "sort": "indexed",
    "order": "desc",
    "page": 1,
    "perPage": 20
})

// 搜索Issue
mcp_github_search_issues({
    "q": "is:issue is:open label:bug repo:my-org/yunshang",
    "sort": "created",
    "order": "desc",
    "page": 1,
    "perPage": 50
})

// 搜索仓库
mcp_github_search_repositories({
    "query": "streamlit dashboard",
    "page": 1,
    "perPage": 10
})

// 搜索用户
mcp_github_search_users({
    "q": "location:China language:Python",
    "sort": "repositories",
    "order": "desc"
})
```

### 9. 🧠 记忆管理工具套件 (OpenMemory)

#### 核心功能
OpenMemory是一个智能记忆管理系统，用于存储、搜索和管理项目相关的重要信息、经验和知识。

#### 记忆管理功能
```javascript
// 添加新记忆
mcp_openmemory_add-memory({
    "content": "用户反馈产品列表加载缓慢，已通过实现虚拟滚动和缓存优化解决，性能提升70%"
})

// 添加技术决策记忆
mcp_openmemory_add-memory({
    "content": "选择PostgreSQL作为主数据库的原因：支持JSON字段、ACID事务、丰富的索引类型、与Streamlit集成良好"
})

// 添加问题解决方案记忆
mcp_openmemory_add-memory({
    "content": "修复登录认证失败问题：检查token过期时间、更新refresh token机制、添加重试逻辑"
})

// 添加最佳实践记忆
mcp_openmemory_add-memory({
    "content": "Streamlit缓存最佳实践：使用@st.cache_data缓存数据，@st.cache_resource缓存连接，设置合理TTL"
})
```

#### 记忆搜索功能
```javascript
// 搜索相关记忆
mcp_openmemory_search-memories({
    "query": "产品列表性能优化"
})

// 搜索技术问题解决方案
mcp_openmemory_search-memories({
    "query": "登录认证失败"
})

// 搜索最佳实践
mcp_openmemory_search-memories({
    "query": "Streamlit缓存"
})

// 搜索数据库相关记忆
mcp_openmemory_search-memories({
    "query": "PostgreSQL 数据库设计"
})
```

#### 记忆管理功能
```javascript
// 列出所有记忆
mcp_openmemory_list-memories()

// 清理所有记忆（谨慎使用）
mcp_openmemory_delete-all-memories()
```

#### 使用场景和最佳实践
- **问题解决记录**: 每次解决重要技术问题后立即记录解决方案
- **决策依据存储**: 记录重要技术选型和架构决策的原因
- **经验积累**: 存储开发过程中的经验教训和最佳实践
- **知识传承**: 为团队成员提供项目相关的知识库
- **快速查找**: 在遇到类似问题时快速找到之前的解决方案

### 10. 🦐 任务管理工具套件 (Shrimp Task Manager)

#### 核心功能
Shrimp Task Manager是一个全面的任务管理系统，提供任务规划、分析、拆分、执行、验证等完整的项目管理功能。

#### 任务规划功能
```javascript
// 规划新任务
mcp_shrimp-task-manager_plan_task({
    "description": "实现云商系统的产品管理模块，包括产品列表展示、搜索筛选、详情页面、CRUD操作等功能",
    "requirements": "需要支持大量产品数据展示、响应式设计、性能优化、错误处理",
    "existingTasksReference": false
})

// 基于现有任务进行规划
mcp_shrimp-task-manager_plan_task({
    "description": "优化现有产品管理模块的性能，解决加载缓慢问题",
    "requirements": "加载时间控制在2秒内、支持虚拟滚动、实现缓存机制",
    "existingTasksReference": true
})
```

#### 任务分析功能
```javascript
// 深度分析任务
mcp_shrimp-task-manager_analyze_task({
    "summary": "产品管理模块开发任务，需要实现完整的产品CRUD功能",
    "initialConcept": "采用Streamlit框架构建前端界面，PostgreSQL存储产品数据，通过云商API获取数据，实现响应式设计和性能优化。主要技术栈：Python、Streamlit、PostgreSQL、Redis缓存。"
})

// 重新分析任务（基于之前分析）
mcp_shrimp-task-manager_analyze_task({
    "summary": "产品管理模块性能优化任务",
    "initialConcept": "通过虚拟滚动、懒加载、缓存策略等技术手段优化产品列表加载性能",
    "previousAnalysis": "之前分析发现主要瓶颈在数据库查询和前端渲染，需要从这两个方面进行优化"
})
```

#### 任务反思功能
```javascript
// 批判性审查任务分析
mcp_shrimp-task-manager_reflect_task({
    "summary": "产品管理模块开发任务分析审查",
    "analysis": "技术方案采用Streamlit+PostgreSQL架构，实现产品CRUD功能。考虑因素包括：1.数据库设计需要支持复杂查询 2.前端需要响应式设计 3.API集成需要错误处理 4.性能优化需要缓存策略。风险点：大数据量下的性能问题、API稳定性、用户体验优化。"
})
```

#### 任务拆分功能
```javascript
// 将复杂任务拆分为子任务
mcp_shrimp-task-manager_split_tasks({
    "updateMode": "clearAllTasks",
    "globalAnalysisResult": "产品管理模块需要实现完整的产品展示和管理功能，支持大量数据处理和良好的用户体验",
    "tasksRaw": JSON.stringify([
        {
            "name": "数据库模型设计",
            "description": "设计产品相关的数据库表结构，包括产品基本信息、分类、标签等",
            "implementationGuide": "创建产品表(products)，包含id、name、description、price、category_id等字段；创建分类表(categories)；建立适当的索引和外键关系",
            "dependencies": [],
            "relatedFiles": [
                {
                    "path": "scripts/create_product_tables.sql",
                    "type": "CREATE",
                    "description": "数据库建表脚本"
                }
            ],
            "verificationCriteria": "数据库表创建成功，字段类型正确，索引建立完成，外键关系正确"
        },
        {
            "name": "API客户端集成",
            "description": "集成云商API，实现产品数据的获取和同步功能",
            "implementationGuide": "创建ProductAPIClient类，实现get_products、get_product_detail等方法；添加错误处理和重试机制；实现数据格式转换",
            "dependencies": ["数据库模型设计"],
            "relatedFiles": [
                {
                    "path": "services/product_api_client.py",
                    "type": "CREATE",
                    "description": "产品API客户端"
                }
            ],
            "verificationCriteria": "API调用成功，数据格式正确，错误处理完善，重试机制有效"
        }
    ])
})
```

#### 任务执行功能
```javascript
// 获取任务执行指导
mcp_shrimp-task-manager_execute_task({
    "taskId": "12345678-1234-4567-8901-123456789012"
})
```

#### 任务验证功能
```javascript
// 验证任务完成情况
mcp_shrimp-task-manager_verify_task({
    "taskId": "12345678-1234-4567-8901-123456789012",
    "score": 85,
    "summary": "数据库模型设计已完成，所有表结构创建成功，索引和外键关系正确建立，满足项目需求。"
})

// 任务未达标的验证
mcp_shrimp-task-manager_verify_task({
    "taskId": "12345678-1234-4567-8901-123456789012",
    "score": 65,
    "summary": "API客户端基本功能已实现，但错误处理机制不完善，需要添加更详细的异常处理和日志记录。"
})
```

#### 任务查询和管理功能
```javascript
// 列出所有任务
mcp_shrimp-task-manager_list_tasks({
    "status": "all"
})

// 列出待执行任务
mcp_shrimp-task-manager_list_tasks({
    "status": "pending"
})

// 搜索任务
mcp_shrimp-task-manager_query_task({
    "query": "产品管理",
    "page": 1,
    "pageSize": 10
})

// 获取任务详情
mcp_shrimp-task-manager_get_task_detail({
    "taskId": "12345678-1234-4567-8901-123456789012"
})

// 更新任务信息
mcp_shrimp-task-manager_update_task({
    "taskId": "12345678-1234-4567-8901-123456789012",
    "description": "更新后的任务描述",
    "notes": "添加新的实现注意事项"
})

// 删除任务
mcp_shrimp-task-manager_delete_task({
    "taskId": "12345678-1234-4567-8901-123456789012"
})
```

#### 高级功能
```javascript
// 思考过程记录
mcp_shrimp-task-manager_process_thought({
    "thought": "分析产品管理模块的技术架构选择，考虑Streamlit的优势和限制",
    "thought_number": 1,
    "total_thoughts": 5,
    "next_thought_needed": true,
    "stage": "Analysis",
    "tags": ["architecture", "streamlit", "product-management"],
    "axioms_used": ["简单优于复杂", "用户体验优先"],
    "assumptions_challenged": ["Streamlit适合所有类型的Web应用"]
})

// 研究模式
mcp_shrimp-task-manager_research_mode({
    "topic": "Streamlit大数据量展示的性能优化方案",
    "currentState": "正在研究虚拟滚动和懒加载技术在Streamlit中的实现方法",
    "nextSteps": "查找相关技术文档，分析现有解决方案，制定具体实施计划",
    "previousState": ""
})

// 初始化项目规范
mcp_shrimp-task-manager_init_project_rules()
```

#### 使用场景和最佳实践
- **项目启动**: 使用plan_task规划整体项目任务
- **需求分析**: 使用analyze_task深度分析技术需求
- **任务分解**: 使用split_tasks将大任务拆分为可执行的小任务
- **开发指导**: 使用execute_task获取具体的开发指导
- **质量保证**: 使用verify_task确保任务完成质量
- **进度跟踪**: 使用list_tasks和query_task跟踪项目进度
- **知识管理**: 使用process_thought记录重要的思考过程
- **技术研究**: 使用research_mode进行深度技术调研

## 🎯 综合应用场景

### 场景1: 完整功能开发流程

```javascript
// 1. 任务规划阶段（新增）
mcp_shrimp-task-manager_plan_task({
    "description": "用户需要一个产品展示页面，核心需求：产品列表、搜索筛选、详情查看、分页导航",
    "requirements": "响应快速、支持大量数据、用户体验良好",
    "existingTasksReference": false
})

// 2. 搜索相关经验（新增）
mcp_openmemory_search-memories({
    "query": "产品展示页面 Streamlit 性能优化"
})

// 3. 需求分析阶段
mcp_think-tool_think({
    "thought": "用户需要一个产品展示页面，核心需求：产品列表、搜索筛选、详情查看、分页导航"
})

// 4. 任务深度分析（新增）
mcp_shrimp-task-manager_analyze_task({
    "summary": "产品展示页面开发任务，需要实现完整的产品展示功能",
    "initialConcept": "采用Streamlit框架构建响应式产品展示页面，支持搜索筛选和分页功能，优化大数据量下的性能表现"
})

// 5. 技术方案设计
mcp_deepseek-claude_reason({
    "query": {
        "context": "Streamlit应用，需要展示大量产品数据，要求响应快速",
        "question": "如何设计高性能的产品展示页面架构？"
    }
})

// 6. 获取技术文档
mcp_context7_get-library-docs({
    "context7CompatibleLibraryID": "/streamlit/streamlit",
    "topic": "data display and caching"
})

// 7. 任务拆分（新增）
mcp_shrimp-task-manager_split_tasks({
    "updateMode": "clearAllTasks",
    "globalAnalysisResult": "产品展示页面需要实现完整的展示和交互功能",
    "tasksRaw": JSON.stringify([
        {
            "name": "产品列表组件开发",
            "description": "创建产品列表展示组件，支持分页和搜索",
            "implementationGuide": "使用Streamlit dataframe组件，集成搜索和分页功能"
        }
    ])
})

// 8. 生成UI组件
mcp_21st-devmagic_21st_magic_component_builder({
    "message": "创建产品列表组件，支持搜索、筛选、分页",
    "searchQuery": "product list"
})

// 9. 更新项目信息
mcp_codelf_update-project-info({
    "rootPath": "/d:/syncdb-nnnnn/yunshang"
})

// 10. 创建GitHub Issue
mcp_github_create_issue({
    "title": "实现产品展示页面",
    "body": "开发产品列表和详情页面功能"
})

// 11. 功能测试
mcp_playwright_playwright_navigate({
    "url": "http://localhost:8501"
})

mcp_playwright_playwright_screenshot({
    "name": "product_page_test"
})

// 12. 任务验证（新增）
mcp_shrimp-task-manager_verify_task({
    "taskId": "task-id",
    "score": 90,
    "summary": "产品展示页面开发完成，功能测试通过，性能表现良好"
})

// 13. 收集反馈
mcp_mcp-feedback-enhanced_interactive_feedback({
    "summary": "产品展示页面开发完成，请测试功能"
})

// 14. 记录开发经验（新增）
mcp_openmemory_add-memory({
    "content": "产品展示页面开发完成，使用Streamlit dataframe组件实现，通过缓存优化提升性能，用户反馈良好"
})

// 15. 创建PR
mcp_github_create_pull_request({
    "title": "feat: 产品展示页面",
    "head": "feature/product-display"
})
```

### 场景2: 性能优化流程

```javascript
// 1. 性能问题分析
mcp_think-tool_think({
    "thought": "页面加载缓慢问题分析：数据库查询慢、缓存未命中、前端渲染阻塞"
})

// 2. 深度分析
mcp_deepseek-claude_reason({
    "query": {
        "context": "页面加载时间超过3秒，用户体验差",
        "question": "制定全面的性能优化方案"
    }
})

// 3. 性能测试
mcp_playwright_playwright_evaluate({
    "script": "return performance.timing"
})

// 4. 组件优化
mcp_21st-devmagic_21st_magic_component_refiner({
    "userMessage": "优化页面加载性能",
    "context": "实现懒加载和缓存优化"
})

// 5. 测试验证
mcp_playwright_playwright_screenshot({
    "name": "performance_after_optimization"
})

// 6. 更新文档
mcp_codelf_update-project-info({
    "rootPath": "/d:/syncdb-nnnnn/yunshang"
})
```

### 场景3: Bug修复流程

```javascript
// 1. 问题分析
mcp_think-tool_think({
    "thought": "登录功能bug分析：用户反馈无法登录，可能是认证逻辑或数据库连接问题"
})

// 2. 复现问题
mcp_playwright_playwright_navigate({
    "url": "http://localhost:8501/login"
})

mcp_playwright_playwright_fill({
    "selector": "input[name='username']",
    "value": "testuser"
})

mcp_playwright_playwright_click({
    "selector": "#login-button"
})

// 3. 获取错误日志
mcp_playwright_playwright_console_logs({
    "type": "error"
})

// 4. 查找相关代码
mcp_github_search_code({
    "q": "login authentication repo:my-org/yunshang"
})

// 5. 修复后测试
mcp_playwright_playwright_screenshot({
    "name": "login_fix_test"
})

// 6. 更新Issue
mcp_github_update_issue({
    "issue_number": 789,
    "state": "closed",
    "body": "Bug已修复，测试通过"
})
```

## 📋 MCP工具使用检查清单

### 开发前检查
- [ ] 使用Shrimp Task Manager规划任务
- [ ] 使用OpenMemory搜索相关经验
- [ ] 使用思考工具分析需求
- [ ] 使用推理工具制定技术方案
- [ ] 获取相关技术文档
- [ ] 创建GitHub Issue记录任务

### 开发中检查
- [ ] 使用Shrimp Task Manager执行任务指导
- [ ] 使用UI工具生成组件
- [ ] 定期更新项目信息
- [ ] 使用浏览器工具测试功能
- [ ] 记录开发思考过程
- [ ] 使用OpenMemory搜索技术问题解决方案

### 开发后检查
- [ ] 使用Shrimp Task Manager验证任务完成
- [ ] 进行全面功能测试
- [ ] 收集用户反馈
- [ ] 创建PR并请求审查
- [ ] 更新相关文档
- [ ] 使用OpenMemory记录开发经验和决策

### 维护阶段检查
- [ ] 监控系统性能
- [ ] 收集用户反馈
- [ ] 及时修复问题
- [ ] 持续优化改进
- [ ] 使用OpenMemory记录维护经验
- [ ] 使用Shrimp Task Manager管理维护任务

## 🚨 强制执行规则

### 核心强制要求
1. **任何开发任务都必须首先考虑使用MCP工具**
2. **每次代码修改后必须更新项目信息**
3. **所有功能必须通过自动化测试验证**
4. **必须收集用户反馈并持续改进**
5. **重要变更必须在GitHub上记录**

### 开发过程强制使用规范
- **每日开发开始前**: 必须使用`mcp_codelf_get-project-info`了解项目状态，使用`mcp_shrimp-task-manager_list_tasks`查看待办任务
- **项目启动时**: 必须使用`mcp_shrimp-task-manager_plan_task`进行任务规划和`mcp_openmemory_search-memories`搜索相关经验
- **需求分析时**: 必须使用`mcp_shrimp-task-manager_analyze_task`进行深度分析和`mcp_think-tool_think`记录思考过程
- **技术设计时**: 必须使用`mcp_openmemory_search-memories`搜索类似技术方案和最佳实践
- **编写代码时**: 必须使用`mcp_21st-devmagic`工具生成和优化组件，使用`mcp_shrimp-task-manager_execute_task`获取执行指导
- **遇到问题时**: 必须使用`mcp_openmemory_search-memories`搜索解决方案，使用`mcp_think-tool_think`记录分析过程
- **功能完成后**: 必须使用`mcp_playwright`工具进行自动化测试，使用`mcp_shrimp-task-manager_verify_task`验证任务完成
- **提交代码前**: 必须使用`mcp_github`工具创建PR和Issue，使用`mcp_openmemory_add-memory`记录重要决策
- **阶段完成后**: 必须使用`mcp_mcp-feedback-enhanced`收集反馈，使用`mcp_openmemory_add-memory`记录经验教训
- **问题解决后**: 必须使用`mcp_openmemory_add-memory`记录解决方案供后续参考
- **技术决策时**: 必须使用`mcp_openmemory_add-memory`记录决策依据和原因

### 违规处理
- 发现未使用MCP工具的代码将被退回重做
- 连续违规将影响代码合并权限
- 项目负责人有权要求重新按MCP规范实现功能

## 📚 参考资源

- [主配置文件](mdc:config.py) - 系统配置参考
- [数据模型设计](mdc:docs/data-models-design.md) - 数据库设计
- [系统架构文档](mdc:docs/system-architecture.md) - 架构设计
- [开发计划](mdc:docs/development-plan.md) - 开发流程
- [Streamlit规范](mdc:.cursor/rules/streamlit-dev-standards.mdc) - 前端开发规范

---

**重要提醒**: 本指南是强制性执行标准，所有开发活动必须严格遵循MCP工具使用规范。充分利用MCP工具将显著提升开发效率、代码质量和项目成功率。


