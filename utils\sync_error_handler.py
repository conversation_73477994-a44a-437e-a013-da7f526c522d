#!/usr/bin/env python3
"""
同步错误处理器

提供增强的异常处理和日志记录功能，确保所有错误都能被完整记录和分析。
"""

import logging
import traceback
import json
import datetime
import os
from typing import Dict, Any, List, Optional, Callable
from functools import wraps

logger = logging.getLogger(__name__)


class SyncErrorHandler:
    """同步错误处理器"""

    def __init__(self, log_file: Optional[str] = None):
        """
        初始化错误处理器

        Args:
            log_file: 日志文件路径
        """
        self.error_count = 0
        self.errors = []
        self.log_file = log_file or "logs/sync_errors.log"

        # 确保日志目录存在
        os.makedirs(os.path.dirname(self.log_file), exist_ok=True)

        # 设置文件日志处理器
        self._setup_file_logger()

    def _setup_file_logger(self):
        """设置文件日志处理器"""
        try:
            # 创建文件处理器
            file_handler = logging.FileHandler(
                self.log_file, mode="a", encoding="utf-8"
            )
            file_handler.setLevel(logging.DEBUG)

            # 设置详细的日志格式
            formatter = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"
            )
            file_handler.setFormatter(formatter)

            # 添加到根日志器
            root_logger = logging.getLogger()
            root_logger.addHandler(file_handler)

            logger.info(f"错误日志将写入: {self.log_file}")

        except Exception as e:
            logger.error(f"设置文件日志处理器失败: {e}")

    def log_exception(
        self,
        exception: Exception,
        context: str,
        extra_data: Optional[Dict[str, Any]] = None,
    ) -> str:
        """
        记录异常的详细信息

        Args:
            exception: 异常对象
            context: 异常发生的上下文
            extra_data: 额外的上下文数据

        Returns:
            错误ID用于跟踪
        """
        self.error_count += 1
        error_id = f"ERR_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}_{self.error_count:03d}"

        # 构建错误信息
        error_info = {
            "error_id": error_id,
            "timestamp": datetime.datetime.now().isoformat(),
            "context": context,
            "exception_type": type(exception).__name__,
            "exception_message": str(exception),
            "traceback": traceback.format_exc(),
            "extra_data": extra_data or {},
        }

        # 记录到内存
        self.errors.append(error_info)

        # 记录到日志
        logger.error(f"[{error_id}] {context}")
        logger.error(f"[{error_id}] 异常类型: {type(exception).__name__}")
        logger.error(f"[{error_id}] 异常信息: {str(exception)}")

        if extra_data:
            logger.error(
                f"[{error_id}] 额外数据: {json.dumps(extra_data, ensure_ascii=False, indent=2)}"
            )

        logger.error(f"[{error_id}] 堆栈跟踪:\n{traceback.format_exc()}")

        # 记录到文件
        self._write_error_to_file(error_info)

        return error_id

    def _write_error_to_file(self, error_info: Dict[str, Any]):
        """将错误信息写入文件"""
        try:
            with open(self.log_file, "a", encoding="utf-8") as f:
                f.write("\n" + "=" * 80 + "\n")
                f.write(f"错误ID: {error_info['error_id']}\n")
                f.write(f"时间: {error_info['timestamp']}\n")
                f.write(f"上下文: {error_info['context']}\n")
                f.write(f"异常类型: {error_info['exception_type']}\n")
                f.write(f"异常信息: {error_info['exception_message']}\n")

                if error_info["extra_data"]:
                    f.write("额外数据:\n")
                    f.write(
                        json.dumps(
                            error_info["extra_data"], ensure_ascii=False, indent=2
                        )
                    )
                    f.write("\n")

                f.write("堆栈跟踪:\n")
                f.write(error_info["traceback"])
                f.write("\n" + "=" * 80 + "\n")

        except Exception as e:
            logger.error(f"写入错误文件失败: {e}")

    def with_error_handling(
        self, context: str, extra_data: Optional[Dict[str, Any]] = None
    ):
        """
        装饰器：为函数添加错误处理

        Args:
            context: 错误上下文描述
            extra_data: 额外的上下文数据
        """

        def decorator(func: Callable):
            @wraps(func)
            def wrapper(*args, **kwargs):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    # 构建额外数据
                    func_extra_data = {
                        "function_name": func.__name__,
                        "args": (
                            str(args)[:200] + "..."
                            if len(str(args)) > 200
                            else str(args)
                        ),
                        "kwargs": (
                            str(kwargs)[:200] + "..."
                            if len(str(kwargs)) > 200
                            else str(kwargs)
                        ),
                    }

                    if extra_data:
                        func_extra_data.update(extra_data)

                    # 记录异常
                    error_id = self.log_exception(
                        e, f"{context} - {func.__name__}", func_extra_data
                    )

                    # 重新抛出异常，但添加错误ID
                    raise Exception(f"[{error_id}] {str(e)}") from e

            return wrapper

        return decorator

    def get_error_summary(self) -> Dict[str, Any]:
        """获取错误摘要"""
        if not self.errors:
            return {"total_errors": 0, "error_types": {}}

        error_types = {}
        for error in self.errors:
            error_type = error["exception_type"]
            error_types[error_type] = error_types.get(error_type, 0) + 1

        return {
            "total_errors": len(self.errors),
            "error_types": error_types,
            "recent_errors": self.errors[-5:] if len(self.errors) > 5 else self.errors,
            "log_file": self.log_file,
        }

    def clear_errors(self):
        """清空错误记录"""
        self.errors.clear()
        self.error_count = 0
        logger.info("错误记录已清空")


# 全局错误处理器实例
error_handler = SyncErrorHandler()


def sync_error_handler(context: str, extra_data: Optional[Dict[str, Any]] = None):
    """
    同步错误处理装饰器

    Args:
        context: 错误上下文
        extra_data: 额外数据
    """
    return error_handler.with_error_handling(context, extra_data)


def log_sync_exception(
    exception: Exception, context: str, extra_data: Optional[Dict[str, Any]] = None
) -> str:
    """
    记录同步异常

    Args:
        exception: 异常对象
        context: 上下文信息
        extra_data: 额外数据

    Returns:
        错误ID
    """
    return error_handler.log_exception(exception, context, extra_data)


def get_sync_error_summary() -> Dict[str, Any]:
    """获取同步错误摘要"""
    return error_handler.get_error_summary()


def clear_sync_errors():
    """清空同步错误记录"""
    error_handler.clear_errors()


# 示例用法
if __name__ == "__main__":
    # 测试错误处理器
    @sync_error_handler("测试函数")
    def test_function():
        raise ValueError("这是一个测试异常")

    try:
        test_function()
    except Exception as e:
        print(f"捕获异常: {e}")

    # 显示错误摘要
    summary = get_sync_error_summary()
    print("错误摘要:")
    print(json.dumps(summary, ensure_ascii=False, indent=2))
