#!/usr/bin/env python3
"""
产品管理器

集成API获取、数据处理和数据库存储功能
"""

import logging
from typing import Dict, List, Optional, Tuple, Any
from utils.api_client import ZKMallClient
from services.product_data_processor import ProductDataProcessor
from utils.api_response_normalizer import ApiResponseNormalizer

logger = logging.getLogger(__name__)


class ProductManager:
    """产品管理器"""

    def __init__(self):
        """初始化管理器"""
        self.api_client = ZKMallClient()
        self.data_processor = ProductDataProcessor()
        self.normalizer = ApiResponseNormalizer()

    def fetch_and_store_products(
        self, **params
    ) -> Tuple[List[Dict], int, Dict[str, int]]:
        """
        获取并存储产品数据

        Args:
            **params: API查询参数

        Returns:
            (产品数据列表, 总数, 处理统计)
        """
        try:
            logger.info(f"开始获取产品数据，参数: {params}")

            # 1. 从API获取数据
            api_response = self.api_client.get_products_with_count(**params)
            if not api_response:
                logger.warning("API返回空响应")
                return [], 0, {"processed": 0, "errors": 1, "skipped": 0, "total": 0}

            # 2. 标准化API响应
            products_data, total_count = self._extract_products_from_response(
                api_response
            )

            if not products_data:
                logger.warning("未从API响应中提取到产品数据")
                return (
                    [],
                    total_count,
                    {"processed": 0, "errors": 0, "skipped": 0, "total": 0},
                )

            logger.info(
                f"从API获取到 {len(products_data)} 条产品数据，总计: {total_count}"
            )

            # 3. 数据预处理 - 确保数据安全
            safe_products_data = self._preprocess_products_data(products_data)

            # 4. 批量处理并存储到数据库
            processing_stats = self.data_processor.process_products_batch(
                safe_products_data
            )

            logger.info(f"产品数据处理完成: {processing_stats}")

            return safe_products_data, total_count, processing_stats

        except Exception as e:
            logger.error(f"获取并存储产品数据失败: {e}", exc_info=True)
            return [], 0, {"processed": 0, "errors": 1, "skipped": 0, "total": 0}

    def _extract_products_from_response(
        self, response: Dict[str, Any]
    ) -> Tuple[List[Dict], int]:
        """
        从API响应中提取产品数据

        Args:
            response: API响应

        Returns:
            (产品数据列表, 总数)
        """
        products_data = []
        total_count = 0

        try:
            # 新格式：直接包含products和pagination字段
            if "products" in response and "pagination" in response:
                products_data = response.get("products", [])
                pagination = response.get("pagination", {})
                total_count = pagination.get("total", response.get("total", 0))
                logger.debug("使用新格式提取产品数据")

            # 旧格式：包含data字段
            elif "data" in response:
                data = response["data"]
                products_data = data.get("records", data.get("rows", []))
                total_count = data.get("total", 0)
                logger.debug("使用旧格式提取产品数据")

            # 其他格式：尝试直接提取
            else:
                products_data = response.get("rows", response.get("records", []))
                total_count = response.get("total", 0)
                logger.debug("使用直接格式提取产品数据")

            # 使用标准化器进一步处理
            if products_data:
                normalized_data = self.normalizer.normalize_list_response(
                    response, "product"
                )
                if normalized_data and len(normalized_data) > 0:
                    products_data = normalized_data
                    logger.debug(f"标准化器处理后得到 {len(products_data)} 条数据")

        except Exception as e:
            logger.error(f"提取产品数据失败: {e}")
            products_data = []
            total_count = 0

        return products_data, total_count

    def _preprocess_products_data(self, products_data: List[Dict]) -> List[Dict]:
        """
        预处理产品数据，确保数据安全

        Args:
            products_data: 原始产品数据

        Returns:
            预处理后的安全数据
        """
        safe_products_data = []

        for product in products_data:
            if not product or not isinstance(product, dict):
                continue

            try:
                # 确保关键字段存在且安全
                safe_product = product.copy()

                # 确保paramInfoList字段安全
                if "paramInfoList" in safe_product:
                    if safe_product["paramInfoList"] is None:
                        safe_product["paramInfoList"] = []
                    elif not isinstance(safe_product["paramInfoList"], list):
                        logger.warning(
                            f"产品 {safe_product.get('id', 'unknown')} 的paramInfoList不是列表类型"
                        )
                        safe_product["paramInfoList"] = []

                # 确保其他可能为None的列表字段也安全
                list_fields = [
                    "labelList",
                    "specList",
                    "instructionsList",
                    "commonProblemList",
                    "accessoryList",
                    "showForCompanyList",
                    "productGenListHandle",
                    "productGenListInstall",
                    "productGenListFault",
                    "productGenListLearning",
                ]

                for field in list_fields:
                    if field in safe_product:
                        if safe_product[field] is None:
                            safe_product[field] = []
                        elif not isinstance(safe_product[field], list):
                            logger.warning(
                                f"产品 {safe_product.get('id', 'unknown')} 的{field}不是列表类型"
                            )
                            safe_product[field] = []

                # 确保基本字段存在
                if "id" not in safe_product and "productId" in safe_product:
                    safe_product["id"] = safe_product["productId"]

                if "name" not in safe_product and "productName" in safe_product:
                    safe_product["name"] = safe_product["productName"]

                safe_products_data.append(safe_product)

            except Exception as e:
                logger.error(f"预处理产品数据失败: {e}")
                continue

        logger.info(f"预处理完成，安全数据: {len(safe_products_data)} 条")
        return safe_products_data

    def get_products_for_display(self, **params) -> Tuple[List[Dict], int]:
        """
        获取用于显示的产品数据（不存储到数据库）

        Args:
            **params: 查询参数

        Returns:
            (产品数据列表, 总数)
        """
        try:
            # 从API获取数据
            api_response = self.api_client.get_products_with_count(**params)
            if not api_response:
                return [], 0

            # 提取和预处理数据
            products_data, total_count = self._extract_products_from_response(
                api_response
            )
            safe_products_data = self._preprocess_products_data(products_data)

            return safe_products_data, total_count

        except Exception as e:
            logger.error(f"获取显示数据失败: {e}")
            return [], 0

    def sync_all_products(self, batch_size: int = 50) -> Dict[str, int]:
        """
        同步所有产品数据到数据库

        Args:
            batch_size: 批处理大小

        Returns:
            处理统计信息
        """
        total_stats = {"processed": 0, "errors": 0, "skipped": 0, "total": 0}

        try:
            page = 1
            page_size = batch_size

            while True:
                logger.info(f"同步第 {page} 页产品数据")

                # 获取一页数据
                params = {"current": page, "pageSize": page_size}
                products_data, total_count, batch_stats = self.fetch_and_store_products(
                    **params
                )

                # 累计统计
                for key in total_stats:
                    total_stats[key] += batch_stats.get(key, 0)

                # 如果没有更多数据，退出循环
                if not products_data or len(products_data) < page_size:
                    break

                page += 1

            logger.info(f"产品数据同步完成: {total_stats}")
            return total_stats

        except Exception as e:
            logger.error(f"同步所有产品数据失败: {e}")
            total_stats["errors"] += 1
            return total_stats
