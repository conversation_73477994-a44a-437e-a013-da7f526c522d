#!/usr/bin/env python3
"""
云商数据获取现状分析脚本

全面分析当前云商API数据获取情况，检查产品知识内容的解析和存储状态
"""

import sys
import os
import logging
import traceback
from typing import Dict, Any, List, Optional

# 添加项目路径
sys.path.append(".")

# 设置环境变量
os.environ["DATABASE_HOST"] = "***********"
os.environ["DATABASE_PORT"] = "5432"
os.environ["DATABASE_NAME"] = "product"
os.environ["DATABASE_USER"] = "username"
os.environ["DATABASE_PASSWORD"] = "123456"
os.environ["ZKMALL_API_BASE"] = "https://zkmall.zktecoiot.com"
os.environ["ZKMALL_USERNAME"] = "18929343717"
os.environ["ZKMALL_PASSWORD"] = "Zk@123456"

from utils.auth import AuthManager
from utils.api_client import ZKMallClient
from utils.database import DatabaseManager
from services.sync_service import SyncService

# 设置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class CloudDataAnalyzer:
    """云商数据分析器"""

    def __init__(self):
        """初始化分析器"""
        self.client = None
        self.db_manager = None
        self.analysis_results = {
            "api_status": {},
            "database_status": {},
            "knowledge_content_analysis": {},
            "attachment_analysis": {},
            "recommendations": [],
        }

    def analyze_all(self) -> Dict[str, Any]:
        """执行完整分析"""
        print("🔍 开始云商数据获取现状分析...")
        print("=" * 60)

        try:
            # 1. API连接分析
            self._analyze_api_connection()

            # 2. 数据库状态分析
            self._analyze_database_status()

            # 3. 产品知识内容分析
            self._analyze_knowledge_content()

            # 4. 附件处理分析
            self._analyze_attachment_processing()

            # 5. 生成建议
            self._generate_recommendations()

            # 6. 输出分析报告
            self._print_analysis_report()

            return self.analysis_results

        except Exception as e:
            logger.error(f"分析过程出错: {e}")
            traceback.print_exc()
            return self.analysis_results

    def _analyze_api_connection(self):
        """分析API连接状态"""
        print("\n1. 📡 API连接状态分析")
        print("-" * 40)

        try:
            # 测试认证
            if AuthManager.ensure_authenticated():
                print("✅ API认证成功")
                user_info = AuthManager.get_current_user()
                if user_info:
                    print(f"   用户: {user_info.get('userName', '未知')}")
                self.analysis_results["api_status"]["auth"] = True
            else:
                print("❌ API认证失败")
                self.analysis_results["api_status"]["auth"] = False
                return

            # 初始化API客户端
            self.client = ZKMallClient()

            # 测试各模块数据获取
            modules = [
                ("products", "产品", self.client.get_products),
                ("cases", "案例", self.client.get_cases),
                ("programmes", "方案", self.client.get_programmes),
                ("information", "资讯", self.client.get_information),
                ("distribution_orders", "配单", self.client.get_distribution_orders),
            ]

            module_results = {}

            for module_name, display_name, api_method in modules:
                try:
                    print(f"   测试{display_name}数据获取...")
                    data = api_method(pageSize=2, current=1)

                    if data and len(data) > 0:
                        print(f"   ✅ {display_name}: 成功获取 {len(data)} 条数据")
                        module_results[module_name] = {
                            "status": "success",
                            "count": len(data),
                            "sample_data": data[0] if data else None,
                        }
                    else:
                        print(f"   ⚠️ {display_name}: 未获取到数据")
                        module_results[module_name] = {
                            "status": "no_data",
                            "count": 0,
                            "sample_data": None,
                        }

                except Exception as e:
                    print(f"   ❌ {display_name}: 获取失败 - {str(e)[:100]}")
                    module_results[module_name] = {
                        "status": "error",
                        "error": str(e),
                        "count": 0,
                    }

            self.analysis_results["api_status"]["modules"] = module_results

        except Exception as e:
            print(f"❌ API连接分析失败: {e}")
            self.analysis_results["api_status"]["error"] = str(e)

    def _analyze_database_status(self):
        """分析数据库状态"""
        print("\n2. 🗄️ 数据库状态分析")
        print("-" * 40)

        try:
            self.db_manager = DatabaseManager()

            # 检查各表数据量
            tables = [
                "products",
                "cases",
                "programmes",
                "information",
                "distribution_orders",
                "attachments",
            ]
            table_stats = {}

            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    print("   数据表统计:")

                    for table in tables:
                        try:
                            cursor.execute(f"SELECT COUNT(*) FROM {table}")
                            count = cursor.fetchone()[0]
                            print(f"     {table}: {count} 条记录")
                            table_stats[table] = count
                        except Exception as e:
                            print(f"     {table}: 查询失败 - {e}")
                            table_stats[table] = -1

            self.analysis_results["database_status"]["table_stats"] = table_stats

        except Exception as e:
            print(f"❌ 数据库状态分析失败: {e}")
            self.analysis_results["database_status"]["error"] = str(e)

    def _analyze_knowledge_content(self):
        """分析产品知识内容"""
        print("\n3. 📚 产品知识内容分析")
        print("-" * 40)

        try:
            if not self.db_manager:
                print("❌ 数据库连接不可用")
                return

            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    # 获取产品总数
                    cursor.execute("SELECT COUNT(*) FROM products")
                    total_products = cursor.fetchone()[0]

                    if total_products == 0:
                        print("   ⚠️ 数据库中没有产品数据")
                        self.analysis_results["knowledge_content_analysis"][
                            "total_products"
                        ] = 0
                        return

                    print(f"   总产品数: {total_products}")

                    # 分析知识内容字段
                    knowledge_fields = [
                        "introduction",
                        "details",
                        "param_info",
                        "use_to",
                        "qualifications",
                        "instructions",
                        "guide",
                        "common_problem",
                    ]

                    field_stats = {}
                    print("   知识内容字段填充情况:")

                    for field in knowledge_fields:
                        cursor.execute(
                            f"""
                            SELECT COUNT(*) FROM products
                            WHERE {field} IS NOT NULL AND {field} != ''
                        """
                        )
                        filled_count = cursor.fetchone()[0]
                        percentage = (filled_count / total_products) * 100

                        status = (
                            "✅"
                            if percentage > 70
                            else "⚠️" if percentage > 30 else "❌"
                        )
                        print(
                            f"     {status} {field}: {filled_count}/{total_products} ({percentage:.1f}%)"
                        )

                        field_stats[field] = {
                            "filled_count": filled_count,
                            "total_count": total_products,
                            "percentage": percentage,
                        }

                    # 分析API数据中的知识内容
                    if self.client and self.analysis_results["api_status"].get(
                        "modules", {}
                    ).get("products", {}).get("sample_data"):
                        sample_product = self.analysis_results["api_status"]["modules"][
                            "products"
                        ]["sample_data"]
                        print("\n   API数据样本分析:")

                        api_knowledge_fields = [
                            "introduction",
                            "details",
                            "paramInfo",
                            "useTo",
                            "qualifications",
                            "instructions",
                            "guide",
                            "commonProblem",
                        ]
                        api_field_stats = {}

                        for field in api_knowledge_fields:
                            value = sample_product.get(field)
                            has_content = bool(value and str(value).strip())
                            status = "✅" if has_content else "❌"
                            content_length = len(str(value)) if value else 0
                            print(
                                f"     {status} {field}: {'有内容' if has_content else '无内容'} ({content_length} 字符)"
                            )

                            api_field_stats[field] = {
                                "has_content": has_content,
                                "content_length": content_length,
                            }

                        self.analysis_results["knowledge_content_analysis"][
                            "api_sample"
                        ] = api_field_stats

                    self.analysis_results["knowledge_content_analysis"][
                        "total_products"
                    ] = total_products
                    self.analysis_results["knowledge_content_analysis"][
                        "field_stats"
                    ] = field_stats

        except Exception as e:
            print(f"❌ 产品知识内容分析失败: {e}")
            self.analysis_results["knowledge_content_analysis"]["error"] = str(e)

    def _analyze_attachment_processing(self):
        """分析附件处理情况"""
        print("\n4. 📎 附件处理分析")
        print("-" * 40)

        try:
            if not self.db_manager:
                print("❌ 数据库连接不可用")
                return

            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    # 检查附件表
                    cursor.execute("SELECT COUNT(*) FROM attachments")
                    total_attachments = cursor.fetchone()[0]
                    print(f"   附件总数: {total_attachments}")

                    if total_attachments > 0:
                        # 按类别统计
                        cursor.execute(
                            "SELECT category, COUNT(*) FROM attachments GROUP BY category"
                        )
                        category_stats = cursor.fetchall()
                        print("   按类别分布:")
                        for category, count in category_stats:
                            print(f"     {category}: {count} 个")

                        # 检查下载状态
                        cursor.execute(
                            """
                            SELECT
                                COUNT(*) as total,
                                COUNT(CASE WHEN local_path IS NOT NULL THEN 1 END) as downloaded
                            FROM attachments
                        """
                        )
                        result = cursor.fetchone()
                        downloaded_count = result[1] if result else 0
                        download_percentage = (
                            (downloaded_count / total_attachments) * 100
                            if total_attachments > 0
                            else 0
                        )

                        print(
                            f"   下载状态: {downloaded_count}/{total_attachments} ({download_percentage:.1f}%)"
                        )

                        self.analysis_results["attachment_analysis"] = {
                            "total_attachments": total_attachments,
                            "downloaded_count": downloaded_count,
                            "download_percentage": download_percentage,
                            "category_stats": (
                                dict(category_stats) if category_stats else {}
                            ),
                        }
                    else:
                        print("   ⚠️ 没有附件数据")
                        self.analysis_results["attachment_analysis"] = {
                            "total_attachments": 0,
                            "downloaded_count": 0,
                            "download_percentage": 0,
                        }

                    # 检查产品附件字段
                    cursor.execute("SELECT COUNT(*) FROM products")
                    total_products = cursor.fetchone()[0]

                    if total_products > 0:
                        attachment_fields = [
                            "other",
                            "qualifications",
                            "small_img",
                            "banner",
                        ]
                        print("   产品附件字段填充情况:")

                        for field in attachment_fields:
                            cursor.execute(
                                f"""
                                SELECT COUNT(*) FROM products
                                WHERE {field} IS NOT NULL AND {field} != ''
                            """
                            )
                            filled_count = cursor.fetchone()[0]
                            percentage = (filled_count / total_products) * 100

                            status = (
                                "✅"
                                if percentage > 50
                                else "⚠️" if percentage > 0 else "❌"
                            )
                            print(
                                f"     {status} {field}: {filled_count}/{total_products} ({percentage:.1f}%)"
                            )

        except Exception as e:
            print(f"❌ 附件处理分析失败: {e}")
            self.analysis_results["attachment_analysis"]["error"] = str(e)

    def _generate_recommendations(self):
        """生成改进建议"""
        print("\n5. 💡 改进建议")
        print("-" * 40)

        recommendations = []

        # API连接建议
        api_status = self.analysis_results.get("api_status", {})
        if not api_status.get("auth"):
            recommendations.append(
                {
                    "category": "API认证",
                    "priority": "high",
                    "issue": "API认证失败",
                    "solution": "检查ZKMALL_USERNAME和ZKMALL_PASSWORD环境变量设置",
                }
            )

        # 数据获取建议
        modules = api_status.get("modules", {})
        for module_name, module_info in modules.items():
            if module_info.get("status") == "error":
                recommendations.append(
                    {
                        "category": "数据获取",
                        "priority": "medium",
                        "issue": f"{module_name}模块数据获取失败",
                        "solution": f"检查{module_name}相关API端点和参数配置",
                    }
                )
            elif module_info.get("status") == "no_data":
                recommendations.append(
                    {
                        "category": "数据获取",
                        "priority": "low",
                        "issue": f"{module_name}模块无数据",
                        "solution": f"确认{module_name}模块在云商平台中是否有数据",
                    }
                )

        # 知识内容建议
        knowledge_analysis = self.analysis_results.get("knowledge_content_analysis", {})
        field_stats = knowledge_analysis.get("field_stats", {})

        for field, stats in field_stats.items():
            percentage = stats.get("percentage", 0)
            if percentage < 30:
                recommendations.append(
                    {
                        "category": "知识内容",
                        "priority": "medium",
                        "issue": f"{field}字段填充率低({percentage:.1f}%)",
                        "solution": f"检查{field}字段的API映射和数据处理逻辑",
                    }
                )

        # 附件处理建议
        attachment_analysis = self.analysis_results.get("attachment_analysis", {})
        download_percentage = attachment_analysis.get("download_percentage", 0)

        if download_percentage < 80:
            recommendations.append(
                {
                    "category": "附件处理",
                    "priority": "medium",
                    "issue": f"附件下载率低({download_percentage:.1f}%)",
                    "solution": "检查附件下载服务和网络连接，优化下载逻辑",
                }
            )

        # 输出建议
        if recommendations:
            for i, rec in enumerate(recommendations, 1):
                priority_icon = (
                    "🔴"
                    if rec["priority"] == "high"
                    else "🟡" if rec["priority"] == "medium" else "🟢"
                )
                print(f"   {i}. {priority_icon} [{rec['category']}] {rec['issue']}")
                print(f"      解决方案: {rec['solution']}")
        else:
            print("   ✅ 系统运行良好，暂无改进建议")

        self.analysis_results["recommendations"] = recommendations

    def _print_analysis_report(self):
        """输出分析报告摘要"""
        print("\n" + "=" * 60)
        print("📊 云商数据获取现状分析报告摘要")
        print("=" * 60)

        # API状态摘要
        api_status = self.analysis_results.get("api_status", {})
        auth_status = "✅ 正常" if api_status.get("auth") else "❌ 失败"
        print(f"🔐 API认证状态: {auth_status}")

        # 数据获取摘要
        modules = api_status.get("modules", {})
        success_count = sum(1 for m in modules.values() if m.get("status") == "success")
        total_modules = len(modules)
        print(f"📡 数据获取状态: {success_count}/{total_modules} 个模块正常")

        # 数据库状态摘要
        db_status = self.analysis_results.get("database_status", {})
        table_stats = db_status.get("table_stats", {})
        total_records = sum(count for count in table_stats.values() if count > 0)
        print(f"🗄️ 数据库状态: 共 {total_records} 条记录")

        # 知识内容摘要
        knowledge_analysis = self.analysis_results.get("knowledge_content_analysis", {})
        field_stats = knowledge_analysis.get("field_stats", {})
        if field_stats:
            avg_percentage = sum(
                stats.get("percentage", 0) for stats in field_stats.values()
            ) / len(field_stats)
            print(f"📚 知识内容完整度: {avg_percentage:.1f}% 平均填充率")

        # 附件处理摘要
        attachment_analysis = self.analysis_results.get("attachment_analysis", {})
        total_attachments = attachment_analysis.get("total_attachments", 0)
        download_percentage = attachment_analysis.get("download_percentage", 0)
        print(
            f"📎 附件处理状态: {total_attachments} 个附件，{download_percentage:.1f}% 已下载"
        )

        # 建议摘要
        recommendations = self.analysis_results.get("recommendations", [])
        high_priority = sum(1 for r in recommendations if r.get("priority") == "high")
        medium_priority = sum(
            1 for r in recommendations if r.get("priority") == "medium"
        )

        if recommendations:
            print(
                f"💡 改进建议: {len(recommendations)} 项 (高优先级: {high_priority}, 中优先级: {medium_priority})"
            )
        else:
            print("💡 改进建议: 系统运行良好")

        print("=" * 60)


def main():
    """主函数"""
    analyzer = CloudDataAnalyzer()
    results = analyzer.analyze_all()

    # 保存分析结果
    import json

    with open("cloud_data_analysis_report.json", "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2, default=str)

    print(f"\n📊 分析报告已保存到: cloud_data_analysis_report.json")


if __name__ == "__main__":
    main()
