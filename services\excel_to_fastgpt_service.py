"""
Excel到FastGPT知识库集成服务

该服务整合了Excel导入、产品型号匹配、Markdown转换和FastGPT知识库创建的完整流程。
严格遵循开发规范，使用真实API接口，禁止模拟数据。
"""

import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from services.excel_import_service import ExcelImportService
from services.product_model_matcher import ProductModelMatcher
from services.fastgpt_knowledge_service import FastGPTKnowledgeService
from utils.database import DatabaseManager

logger = logging.getLogger(__name__)


class ExcelToFastGPTService:
    """Excel到FastGPT知识库集成服务"""

    def __init__(self):
        """初始化服务"""
        self.excel_service = ExcelImportService()
        self.matcher_service = ProductModelMatcher()
        self.fastgpt_service = FastGPTKnowledgeService()
        self.db_manager = DatabaseManager()

    def process_excel_to_knowledge_base(
        self,
        uploaded_file,
        dataset_name: str,
        dataset_description: str = "",
        sheet_name: Optional[str] = None,
        header_row: int = 1,
        model_column: str = "产品型号",
        dataset_id_column: str = "知识库ID",
        user_id: str = "system",
        create_new_dataset: bool = True,
        existing_dataset_id: str = "",
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        完整的Excel到FastGPT知识库处理流程

        Args:
            uploaded_file: Streamlit上传的文件对象
            dataset_name: 知识库名称
            dataset_description: 知识库描述
            sheet_name: 工作表名称
            header_row: 标题行号
            model_column: 产品型号列名
            dataset_id_column: FastGPT知识库ID列名
            user_id: 用户ID
            create_new_dataset: 是否创建新知识库
            existing_dataset_id: 现有知识库ID

        Returns:
            Tuple[是否成功, 处理结果详情]
        """
        result = {
            "success": False,
            "batch_id": "",
            "dataset_id": "",
            "total_rows": 0,
            "processed_rows": 0,
            "matched_rows": 0,
            "uploaded_rows": 0,
            "failed_rows": 0,
            "processing_details": {},
            "errors": [],
            "warnings": [],
        }

        try:
            logger.info(f"开始Excel到FastGPT知识库处理流程: {uploaded_file.name}")

            # 第一步：处理Excel文件
            logger.info("步骤1: 处理Excel文件")
            batch_id, excel_result = self.excel_service.process_uploaded_file(
                uploaded_file=uploaded_file,
                sheet_name=sheet_name,
                header_row=header_row,
                model_column=model_column,
                dataset_id_column=dataset_id_column,
                user_id=user_id,
            )

            if not excel_result.get("success", False):
                result["errors"].append(
                    f"Excel文件处理失败: {excel_result.get('message', '未知错误')}"
                )
                return False, result

            result["batch_id"] = batch_id
            result["total_rows"] = excel_result.get("total_rows", 0)
            models_data = excel_result.get("models_data", [])

            logger.info(f"Excel文件处理成功，提取到 {len(models_data)} 个产品型号")

            # 第二步：创建或使用FastGPT知识库
            logger.info("步骤2: 创建/获取FastGPT知识库")
            if create_new_dataset:
                success, dataset_id, fastgpt_result = (
                    self.fastgpt_service.create_dataset(
                        name=dataset_name,
                        description=dataset_description
                        or f"从Excel文件 {uploaded_file.name} 创建的知识库",
                        tags=["产品信息", "Excel导入"],
                    )
                )

                if not success:
                    result["errors"].append(
                        f"FastGPT知识库创建失败: {fastgpt_result.get('error', '未知错误')}"
                    )
                    return False, result

                result["dataset_id"] = dataset_id
                logger.info(f"FastGPT知识库创建成功: {dataset_name} (ID: {dataset_id})")
            else:
                if not existing_dataset_id:
                    result["errors"].append("未提供现有知识库ID")
                    return False, result

                result["dataset_id"] = existing_dataset_id
                logger.info(f"使用现有FastGPT知识库: {existing_dataset_id}")

            # 第三步：批量匹配产品型号
            logger.info("步骤3: 批量匹配产品型号")
            match_result = self.matcher_service.batch_match_models(
                models_data=models_data,
                batch_id=batch_id,
                user_id=user_id,
            )

            result["processed_rows"] = match_result.get("total_count", 0)
            result["matched_rows"] = match_result.get("matched_count", 0)
            result["failed_rows"] = match_result.get("failed_count", 0)

            logger.info(
                f"产品型号匹配完成: 处理 {result['processed_rows']}, 匹配 {result['matched_rows']}, 失败 {result['failed_rows']}"
            )

            # 第四步：获取匹配的产品详细信息
            logger.info("步骤4: 获取产品详细信息")
            matched_products = self._get_matched_products(batch_id)

            if not matched_products:
                result["warnings"].append("没有成功匹配的产品信息")
                logger.warning("没有成功匹配的产品信息")
            else:
                logger.info(f"获取到 {len(matched_products)} 个匹配的产品详细信息")

            # 第五步：上传产品信息到FastGPT知识库
            logger.info("步骤5: 上传产品信息到FastGPT知识库")
            if matched_products:
                upload_success, upload_ids, upload_result = (
                    self.fastgpt_service.batch_upload_product_data(
                        dataset_id=result["dataset_id"],
                        products_data=matched_products,
                        source=f"Excel批量导入-{batch_id}",
                    )
                )

                result["uploaded_rows"] = upload_result.get("success", 0)
                result["processing_details"]["upload_result"] = upload_result

                if upload_success:
                    logger.info(f"产品信息上传成功: {result['uploaded_rows']} 条记录")
                else:
                    result["warnings"].append(
                        f"部分产品信息上传失败: 成功 {upload_result.get('success', 0)}, 失败 {upload_result.get('failed', 0)}"
                    )

            # 第六步：更新处理状态
            logger.info("步骤6: 更新处理状态")
            self.excel_service.update_import_progress(
                batch_id=batch_id,
                processed_rows=result["processed_rows"],
                matched_rows=result["matched_rows"],
                failed_rows=result["failed_rows"],
                status="completed",
            )

            # 记录处理完成
            self._record_processing_completion(
                batch_id=batch_id,
                dataset_id=result["dataset_id"],
                result=result,
            )

            result["success"] = True
            result["processing_details"]["completion_time"] = datetime.now().isoformat()

            logger.info(f"Excel到FastGPT知识库处理流程完成: 批次 {batch_id}")
            return True, result

        except Exception as e:
            error_msg = f"Excel到FastGPT知识库处理异常: {str(e)}"
            logger.error(error_msg)
            result["errors"].append(error_msg)
            return False, result

    def _get_matched_products(self, batch_id: str) -> List[Dict[str, Any]]:
        """
        获取匹配的产品详细信息

        Args:
            batch_id: 批次ID

        Returns:
            匹配的产品信息列表
        """
        try:
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(
                        """
                        SELECT sq.product_id, sq.match_confidence, sq.match_type,
                               sq.raw_data, p.id, p.name, p.model, p.category_id,
                               p.category_name, p.brand_name, p.price, p.description,
                               p.specification, p.application, p.features
                        FROM fastgpt_sync_queue sq
                        LEFT JOIN products p ON sq.product_id = p.id
                        WHERE sq.batch_id = %s AND sq.status = 'pending'
                        ORDER BY sq.match_confidence DESC
                    """,
                        (batch_id,),
                    )

                    products = []
                    for row in cursor.fetchall():
                        if row[4]:  # 确保产品存在
                            product = {
                                "id": row[4],
                                "name": row[5],
                                "model": row[6],
                                "categoryId": row[7],
                                "categoryName": row[8],
                                "brandName": row[9],
                                "price": row[10],
                                "description": row[11],
                                "specification": row[12],
                                "application": row[13],
                                "features": row[14],
                                "match_confidence": row[1],
                                "match_type": row[2],
                            }
                            products.append(product)

                    return products

        except Exception as e:
            logger.error(f"获取匹配产品信息失败: {e}")
            return []

    def _record_processing_completion(
        self, batch_id: str, dataset_id: str, result: Dict[str, Any]
    ) -> None:
        """
        记录处理完成信息

        Args:
            batch_id: 批次ID
            dataset_id: 知识库ID
            result: 处理结果
        """
        try:
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(
                        """
                        INSERT INTO excel_to_fastgpt_log (
                            batch_id, dataset_id, total_rows, matched_rows,
                            uploaded_rows, failed_rows, processing_result,
                            created_at
                        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    """,
                        (
                            batch_id,
                            dataset_id,
                            result.get("total_rows", 0),
                            result.get("matched_rows", 0),
                            result.get("uploaded_rows", 0),
                            result.get("failed_rows", 0),
                            str(result),
                            datetime.now(),
                        ),
                    )
                    conn.commit()

        except Exception as e:
            logger.error(f"记录处理完成信息失败: {e}")

    def get_processing_history(
        self, limit: int = 50, user_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        获取处理历史记录

        Args:
            limit: 返回记录数限制
            user_id: 用户ID过滤

        Returns:
            处理历史记录列表
        """
        try:
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    base_query = """
                        SELECT eftl.batch_id, eftl.dataset_id, eftl.total_rows,
                               eftl.matched_rows, eftl.uploaded_rows, eftl.failed_rows,
                               eftl.created_at, eir.filename, eir.user_id
                        FROM excel_to_fastgpt_log eftl
                        LEFT JOIN excel_import_records eir ON eftl.batch_id = eir.batch_id
                    """

                    if user_id:
                        query = (
                            base_query
                            + " WHERE eir.user_id = %s ORDER BY eftl.created_at DESC LIMIT %s"
                        )
                        cursor.execute(query, (user_id, limit))
                    else:
                        query = base_query + " ORDER BY eftl.created_at DESC LIMIT %s"
                        cursor.execute(query, (limit,))

                    history = []
                    for row in cursor.fetchall():
                        history.append(
                            {
                                "batch_id": row[0],
                                "dataset_id": row[1],
                                "total_rows": row[2],
                                "matched_rows": row[3],
                                "uploaded_rows": row[4],
                                "failed_rows": row[5],
                                "created_at": row[6],
                                "filename": row[7],
                                "user_id": row[8],
                            }
                        )

                    return history

        except Exception as e:
            logger.error(f"获取处理历史记录失败: {e}")
            return []


# 便捷函数
def process_excel_to_fastgpt(
    uploaded_file,
    dataset_name: str,
    dataset_description: str = "",
    create_new_dataset: bool = True,
    existing_dataset_id: str = "",
    user_id: str = "system",
) -> Tuple[bool, Dict[str, Any]]:
    """
    便捷函数：Excel到FastGPT知识库处理

    Args:
        uploaded_file: 上传的Excel文件
        dataset_name: 知识库名称
        dataset_description: 知识库描述
        create_new_dataset: 是否创建新知识库
        existing_dataset_id: 现有知识库ID
        user_id: 用户ID

    Returns:
        Tuple[是否成功, 处理结果]
    """
    service = ExcelToFastGPTService()
    return service.process_excel_to_knowledge_base(
        uploaded_file=uploaded_file,
        dataset_name=dataset_name,
        dataset_description=dataset_description,
        create_new_dataset=create_new_dataset,
        existing_dataset_id=existing_dataset_id,
        user_id=user_id,
    )
