import streamlit as st
import logging
import pandas as pd
from typing import Optional, Dict, Any, List
from utils.session import SessionManager
from utils.logging_config import get_logger
from utils.api_client import ZKMallClient

# 配置日志
logger = get_logger()


def main():
    """资讯管理页面主函数"""
    # 页面配置
    st.set_page_config(
        page_title="资讯管理 - 云商系统",
        page_icon="📰",
        layout="wide",
        initial_sidebar_state="expanded",
    )

    # 初始化session
    initialize_session()

    # 渲染页面
    render_header()
    render_sidebar()
    render_content()
    render_footer()


def initialize_session():
    """初始化页面session状态"""
    defaults = {
        "current_information": None,
        "search_term": "",
        "filter_category": "all",
        "current_page": 1,
        "page_size": 20,
    }

    for key, value in defaults.items():
        if f"information_management_{key}" not in st.session_state:
            SessionManager.set(f"information_management_{key}", value)


@st.cache_data(ttl=300)
def load_information_from_api(
    search_term: str = "",
    current_page: int = 1,
    page_size: int = 20,
    product_id: Optional[int] = None,
    category: Optional[str] = None,
) -> Dict[str, Any]:
    """
    从API加载资讯数据

    Args:
        search_term: 搜索关键词
        current_page: 当前页码
        page_size: 每页数量
        product_id: 产品ID（可选）
        category: 分类（可选）

    Returns:
        包含资讯数据和分页信息的字典
    """
    try:
        client = ZKMallClient()

        # 构建查询参数
        params = {"current": current_page, "pageSize": page_size}

        if product_id:
            params["productId"] = product_id

        if category and category != "all":
            # 根据category映射到具体的分类ID
            category_mapping = {
                "news": 101,  # 新闻资讯分类ID
                "announcement": 102,  # 公告通知分类ID
                "product": 103,  # 产品动态分类ID
                "technology": 104,  # 技术文章分类ID
            }
            if category in category_mapping:
                params["category"] = category_mapping[category]

        # 调用API获取资讯数据
        information = client.get_information(**params)

        logger.info(f"成功获取资讯数据，数量: {len(information)}")

        # 如果有搜索关键词，进行本地过滤
        if search_term:
            filtered_information = []
            for info in information:
                if (
                    search_term.lower() in info.get("title", "").lower()
                    or search_term.lower() in info.get("details", "").lower()
                    or search_term.lower() in info.get("categoryName", "").lower()
                ):
                    filtered_information.append(info)
            information = filtered_information

        return {
            "information": information,
            "total": len(information),
            "current_page": current_page,
            "page_size": page_size,
        }

    except Exception as e:
        logger.error(f"获取资讯数据失败: {e}")
        st.error(f"获取资讯数据失败: {e}")
        return {
            "information": [],
            "total": 0,
            "current_page": current_page,
            "page_size": page_size,
        }


def render_header():
    """渲染页面头部"""
    st.title("📰 资讯管理")
    st.markdown("管理和查看云商系统中的资讯信息")

    # 导航面包屑
    st.markdown("🏢 [首页](/) > 📰 资讯管理")


def render_sidebar():
    """渲染侧边栏"""
    with st.sidebar:
        st.subheader("📰 资讯管理")

        # 搜索功能
        search_term = st.text_input(
            "搜索资讯",
            value=SessionManager.get("information_management_search_term", ""),
            placeholder="输入资讯标题或关键词",
        )
        SessionManager.set("information_management_search_term", search_term)

        # 类别筛选
        filter_category = st.selectbox(
            "资讯类别",
            options=["all", "news", "announcement", "product", "technology"],
            format_func=lambda x: {
                "all": "全部类别",
                "news": "新闻资讯",
                "announcement": "公告通知",
                "product": "产品动态",
                "technology": "技术文章",
            }[x],
            index=["all", "news", "announcement", "product", "technology"].index(
                SessionManager.get("information_management_filter_category", "all")
            ),
        )
        SessionManager.set("information_management_filter_category", filter_category)

        # 分页设置
        page_size = st.selectbox(
            "每页显示数量",
            options=[10, 20, 50, 100],
            index=[10, 20, 50, 100].index(
                SessionManager.get("information_management_page_size", 20)
            ),
        )
        SessionManager.set("information_management_page_size", page_size)

        if st.button("🔄 刷新数据", use_container_width=True):
            st.cache_data.clear()
            st.rerun()

        if st.button("🏠 返回首页", use_container_width=True):
            st.switch_page("main.py")


def render_content():
    """渲染主要内容"""
    tab1, tab2, tab3 = st.tabs(["资讯列表", "资讯详情", "操作记录"])

    with tab1:
        render_information_list()

    with tab2:
        render_information_detail()

    with tab3:
        render_operation_log()


def render_information_list():
    """渲染资讯列表"""
    st.subheader("资讯列表")

    # 操作按钮
    col1, col2, col3 = st.columns([1, 1, 2])
    with col1:
        if st.button("➕ 新增资讯"):
            st.info("新增资讯功能开发中...")

    with col2:
        if st.button("📤 导出数据"):
            st.info("导出功能开发中...")

    # 获取搜索参数
    search_term = SessionManager.get("information_management_search_term", "")
    filter_category = SessionManager.get(
        "information_management_filter_category", "all"
    )
    current_page = int(SessionManager.get("information_management_current_page", 1))
    page_size = int(SessionManager.get("information_management_page_size", 20))

    # 数据展示区域
    with st.container(border=True):
        with st.spinner("正在加载资讯数据..."):
            # 从API获取真实数据
            data = load_information_from_api(
                search_term=search_term,
                current_page=current_page,
                page_size=page_size,
                category=filter_category,
            )

            information = data["information"]
            total = data["total"]

            if information:
                # 显示数据统计
                st.info(f"共找到 {total} 条资讯")

                # 转换为DataFrame显示
                df_data = []
                for info in information:
                    df_data.append(
                        {
                            "ID": info.get("id", ""),
                            "标题": info.get("title", ""),
                            "分类": info.get("categoryName", ""),
                            "详情": (
                                info.get("details", "")[:100] + "..."
                                if len(info.get("details", "")) > 100
                                else info.get("details", "")
                            ),
                            "状态": "正常" if info.get("status") == "0" else "停用",
                            "浏览量": info.get("watch", 0),
                            "点赞数": info.get("likeCount", 0),
                            "收藏数": info.get("favoriteCount", 0),
                            "是否推荐": "是" if info.get("isSuggest") == "0" else "否",
                            "产品ID": info.get("productId", ""),
                        }
                    )

                if df_data:
                    df = pd.DataFrame(df_data)

                    # 显示数据表格
                    selected_rows = st.dataframe(
                        df,
                        use_container_width=True,
                        hide_index=True,
                        selection_mode="single-row",
                        on_select="rerun",
                    )

                    # 处理行选择
                    if selected_rows.selection.rows:
                        selected_index = selected_rows.selection.rows[0]
                        selected_info = information[selected_index]
                        SessionManager.set(
                            "information_management_current_information", selected_info
                        )
                        st.success(f"已选择资讯: {selected_info.get('title', '')}")

                        # 显示快速操作按钮
                        col1, col2, col3 = st.columns(3)
                        with col1:
                            if st.button("查看详情"):
                                st.info("请切换到'资讯详情'标签页查看完整信息")
                        with col2:
                            if st.button("编辑资讯"):
                                st.info("编辑功能开发中...")
                        with col3:
                            if st.button("删除资讯"):
                                st.warning("删除功能需要管理员权限")
                else:
                    st.warning("暂无资讯数据")

                # 分页控制
                if total > page_size:
                    total_pages = (total + page_size - 1) // page_size

                    col1, col2, col3, col4, col5 = st.columns([1, 1, 2, 1, 1])

                    with col1:
                        if st.button("⬅️ 上一页", disabled=current_page <= 1):
                            SessionManager.set(
                                "information_management_current_page", current_page - 1
                            )
                            st.rerun()

                    with col2:
                        st.write(f"第 {current_page} 页")

                    with col3:
                        new_page = st.number_input(
                            "跳转到页面",
                            min_value=1,
                            max_value=total_pages,
                            value=current_page,
                            key="info_page_input",
                        )
                        if int(new_page) != current_page:
                            SessionManager.set(
                                "information_management_current_page", int(new_page)
                            )
                            st.rerun()

                    with col4:
                        st.write(f"共 {total_pages} 页")

                    with col5:
                        if st.button("下一页 ➡️", disabled=current_page >= total_pages):
                            SessionManager.set(
                                "information_management_current_page", current_page + 1
                            )
                            st.rerun()
            else:
                if search_term:
                    st.warning(f"未找到包含'{search_term}'的资讯")
                else:
                    st.warning("暂无资讯数据")


def render_information_detail():
    """渲染资讯详情"""
    st.subheader("资讯详情")

    current_info = SessionManager.get("information_management_current_information")

    if current_info:
        # 显示资讯详情
        col1, col2 = st.columns([2, 1])

        with col1:
            st.markdown(f"### {current_info.get('title', 'N/A')}")
            st.markdown(f"**分类**: {current_info.get('categoryName', 'N/A')}")

            # 显示详细内容
            if current_info.get("details"):
                st.markdown("**详细内容**:")
                st.markdown(current_info["details"], unsafe_allow_html=True)

        with col2:
            st.markdown("**基本信息**")
            st.write(f"资讯ID: {current_info.get('id', 'N/A')}")
            st.write(f"状态: {'正常' if current_info.get('status') == '0' else '停用'}")
            st.write(f"浏览量: {current_info.get('watch', 0)}")
            st.write(f"点赞数: {current_info.get('likeCount', 0)}")
            st.write(f"收藏数: {current_info.get('favoriteCount', 0)}")
            st.write(
                f"是否推荐: {'是' if current_info.get('isSuggest') == '0' else '否'}"
            )
            st.write(f"产品ID: {current_info.get('productId', 'N/A')}")

            # 显示图片
            if current_info.get("picVideo"):
                st.image(
                    current_info["picVideo"],
                    caption="资讯图片",
                    use_container_width=True,
                )

            if current_info.get("smallImg"):
                st.image(
                    current_info["smallImg"],
                    caption="资讯小图",
                    use_container_width=True,
                )

            # 显示视频链接
            if current_info.get("videoUrl"):
                st.markdown(f"**视频链接**: [查看视频]({current_info['videoUrl']})")

            # 显示附件
            if current_info.get("otherUrl"):
                st.markdown(f"**附件**: [下载附件]({current_info['otherUrl']})")
    else:
        st.info("请从资讯列表中选择一个资讯查看详情")


def render_operation_log():
    """渲染操作记录"""
    st.subheader("操作记录")
    st.info("操作记录功能开发中...")


def render_footer():
    """渲染页面脚部"""
    st.markdown("---")
    st.markdown("© 2024 云商系统 - 资讯管理模块")


if __name__ == "__main__":
    main()
