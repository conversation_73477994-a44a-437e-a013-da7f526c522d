"""
数据库连接规范修复工具

该模块用于自动检查和修复项目中所有数据库连接的使用情况，
确保统一使用连接池，修复不规范的数据库连接使用。
"""

import os
import re
import logging
from typing import List, Dict, Any, Tuple
from pathlib import Path
from utils.database import get_db_connection, return_db_connection

logger = logging.getLogger(__name__)


class DatabaseComplianceFixer:
    """数据库连接规范修复器"""

    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.fixed_files = []
        self.violations_found = []

    def fix_all_violations(self) -> Dict[str, Any]:
        """
        修复整个项目的数据库连接违规

        Returns:
            修复结果报告
        """
        logger.info("开始修复项目数据库连接违规...")

        # 重置结果
        self.fixed_files = []
        self.violations_found = []

        # 获取所有Python文件
        python_files = self._get_python_files()

        # 检查和修复每个文件
        for file_path in python_files:
            try:
                fixed = self._fix_file_violations(file_path)
                if fixed:
                    self.fixed_files.append(str(file_path))
            except Exception as e:
                logger.error(f"修复文件失败 {file_path}: {e}")

        # 生成报告
        report = self._generate_report()
        logger.info(f"数据库连接违规修复完成，修复了 {len(self.fixed_files)} 个文件")

        return report

    def _get_python_files(self) -> List[Path]:
        """获取项目中所有Python文件"""
        python_files = []

        # 排除的目录
        exclude_dirs = {
            ".git",
            "__pycache__",
            ".pytest_cache",
            "venv",
            "env",
            "node_modules",
            "venv_product",
            "venv_new",
        }

        for root, dirs, files in os.walk(self.project_root):
            # 过滤排除的目录
            dirs[:] = [d for d in dirs if d not in exclude_dirs]

            for file in files:
                if file.endswith(".py"):
                    python_files.append(Path(root) / file)

        return python_files

    def _fix_file_violations(self, file_path: Path) -> bool:
        """
        修复单个文件的数据库连接违规

        Args:
            file_path: 文件路径

        Returns:
            是否进行了修复
        """
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                original_content = f.read()

            modified_content = original_content
            has_modifications = False

            # 检查是否有直接的psycopg2.connect调用
            if "psycopg2.connect" in modified_content:
                violation = {
                    "file": str(file_path),
                    "type": "direct_psycopg2_connect",
                    "message": "发现直接使用psycopg2.connect",
                }
                self.violations_found.append(violation)

                # 这种情况需要手动修复，记录但不自动修复
                logger.warning(
                    f"文件 {file_path} 包含直接的psycopg2.connect调用，需要手动修复"
                )

            # 检查是否有self.conn的使用但没有连接池管理
            if (
                "self.conn" in modified_content
                and "get_db_connection" not in modified_content
            ):
                violation = {
                    "file": str(file_path),
                    "type": "self_conn_without_pool",
                    "message": "使用self.conn但未使用连接池",
                }
                self.violations_found.append(violation)
                logger.warning(
                    f"文件 {file_path} 使用self.conn但未使用连接池，需要手动修复"
                )

            # 如果有修改，保存文件
            if has_modifications:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(modified_content)
                logger.info(f"已修复文件: {file_path}")
                return True

            return False

        except Exception as e:
            logger.error(f"修复文件失败 {file_path}: {e}")
            return False

    def _generate_report(self) -> Dict[str, Any]:
        """生成修复报告"""
        return {
            "fixed_files_count": len(self.fixed_files),
            "fixed_files": self.fixed_files,
            "violations_found_count": len(self.violations_found),
            "violations_found": self.violations_found,
            "status": "completed",
        }

    def print_report(self, report: Dict[str, Any]):
        """打印修复报告"""
        print("\n" + "=" * 60)
        print("数据库连接规范修复报告")
        print("=" * 60)

        print(f"修复的文件数量: {report['fixed_files_count']}")
        if report["fixed_files"]:
            print("修复的文件:")
            for file in report["fixed_files"]:
                print(f"  - {file}")

        print(f"\n发现的违规数量: {report['violations_found_count']}")
        if report["violations_found"]:
            print("发现的违规:")
            for violation in report["violations_found"]:
                print(f"  - {violation['file']}: {violation['message']}")

        print(f"\n修复状态: {report['status']}")
        print("=" * 60)

    def test_database_connections(self) -> Dict[str, Any]:
        """
        测试数据库连接功能

        Returns:
            测试结果
        """
        logger.info("开始测试数据库连接功能...")

        test_results = {
            "connection_pool_test": False,
            "basic_query_test": False,
            "transaction_test": False,
            "error_handling_test": False,
            "details": [],
        }

        try:
            # 测试1: 连接池基本功能
            logger.info("测试1: 连接池基本功能")
            conn = get_db_connection()
            if conn:
                test_results["connection_pool_test"] = True
                test_results["details"].append("✓ 连接池获取连接成功")

                # 测试2: 基本查询
                logger.info("测试2: 基本查询")
                try:
                    with conn.cursor() as cursor:
                        cursor.execute("SELECT 1 as test")
                        result = cursor.fetchone()
                        if result and result[0] == 1:
                            test_results["basic_query_test"] = True
                            test_results["details"].append("✓ 基本查询测试成功")
                        else:
                            test_results["details"].append("✗ 基本查询测试失败")
                except Exception as e:
                    test_results["details"].append(f"✗ 基本查询测试失败: {e}")

                # 测试3: 事务测试
                logger.info("测试3: 事务测试")
                try:
                    with conn.cursor() as cursor:
                        # 开始事务
                        cursor.execute("BEGIN")
                        cursor.execute("SELECT 2 as test")
                        result = cursor.fetchone()
                        # 回滚事务
                        conn.rollback()

                        if result and result[0] == 2:
                            test_results["transaction_test"] = True
                            test_results["details"].append("✓ 事务测试成功")
                        else:
                            test_results["details"].append("✗ 事务测试失败")
                except Exception as e:
                    test_results["details"].append(f"✗ 事务测试失败: {e}")

                # 归还连接
                return_db_connection(conn)
                test_results["details"].append("✓ 连接归还成功")

            else:
                test_results["details"].append("✗ 连接池获取连接失败")

        except Exception as e:
            test_results["details"].append(f"✗ 连接池测试失败: {e}")

        # 测试4: 错误处理
        logger.info("测试4: 错误处理")
        try:
            conn = get_db_connection()
            if conn:
                try:
                    with conn.cursor() as cursor:
                        # 执行一个会失败的查询
                        cursor.execute("SELECT * FROM non_existent_table_12345")
                except Exception:
                    # 预期会失败，这是正常的
                    test_results["error_handling_test"] = True
                    test_results["details"].append("✓ 错误处理测试成功")
                finally:
                    return_db_connection(conn)
            else:
                test_results["details"].append("✗ 错误处理测试失败：无法获取连接")
        except Exception as e:
            test_results["details"].append(f"✗ 错误处理测试失败: {e}")

        # 计算总体成功率
        total_tests = 4
        passed_tests = sum(
            [
                test_results["connection_pool_test"],
                test_results["basic_query_test"],
                test_results["transaction_test"],
                test_results["error_handling_test"],
            ]
        )

        test_results["success_rate"] = (passed_tests / total_tests) * 100
        test_results["overall_status"] = (
            "PASS" if passed_tests == total_tests else "PARTIAL"
        )

        logger.info(f"数据库连接测试完成，成功率: {test_results['success_rate']:.1f}%")

        return test_results

    def print_test_report(self, test_results: Dict[str, Any]):
        """打印测试报告"""
        print("\n" + "=" * 60)
        print("数据库连接测试报告")
        print("=" * 60)

        print(f"总体状态: {test_results['overall_status']}")
        print(f"成功率: {test_results['success_rate']:.1f}%")

        print("\n测试详情:")
        for detail in test_results["details"]:
            print(f"  {detail}")

        print("\n各项测试结果:")
        print(
            f"  连接池测试: {'✓ PASS' if test_results['connection_pool_test'] else '✗ FAIL'}"
        )
        print(
            f"  基本查询测试: {'✓ PASS' if test_results['basic_query_test'] else '✗ FAIL'}"
        )
        print(
            f"  事务测试: {'✓ PASS' if test_results['transaction_test'] else '✗ FAIL'}"
        )
        print(
            f"  错误处理测试: {'✓ PASS' if test_results['error_handling_test'] else '✗ FAIL'}"
        )

        print("=" * 60)


def main():
    """主函数"""
    logging.basicConfig(level=logging.INFO)

    fixer = DatabaseComplianceFixer()

    # 执行修复
    print("正在检查和修复数据库连接违规...")
    fix_report = fixer.fix_all_violations()
    fixer.print_report(fix_report)

    # 执行测试
    print("\n正在测试数据库连接功能...")
    test_report = fixer.test_database_connections()
    fixer.print_test_report(test_report)


if __name__ == "__main__":
    main()
