#!/usr/bin/env python3
"""
数据处理工具模块
处理云商接口数据的各种格式问题，包括HTML到Markdown转换
"""

import logging
import re
from typing import Any, Dict, List, Optional, Union
from .html_processor import HTMLProcessor, process_api_response_html

logger = logging.getLogger(__name__)


class DataProcessor:
    """数据处理器"""

    def __init__(self):
        self.html_processor = HTMLProcessor()

    def process_api_response(
        self, response_data: Any, data_type: str = "generic"
    ) -> Any:
        """
        处理API响应数据

        Args:
            response_data: API响应数据
            data_type: 数据类型（product, case, programme, information等）

        Returns:
            处理后的数据
        """
        try:
            # 1. 基本数据清理
            cleaned_data = self._clean_basic_data(response_data)

            # 2. HTML内容转换
            processed_data = self._process_html_content(cleaned_data, data_type)

            # 3. 图片处理（下载并保存到本地）
            image_processed_data = self._process_images_for_api_response(
                processed_data, data_type
            )

            # 4. 数据类型特定处理
            final_data = self._apply_type_specific_processing(
                image_processed_data, data_type
            )

            return final_data

        except Exception as e:
            logger.error(f"数据处理失败: {e}")
            return response_data

    def _clean_basic_data(self, data: Any) -> Any:
        """基本数据清理"""
        if isinstance(data, dict):
            cleaned = {}
            for key, value in data.items():
                # 清理键名
                clean_key = self._clean_field_name(key)
                # 清理值
                clean_value = self._clean_field_value(value)
                cleaned[clean_key] = clean_value
            return cleaned
        elif isinstance(data, list):
            return [self._clean_basic_data(item) for item in data]
        else:
            return data

    def _clean_field_name(self, field_name: str) -> str:
        """清理字段名"""
        if not isinstance(field_name, str):
            return str(field_name)

        # 移除特殊字符，保持驼峰命名
        cleaned = re.sub(r"[^\w]", "", field_name)
        return cleaned

    def _clean_field_value(self, value: Any) -> Any:
        """清理字段值"""
        if isinstance(value, str):
            # 移除首尾空格
            value = value.strip()
            # 移除控制字符
            value = re.sub(r"[\x00-\x08\x0b-\x0c\x0e-\x1f\x7f-\x9f]", "", value)
        return value

    def _process_html_content(self, data: Any, data_type: str) -> Any:
        """处理HTML内容"""
        # 根据数据类型确定可能包含HTML的字段
        html_fields = self._get_html_fields_by_type(data_type)

        return process_api_response_html(data, html_fields)

    def _get_html_fields_by_type(self, data_type: str) -> List[str]:
        """根据数据类型确定需要HTML处理的字段"""
        html_fields_map = {
            "product": [
                "details",
                "param_info",
                "specifications",
                "introduction",
                "guide",
            ],
            "case": ["content", "details", "introduction"],
            "programme": ["content", "details", "description"],
            "information": ["content", "details", "title"],
        }
        return html_fields_map.get(data_type, [])

    def _get_image_fields_by_type(self, data_type: str) -> Dict[str, Dict[str, str]]:
        """根据数据类型确定需要图片处理的字段及其用途"""
        image_fields_map = {
            "product": {"smallImg": "缩略图", "banner": "轮播图", "sizeImg": "尺寸图"},
            "case": {
                "img": "案例图片",
                "banner": "案例轮播图",
                "smallImg": "案例缩略图",
            },
            "programme": {"smallImg": "方案缩略图", "banner": "方案轮播图"},
            "information": {
                "picVideo": "视频封面",
                "smallImg": "资讯缩略图",
                "images": "资讯图片",
            },
        }
        return image_fields_map.get(data_type, {})

    def _process_images(self, data: List[Dict], data_type: str) -> List[Dict]:
        """
        处理图片字段，下载并保存到本地

        Args:
            data: 数据列表
            data_type: 数据类型

        Returns:
            处理后的数据列表
        """
        try:
            from utils.image_processor import ImageProcessor

            image_processor = ImageProcessor()
            image_fields_with_purpose = self._get_image_fields_by_type(data_type)

            if not image_fields_with_purpose:
                return data

            for item in data:
                item_id = str(item.get("id", ""))
                if not item_id:
                    continue

                # 处理图片字段
                for field, purpose in image_fields_with_purpose.items():
                    if field in item and item[field]:
                        field_value = item[field]

                        # 检查是否为有效的图片URL
                        if isinstance(field_value, str):
                            if "," in field_value:
                                # 多个URL（逗号分隔）
                                urls = [
                                    url.strip()
                                    for url in field_value.split(",")
                                    if url.strip()
                                ]
                                local_paths = []
                                image_infos = []

                                for i, url in enumerate(urls):
                                    if self._is_valid_image_url(url):
                                        image_info = (
                                            image_processor.download_image_with_purpose(
                                                url,
                                                data_type,
                                                item_id,
                                                f"{field}_{i}",
                                                purpose,
                                            )
                                        )
                                        if image_info:
                                            local_paths.append(image_info.local_path)
                                            image_infos.append(image_info.to_dict())

                                if local_paths:
                                    item[f"{field}_local"] = local_paths
                                    item[f"{field}_info"] = image_infos
                                    item[f"{field}_purpose"] = purpose

                            else:
                                # 单个URL
                                if self._is_valid_image_url(field_value):
                                    image_info = (
                                        image_processor.download_image_with_purpose(
                                            field_value,
                                            data_type,
                                            item_id,
                                            field,
                                            purpose,
                                        )
                                    )
                                    if image_info:
                                        item[f"{field}_local"] = image_info.local_path
                                        item[f"{field}_info"] = image_info.to_dict()
                                        item[f"{field}_purpose"] = purpose

            return data

        except ImportError:
            logger.warning("图片处理器不可用，跳过图片处理")
            return data
        except Exception as e:
            logger.error(f"图片处理失败: {e}")
            return data

    def _is_valid_image_url(self, url: str) -> bool:
        """检查是否为有效的图片URL"""
        if not isinstance(url, str) or not url.strip():
            return False

        url = url.strip()

        if not url.startswith(("http://", "https://")):
            return False

        # 简单的图片格式检查
        image_extensions = {".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg"}

        try:
            from urllib.parse import urlparse, unquote

            parsed_url = urlparse(url)
            path = parsed_url.path.lower()

            # 检查扩展名
            for ext in image_extensions:
                if path.endswith(ext):
                    return True

            # 检查查询参数中的文件名
            if "file=" in parsed_url.query:
                for param in parsed_url.query.split("&"):
                    if param.startswith("file="):
                        filename = unquote(param.split("=", 1)[1]).lower()
                        for ext in image_extensions:
                            if filename.endswith(ext):
                                return True

            # 如果没有明确的扩展名，假设可能是图片
            return True

        except Exception:
            return False

    def _process_images_for_api_response(self, data: Any, data_type: str) -> Any:
        """
        处理API响应中的图片字段 - 适配器方法

        Args:
            data: API响应数据
            data_type: 数据类型

        Returns:
            处理后的数据
        """
        if isinstance(data, list):
            # 如果是列表，每个元素都是一个项目
            return self._process_images(data, data_type)
        elif isinstance(data, dict):
            # 如果是字典，可能是单个项目或包含列表的响应
            if "data" in data and isinstance(data["data"], list):
                # 典型的API响应格式：{"data": [...], "total": 100}
                processed_data = data.copy()
                processed_data["data"] = self._process_images(data["data"], data_type)
                return processed_data
            elif "items" in data and isinstance(data["items"], list):
                # 另一种API响应格式：{"items": [...], "total": 100}
                processed_data = data.copy()
                processed_data["items"] = self._process_images(data["items"], data_type)
                return processed_data
            else:
                # 单个项目
                if data.get("id"):
                    processed_items = self._process_images([data], data_type)
                    return processed_items[0] if processed_items else data
                else:
                    return data
        else:
            # 其他类型直接返回
            return data

    def _apply_type_specific_processing(self, data: Any, data_type: str) -> Any:
        """应用特定类型的数据处理"""
        if data_type == "product":
            return self._process_product_data(data)
        elif data_type == "case":
            return self._process_case_data(data)
        elif data_type == "programme":
            return self._process_programme_data(data)
        elif data_type == "information":
            return self._process_information_data(data)
        else:
            return data

    def _process_product_data(self, data: Any) -> Any:
        """处理产品数据"""
        if isinstance(data, list):
            return [self._process_single_product(item) for item in data]
        elif isinstance(data, dict):
            return self._process_single_product(data)
        else:
            return data

    def _process_single_product(self, product: Dict[str, Any]) -> Dict[str, Any]:
        """处理单个产品数据"""
        if not isinstance(product, dict):
            return product

        processed = product.copy()

        # 特殊处理产品参数信息
        if "param_info" in processed:
            param_info = processed["param_info"]
            if isinstance(param_info, str) and self.html_processor._contains_html_tags(
                param_info
            ):
                # 参数信息通常包含表格，使用表格转换
                processed["param_info"] = self.html_processor.process_html_content(
                    param_info, "table"
                )

        # 处理产品详情
        if "details" in processed:
            details = processed["details"]
            if isinstance(details, str) and self.html_processor._contains_html_tags(
                details
            ):
                processed["details"] = self.html_processor.process_html_content(
                    details, "mixed"
                )

        return processed

    def _process_case_data(self, data: Any) -> Any:
        """处理案例数据"""
        if isinstance(data, list):
            return [self._process_single_case(item) for item in data]
        elif isinstance(data, dict):
            return self._process_single_case(data)
        else:
            return data

    def _process_single_case(self, case: Dict[str, Any]) -> Dict[str, Any]:
        """处理单个案例数据"""
        if not isinstance(case, dict):
            return case

        processed = case.copy()

        # 处理案例内容
        if "content" in processed:
            content = processed["content"]
            if isinstance(content, str) and self.html_processor._contains_html_tags(
                content
            ):
                processed["content"] = self.html_processor.process_html_content(
                    content, "mixed"
                )

        return processed

    def _process_programme_data(self, data: Any) -> Any:
        """处理方案数据"""
        if isinstance(data, list):
            return [self._process_single_programme(item) for item in data]
        elif isinstance(data, dict):
            return self._process_single_programme(data)
        else:
            return data

    def _process_single_programme(self, programme: Dict[str, Any]) -> Dict[str, Any]:
        """处理单个方案数据"""
        if not isinstance(programme, dict):
            return programme

        processed = programme.copy()

        # 处理方案内容
        if "content" in processed:
            content = processed["content"]
            if isinstance(content, str) and self.html_processor._contains_html_tags(
                content
            ):
                processed["content"] = self.html_processor.process_html_content(
                    content, "mixed"
                )

        return processed

    def _process_information_data(self, data: Any) -> Any:
        """处理资讯数据"""
        if isinstance(data, list):
            return [self._process_single_information(item) for item in data]
        elif isinstance(data, dict):
            return self._process_single_information(data)
        else:
            return data

    def _process_single_information(self, info: Dict[str, Any]) -> Dict[str, Any]:
        """处理单个资讯数据"""
        if not isinstance(info, dict):
            return info

        processed = info.copy()

        # 处理资讯内容
        if "content" in processed:
            content = processed["content"]
            if isinstance(content, str) and self.html_processor._contains_html_tags(
                content
            ):
                processed["content"] = self.html_processor.process_html_content(
                    content, "mixed"
                )

        return processed


def clean_integer_field(value: Any) -> Optional[int]:
    """
    清洗整数字段，处理逗号分隔的值

    Args:
        value: 原始值

    Returns:
        清洗后的整数值或None
    """
    if not value:
        return None

    if isinstance(value, (int, float)):
        return int(value)

    if isinstance(value, str):
        # 移除逗号、空格和其他非数字字符
        cleaned = re.sub(r"[^0-9.-]", "", value.strip())

        # 如果原值包含逗号，可能是多个ID，取第一个
        if "," in value:
            parts = value.split(",")
            if parts:
                cleaned = re.sub(r"[^0-9.-]", "", parts[0].strip())

        try:
            return int(float(cleaned)) if cleaned else None
        except (ValueError, TypeError):
            logger.warning(f"无法转换为整数: {value}")
            return None

    return None


def clean_text_field(value: Any, max_length: Optional[int] = None) -> str:
    """
    清洗文本字段

    Args:
        value: 原始值
        max_length: 最大长度限制

    Returns:
        清洗后的文本
    """
    if not value:
        return ""

    if not isinstance(value, str):
        value = str(value)

    # 移除首尾空格
    cleaned = value.strip()

    # 移除特殊控制字符
    cleaned = re.sub(r"[\x00-\x08\x0b-\x0c\x0e-\x1f\x7f-\x9f]", "", cleaned)

    # 限制长度
    if max_length and len(cleaned) > max_length:
        cleaned = cleaned[:max_length]

    return cleaned


# 全局数据处理器实例
_global_processor = None


def get_data_processor() -> DataProcessor:
    """获取全局数据处理器实例"""
    global _global_processor
    if _global_processor is None:
        _global_processor = DataProcessor()
    return _global_processor


def process_api_data(data: Any, data_type: str = "generic") -> Any:
    """
    处理API数据的便捷函数

    Args:
        data: API数据
        data_type: 数据类型

    Returns:
        处理后的数据
    """
    processor = get_data_processor()
    return processor.process_api_response(data, data_type)
