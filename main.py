import streamlit as st
import logging
from utils.session import SessionManager
from utils.logging_config import get_logger
from utils.auth import AuthManager

# 加载环境变量
from config import load_env_file

load_env_file()

# 配置日志
logger = get_logger()


def main():
    """应用主入口"""
    # 页面配置
    st.set_page_config(
        page_title="云商系统",
        page_icon="🏢",
        layout="wide",
        initial_sidebar_state="expanded",
    )

    # 初始化session
    initialize_session()

    # 渲染页面
    render_header()
    render_content()
    render_footer()


def initialize_session():
    """初始化session状态"""
    defaults = {
        "user_authenticated": False,
        "current_page": "home",
        "data_cache": {},
        "user_preferences": {},
    }

    for key, value in defaults.items():
        if key not in st.session_state:
            SessionManager.set(key, value)


def render_header():
    """渲染页面头部"""
    st.title("🏢 云商系统")
    st.markdown("基于熵基云商接口对接的Streamlit应用系统")


def render_content():
    """渲染主要内容"""
    # 检查用户是否已认证
    if not AuthManager.is_authenticated():
        render_login_form()
    else:
        render_dashboard()


def render_login_form():
    """渲染登录表单"""
    with st.container():
        st.subheader("系统登录")

        col1, col2 = st.columns([1, 1])

        with col1:
            username = st.text_input("用户名", placeholder="请输入用户名")
            password = st.text_input("密码", type="password", placeholder="请输入密码")

            if st.button("登录", use_container_width=True):
                if not username or not password:
                    st.error("请输入用户名和密码")
                    return

                with st.spinner("正在登录..."):
                    try:
                        # 使用真正的认证逻辑
                        success, user_info = AuthManager.login(username, password)

                        if success:
                            st.success("登录成功！")
                            logger.info(f"用户 {username} 登录成功")
                            # 等待一秒让用户看到成功消息，然后刷新页面
                            st.rerun()
                        else:
                            st.error("登录失败：用户名或密码错误，请检查后重试")
                            logger.warning(f"用户 {username} 登录失败")

                    except Exception as e:
                        logger.error(f"登录过程发生异常: {e}")
                        st.error("登录过程中发生错误，请稍后重试")

        with col2:
            st.image("https://zkmall.zktecoiot.com/static/img/logo.png", width=200)
            st.markdown("### 系统说明")
            st.markdown(
                "本系统提供熵基云商接口对接服务，包含产品管理、案例管理、方案管理、资讯管理和配单管理等功能。"
            )


def render_dashboard():
    """渲染仪表盘"""
    # 显示当前用户信息
    user_info = AuthManager.get_current_user()
    if user_info:
        with st.sidebar:
            st.success(f"欢迎，{user_info.get('userName', '用户')}！")
            if st.button("退出登录"):
                AuthManager.logout()
                st.rerun()

    st.subheader("系统概览")

    # 显示模块卡片
    col1, col2, col3 = st.columns(3)

    with col1:
        with st.container(border=True):
            st.markdown("### 📦 产品管理")
            st.markdown("管理和查看产品信息")
            if st.button("进入产品管理", key="btn_product"):
                st.switch_page("pages/01_product_management.py")

    with col2:
        with st.container(border=True):
            st.markdown("### 📋 案例管理")
            st.markdown("管理和查看案例信息")
            if st.button("进入案例管理", key="btn_case"):
                st.switch_page("pages/03_case_management.py")

    with col3:
        with st.container(border=True):
            st.markdown("### 🎯 方案管理")
            st.markdown("管理和查看方案信息")
            if st.button("进入方案管理", key="btn_programme"):
                st.switch_page("pages/04_solution_management.py")

    col1, col2, col3 = st.columns(3)

    with col1:
        with st.container(border=True):
            st.markdown("### 📰 资讯管理")
            st.markdown("管理和查看资讯信息")
            if st.button("进入资讯管理", key="btn_information"):
                st.switch_page("pages/05_news_management.py")

    with col2:
        with st.container(border=True):
            st.markdown("### 📊 配单管理")
            st.markdown("管理和查看配单信息")
            if st.button("进入配单管理", key="btn_order"):
                st.switch_page("pages/06_order_management.py")

    with col3:
        with st.container(border=True):
            st.markdown("### 📎 附件管理")
            st.markdown("管理和查看系统附件文件")
            if st.button("进入附件管理", key="btn_attachment"):
                st.switch_page("pages/07_attachment_management.py")

    col1, col2 = st.columns(2)

    with col1:
        with st.container(border=True):
            st.markdown("### 🔄 数据同步")
            st.markdown("同步云商接口数据到本地")
            if st.button("进入数据同步", key="btn_sync"):
                st.switch_page("pages/08_data_sync.py")

    with col2:
        with st.container(border=True):
            st.markdown("### 📊 数据查询")
            st.markdown("查询和显示数据库中的数据")
            if st.button("进入数据查询", key="btn_query"):
                st.switch_page("pages/09_data_query.py")

    # 新增产品知识库管理
    col1, col2 = st.columns(2)

    with col1:
        with st.container(border=True):
            st.markdown("### 🧠 产品知识库")
            st.markdown("管理产品知识库，处理文档，同步FastGPT")
            if st.button("进入产品知识库", key="btn_knowledge"):
                st.switch_page("pages/product_knowledge_management.py")


def render_footer():
    """渲染页面脚部"""
    st.markdown("---")
    st.markdown("© 2024 云商系统 | 版本: v0.1.0")


if __name__ == "__main__":
    main()
