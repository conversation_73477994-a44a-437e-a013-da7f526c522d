---
description:
globs:
alwaysApply: false
---
# 数据库配置强制执行规则

## 🎯 核心要求

**ALL DATABASE CONFIGURATIONS MUST USE: `***********************************************/product`**

### 🔒 强制配置参数

```python
# 强制性默认值 - 违反将导致系统故障
REQUIRED_DB_HOST = "***********"
REQUIRED_DB_PORT = 5432
REQUIRED_DB_NAME = "product"
REQUIRED_DB_USER = "username"
REQUIRED_DB_PASSWORD = "password"
REQUIRED_DB_URL = "***********************************************/product"
```

### 📋 配置模板强制执行

**所有新建和修改的数据库配置必须使用以下模板：**

#### 1. 环境变量配置模板
```python
# config/config.py - 主配置模板（必须严格遵循）
DATABASE_URL = os.getenv("DATABASE_URL", "***********************************************/product")

# 分离配置模板（如果必须使用分离配置）
db_config = {
    "host": os.getenv("DB_HOST", "***********"),
    "port": os.getenv("DB_PORT", 5432),
    "database": os.getenv("DB_NAME", "product"),
    "user": os.getenv("DB_USER", "username"),
    "password": os.getenv("DB_PASSWORD", "password"),
}
```

#### 2. 连接池配置模板
```python
# utils/database.py - 连接池模板
database_url = os.getenv("DATABASE_URL", "***********************************************/product")
self._pool = ThreadedConnectionPool(
    minconn=5, maxconn=20, dsn=database_url, cursor_factory=RealDictCursor
)
```

#### 3. 测试文件配置模板
```python
# test_*.py - 测试文件模板
db_config = {
    "host": os.getenv("DATABASE_HOST", "***********"),
    "port": int(os.getenv("DATABASE_PORT", "5432")),
    "database": os.getenv("DATABASE_NAME", "product"),
    "user": os.getenv("DATABASE_USER", "username"),
    "password": os.getenv("DATABASE_PASSWORD", "password"),
}
```

### 🚨 严禁使用的配置

**以下配置模式已被严格禁止，发现后必须立即修复：**

```python
# 🚫 绝对禁止的主机配置
"host": "localhost"
"host": "127.0.0.1"  
"host": "0.0.0.0"

# 🚫 绝对禁止的数据库名
"database": "yunshang"
"database": "dataann_email"
"database": "postgres"
"database": "test"

# 🚫 绝对禁止的用户名
"user": "postgres"
"user": "admin"
"user": "root"

# 🚫 绝对禁止的连接字符串格式
"postgresql://postgres:*@localhost:5432/*"
"postgresql://*:*@127.0.0.1:5432/*"
"***********************/yunshang"
```

### 🔧 代码修复自动化

**使用以下脚本自动修复违规配置：**

```bash
#!/bin/bash
# database_config_fix.sh - 自动修复数据库配置

echo "🔍 扫描违规数据库配置..."

# 检查并修复主机配置
find . -name "*.py" -not -path "./venv*" -not -path "./.git*" | xargs grep -l "localhost" | while read file; do
    echo "修复文件: $file"
    sed -i 's/"localhost"/"***********"/g' "$file"
    sed -i "s/'localhost'/'***********'/g" "$file"
done

# 检查并修复数据库名
find . -name "*.py" -not -path "./venv*" -not -path "./.git*" | xargs grep -l '"yunshang"' | while read file; do
    echo "修复数据库名: $file"
    sed -i 's/"yunshang"/"product"/g' "$file"
    sed -i "s/'yunshang'/'product'/g" "$file"
done

# 检查并修复用户名
find . -name "*.py" -not -path "./venv*" -not -path "./.git*" | xargs grep -l '"postgres"' | while read file; do
    echo "修复用户名: $file"
    sed -i 's/"postgres"/"username"/g' "$file"
    sed -i "s/'postgres'/'username'/g" "$file"
done

echo "✅ 数据库配置修复完成！"
```

### 📝 配置验证清单

**每次代码提交前必须验证：**

- [ ] ✅ 所有 `*.py` 文件中不包含 `localhost`
- [ ] ✅ 所有 `*.py` 文件中不包含 `yunshang` 数据库名
- [ ] ✅ 所有 `*.py` 文件中不包含 `postgres` 用户名
- [ ] ✅ 所有连接字符串使用正确格式
- [ ] ✅ 环境变量默认值正确设置
- [ ] ✅ 配置文件引用正确的主机和端口

### 🛡️ 预提交检查钩子

**Git 预提交钩子代码（必须配置）：**

```bash
#!/bin/sh
# .git/hooks/pre-commit

echo "🔍 检查数据库配置合规性..."

# 检查是否有违规的数据库配置
if grep -r "localhost" . --include="*.py" --exclude-dir=venv* --exclude-dir=.git; then
    echo "❌ 发现违规的 localhost 配置！"
    echo "请使用 *********** 替代"
    exit 1
fi

if grep -r '"yunshang"' . --include="*.py" --exclude-dir=venv* --exclude-dir=.git; then
    echo "❌ 发现违规的 yunshang 数据库名！"
    echo "请使用 product 替代"
    exit 1
fi

if grep -r '"postgres".*user' . --include="*.py" --exclude-dir=venv* --exclude-dir=.git; then
    echo "❌ 发现违规的 postgres 用户名！"
    echo "请使用 username 替代"
    exit 1
fi

echo "✅ 数据库配置检查通过"
```

### 🔄 持续集成检查

**CI/CD 管道中必须包含的检查：**

```yaml
# .github/workflows/database-config-check.yml
name: Database Configuration Check
on: [push, pull_request]
jobs:
  config-check:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Check Database Configuration
        run: |
          if grep -r "localhost" . --include="*.py" --exclude-dir=venv*; then
            echo "❌ Found localhost in database configuration"
            exit 1
          fi
          echo "✅ Database configuration check passed"
```

### 📚 相关文件强制更新

**以下文件必须立即检查并更新到标准配置：**

1. [config/config.py](mdc:config/config.py) - 主配置文件
2. [utils/database.py](mdc:utils/database.py) - 数据库连接池
3. [utils/attachment_db.py](mdc:utils/attachment_db.py) - 附件数据库
4. [env.example](mdc:env.example) - 环境变量示例
5. 所有 `fix_*.py` 修复脚本
6. 所有 `test_*.py` 测试文件
7. 所有 `*_db*.py` 数据库相关文件

### ⚡ 紧急响应程序

**如发现违规配置，立即执行：**

1. **立即停止**相关服务
2. **批量修复**所有违规文件
3. **验证连接**确保修复生效
4. **重启服务**并进行功能测试
5. **文档更新**记录修复过程

---

**🚨 零容忍政策**: 任何违反此配置标准的代码提交将被拒绝。所有开发人员必须严格遵守此规范，确保系统稳定运行！
