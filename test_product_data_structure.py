#!/usr/bin/env python3
"""
测试产品数据结构，检查产品型号和类别信息
"""

import os
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 加载环境变量
from config import load_env_file

load_env_file()

from utils.api_client import ZKMallClient
from utils.auth import AuthManager
from utils.logging_config import get_logger

logger = get_logger()


def test_product_data_structure():
    """测试产品数据结构"""
    try:
        # 先进行登录认证
        print("🔐 正在登录...")
        success, user_info = AuthManager.login("18929343717", "Zk@123456")

        if not success:
            print("❌ 登录失败")
            return

        print(f"✅ 登录成功: {user_info.get('userName', '用户')}")

        # 初始化API客户端
        client = ZKMallClient()

        # 获取少量产品数据进行分析
        print("🔍 获取产品数据...")
        products = client.get_products(pageSize=5, current=1)

        if not products:
            print("❌ 未获取到产品数据")
            return

        print(f"✅ 获取到 {len(products)} 个产品")

        # 分析每个产品的数据结构
        for i, product in enumerate(products, 1):
            print(f"\n📦 产品 {i}:")
            print(f"原始数据键: {list(product.keys())}")

            # 检查基本信息
            product_id = product.get("productId") or product.get("id")
            product_name = product.get("productName") or product.get("name")
            category_name = product.get("categoryName")
            category_id = product.get("categoryId")
            brand_name = product.get("brandName")

            print(f"  - ID: {product_id}")
            print(f"  - 名称: {product_name}")
            print(f"  - 类别名称: {category_name}")
            print(f"  - 类别ID: {category_id}")
            print(f"  - 品牌: {brand_name}")

            # 检查参数信息列表（产品型号可能在这里）
            param_info_list = product.get("paramInfoList", [])
            print(f"  - 参数信息列表长度: {len(param_info_list)}")

            if param_info_list:
                print("  - 参数信息详情:")
                for param in param_info_list:
                    param_name = param.get("params", "")
                    param_content = param.get("content", "")
                    print(f"    * {param_name}: {param_content}")

                    # 特别检查型号信息
                    if "型号" in param_name:
                        print(f"    🎯 找到产品型号: {param_content}")

            # 检查其他可能包含型号的字段
            spec = product.get("spec", "")
            if spec:
                print(f"  - 规格: {spec}")

            # 检查新参数字段
            new_param = product.get("newParam", "")
            if new_param:
                print(f"  - 新参数: {new_param}")

            # 输出完整的产品数据（仅第一个产品）
            if i == 1:
                print(f"\n📋 完整产品数据结构:")
                print(json.dumps(product, ensure_ascii=False, indent=2))

        # 测试获取产品详情
        if products:
            first_product_id = products[0].get("productId") or products[0].get("id")
            if first_product_id:
                print(f"\n🔍 获取产品详情 (ID: {first_product_id})...")
                product_detail = client.get_product_detail(first_product_id)

                if product_detail:
                    print("✅ 获取产品详情成功")
                    print(f"详情数据键: {list(product_detail.keys())}")

                    # 检查详情中的参数信息
                    detail_param_info = product_detail.get("paramInfoList", [])
                    print(f"详情参数信息列表长度: {len(detail_param_info)}")

                    if detail_param_info:
                        print("详情参数信息:")
                        for param in detail_param_info:
                            param_name = param.get("params", "")
                            param_content = param.get("content", "")
                            print(f"  * {param_name}: {param_content}")
                else:
                    print("❌ 获取产品详情失败")

    except Exception as e:
        logger.error(f"测试产品数据结构失败: {e}", exc_info=True)
        print(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    test_product_data_structure()
