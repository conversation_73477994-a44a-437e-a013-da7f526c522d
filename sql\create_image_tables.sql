-- 云商系统图片存储相关表结构
-- 支持图片用途分类和管理

-- 1. 图片用途分类表
CREATE TABLE IF NOT EXISTS image_purposes (
    id SERIAL PRIMARY KEY,
    purpose_name VARCHAR(50) NOT NULL UNIQUE COMMENT '用途名称',
    category VARCHAR(20) NOT NULL COMMENT '用途分类',
    description TEXT COMMENT '用途描述',
    storage_config JSONB COMMENT '存储配置',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    sort_order INTEGER DEFAULT 0 COMMENT '排序',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入默认图片用途
INSERT INTO image_purposes (purpose_name, category, description, storage_config) VALUES
('缩略图', '展示类', '用于列表展示的小尺寸图片', '{"quality": 85, "max_size": [300, 300], "format": "JPEG"}'),
('轮播图', '展示类', '用于轮播展示的高质量图片', '{"quality": 90, "max_size": [1200, 800], "format": "JPEG"}'),
('尺寸图', '技术类', '展示产品尺寸的技术图片', '{"quality": 95, "max_size": [800, 600], "format": "PNG"}'),
('案例图片', '展示类', '案例展示图片', '{"quality": 90, "max_size": [1000, 750], "format": "JPEG"}'),
('方案图片', '展示类', '方案展示图片', '{"quality": 90, "max_size": [1000, 750], "format": "JPEG"}'),
('资讯图片', '展示类', '资讯内容图片', '{"quality": 88, "max_size": [800, 600], "format": "JPEG"}'),
('视频封面', '媒体类', '视频内容的封面图片', '{"quality": 85, "max_size": [640, 360], "format": "JPEG"}'),
('参数图', '技术类', '产品参数说明图片', '{"quality": 95, "max_size": [800, 600], "format": "PNG"}'),
('规格图', '技术类', '产品规格说明图片', '{"quality": 95, "max_size": [800, 600], "format": "PNG"}'),
('安装图', '技术类', '安装说明图片', '{"quality": 90, "max_size": [800, 600], "format": "JPEG"}'),
('说明图', '文档类', '说明文档图片', '{"quality": 88, "max_size": [800, 600], "format": "JPEG"}'),
('示意图', '文档类', '示意说明图片', '{"quality": 90, "max_size": [800, 600], "format": "PNG"}'),
('流程图', '文档类', '流程说明图片', '{"quality": 90, "max_size": [1000, 750], "format": "PNG"}'),
('架构图', '文档类', '架构说明图片', '{"quality": 95, "max_size": [1200, 800], "format": "PNG"}'),
('未分类', '其他', '未明确分类的图片', '{"quality": 85, "max_size": [800, 600], "format": "JPEG"}'),
('自定义', '其他', '自定义用途图片', '{"quality": 85, "max_size": [800, 600], "format": "JPEG"}')
ON CONFLICT (purpose_name) DO NOTHING;

-- 2. 图片下载记录表（扩展版）
CREATE TABLE IF NOT EXISTS image_downloads (
    id SERIAL PRIMARY KEY,
    module VARCHAR(50) NOT NULL COMMENT '业务模块',
    item_id VARCHAR(50) NOT NULL COMMENT '业务数据ID',
    field_name VARCHAR(50) NOT NULL COMMENT '字段名称',
    purpose_name VARCHAR(50) NOT NULL COMMENT '图片用途',
    original_url TEXT NOT NULL COMMENT '原始URL',
    local_path TEXT NOT NULL COMMENT '本地存储路径',
    filename VARCHAR(255) NOT NULL COMMENT '文件名',
    file_size INTEGER NOT NULL DEFAULT 0 COMMENT '文件大小(字节)',
    format VARCHAR(20) COMMENT '图片格式',
    width INTEGER DEFAULT 0 COMMENT '图片宽度',
    height INTEGER DEFAULT 0 COMMENT '图片高度',
    md5_hash VARCHAR(32) COMMENT 'MD5哈希值',
    thumbnail_path TEXT COMMENT '缩略图路径',
    download_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '下载时间',
    status VARCHAR(20) DEFAULT 'success' COMMENT '状态',
    error_message TEXT COMMENT '错误信息',
    metadata JSONB COMMENT '扩展元数据',
    
    -- 索引
    UNIQUE(module, item_id, field_name, original_url),
    FOREIGN KEY (purpose_name) REFERENCES image_purposes(purpose_name) ON UPDATE CASCADE
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_image_downloads_module_item ON image_downloads(module, item_id);
CREATE INDEX IF NOT EXISTS idx_image_downloads_purpose ON image_downloads(purpose_name);
CREATE INDEX IF NOT EXISTS idx_image_downloads_status ON image_downloads(status);
CREATE INDEX IF NOT EXISTS idx_image_downloads_download_time ON image_downloads(download_time);
CREATE INDEX IF NOT EXISTS idx_image_downloads_file_size ON image_downloads(file_size);

-- 3. 图片使用统计表
CREATE TABLE IF NOT EXISTS image_usage_stats (
    id SERIAL PRIMARY KEY,
    module VARCHAR(50) NOT NULL,
    purpose_name VARCHAR(50) NOT NULL,
    total_count INTEGER DEFAULT 0 COMMENT '总数量',
    total_size BIGINT DEFAULT 0 COMMENT '总大小(字节)',
    avg_size INTEGER DEFAULT 0 COMMENT '平均大小(字节)',
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(module, purpose_name),
    FOREIGN KEY (purpose_name) REFERENCES image_purposes(purpose_name) ON UPDATE CASCADE
);

-- 4. 图片清理日志表
CREATE TABLE IF NOT EXISTS image_cleanup_logs (
    id SERIAL PRIMARY KEY,
    cleanup_type VARCHAR(20) NOT NULL COMMENT '清理类型',
    module VARCHAR(50) COMMENT '模块',
    purpose_name VARCHAR(50) COMMENT '用途',
    files_deleted INTEGER DEFAULT 0 COMMENT '删除文件数',
    size_freed BIGINT DEFAULT 0 COMMENT '释放空间(字节)',
    cleanup_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    details JSONB COMMENT '清理详情'
);

-- 5. 为产品表添加图片本地路径字段
ALTER TABLE products 
ADD COLUMN IF NOT EXISTS small_img_local TEXT COMMENT '缩略图本地路径',
ADD COLUMN IF NOT EXISTS banner_local TEXT COMMENT '轮播图本地路径',
ADD COLUMN IF NOT EXISTS size_img_local TEXT COMMENT '尺寸图本地路径',
ADD COLUMN IF NOT EXISTS image_metadata JSONB COMMENT '图片元数据';

-- 6. 为案例表添加图片本地路径字段
ALTER TABLE cases 
ADD COLUMN IF NOT EXISTS img_local TEXT COMMENT '案例图片本地路径',
ADD COLUMN IF NOT EXISTS banner_local TEXT COMMENT '案例轮播图本地路径',
ADD COLUMN IF NOT EXISTS small_img_local TEXT COMMENT '案例缩略图本地路径',
ADD COLUMN IF NOT EXISTS image_metadata JSONB COMMENT '图片元数据';

-- 7. 为方案表添加图片本地路径字段
ALTER TABLE programmes 
ADD COLUMN IF NOT EXISTS small_img_local TEXT COMMENT '方案缩略图本地路径',
ADD COLUMN IF NOT EXISTS banner_local TEXT COMMENT '方案轮播图本地路径',
ADD COLUMN IF NOT EXISTS image_metadata JSONB COMMENT '图片元数据';

-- 8. 为资讯表添加图片本地路径字段
ALTER TABLE information 
ADD COLUMN IF NOT EXISTS pic_video_local TEXT COMMENT '视频封面本地路径',
ADD COLUMN IF NOT EXISTS small_img_local TEXT COMMENT '资讯缩略图本地路径',
ADD COLUMN IF NOT EXISTS images_local TEXT COMMENT '资讯图片本地路径',
ADD COLUMN IF NOT EXISTS image_metadata JSONB COMMENT '图片元数据';

-- 9. 创建图片用途统计视图
CREATE OR REPLACE VIEW v_image_purpose_stats AS
SELECT 
    ip.purpose_name,
    ip.category,
    ip.description,
    COALESCE(stats.module_count, 0) as module_count,
    COALESCE(stats.total_files, 0) as total_files,
    COALESCE(stats.total_size, 0) as total_size,
    COALESCE(stats.avg_file_size, 0) as avg_file_size
FROM image_purposes ip
LEFT JOIN (
    SELECT 
        purpose_name,
        COUNT(DISTINCT module) as module_count,
        COUNT(*) as total_files,
        SUM(file_size) as total_size,
        AVG(file_size)::INTEGER as avg_file_size
    FROM image_downloads 
    WHERE status = 'success'
    GROUP BY purpose_name
) stats ON ip.purpose_name = stats.purpose_name
WHERE ip.is_active = TRUE
ORDER BY ip.sort_order, ip.purpose_name;

-- 10. 创建图片存储概览视图
CREATE OR REPLACE VIEW v_image_storage_overview AS
SELECT 
    module,
    COUNT(*) as total_files,
    SUM(file_size) as total_size,
    AVG(file_size)::INTEGER as avg_file_size,
    COUNT(DISTINCT purpose_name) as purpose_count,
    COUNT(DISTINCT item_id) as item_count,
    MIN(download_time) as first_download,
    MAX(download_time) as last_download
FROM image_downloads 
WHERE status = 'success'
GROUP BY module
ORDER BY total_size DESC;

-- 11. 创建触发器函数：更新统计信息
CREATE OR REPLACE FUNCTION update_image_usage_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- 插入或更新统计信息
    INSERT INTO image_usage_stats (module, purpose_name, total_count, total_size, avg_size, last_updated)
    SELECT 
        NEW.module,
        NEW.purpose_name,
        COUNT(*),
        SUM(file_size),
        AVG(file_size)::INTEGER,
        CURRENT_TIMESTAMP
    FROM image_downloads 
    WHERE module = NEW.module 
      AND purpose_name = NEW.purpose_name 
      AND status = 'success'
    GROUP BY module, purpose_name
    ON CONFLICT (module, purpose_name) 
    DO UPDATE SET
        total_count = EXCLUDED.total_count,
        total_size = EXCLUDED.total_size,
        avg_size = EXCLUDED.avg_size,
        last_updated = EXCLUDED.last_updated;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 12. 创建触发器
DROP TRIGGER IF EXISTS tr_update_image_stats ON image_downloads;
CREATE TRIGGER tr_update_image_stats
    AFTER INSERT OR UPDATE ON image_downloads
    FOR EACH ROW
    EXECUTE FUNCTION update_image_usage_stats();

-- 13. 创建图片清理存储过程
CREATE OR REPLACE FUNCTION cleanup_old_images(
    days_old INTEGER DEFAULT 30,
    module_filter VARCHAR(50) DEFAULT NULL,
    purpose_filter VARCHAR(50) DEFAULT NULL
)
RETURNS TABLE(
    files_deleted INTEGER,
    size_freed BIGINT,
    cleanup_details JSONB
) AS $$
DECLARE
    cutoff_date TIMESTAMP;
    deleted_count INTEGER := 0;
    freed_size BIGINT := 0;
    cleanup_detail JSONB;
BEGIN
    cutoff_date := CURRENT_TIMESTAMP - INTERVAL '1 day' * days_old;
    
    -- 查询需要清理的记录
    WITH to_delete AS (
        SELECT id, local_path, file_size, module, purpose_name
        FROM image_downloads 
        WHERE download_time < cutoff_date
          AND status = 'success'
          AND (module_filter IS NULL OR module = module_filter)
          AND (purpose_filter IS NULL OR purpose_name = purpose_filter)
    )
    SELECT COUNT(*), COALESCE(SUM(file_size), 0)
    INTO deleted_count, freed_size
    FROM to_delete;
    
    -- 更新状态为已删除
    UPDATE image_downloads 
    SET status = 'deleted', 
        error_message = 'Cleaned up due to age: ' || days_old || ' days'
    WHERE download_time < cutoff_date
      AND status = 'success'
      AND (module_filter IS NULL OR module = module_filter)
      AND (purpose_filter IS NULL OR purpose_name = purpose_filter);
    
    -- 记录清理详情
    cleanup_detail := jsonb_build_object(
        'cutoff_date', cutoff_date,
        'module_filter', module_filter,
        'purpose_filter', purpose_filter,
        'days_old', days_old
    );
    
    -- 插入清理日志
    INSERT INTO image_cleanup_logs (
        cleanup_type, module, purpose_name, 
        files_deleted, size_freed, details
    ) VALUES (
        'scheduled', module_filter, purpose_filter,
        deleted_count, freed_size, cleanup_detail
    );
    
    RETURN QUERY SELECT deleted_count, freed_size, cleanup_detail;
END;
$$ LANGUAGE plpgsql;

-- 14. 创建图片用途验证函数
CREATE OR REPLACE FUNCTION validate_image_purpose(purpose VARCHAR(50))
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM image_purposes 
        WHERE purpose_name = purpose AND is_active = TRUE
    );
END;
$$ LANGUAGE plpgsql;

-- 15. 添加注释
COMMENT ON TABLE image_purposes IS '图片用途分类配置表';
COMMENT ON TABLE image_downloads IS '图片下载记录表，包含用途分类';
COMMENT ON TABLE image_usage_stats IS '图片使用统计表';
COMMENT ON TABLE image_cleanup_logs IS '图片清理日志表';
COMMENT ON VIEW v_image_purpose_stats IS '图片用途统计视图';
COMMENT ON VIEW v_image_storage_overview IS '图片存储概览视图';
COMMENT ON FUNCTION cleanup_old_images IS '清理过期图片的存储过程';
COMMENT ON FUNCTION validate_image_purpose IS '验证图片用途是否有效';

-- 16. 权限设置（如需要）
-- GRANT SELECT, INSERT, UPDATE ON image_purposes TO app_user;
-- GRANT SELECT, INSERT, UPDATE ON image_downloads TO app_user;
-- GRANT SELECT ON v_image_purpose_stats TO app_user;
-- GRANT SELECT ON v_image_storage_overview TO app_user; 