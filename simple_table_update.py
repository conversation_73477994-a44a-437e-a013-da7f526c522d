#!/usr/bin/env python3
"""
简化版本的产品表结构更新脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.database import get_db_connection

def update_products_table():
    """更新产品表结构"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        print("开始更新产品表结构...")
        
        # 需要添加的字段
        new_columns = [
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS new_param TEXT",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS param_info_list TEXT",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS video_explanation TEXT",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS video_installation TEXT",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS video_troubleshooting TEXT",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS accessory TEXT",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS accessory_list TEXT",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS size_img TEXT",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS unit INTEGER",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS unit_name VARCHAR(100)",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS show_for_company TEXT",
            "ALTER TABLE products ADD COLUMN IF NOT EXISTS show_for_company_name TEXT",
        ]
        
        # 执行每个ALTER语句
        success_count = 0
        for sql in new_columns:
            try:
                cursor.execute(sql)
                field_name = sql.split("ADD COLUMN IF NOT EXISTS ")[1].split(" ")[0]
                print(f"✅ 添加字段: {field_name}")
                success_count += 1
            except Exception as e:
                field_name = sql.split("ADD COLUMN IF NOT EXISTS ")[1].split(" ")[0]
                print(f"❌ 添加字段 {field_name} 失败: {e}")
        
        # 提交更改
        conn.commit()
        print(f"成功添加 {success_count} 个字段")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"更新失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = update_products_table()
    if success:
        print("✅ 表结构更新完成")
    else:
        print("❌ 表结构更新失败")
