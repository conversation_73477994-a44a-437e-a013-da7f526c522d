narwhals-1.43.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
narwhals-1.43.1.dist-info/METADATA,sha256=ZEiCJ3kWkJ4OpsGFcoMwZhvbJ0OVAfGiAeR__GqBBSc,11103
narwhals-1.43.1.dist-info/RECORD,,
narwhals-1.43.1.dist-info/WHEEL,sha256=qtCwoSJWgHk21S1Kb4ihdzI2rlJ1ZKaIurTj_ngOhyQ,87
narwhals-1.43.1.dist-info/licenses/LICENSE.md,sha256=heMD6hta6RzeBucppx59AUCgr_ukRY0ABj0bcrN3mKs,1071
narwhals/__init__.py,sha256=lmD4l3YwOdbC7IHfiIK0BKigKAQO9tyBqIXjHIji-zY,3226
narwhals/__pycache__/__init__.cpython-312.pyc,,
narwhals/__pycache__/_duration.cpython-312.pyc,,
narwhals/__pycache__/_enum.cpython-312.pyc,,
narwhals/__pycache__/_expression_parsing.cpython-312.pyc,,
narwhals/__pycache__/_namespace.cpython-312.pyc,,
narwhals/__pycache__/_translate.cpython-312.pyc,,
narwhals/__pycache__/_typing_compat.cpython-312.pyc,,
narwhals/__pycache__/_utils.cpython-312.pyc,,
narwhals/__pycache__/dataframe.cpython-312.pyc,,
narwhals/__pycache__/dependencies.cpython-312.pyc,,
narwhals/__pycache__/dtypes.cpython-312.pyc,,
narwhals/__pycache__/exceptions.cpython-312.pyc,,
narwhals/__pycache__/expr.cpython-312.pyc,,
narwhals/__pycache__/expr_cat.cpython-312.pyc,,
narwhals/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/__pycache__/expr_list.cpython-312.pyc,,
narwhals/__pycache__/expr_name.cpython-312.pyc,,
narwhals/__pycache__/expr_str.cpython-312.pyc,,
narwhals/__pycache__/expr_struct.cpython-312.pyc,,
narwhals/__pycache__/functions.cpython-312.pyc,,
narwhals/__pycache__/group_by.cpython-312.pyc,,
narwhals/__pycache__/schema.cpython-312.pyc,,
narwhals/__pycache__/selectors.cpython-312.pyc,,
narwhals/__pycache__/series.cpython-312.pyc,,
narwhals/__pycache__/series_cat.cpython-312.pyc,,
narwhals/__pycache__/series_dt.cpython-312.pyc,,
narwhals/__pycache__/series_list.cpython-312.pyc,,
narwhals/__pycache__/series_str.cpython-312.pyc,,
narwhals/__pycache__/series_struct.cpython-312.pyc,,
narwhals/__pycache__/this.cpython-312.pyc,,
narwhals/__pycache__/translate.cpython-312.pyc,,
narwhals/__pycache__/typing.cpython-312.pyc,,
narwhals/__pycache__/utils.cpython-312.pyc,,
narwhals/_arrow/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_arrow/__pycache__/__init__.cpython-312.pyc,,
narwhals/_arrow/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_arrow/__pycache__/expr.cpython-312.pyc,,
narwhals/_arrow/__pycache__/group_by.cpython-312.pyc,,
narwhals/_arrow/__pycache__/namespace.cpython-312.pyc,,
narwhals/_arrow/__pycache__/selectors.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_cat.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_dt.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_list.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_str.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_struct.cpython-312.pyc,,
narwhals/_arrow/__pycache__/typing.cpython-312.pyc,,
narwhals/_arrow/__pycache__/utils.cpython-312.pyc,,
narwhals/_arrow/dataframe.py,sha256=2_L3fNH4wJXTPss8OT_mrMBNZfBIUPD5ERqb3zNAwCc,27958
narwhals/_arrow/expr.py,sha256=4RbfAcPlOyKdX9-3E4AUD8CQuuLXAlOAEb-d7cw_5rk,7887
narwhals/_arrow/group_by.py,sha256=2rVZ2HjBpyTIA1rZu53mXhNVn6eb-irPUppwJ013-JI,6555
narwhals/_arrow/namespace.py,sha256=fM2kwjQhTAv8ZeTWI6RtvokyR_Cauz9O_7y-DzqIHp0,11253
narwhals/_arrow/selectors.py,sha256=adTwvKdjojRAaBPTywKdICQEdqv4hRUW2qvUjP7FOqA,1011
narwhals/_arrow/series.py,sha256=2CgLSj1UZRO9kGgRA4RnRuNgvppXpf2s0tyGCQmyqNc,44233
narwhals/_arrow/series_cat.py,sha256=vvNlPaHHcA-ORzh_79-oY03wt6aIg1rLI0At8FXr2Ok,598
narwhals/_arrow/series_dt.py,sha256=FVHP-mpYSKIeYkv4zHx6Z-4aJD4BG2Qj1A84FIoyWTs,7668
narwhals/_arrow/series_list.py,sha256=EpSul8DmTjQW00NQ5nLn9ZBSSUR0uuZ0IK6TLX1utwI,421
narwhals/_arrow/series_str.py,sha256=xq-hfe3mOLaHxMbS7or2jH1oQzNY6plYbNTXLfcTo1E,2488
narwhals/_arrow/series_struct.py,sha256=85pQSUqOdeMyjsnjaSr_4YBC2HRGD-dsnNy2tPveJRM,410
narwhals/_arrow/typing.py,sha256=TmgG8eqF4uCRW5NFzWTiBvlUGvD46govtIC8gRyrkmA,2286
narwhals/_arrow/utils.py,sha256=1jlExp7eXuQYHNCsHygQTd8i6qe1HLXuSBNiCKd2ckE,17662
narwhals/_compliant/__init__.py,sha256=aVjWIMakgpdSJd08jlYDm79HZDckFpTybP7bopLnEuk,1901
narwhals/_compliant/__pycache__/__init__.cpython-312.pyc,,
narwhals/_compliant/__pycache__/any_namespace.cpython-312.pyc,,
narwhals/_compliant/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_compliant/__pycache__/expr.cpython-312.pyc,,
narwhals/_compliant/__pycache__/group_by.cpython-312.pyc,,
narwhals/_compliant/__pycache__/namespace.cpython-312.pyc,,
narwhals/_compliant/__pycache__/selectors.cpython-312.pyc,,
narwhals/_compliant/__pycache__/series.cpython-312.pyc,,
narwhals/_compliant/__pycache__/typing.cpython-312.pyc,,
narwhals/_compliant/__pycache__/when_then.cpython-312.pyc,,
narwhals/_compliant/__pycache__/window.cpython-312.pyc,,
narwhals/_compliant/any_namespace.py,sha256=jdODHC7ONo5181GHWpsNZq91wrhI-_7Dcvi_bHRGPuU,3284
narwhals/_compliant/dataframe.py,sha256=Bd44QzjxiUeN4Dc8uYTUW0rst42a-a38iBK1odG-U04,17484
narwhals/_compliant/expr.py,sha256=_mNY9QCGhfboU2zECssSF6onxV8EggDsIk5dSa5HJho,39151
narwhals/_compliant/group_by.py,sha256=SWPpL7wSaTo9Gm7ny9UXffUlxokaL1gBxkTIgqgmeRM,8310
narwhals/_compliant/namespace.py,sha256=wd5lxaK60zLkTOM7C8yDDHKmXgxDkNzQKVeYxup8RXw,6652
narwhals/_compliant/selectors.py,sha256=4jYyUKrJHXldcmXpK3cjhoEjcGHMQSC6oI16AV0quD4,12018
narwhals/_compliant/series.py,sha256=59KLeOu677r6nxMpa3VfmmRBRw_uM7whRzSReK0Ypg8,13444
narwhals/_compliant/typing.py,sha256=ffa8STIhbIXI8CvrsKLl-JKKGeZ-93FfAFOLPfnUjBw,6012
narwhals/_compliant/when_then.py,sha256=vdB8IAS4PA4CIUVRi22eiWyidIxGp_-7P-wb4jZoicE,7971
narwhals/_compliant/window.py,sha256=PUQ22r050YATk2ZjQFW8n1PlFZ9K-cFW26O7kOGuWtg,477
narwhals/_dask/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_dask/__pycache__/__init__.cpython-312.pyc,,
narwhals/_dask/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_dask/__pycache__/expr.cpython-312.pyc,,
narwhals/_dask/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/_dask/__pycache__/expr_str.cpython-312.pyc,,
narwhals/_dask/__pycache__/group_by.cpython-312.pyc,,
narwhals/_dask/__pycache__/namespace.cpython-312.pyc,,
narwhals/_dask/__pycache__/selectors.cpython-312.pyc,,
narwhals/_dask/__pycache__/utils.cpython-312.pyc,,
narwhals/_dask/dataframe.py,sha256=rd8oRDJ_o7v9T7BnjpJ9cEAYYsN9JNFe5QmRdvg3Xtg,16049
narwhals/_dask/expr.py,sha256=fge5hHvPhkCSVqskjVEoCkJrf2QmqN6Cz9TqmoCkBoY,25531
narwhals/_dask/expr_dt.py,sha256=uiG_UdL9HihpvLoZE4aPPhyFkmBc85sImR7-rIEtSII,6254
narwhals/_dask/expr_str.py,sha256=5-leOxrCPbOtqV0g-DAC2k3EQ_Ao138nh6i3Wc-3O3A,3287
narwhals/_dask/group_by.py,sha256=wu72jvAY-9dxPtV-ywXsynAnjBsQu94S9lGNdMEYopA,4297
narwhals/_dask/namespace.py,sha256=tTd1fRs6riSLl567dty3t3i5VYilN9sJr2A8Nj0wFl0,12376
narwhals/_dask/selectors.py,sha256=p-1eITSnrq-OQrJ9rAiUSfSeyy55yPiMTlDmXCYZA0M,984
narwhals/_dask/utils.py,sha256=FGACVQM7binKVGSgWIH6DQnD5cRhvOFqCDae-ZsqS9Y,6706
narwhals/_duckdb/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_duckdb/__pycache__/__init__.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr_list.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr_str.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr_struct.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/group_by.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/namespace.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/selectors.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/series.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/utils.cpython-312.pyc,,
narwhals/_duckdb/dataframe.py,sha256=CgCDiSwCEBcckBT3aq7HZ2fAHDjeEXxG9bUuVcaO--0,19520
narwhals/_duckdb/expr.py,sha256=kzvI_LeX2DO0BqMrhrLvWSKWmbSRWGlckIlXVRfhKTE,35182
narwhals/_duckdb/expr_dt.py,sha256=FQATCO_3DreCQf23wWVkjUr31Zl9Y0eNcArYiAnD2Kc,6046
narwhals/_duckdb/expr_list.py,sha256=jH5SLl-Sil3IG1itweg4PKReYd5GHC7-dW9jWY2HldI,448
narwhals/_duckdb/expr_str.py,sha256=wWiJR8CL6BwxD5XkXalm9RqO-kzGUtzS2H35GJZY4Q4,3587
narwhals/_duckdb/expr_struct.py,sha256=-wB6ZXcJQIlA1G-h3MVUZA35ms56LkJymVNrp0OHcO0,537
narwhals/_duckdb/group_by.py,sha256=s4zQulWKM2veH_ynUsqOJz5vuMdS_HSwYnpLpsB8-D8,1122
narwhals/_duckdb/namespace.py,sha256=Vm7HeDiSJXpnma1lmPYx3dzi-B0ozhL7uOZQeFrWtQM,7603
narwhals/_duckdb/selectors.py,sha256=iwNJ0vb5AKGdbzJHrMG4da-bI2VWHxBjhjgV8ppExUM,967
narwhals/_duckdb/series.py,sha256=xBpuPUnSSIQ1vYEKjHQFZN7ix1ZyMwSchliDPpkf3Wk,1397
narwhals/_duckdb/utils.py,sha256=-ZX-fx3nBzbTUs2YlLWXnIAGT0T8hjIkYIhEje1sFe0,10342
narwhals/_duration.py,sha256=9xqXxT2i6Imfry8eIh_xTMlZSiXHxGjlFWQB2uOIqDY,2008
narwhals/_enum.py,sha256=sUR-04yIwjAMsX5eelKnc1UKXc5dBoj1do0krubAE04,1192
narwhals/_expression_parsing.py,sha256=X-5VV_3knecN0XfYUdXACqGlEFX_Xw4HPP2gV41FW5s,21828
narwhals/_ibis/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_ibis/__pycache__/__init__.cpython-312.pyc,,
narwhals/_ibis/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_ibis/__pycache__/expr.cpython-312.pyc,,
narwhals/_ibis/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/_ibis/__pycache__/expr_list.cpython-312.pyc,,
narwhals/_ibis/__pycache__/expr_str.cpython-312.pyc,,
narwhals/_ibis/__pycache__/expr_struct.cpython-312.pyc,,
narwhals/_ibis/__pycache__/group_by.cpython-312.pyc,,
narwhals/_ibis/__pycache__/namespace.cpython-312.pyc,,
narwhals/_ibis/__pycache__/selectors.cpython-312.pyc,,
narwhals/_ibis/__pycache__/series.cpython-312.pyc,,
narwhals/_ibis/__pycache__/utils.cpython-312.pyc,,
narwhals/_ibis/dataframe.py,sha256=P25XOB7uvCijU_KQho7l8LosL1L0THmTH_iP8HYKNBM,16113
narwhals/_ibis/expr.py,sha256=u09I-0psMoJ39u065LNSLHN-Y0WZ7x3hn5tYn8jRTnM,26206
narwhals/_ibis/expr_dt.py,sha256=WIz7cCXdpExkYy09vESFwwWQQB2dAj5B2mHq3wu2O4c,3725
narwhals/_ibis/expr_list.py,sha256=Fu6R8jFA2-nD8C4frh0rHev66Wmgmh5bNkwDzbP6hgs,359
narwhals/_ibis/expr_str.py,sha256=HrG-fqDL7wB2tHKe4cUt9y4kcFkpIKJK0x_RXH0NaXU,3729
narwhals/_ibis/expr_struct.py,sha256=ZNA3z76BR2DlNU8CDUFmvaBKMEzDhm-KRefkzeKKc0Q,483
narwhals/_ibis/group_by.py,sha256=1vSgGA9awkbQV2gesr_G_CxUsV2ulLqTQJ8yj5GFnOE,1030
narwhals/_ibis/namespace.py,sha256=wrlNTmUIqdazXryqV8yH5BLMm9nwZ53iA43e8jgG6bI,8232
narwhals/_ibis/selectors.py,sha256=W9bKXeYuA3vGmraDP9WLloZ4WtdsC8GcO0v822fQW_I,901
narwhals/_ibis/series.py,sha256=CZDwDPsdELKtdr7OWmcFyGqexr33Ucfnv_RU95VJxIQ,1218
narwhals/_ibis/utils.py,sha256=4vCiXb6Gr7sgs8o3kLXh8RZHQXmuw2i3oW_7NjeKewU,7961
narwhals/_interchange/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_interchange/__pycache__/__init__.cpython-312.pyc,,
narwhals/_interchange/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_interchange/__pycache__/series.cpython-312.pyc,,
narwhals/_interchange/dataframe.py,sha256=GWlbo9OqzQh-Y-uevJ1Kr762oaFHqFJSc3ql00LDH9w,5921
narwhals/_interchange/series.py,sha256=nSxdlOZrw3wtavS42TMR_b_EGgPBv224ioZBMo5eoC8,1651
narwhals/_namespace.py,sha256=KGLgdakAfEKhwBsudegLS7dgvdECrdEHSYbt308BlgM,13607
narwhals/_pandas_like/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_pandas_like/__pycache__/__init__.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/expr.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/group_by.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/namespace.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/selectors.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_cat.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_dt.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_list.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_str.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_struct.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/typing.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/utils.cpython-312.pyc,,
narwhals/_pandas_like/dataframe.py,sha256=mWr5BVPJid7xX6oFJB8ZFzcbBH0frLiresS3trBkSlE,41395
narwhals/_pandas_like/expr.py,sha256=Acl7mBabNox-SyT3B6e-lLSvDW6lKT9ukkbNxrHCYYw,16200
narwhals/_pandas_like/group_by.py,sha256=_BaVTlUOc_d40w0dvLrFDLb5LUZj6tXpr2GEkGMZlBc,12747
narwhals/_pandas_like/namespace.py,sha256=xBg5Tl3Xg6b_S1XjE4_1kY2khYMbeFmpJjrYySnnsE0,12945
narwhals/_pandas_like/selectors.py,sha256=5tKYNWYPrZ3dgarWbE3U8MGbxdxrgijvcUQu4wFPeGc,1144
narwhals/_pandas_like/series.py,sha256=DRFU7WRM_dobll4Acm4758vg1wI44CQgSdwfORuvqf4,41621
narwhals/_pandas_like/series_cat.py,sha256=MJwCnJ49hfnODh6JgMHOCQ2KBlTbmySU6_X4XWaqiz4,527
narwhals/_pandas_like/series_dt.py,sha256=6Md8Hwf9DkF_24Oi1j0IVKg_SqbEo44ba1mhY4o1dQo,9614
narwhals/_pandas_like/series_list.py,sha256=iriDqAN3SEq8UOiYyC8kl2q1-OZn2LBY2SlWDoFnp20,1138
narwhals/_pandas_like/series_str.py,sha256=fWQOWTgJZa12QNDLDOjvuHox6WSJCZYWysW-c2fKz-U,3347
narwhals/_pandas_like/series_struct.py,sha256=vX9HoO42vHackvVozUfp8odM9uJ4owct49ydKDnohdk,518
narwhals/_pandas_like/typing.py,sha256=VXn_-t8X5fyKzUMBi3H1wOoc8dbXUYl584P9io7MztY,496
narwhals/_pandas_like/utils.py,sha256=1wN3Q0uv_CcdQ4I9TpAXERPLxMTWtB5ICt3QTxPxiL0,25714
narwhals/_polars/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_polars/__pycache__/__init__.cpython-312.pyc,,
narwhals/_polars/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_polars/__pycache__/expr.cpython-312.pyc,,
narwhals/_polars/__pycache__/group_by.cpython-312.pyc,,
narwhals/_polars/__pycache__/namespace.cpython-312.pyc,,
narwhals/_polars/__pycache__/series.cpython-312.pyc,,
narwhals/_polars/__pycache__/typing.cpython-312.pyc,,
narwhals/_polars/__pycache__/utils.cpython-312.pyc,,
narwhals/_polars/dataframe.py,sha256=zVTXYDmAU-O_gXxMNhA220WchOvkK2_Ba_Hynbq6SHM,22303
narwhals/_polars/expr.py,sha256=I4ly489x-h5THHAepX2SFa6Ay3T5io_jvYxNpNe4BEk,14476
narwhals/_polars/group_by.py,sha256=v88hD-rOCNtCeT_YqMVII2V1c1B5TEwd0s6qOa1yXb4,2491
narwhals/_polars/namespace.py,sha256=NxsNka562nY71SmicGMY10sdlzzFHdO9pyDACp5tayg,11088
narwhals/_polars/series.py,sha256=26kO2Qn2_AzCHd-fH0wBOYy7UsN1flde-aLmYjHjI1A,25519
narwhals/_polars/typing.py,sha256=iBAA0Z0FT6vG4Zxn-Z9pCLcHnrkKtyIUAeM-mOxlBJU,655
narwhals/_polars/utils.py,sha256=0Z-79dTX-eFnfWAGldAOD2Dhn_hMEkYm7mE3LLizkZs,8686
narwhals/_spark_like/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_spark_like/__pycache__/__init__.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr_list.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr_str.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr_struct.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/group_by.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/namespace.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/selectors.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/utils.cpython-312.pyc,,
narwhals/_spark_like/dataframe.py,sha256=eDAhwd1m5Nz-IutdPZ1nmlP-dEss2Zu5ePCgs0rD-9Q,20011
narwhals/_spark_like/expr.py,sha256=v_O8q7oNQoqrGyzzmleh3_K7L1q6I1zkoeaNvM31bAw,34199
narwhals/_spark_like/expr_dt.py,sha256=7Xky2yF606Iu7MTCDzMGocY76qvksI10V17ddWrdaSw,7790
narwhals/_spark_like/expr_list.py,sha256=y3ZKxxoLz0_JAeGNi6ATSbrG5jeulThQmrk1AQP85mg,393
narwhals/_spark_like/expr_str.py,sha256=-C2CYF9usbZzC8pNqQ4bVeNcpQqKqcvpwI5y-MXB6Tg,4401
narwhals/_spark_like/expr_struct.py,sha256=w-MMXzO07YeP2Y9KyiIqWPz9vr3mt6z4ja9-nHU9L_4,517
narwhals/_spark_like/group_by.py,sha256=DJsR4558F8jsiaEQHpox09heEvWKuG39aAPQq-Tqel4,1245
narwhals/_spark_like/namespace.py,sha256=PHmlK_Nyzyxek5ASwSoAcm8czNG8f3uy09H5c8gyaNc,10355
narwhals/_spark_like/selectors.py,sha256=odBRkCQR3gXtSLg59uDZxybI5skZi7I2QObQC4nwP78,1018
narwhals/_spark_like/utils.py,sha256=oMAs_QTtgpdYEBPCraT9TltpzzXVeFV5pAFLrkpdXGI,9914
narwhals/_translate.py,sha256=e8RjNCNX4QGJWKjM6VANDTG_bVT2VusjNfjsnkCBO3g,6112
narwhals/_typing_compat.py,sha256=jjj5DKl00ekoQ8DehAQEyr_AWRWi3Bpa59bpwrH1YpM,2224
narwhals/_utils.py,sha256=_ID8T78ML5itQmTZgdufXMAoc0y8YyGkZzvSQFOjhuA,64454
narwhals/dataframe.py,sha256=Vf46BZDSLNy4k_faresJ_fF5WCdHv_jHGtT3Imyg5ZM,126324
narwhals/dependencies.py,sha256=ZG1p0GI8ko2zLWodzOjLZTHFRFWlCKwmL_XxVdAhQA0,18697
narwhals/dtypes.py,sha256=q76kn1IaU-hROpPP7xhWTPncdKF9FG5OXxZ1aXSzEBk,23320
narwhals/exceptions.py,sha256=ZMVtWO30_LmIB_w5ma6Ae6UXVYlzGNyN_ok4yjeHXbo,4451
narwhals/expr.py,sha256=yIht5F74bfuWfS2Eo8UHmke6TMJfhIj5R3DyOQvT--Q,106845
narwhals/expr_cat.py,sha256=E0FooLjYVXQ69U0Cb38_6Kiu0F-9-VwXGkjJXXV9klE,1261
narwhals/expr_dt.py,sha256=PRcNelO21RAD3vTa57egHDXEChi8uksWhaXWphk8WO4,31229
narwhals/expr_list.py,sha256=E0_B0tXtL4KtfKIUk7u1llC-qHENoHTjlpX3zs0laoA,1772
narwhals/expr_name.py,sha256=nB09yF61KMWV0pcM8u3ex3maCtnFuryTyDKr0GwToSo,6012
narwhals/expr_str.py,sha256=oB_M62vl6yEFCBNcsAGrHIkAp87RHXUnqvFZeipILVg,17781
narwhals/expr_struct.py,sha256=O5GbmFra17OCJEcG9wo3rVKS2vIRqI7z0SM17aSUmrM,1793
narwhals/functions.py,sha256=PwhZU8PorAC3Y_Sd_NxhFSrfK7J7fxJzgvDXSyWNhlE,67491
narwhals/group_by.py,sha256=4Hmiap6ri94owOWkps4ODQTbxMKKJUW56Hb_DEAgYJo,7258
narwhals/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/schema.py,sha256=_KvNPN6ndaW859JQRBfMOa-lgnXJxdJWJlkYd3B3DFk,6488
narwhals/selectors.py,sha256=ybbFG7Sjebr8qoMgD43O6QuHBGl52yUpGRe08L1LKyo,10759
narwhals/series.py,sha256=HK0DgvI3RUSBUShbErkoU8NIhsI1tDPy3X14h2RqNPs,90106
narwhals/series_cat.py,sha256=I5osb8Fj04iWqfEWjiyhVPiFYe3Kk_mTZXZjwn3jnRc,911
narwhals/series_dt.py,sha256=9EAEP2dP9yxMQu3snmufwS0Y9inqMQ8B9EayEdvBMtw,24109
narwhals/series_list.py,sha256=NznN1Z50RSGX4uQBO4OBMtu7YBHRM58tgPKoJjmOrDg,1041
narwhals/series_str.py,sha256=4a_nTdloK3PLfrVtYbfCAYmjMmnMCps9RSEloOIlSI8,14591
narwhals/series_struct.py,sha256=pmKigkmKe8m-40X9UWW5_8PLqNzHIKubElv2V2Ohu4I,974
narwhals/stable/__init__.py,sha256=b9soCkGkQzgF5jO5EdQ6IOQpnc6G6eqWmY6WwpoSjhk,85
narwhals/stable/__pycache__/__init__.cpython-312.pyc,,
narwhals/stable/v1/__init__.py,sha256=TBPtMv0gmYEYAravglExlwW35judwjhrNbZb32s_my4,59651
narwhals/stable/v1/__pycache__/__init__.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/_dtypes.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/_namespace.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/dependencies.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/dtypes.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/selectors.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/typing.cpython-312.pyc,,
narwhals/stable/v1/_dtypes.py,sha256=7zGmarnurUTgY6DI4KQ1MSAC7B9ZZiI5Em7plb-HAEs,2700
narwhals/stable/v1/_namespace.py,sha256=gfsbT4R4aLmmdArY35LRpEHPiUeZKEEnXGiY9ypFtwE,296
narwhals/stable/v1/dependencies.py,sha256=aM0IShF4hbaaMEDRJQXvsu4RABZOdBG4QhrpJPxb7fg,5001
narwhals/stable/v1/dtypes.py,sha256=u2NFDJyCkjsK6p3K9ULJS7CoG16z0Z1MQiACTVkhkH4,1082
narwhals/stable/v1/selectors.py,sha256=xEA9bBzkpTwUanGGoFwBCcHIAXb8alwrPX1mjzE9mDM,312
narwhals/stable/v1/typing.py,sha256=HFIIvrvir15JN6jVDMgEPV7Fry3TQSPz7l47yzP-7Ms,6896
narwhals/this.py,sha256=BbKcj0ReWqE01lznzKjuqq7otXONvjBevWWC5aJhQxs,1584
narwhals/translate.py,sha256=iI7BTGEDoqKdrewOQlwNDA757gSSnJ6d9d-Z6FLdhmk,27398
narwhals/typing.py,sha256=YYajUDHIrMaa1pFbshvWjPThanZDNN71mUenCg_kaXI,15334
narwhals/utils.py,sha256=2GT3XxucWI6l9r9jTwMw7Aha2G73FsSXgXNFZ3O_ZyA,223
