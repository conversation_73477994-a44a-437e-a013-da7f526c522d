---
description: 
globs: 
alwaysApply: true
---
# 代码质量标准规范

## 📋 概述

本文档基于云商系统项目的全局代码分析结果，制定严格的代码质量标准，旨在预防历史异常的再次发生，确保代码的可维护性、稳定性和安全性。

## 🚨 强制执行原则

### 核心质量要求
1. **零TODO原则**: 禁止提交包含TODO标记的代码到主分支
2. **具体异常原则**: 禁止使用通用`except Exception`，必须使用具体异常类型
3. **真实数据原则**: 禁止在生产代码中使用模拟数据和占位符
4. **完整实现原则**: 所有功能必须完整实现，不允许空函数体
5. **统一规范原则**: 严格遵循命名规范和代码格式标准

## 🔍 历史问题分析与预防

### 1. 功能完整性问题预防

#### 发现的问题
- [sync_service.py](mdc:services/sync_service.py) 中存在3个TODO标记：案例、方案、资讯保存逻辑未实现
- [main.py](mdc:main.py) 中登录逻辑标记为TODO
- 多个同步方法只是占位符，返回"API尚未实现"

#### 预防措施
```python
# ❌ 禁止的代码模式
def _save_case(self, case: Dict[str, Any]) -> None:
    """保存案例数据"""
    # TODO: 实现案例保存逻辑
    pass

# ✅ 必须的实现模式
def _save_case(self, case: Dict[str, Any]) -> None:
    """
    保存案例数据到数据库
    
    Args:
        case: 案例数据字典，必须包含id、title、content等字段
        
    Raises:
        ValidationError: 当案例数据格式不正确时
        DatabaseError: 当数据库操作失败时
    """
    # 1. 数据验证
    if not case.get('id'):
        raise ValidationError("案例ID不能为空")
    if not case.get('title'):
        raise ValidationError("案例标题不能为空")
    
    # 2. 数据库操作
    def _save_operation(conn, case_data):
        with conn.cursor() as cursor:
            sql = """
            INSERT INTO cases (id, title, content, status, created_at, updated_at)
            VALUES (%(id)s, %(title)s, %(content)s, %(status)s, NOW(), NOW())
            ON CONFLICT (id) DO UPDATE SET
                title = EXCLUDED.title,
                content = EXCLUDED.content,
                status = EXCLUDED.status,
                updated_at = NOW()
            """
            cursor.execute(sql, case_data)
            conn.commit()
            logger.info(f"案例保存成功: {case_data.get('title')}")
    
    # 3. 执行保存
    try:
        self._execute_with_connection(_save_operation, case)
    except psycopg2.Error as e:
        logger.error(f"案例保存失败: {e}")
        raise DatabaseError(f"案例保存失败: {e}")
```

### 2. 异常处理规范化

#### 发现的问题
- 80%+的异常处理使用通用`except Exception`
- 缺乏统一的异常处理策略
- 异常日志记录不详细

#### 预防措施
```python
# ❌ 禁止的异常处理
try:
    result = api_call()
except Exception as e:  # 通用异常捕获
    pass  # 空处理

# ✅ 必须的异常处理模式
try:
    result = api_client.get_products()
    return self._process_api_response(result)
except ConnectionError as e:
    logger.error(f"API连接失败: {e}", extra={
        'endpoint': 'get_products',
        'timestamp': datetime.now().isoformat(),
        'user_id': self.current_user_id
    })
    raise APIConnectionError(f"无法连接到产品服务: {e}")
except TimeoutError as e:
    logger.error(f"API请求超时: {e}")
    raise APITimeoutError(f"产品数据获取超时: {e}")
except ValidationError as e:
    logger.error(f"API响应数据验证失败: {e}")
    raise APIValidationError(f"产品数据格式错误: {e}")
except APIAuthenticationError as e:
    logger.error(f"API认证失败: {e}")
    # 尝试刷新token
    self._refresh_authentication()
    raise
```

#### 自定义异常类体系
```python
# utils/exceptions.py
class YunshangBaseException(Exception):
    """云商系统基础异常类"""
    def __init__(self, message: str, error_code: str = None, details: Dict = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        self.timestamp = datetime.now()

class APIException(YunshangBaseException):
    """API相关异常"""
    pass

class APIConnectionError(APIException):
    """API连接异常"""
    pass

class APITimeoutError(APIException):
    """API超时异常"""
    pass

class APIAuthenticationError(APIException):
    """API认证异常"""
    pass

class APIValidationError(APIException):
    """API数据验证异常"""
    pass

class DatabaseException(YunshangBaseException):
    """数据库相关异常"""
    pass

class DatabaseConnectionError(DatabaseException):
    """数据库连接异常"""
    pass

class ValidationError(YunshangBaseException):
    """数据验证异常"""
    pass
```

### 3. 项目结构规范化

#### 发现的问题
- 根目录散落50+个测试文件和报告
- 存在3个不同虚拟环境目录
- 临时文件未清理

#### 强制目录结构
```
yunshang/
├── main.py                          # 主应用入口
├── config.py                        # 配置管理
├── requirements.txt                 # 依赖管理
├── .env.example                     # 环境变量模板
├── README.md                        # 项目说明
├── pages/                           # Streamlit页面
│   ├── 01_product_management.py     # 产品管理（英文命名）
│   ├── 02_product_detail.py         # 产品详情
│   └── ...
├── components/                      # 可复用组件
│   ├── __init__.py
│   ├── data_table.py               # 数据表格组件
│   └── search_filter.py            # 搜索筛选组件
├── services/                        # 业务服务层
│   ├── __init__.py
│   ├── sync_service.py             # 数据同步服务
│   └── product_service.py          # 产品服务
├── utils/                          # 工具函数
│   ├── __init__.py
│   ├── exceptions.py               # 自定义异常
│   ├── database.py                 # 数据库工具
│   ├── auth.py                     # 认证工具
│   └── logging_config.py           # 日志配置
├── tests/                          # 测试文件（统一目录）
│   ├── unit/                       # 单元测试
│   ├── integration/                # 集成测试
│   └── fixtures/                   # 测试数据
├── docs/                           # 项目文档
│   ├── api/                        # API文档
│   ├── deployment/                 # 部署文档
│   └── development/                # 开发文档
├── scripts/                        # 脚本文件
│   ├── setup/                      # 安装脚本
│   ├── migration/                  # 数据迁移脚本
│   └── maintenance/                # 维护脚本
├── data/                          # 数据文件
│   ├── raw/                       # 原始数据
│   ├── processed/                 # 处理后数据
│   └── cache/                     # 缓存数据
├── logs/                          # 日志文件
├── venv/                          # 虚拟环境（统一使用）
├── .streamlit/                    # Streamlit配置
├── .cursor/                       # Cursor规则
└── .git/                          # Git版本控制
```

### 4. 文件命名强制规范

#### 禁止的命名模式
```bash
# ❌ 严格禁止
01_📦_产品管理.py           # 中文+表情符号
02_📋_产品详情.py           # 特殊字符
数据表格组件.py              # 纯中文
test_产品_功能.py            # 中英文混合
```

#### 强制的命名模式
```bash
# ✅ 必须使用
01_product_management.py     # 英文+下划线
02_product_detail.py         # 简洁描述性
data_table_component.py      # 组件命名
test_product_functionality.py # 测试文件命名
```

### 5. 性能优化标准

#### Streamlit缓存规范
```python
# ✅ 正确的缓存使用
@st.cache_data(ttl=600, show_spinner=True)  # 10分钟缓存，显示加载状态
def load_products_data(category_id: Optional[int] = None) -> List[Dict[str, Any]]:
    """
    加载产品数据
    
    Args:
        category_id: 分类ID，None表示加载所有分类
        
    Returns:
        产品数据列表
    """
    try:
        params = {'category_id': category_id} if category_id else {}
        response = api_client.get_products(**params)
        return response.get('data', [])
    except Exception as e:
        logger.error(f"产品数据加载失败: {e}")
        st.error("产品数据加载失败，请刷新页面重试")
        return []

# 缓存清理机制
def clear_products_cache():
    """清理产品相关缓存"""
    load_products_data.clear()
    st.cache_data.clear()
```

#### 数据库查询优化
```python
# ✅ 优化的数据库查询
class ProductService:
    def get_products_with_pagination(
        self, 
        page: int = 1, 
        size: int = 20,
        search_term: str = "",
        category_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        分页获取产品数据
        
        Args:
            page: 页码（从1开始）
            size: 每页数量
            search_term: 搜索关键词
            category_id: 分类ID
            
        Returns:
            包含产品数据和分页信息的字典
        """
        def _query_operation(conn):
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                # 构建WHERE条件
                where_conditions = ["status = 'active'"]
                params = {'offset': (page - 1) * size, 'limit': size}
                
                if search_term:
                    where_conditions.append("(name ILIKE %(search)s OR description ILIKE %(search)s)")
                    params['search'] = f"%{search_term}%"
                
                if category_id:
                    where_conditions.append("category_id = %(category_id)s")
                    params['category_id'] = category_id
                
                where_clause = " AND ".join(where_conditions)
                
                # 查询总数
                count_sql = f"SELECT COUNT(*) as total FROM products WHERE {where_clause}"
                cursor.execute(count_sql, params)
                total = cursor.fetchone()['total']
                
                # 查询数据
                data_sql = f"""
                SELECT p.*, c.name as category_name
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE {where_clause}
                ORDER BY p.updated_at DESC
                OFFSET %(offset)s LIMIT %(limit)s
                """
                cursor.execute(data_sql, params)
                products = cursor.fetchall()
                
                return {
                    'products': [dict(row) for row in products],
                    'pagination': {
                        'page': page,
                        'size': size,
                        'total': total,
                        'pages': (total + size - 1) // size
                    }
                }
        
        return self._execute_with_connection(_query_operation)
```

### 6. 安全性强化标准

#### 输入验证规范
```python
# utils/validators.py
from typing import Any, Dict, List, Optional
import re

class InputValidator:
    """输入验证器"""
    
    @staticmethod
    def validate_product_data(data: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证产品数据
        
        Args:
            data: 产品数据字典
            
        Returns:
            验证结果字典
            
        Raises:
            ValidationError: 验证失败时抛出
        """
        errors = []
        
        # 必需字段验证
        required_fields = ['name', 'category_id', 'price']
        for field in required_fields:
            if not data.get(field):
                errors.append(f"字段 {field} 不能为空")
        
        # 数据类型验证
        if data.get('price') and not isinstance(data['price'], (int, float)):
            errors.append("价格必须是数字")
        
        if data.get('category_id') and not isinstance(data['category_id'], int):
            errors.append("分类ID必须是整数")
        
        # 数据格式验证
        if data.get('name'):
            name = str(data['name']).strip()
            if len(name) < 2 or len(name) > 100:
                errors.append("产品名称长度必须在2-100字符之间")
            
            # 防止SQL注入和XSS
            if re.search(r'[<>"\';]', name):
                errors.append("产品名称包含非法字符")
        
        if errors:
            raise ValidationError("数据验证失败", details={'errors': errors})
        
        return {'valid': True, 'cleaned_data': data}

# 使用示例
def save_product(product_data: Dict[str, Any]) -> Dict[str, Any]:
    """保存产品数据"""
    try:
        # 验证输入
        validation_result = InputValidator.validate_product_data(product_data)
        cleaned_data = validation_result['cleaned_data']
        
        # 保存到数据库
        result = product_service.create_product(cleaned_data)
        return {'success': True, 'product_id': result['id']}
        
    except ValidationError as e:
        logger.warning(f"产品数据验证失败: {e}")
        return {'success': False, 'errors': e.details.get('errors', [])}
    except DatabaseError as e:
        logger.error(f"产品保存失败: {e}")
        return {'success': False, 'error': '保存失败，请重试'}
```

## 📋 代码质量检查清单

### 提交前强制检查
- [ ] **功能完整性**: 所有功能必须完整实现，无TODO标记
- [ ] **异常处理**: 使用具体异常类型，有完整的错误处理
- [ ] **数据验证**: 所有用户输入都经过验证
- [ ] **日志记录**: 关键操作都有详细日志
- [ ] **文件命名**: 使用英文命名，遵循snake_case规范
- [ ] **代码格式**: 通过black格式化，符合PEP8规范
- [ ] **类型提示**: 函数参数和返回值都有类型注解
- [ ] **文档字符串**: 所有函数都有完整的docstring
- [ ] **测试覆盖**: 核心功能有对应的单元测试
- [ ] **性能优化**: 合理使用缓存，避免重复计算

### 代码审查强制要求
- [ ] **架构一致性**: 新代码符合现有架构模式
- [ ] **安全性检查**: 没有安全漏洞和敏感信息泄露
- [ ] **可维护性**: 代码结构清晰，易于理解和修改
- [ ] **可扩展性**: 设计考虑了未来的扩展需求
- [ ] **兼容性**: 跨平台兼容，环境配置正确

## 🛠️ 自动化质量检查

### 预提交钩子配置
```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
        language_version: python3.9

  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        args: [--max-line-length=88, --extend-ignore=E203]

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.3.0
    hooks:
      - id: mypy
        additional_dependencies: [types-requests]

  - repo: local
    hooks:
      - id: check-todo
        name: Check for TODO comments
        entry: grep -r "TODO\|FIXME\|XXX" --include="*.py" .
        language: system
        pass_filenames: false
        always_run: true
        stages: [commit]
```

### 质量检查脚本
```python
# scripts/quality_check.py
import os
import re
import subprocess
from typing import List, Dict, Any

class CodeQualityChecker:
    """代码质量检查器"""
    
    def __init__(self, project_root: str):
        self.project_root = project_root
        self.errors = []
        self.warnings = []
    
    def check_all(self) -> Dict[str, Any]:
        """执行所有质量检查"""
        self.check_todo_markers()
        self.check_exception_handling()
        self.check_file_naming()
        self.check_import_structure()
        self.run_static_analysis()
        
        return {
            'passed': len(self.errors) == 0,
            'errors': self.errors,
            'warnings': self.warnings
        }
    
    def check_todo_markers(self):
        """检查TODO标记"""
        for root, dirs, files in os.walk(self.project_root):
            # 跳过虚拟环境和缓存目录
            dirs[:] = [d for d in dirs if d not in ['venv', '__pycache__', '.git']]
            
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if re.search(r'TODO|FIXME|XXX', content):
                            self.errors.append(f"发现TODO标记: {file_path}")
    
    def check_exception_handling(self):
        """检查异常处理"""
        pattern = r'except\s+Exception\s*:'
        for root, dirs, files in os.walk(self.project_root):
            dirs[:] = [d for d in dirs if d not in ['venv', '__pycache__', '.git']]
            
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if re.search(pattern, content):
                            self.warnings.append(f"发现通用异常处理: {file_path}")
    
    def run_static_analysis(self):
        """运行静态分析"""
        try:
            # 运行flake8
            result = subprocess.run(['flake8', '.'], capture_output=True, text=True)
            if result.returncode != 0:
                self.errors.append(f"代码格式检查失败:\n{result.stdout}")
            
            # 运行mypy
            result = subprocess.run(['mypy', '.'], capture_output=True, text=True)
            if result.returncode != 0:
                self.warnings.append(f"类型检查警告:\n{result.stdout}")
                
        except FileNotFoundError:
            self.warnings.append("静态分析工具未安装，请安装flake8和mypy")

if __name__ == "__main__":
    checker = CodeQualityChecker(".")
    result = checker.check_all()
    
    if result['passed']:
        print("✅ 代码质量检查通过")
    else:
        print("❌ 代码质量检查失败")
        for error in result['errors']:
            print(f"错误: {error}")
        for warning in result['warnings']:
            print(f"警告: {warning}")
```

## 🚨 违规处理机制

### 严重违规（阻止合并）
1. **功能不完整**: 存在TODO标记或空函数体
2. **异常处理不当**: 使用通用Exception捕获
3. **安全漏洞**: 存在SQL注入、XSS等安全问题
4. **文件命名违规**: 使用中文或特殊字符命名

### 一般违规（需要修复）
1. **代码格式问题**: 不符合PEP8规范
2. **缺少文档**: 函数缺少docstring
3. **性能问题**: 未使用缓存或存在性能瓶颈
4. **测试覆盖不足**: 核心功能缺少测试

### 处理流程
1. **自动检查**: 提交时自动运行质量检查
2. **人工审查**: 代码审查时检查规范遵循情况
3. **修复要求**: 发现问题必须修复后才能合并
4. **持续改进**: 定期更新质量标准和检查工具

---

**重要提醒**: 本规范是强制性执行标准，所有开发人员必须严格遵循。代码质量是项目成功的基础，不符合质量标准的代码将不被接受。

**文档版本**: v1.0  
**最后更新**: 2024-12-19  
**维护者**: 开发团队

