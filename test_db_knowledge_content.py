#!/usr/bin/env python3
"""
测试数据库中产品知识内容字段的存储情况
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.database import get_db_connection

def test_knowledge_content_in_db():
    """测试数据库中的产品知识内容"""
    try:
        print("开始测试数据库中的产品知识内容...")
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 获取产品数据
        print("从数据库获取产品数据...")
        cursor.execute("""
            SELECT id, name, introduction, details, param_info, use_to, 
                   qualifications, instructions, guide, common_problem,
                   new_param, param_info_list, video_explanation,
                   video_installation, video_troubleshooting, accessory
            FROM products 
            LIMIT 10
        """)
        
        products = cursor.fetchall()
        columns = [desc[0] for desc in cursor.description]
        
        if not products:
            print("❌ 数据库中没有产品数据")
            return False
        
        print(f"获取到 {len(products)} 个产品")
        
        # 分析每个产品的知识内容字段
        knowledge_fields = [
            'introduction', 'details', 'param_info', 'use_to', 
            'qualifications', 'instructions', 'guide', 'common_problem',
            'new_param', 'param_info_list', 'video_explanation', 
            'video_installation', 'video_troubleshooting', 'accessory'
        ]
        
        field_stats = {}
        for field in knowledge_fields:
            field_stats[field] = {'count': 0, 'non_empty': 0}
        
        for i, product_row in enumerate(products):
            product = dict(zip(columns, product_row))
            print(f"\n产品 {i+1}: {product.get('name', 'Unknown')}")
            print(f"ID: {product.get('id')}")
            
            for field in knowledge_fields:
                field_stats[field]['count'] += 1
                value = product.get(field, '')
                if value and str(value).strip():
                    field_stats[field]['non_empty'] += 1
                    print(f"  {field}: ✅ 有内容 ({len(str(value))} 字符)")
                else:
                    print(f"  {field}: ❌ 无内容")
        
        # 输出统计结果
        print("\n=== 知识内容字段统计 ===")
        total_fields = len(knowledge_fields)
        filled_fields = 0
        
        for field, stats in field_stats.items():
            percentage = (stats['non_empty'] / stats['count'] * 100) if stats['count'] > 0 else 0
            print(f"{field}: {stats['non_empty']}/{stats['count']} ({percentage:.1f}%)")
            if percentage > 0:
                filled_fields += 1
        
        print(f"\n总体统计:")
        print(f"有数据的字段: {filled_fields}/{total_fields} ({filled_fields/total_fields*100:.1f}%)")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_table_structure():
    """测试表结构"""
    try:
        print("\n开始检查表结构...")
        
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 检查新增字段是否存在
        new_fields = [
            'new_param', 'param_info_list', 'video_explanation',
            'video_installation', 'video_troubleshooting', 'accessory',
            'accessory_list', 'size_img', 'unit', 'unit_name'
        ]
        
        for field in new_fields:
            try:
                cursor.execute(f"SELECT {field} FROM products LIMIT 1")
                print(f"✅ 字段 {field} 存在")
            except Exception as e:
                print(f"❌ 字段 {field} 不存在: {e}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"表结构检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=== 数据库知识内容测试 ===")
    
    # 测试表结构
    structure_test = test_table_structure()
    
    # 测试数据内容
    content_test = test_knowledge_content_in_db()
    
    # 总结
    print("\n=== 测试结果总结 ===")
    print(f"表结构检查: {'✅ 通过' if structure_test else '❌ 失败'}")
    print(f"数据内容检查: {'✅ 通过' if content_test else '❌ 失败'}")
    
    if structure_test and content_test:
        print("🎉 数据库知识内容测试全部通过！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步优化")
        return False

if __name__ == "__main__":
    main()
