import os
from pathlib import Path


class Config:
    """应用配置类"""

    # 基础配置
    APP_NAME = "云商数据同步系统"
    APP_VERSION = "1.0.0"

    # 数据库配置
    DATABASE_URL = os.getenv(
        "DATABASE_URL", "***********************************************/product"
    )

    # API配置
    API_BASE_URL = os.getenv("API_BASE_URL", "https://api.zkmall.com")
    API_TIMEOUT = int(os.getenv("API_TIMEOUT", "30"))
    API_RETRY_COUNT = int(os.getenv("API_RETRY_COUNT", "3"))

    # 附件存储配置
    ATTACHMENT_BASE_DIR = os.getenv("ATTACHMENT_BASE_DIR", "data/attachments")
    ATTACHMENT_MAX_SIZE = int(os.getenv("ATTACHMENT_MAX_SIZE", "104857600"))  # 100MB
    ATTACHMENT_ALLOWED_TYPES = [
        ".jpg",
        ".jpeg",
        ".png",
        ".gif",
        ".bmp",
        ".webp",
        ".svg",  # 图片
        ".mp4",
        ".avi",
        ".mov",
        ".wmv",
        ".flv",
        ".mkv",
        ".m4v",  # 视频
        ".pdf",
        ".doc",
        ".docx",
        ".xls",
        ".xlsx",
        ".ppt",
        ".pptx",
        ".txt",  # 文档
        ".zip",
        ".rar",
        ".7z",
        ".tar",
        ".gz",  # 压缩包
    ]

    # 并发配置
    MAX_WORKERS = int(os.getenv("MAX_WORKERS", "4"))
    BATCH_SIZE = int(os.getenv("BATCH_SIZE", "100"))

    # 日志配置
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE = os.getenv("LOG_FILE", "logs/app.log")

    # 同步配置
    SYNC_INTERVAL = int(os.getenv("SYNC_INTERVAL", "3600"))  # 1小时
    AUTO_SYNC_ENABLED = os.getenv("AUTO_SYNC_ENABLED", "false").lower() == "true"

    @classmethod
    def get_attachment_dir(cls) -> Path:
        """获取附件存储目录路径"""
        attachment_dir = Path(cls.ATTACHMENT_BASE_DIR)
        attachment_dir.mkdir(parents=True, exist_ok=True)
        return attachment_dir

    @classmethod
    def get_log_dir(cls) -> Path:
        """获取日志目录路径"""
        log_file = Path(cls.LOG_FILE)
        log_dir = log_file.parent
        log_dir.mkdir(parents=True, exist_ok=True)
        return log_dir

    @classmethod
    def validate_config(cls) -> bool:
        """验证配置有效性"""
        try:
            # 验证必要目录
            cls.get_attachment_dir()
            cls.get_log_dir()

            # 验证数据库URL格式
            if not cls.DATABASE_URL.startswith(("postgresql://", "postgres://")):
                raise ValueError(
                    "DATABASE_URL must be a valid PostgreSQL connection string"
                )

            # 验证API URL格式
            if not cls.API_BASE_URL.startswith(("http://", "https://")):
                raise ValueError("API_BASE_URL must be a valid HTTP URL")

            return True

        except Exception as e:
            print(f"配置验证失败: {e}")
            return False
