# 产品知识库管理使用说明

## 📋 功能概述

产品知识库管理系统是云商系统的核心功能之一，专门用于：

1. **产品知识整理** - 以产品型号为标识，整理产品介绍、特性和相关文档
2. **智能文档处理** - 自动处理PDF、Word等文档，转换为标准化Markdown格式
3. **FastGPT集成** - 根据产品类别自动同步到对应的FastGPT知识库
4. **知识召回优化** - 通过索引、摘要和标签提升产品知识的召回率

## 🚀 快速开始

### 1. 环境配置

首先需要配置相关的环境变量：

```bash
# FastGPT基础配置
FASTGPT_API_BASE=https://api.fastgpt.in
FASTGPT_API_KEY=your_fastgpt_api_key_here

# 产品知识库配置 - 根据产品类别配置不同的知识库ID
# 门禁类产品
FASTGPT_ACCESS_CONTROL_DATASET_ID=your_access_control_dataset_id
FASTGPT_ATTENDANCE_DATASET_ID=your_attendance_dataset_id
FASTGPT_FACE_RECOGNITION_DATASET_ID=your_face_recognition_dataset_id

# 安检类产品
FASTGPT_SECURITY_CHECK_DATASET_ID=your_security_check_dataset_id
FASTGPT_XRAY_DATASET_ID=your_xray_dataset_id
FASTGPT_METAL_DETECTOR_DATASET_ID=your_metal_detector_dataset_id

# 智能硬件
FASTGPT_SMART_LOCK_DATASET_ID=your_smart_lock_dataset_id
FASTGPT_CAMERA_DATASET_ID=your_camera_dataset_id
FASTGPT_SENSOR_DATASET_ID=your_sensor_dataset_id

# 默认分类
FASTGPT_DEFAULT_PRODUCT_DATASET_ID=your_default_product_dataset_id

# AI多模态服务配置（用于图片标注）
AI_MULTIMODAL_SERVICE_URL=https://api.openai.com/v1/chat/completions
AI_MULTIMODAL_SERVICE_KEY=your_openai_api_key_here
```

### 2. 安装依赖

确保安装了必要的Python包：

```bash
pip install docx2pdf requests pathlib
```

### 3. 运行测试

运行测试脚本验证功能：

```bash
python test_product_knowledge.py
```

## 📖 详细功能说明

### 1. 产品知识块创建

#### 功能描述
- 自动从云商API获取产品完整信息
- 提取产品介绍、特性、技术参数等关键信息
- 生成结构化的产品知识块
- 包含索引、摘要、标签等元数据

#### 使用方法
1. 进入"产品知识库"页面
2. 选择"创建产品知识块"
3. 从产品列表中选择目标产品
4. 系统自动检测相关文档
5. 点击"创建产品知识块"
6. 可选择同步到FastGPT

#### 知识块结构
```json
{
  "product_model": "产品型号",
  "title": "产品知识库标题",
  "summary": "产品摘要",
  "introduction": "详细介绍",
  "basic_info": {
    "name": "产品名称",
    "category_name": "产品类别",
    "brand": "品牌",
    "model": "型号",
    "price": "价格"
  },
  "documents": ["相关文档列表"],
  "indexes": ["关键词索引"],
  "tags": ["标签列表"],
  "dataset_id": "对应的FastGPT知识库ID"
}
```

### 2. 文档处理功能

#### 支持的文档格式
- **PDF文档** - 使用mineru转换为Markdown
- **Word文档** - 先转换为PDF，再处理
- **文本文档** - 直接处理为Markdown格式

#### 处理流程
1. **格式转换** - 统一转换为Markdown格式
2. **格式优化** - 标准化标题层级和结构
3. **图片处理** - 自动标注图片说明（需配置AI服务）
4. **内容分段** - 按二级标题进行分段
5. **标题优化** - 添加"文档名+一级标题+二级标题"的标识

#### 分段规则
- 以二级标题（## ）作为分段点
- 每个分段包含完整的上下文信息
- 分段标题格式：`文档名 - 一级标题 - 二级标题`

### 3. FastGPT知识库集成

#### 类别映射机制
系统根据产品类别自动选择对应的FastGPT知识库：

| 产品类别 | 环境变量 | 说明 |
|---------|---------|------|
| 门禁 | FASTGPT_ACCESS_CONTROL_DATASET_ID | 门禁系统产品 |
| 考勤 | FASTGPT_ATTENDANCE_DATASET_ID | 考勤管理产品 |
| 人脸识别 | FASTGPT_FACE_RECOGNITION_DATASET_ID | 人脸识别产品 |
| 安检 | FASTGPT_SECURITY_CHECK_DATASET_ID | 安检设备产品 |
| X光机 | FASTGPT_XRAY_DATASET_ID | X光检测设备 |
| 金属探测 | FASTGPT_METAL_DETECTOR_DATASET_ID | 金属探测设备 |
| 智能锁 | FASTGPT_SMART_LOCK_DATASET_ID | 智能锁产品 |
| 摄像头 | FASTGPT_CAMERA_DATASET_ID | 摄像监控产品 |
| 传感器 | FASTGPT_SENSOR_DATASET_ID | 传感器产品 |
| 其他 | FASTGPT_DEFAULT_PRODUCT_DATASET_ID | 默认知识库 |

#### 同步内容
- 产品完整介绍和特性
- 技术参数和规格信息
- 相关文档列表和类型
- 关键词索引和标签
- 产品基本信息

### 4. 批量处理功能

#### 功能特点
- 支持批量处理所有产品
- 可配置批次大小（建议10-50个产品/批次）
- 自动错误处理和重试机制
- 详细的处理进度和结果报告

#### 使用步骤
1. 进入"批量处理产品"页面
2. 设置批次大小和处理选项
3. 点击"开始批量处理"
4. 监控处理进度
5. 查看处理结果和错误报告

## 🔧 高级配置

### 1. 自定义类别映射

如需添加新的产品类别映射：

1. 在FastGPT中创建新的知识库
2. 获取知识库的Dataset ID
3. 添加对应的环境变量
4. 更新`ProductKnowledgeManager`中的映射配置

### 2. 文档处理优化

#### 自定义文档类型识别
在`DocumentProcessor._classify_document_type()`方法中添加新的关键词匹配规则。

#### 图片标注配置
配置AI多模态服务以启用自动图片标注功能：
- 支持OpenAI GPT-4 Vision
- 可扩展支持其他多模态AI服务

### 3. 知识召回优化

#### 索引策略
- 产品名称和型号的多种变体
- 技术关键词提取
- 数字+单位组合识别
- 同义词和缩写扩展

#### 标签策略
- 基于产品类别的自动标签
- 基于功能特性的智能标签
- 基于文档类型的标签
- 自定义业务标签

## 📊 监控和维护

### 1. 处理状态监控
- 实时查看处理进度
- 错误日志和异常处理
- 成功率统计和分析

### 2. 数据质量检查
- 知识块完整性验证
- 文档处理质量检查
- FastGPT同步状态确认

### 3. 性能优化
- 批处理大小调优
- 并发处理配置
- 缓存策略优化

## ❓ 常见问题

### Q1: 文档处理失败怎么办？
**A:** 检查文档格式是否支持，确保文件路径正确，查看错误日志获取详细信息。

### Q2: FastGPT同步失败？
**A:** 验证API密钥和知识库ID配置，检查网络连接，确认FastGPT服务状态。

### Q3: 如何添加新的产品类别？
**A:** 在FastGPT创建新知识库，配置对应环境变量，更新类别映射配置。

### Q4: 批量处理中断怎么办？
**A:** 系统支持断点续传，重新启动批量处理会自动跳过已处理的产品。

### Q5: 如何优化知识召回效果？
**A:** 完善产品描述信息，添加更多相关文档，优化标签和索引配置。

## 📞 技术支持

如遇到技术问题，请：
1. 查看系统日志获取详细错误信息
2. 运行测试脚本验证功能状态
3. 检查配置文件和环境变量
4. 联系技术支持团队

---

*最后更新: 2025-06-21*  
*版本: v1.0.0*
