"""
云商API客户端模块

提供对云商系统API的访问功能，包括认证、产品查询等
"""

import logging
from typing import Dict, List, Any, Optional
from utils.api_client import ZKMallClient as BaseZKMallClient

logger = logging.getLogger(__name__)


class ZKMallClient(BaseZKMallClient):
    """
    云商API客户端

    继承自基础API客户端，提供云商特定的功能
    """

    def __init__(self):
        """初始化云商客户端"""
        super().__init__()
        logger.info("ZKMallClient初始化完成")

    def get_product_by_model(self, model: str) -> Optional[Dict[str, Any]]:
        """
        根据产品型号获取产品信息

        Args:
            model: 产品型号

        Returns:
            产品信息字典，如果未找到则返回None
        """
        try:
            # 调用基础API客户端的产品查询功能
            products = self.get_products(search_term=model)

            if products and isinstance(products, list) and len(products) > 0:
                # 查找精确匹配的产品
                for product in products:
                    if product.get("model") == model:
                        return product

                # 如果没有精确匹配，返回第一个结果
                return products[0]

            return None

        except Exception as e:
            logger.error(f"获取产品信息失败 (型号: {model}): {e}")
            return None

    def batch_get_products_by_models(
        self, models: List[str]
    ) -> Dict[str, Optional[Dict[str, Any]]]:
        """
        批量根据产品型号获取产品信息

        Args:
            models: 产品型号列表

        Returns:
            型号到产品信息的映射字典
        """
        results = {}

        for model in models:
            try:
                product = self.get_product_by_model(model)
                results[model] = product

            except Exception as e:
                logger.error(f"批量获取产品信息失败 (型号: {model}): {e}")
                results[model] = None

        return results
