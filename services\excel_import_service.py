"""
Excel导入服务模块

该模块提供Excel文件导入、解析、数据提取等功能，
用于从Excel文件中提取产品型号列表并进行后续匹配处理。
"""

import logging
import os
import io
import hashlib
import json
import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

import pandas as pd
import streamlit as st
from utils.database import DatabaseManager

logger = logging.getLogger(__name__)


class ExcelImportService:
    """Excel导入服务类"""

    def __init__(self):
        """初始化服务"""
        self.db_manager = DatabaseManager()

    def process_uploaded_file(
        self,
        uploaded_file,
        sheet_name: Optional[str] = None,
        header_row: int = 1,
        model_column: str = "产品型号",
        dataset_id_column: str = "知识库ID",
        user_id: str = "system",
    ) -> Tuple[str, Dict[str, Any]]:
        """
        处理上传的Excel文件

        Args:
            uploaded_file: Streamlit上传的文件对象
            sheet_name: 工作表名称，None表示第一个工作表
            header_row: 标题行号，从1开始
            model_column: 产品型号列名
            dataset_id_column: FastGPT知识库ID列名
            user_id: 用户ID

        Returns:
            Tuple[批次ID, 处理结果]
        """
        try:
            # 生成批次ID
            batch_id = self._generate_batch_id()

            # 读取文件内容
            file_content = uploaded_file.getvalue()
            file_md5 = self._calculate_md5(file_content)

            # 解析Excel文件
            models_data = self._parse_excel_file(
                file_content, sheet_name, header_row, model_column, dataset_id_column
            )

            # 记录导入信息
            import_record = self._create_import_record(
                batch_id=batch_id,
                filename=uploaded_file.name,
                file_size=len(file_content),
                file_md5=file_md5,
                sheet_name=sheet_name or "Sheet1",
                total_rows=len(models_data),
                header_row=header_row,
                model_column=model_column,
                dataset_id_column=dataset_id_column,
                user_id=user_id,
            )

            result = {
                "batch_id": batch_id,
                "filename": uploaded_file.name,
                "total_rows": len(models_data),
                "models_data": models_data,
                "import_record": import_record,
                "success": True,
                "message": f"成功解析Excel文件，共提取到 {len(models_data)} 个产品型号",
            }

            logger.info(f"Excel文件处理成功: {uploaded_file.name}, 批次ID: {batch_id}")
            return batch_id, result

        except Exception as e:
            logger.error(f"Excel文件处理失败: {str(e)}")
            return "", {
                "success": False,
                "message": f"Excel文件处理失败: {str(e)}",
                "error": str(e),
            }

    def _parse_excel_file(
        self,
        file_content: bytes,
        sheet_name: Optional[str],
        header_row: int,
        model_column: str,
        dataset_id_column: str,
    ) -> List[Dict[str, Any]]:
        """
        解析Excel文件内容

        Args:
            file_content: 文件内容字节
            sheet_name: 工作表名称
            header_row: 标题行号
            model_column: 产品型号列名
            dataset_id_column: FastGPT知识库ID列名

        Returns:
            提取的产品型号数据列表
        """
        try:
            # 读取Excel文件
            file_buffer = io.BytesIO(file_content)

            # 读取指定工作表
            if sheet_name:
                df = pd.read_excel(
                    file_buffer, sheet_name=sheet_name, header=header_row - 1
                )
            else:
                df = pd.read_excel(file_buffer, header=header_row - 1)

            # 检查型号列是否存在
            if model_column not in df.columns:
                # 尝试通过列号访问
                try:
                    model_col_index = int(model_column) - 1
                    if 0 <= model_col_index < len(df.columns):
                        model_column = df.columns[model_col_index]
                    else:
                        raise ValueError(f"列号 {model_column} 超出范围")
                except ValueError:
                    # 尝试模糊匹配列名
                    possible_columns = [
                        col
                        for col in df.columns
                        if any(
                            keyword in str(col).lower()
                            for keyword in ["型号", "model", "规格", "spec"]
                        )
                    ]
                    if possible_columns:
                        model_column = possible_columns[0]
                        logger.warning(f"未找到指定列名，自动匹配到: {model_column}")
                    else:
                        available_columns = ", ".join(df.columns.astype(str))
                        raise ValueError(
                            f"未找到产品型号列 '{model_column}'。可用列: {available_columns}"
                        )

            # 检查知识库ID列是否存在
            dataset_column_found = None
            if dataset_id_column not in df.columns:
                # 尝试通过列号访问（默认第二列）
                try:
                    dataset_col_index = 1  # 第二列
                    if dataset_col_index < len(df.columns):
                        dataset_column_found = df.columns[dataset_col_index]
                        logger.info(f"使用第二列作为知识库ID列: {dataset_column_found}")
                    else:
                        logger.warning("第二列不存在，将使用默认知识库ID")
                except Exception:
                    # 尝试模糊匹配列名
                    possible_columns = [
                        col
                        for col in df.columns
                        if any(
                            keyword in str(col).lower()
                            for keyword in ["知识库", "dataset", "库id", "knowledge"]
                        )
                    ]
                    if possible_columns:
                        dataset_column_found = possible_columns[0]
                        logger.warning(f"自动匹配到知识库ID列: {dataset_column_found}")
                    else:
                        logger.warning("未找到知识库ID列，将使用默认知识库ID")
            else:
                dataset_column_found = dataset_id_column

            # 提取型号数据
            models_data = []
            for index, row in df.iterrows():
                model_value = row.get(model_column)
                if pd.notna(model_value) and str(model_value).strip():
                    model_str = str(model_value).strip()
                    if model_str:  # 确保不是空字符串
                        # 提取知识库ID
                        dataset_id = ""
                        if dataset_column_found:
                            dataset_value = row.get(dataset_column_found)
                            if pd.notna(dataset_value) and str(dataset_value).strip():
                                dataset_id = str(dataset_value).strip()

                        model_data = {
                            "row_number": index + header_row + 1,  # Excel行号
                            "model": model_str,
                            "dataset_id": dataset_id,  # 添加知识库ID
                            "raw_data": row.to_dict(),  # 保存原始行数据
                        }
                        models_data.append(model_data)

            if not models_data:
                raise ValueError("Excel文件中未找到有效的产品型号数据")

            logger.info(f"成功解析Excel文件，提取到 {len(models_data)} 个型号")
            return models_data

        except Exception as e:
            logger.error(f"Excel文件解析失败: {str(e)}")
            raise

    def _create_import_record(
        self,
        batch_id: str,
        filename: str,
        file_size: int,
        file_md5: str,
        sheet_name: str,
        total_rows: int,
        header_row: int,
        model_column: str,
        dataset_id_column: str,
        user_id: str,
    ) -> Dict[str, Any]:
        """
        创建导入记录

        Args:
            batch_id: 批次ID
            filename: 文件名
            file_size: 文件大小
            file_md5: 文件MD5
            sheet_name: 工作表名称
            total_rows: 总行数
            header_row: 标题行号
            model_column: 型号列名
            dataset_id_column: FastGPT知识库ID列名
            user_id: 用户ID

        Returns:
            导入记录信息
        """
        try:
            processing_config = {
                "sheet_name": sheet_name,
                "header_row": header_row,
                "model_column": model_column,
                "dataset_id_column": dataset_id_column,
                "parse_time": datetime.now().isoformat(),
            }

            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    insert_sql = """
                        INSERT INTO excel_import_records (
                            batch_id, filename, file_size, file_md5, sheet_name,
                            total_rows, header_row, model_column, dataset_id_column,
                            processed_rows, matched_rows, failed_rows, import_status,
                            processing_config, create_by, update_by
                        ) VALUES (
                            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                        )
                    """

                    cursor.execute(
                        insert_sql,
                        (
                            batch_id,
                            filename,
                            file_size,
                            file_md5,
                            sheet_name,
                            total_rows,
                            header_row,
                            model_column,
                            dataset_id_column,
                            0,  # processed_rows
                            0,  # matched_rows
                            0,  # failed_rows
                            "processing",  # import_status
                            json.dumps(processing_config, ensure_ascii=False),
                            user_id,
                            user_id,
                        ),
                    )
                    conn.commit()

            record = {
                "batch_id": batch_id,
                "filename": filename,
                "file_size": file_size,
                "file_md5": file_md5,
                "sheet_name": sheet_name,
                "total_rows": total_rows,
                "header_row": header_row,
                "model_column": model_column,
                "dataset_id_column": dataset_id_column,
                "import_status": "processing",
                "processing_config": processing_config,
            }

            logger.info(f"导入记录创建成功: {batch_id}")
            return record

        except Exception as e:
            logger.error(f"创建导入记录失败: {str(e)}")
            raise

    def update_import_progress(
        self,
        batch_id: str,
        processed_rows: int,
        matched_rows: int,
        failed_rows: int,
        status: str = "processing",
        error_message: Optional[str] = None,
    ) -> bool:
        """
        更新导入进度

        Args:
            batch_id: 批次ID
            processed_rows: 已处理行数
            matched_rows: 匹配成功行数
            failed_rows: 匹配失败行数
            status: 导入状态
            error_message: 错误信息

        Returns:
            更新是否成功
        """
        try:
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    update_sql = """
                        UPDATE excel_import_records
                        SET processed_rows = %s,
                            matched_rows = %s,
                            failed_rows = %s,
                            import_status = %s,
                            error_message = %s,
                            update_time = CURRENT_TIMESTAMP
                        WHERE batch_id = %s
                    """

                    cursor.execute(
                        update_sql,
                        (
                            processed_rows,
                            matched_rows,
                            failed_rows,
                            status,
                            error_message,
                            batch_id,
                        ),
                    )
                    conn.commit()

            logger.info(f"导入进度更新成功: {batch_id}")
            return True

        except Exception as e:
            logger.error(f"更新导入进度失败: {str(e)}")
            return False

    def get_import_record(self, batch_id: str) -> Optional[Dict[str, Any]]:
        """
        获取导入记录

        Args:
            batch_id: 批次ID

        Returns:
            导入记录信息
        """
        try:
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    select_sql = """
                        SELECT * FROM excel_import_records
                        WHERE batch_id = %s
                    """

                    cursor.execute(select_sql, (batch_id,))
                    record = cursor.fetchone()

                    if record:
                        columns = [desc[0] for desc in cursor.description]
                        return dict(zip(columns, record))

            return None

        except Exception as e:
            logger.error(f"获取导入记录失败: {str(e)}")
            return None

    def get_import_history(
        self, limit: int = 50, user_id: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        获取导入历史记录

        Args:
            limit: 返回记录数限制
            user_id: 用户ID过滤

        Returns:
            导入历史记录列表
        """
        try:
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    if user_id:
                        select_sql = """
                            SELECT * FROM excel_import_records
                            WHERE create_by = %s
                            ORDER BY create_time DESC
                            LIMIT %s
                        """
                        cursor.execute(select_sql, (user_id, limit))
                    else:
                        select_sql = """
                            SELECT * FROM excel_import_records
                            ORDER BY create_time DESC
                            LIMIT %s
                        """
                        cursor.execute(select_sql, (limit,))

                    records = cursor.fetchall()
                    columns = [desc[0] for desc in cursor.description]

                    return [dict(zip(columns, record)) for record in records]

        except Exception as e:
            logger.error(f"获取导入历史失败: {str(e)}")
            return []

    def validate_excel_file(self, uploaded_file) -> Tuple[bool, str]:
        """
        验证上传的Excel文件

        Args:
            uploaded_file: 上传的文件对象

        Returns:
            Tuple[是否有效, 错误信息]
        """
        try:
            # 检查文件大小 (最大10MB)
            max_size = 10 * 1024 * 1024
            if uploaded_file.size > max_size:
                return False, f"文件大小超过限制 ({max_size // 1024 // 1024}MB)"

            # 检查文件类型
            allowed_extensions = [".xlsx", ".xls"]
            if not any(
                uploaded_file.name.lower().endswith(ext) for ext in allowed_extensions
            ):
                return (
                    False,
                    f"不支持的文件格式，请上传 {', '.join(allowed_extensions)} 文件",
                )

            # 尝试读取文件
            try:
                file_buffer = io.BytesIO(uploaded_file.getvalue())
                pd.read_excel(file_buffer, header=0, nrows=1)
            except Exception as e:
                return False, f"文件格式错误或文件损坏: {str(e)}"

            return True, ""

        except Exception as e:
            return False, f"文件验证失败: {str(e)}"

    def get_excel_sheets(self, uploaded_file) -> List[str]:
        """
        获取Excel文件的工作表列表

        Args:
            uploaded_file: 上传的文件对象

        Returns:
            工作表名称列表
        """
        try:
            file_buffer = io.BytesIO(uploaded_file.getvalue())
            excel_file = pd.ExcelFile(file_buffer)
            return excel_file.sheet_names

        except Exception as e:
            logger.error(f"获取工作表列表失败: {str(e)}")
            return []

    def preview_excel_data(
        self,
        uploaded_file,
        sheet_name: Optional[str] = None,
        header_row: int = 1,
        preview_rows: int = 5,
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        预览Excel文件数据

        Args:
            uploaded_file: 上传的文件对象
            sheet_name: 工作表名称
            header_row: 标题行号
            preview_rows: 预览行数

        Returns:
            Tuple[是否成功, 预览数据]
        """
        try:
            file_buffer = io.BytesIO(uploaded_file.getvalue())

            if sheet_name:
                df = pd.read_excel(
                    file_buffer, sheet_name=sheet_name, header=header_row - 1
                )
            else:
                df = pd.read_excel(file_buffer, header=header_row - 1)

            # 获取前几行数据
            preview_data = df.head(preview_rows)

            result = {
                "columns": df.columns.tolist(),
                "data": preview_data.to_dict("records"),
                "total_rows": len(df),
                "success": True,
            }

            return True, result

        except Exception as e:
            logger.error(f"预览Excel数据失败: {str(e)}")
            return False, {"success": False, "error": str(e)}

    def _generate_batch_id(self) -> str:
        """生成批次ID"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        random_suffix = str(uuid.uuid4())[:8]
        return f"IMPORT_{timestamp}_{random_suffix}"

    def _calculate_md5(self, content: bytes) -> str:
        """计算文件MD5值"""
        return hashlib.md5(content).hexdigest()


# 单例实例
excel_import_service = ExcelImportService()
