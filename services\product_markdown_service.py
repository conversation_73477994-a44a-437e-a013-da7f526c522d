import json
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

import psycopg2
from utils.database import DatabaseManager
from services.fastgpt_knowledge_service import FastGPTKnowledgeService

logger = logging.getLogger(__name__)


class ProductMarkdownService:
    """产品Markdown确认服务"""

    def __init__(self):
        self.db_manager = DatabaseManager()
        self.fastgpt_service = FastGPTKnowledgeService()

    def add_matched_products_to_queue(
        self,
        matched_results: List[Dict[str, Any]],
        batch_id: str,
        dataset_id: str = "",
        user_id: str = "system",
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        将匹配成功的产品添加到Markdown确认队列

        Args:
            matched_results: 匹配成功的产品列表
            batch_id: 批次ID
            dataset_id: FastGPT数据集ID
            user_id: 用户ID

        Returns:
            (成功标志, 处理结果)
        """
        try:
            total_count = len(matched_results)
            success_count = 0
            failed_count = 0
            details = []

            logger.info(f"开始将 {total_count} 个匹配产品添加到Markdown确认队列")

            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    for result in matched_results:
                        try:
                            # 获取完整的产品数据
                            product_data = self._get_complete_product_data(
                                cursor, result["product_id"]
                            )

                            if not product_data:
                                failed_count += 1
                                details.append(
                                    {
                                        "row_number": result["row_number"],
                                        "product_model": result["model"],
                                        "status": "failed",
                                        "error": f"未找到产品ID {result['product_id']} 的详细信息",
                                    }
                                )
                                continue

                            # 转换为Markdown格式
                            markdown_content = self._convert_product_to_markdown(
                                product_data, result["model"]
                            )

                            # 插入到确认队列
                            cursor.execute(
                                """
                                INSERT INTO product_markdown_queue (
                                    batch_id, row_number, product_model, product_id,
                                    product_name, product_data, markdown_content,
                                    dataset_id, status, created_by
                                ) VALUES (
                                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                                ) ON CONFLICT (batch_id, product_model) DO UPDATE SET
                                    product_data = EXCLUDED.product_data,
                                    markdown_content = EXCLUDED.markdown_content,
                                    updated_at = CURRENT_TIMESTAMP,
                                    updated_by = EXCLUDED.created_by
                            """,
                                (
                                    batch_id,
                                    result["row_number"],
                                    result["model"],
                                    result["product_id"],
                                    result["product_name"],
                                    json.dumps(product_data, ensure_ascii=False),
                                    markdown_content,
                                    dataset_id,
                                    "pending",
                                    user_id,
                                ),
                            )

                            success_count += 1
                            details.append(
                                {
                                    "row_number": result["row_number"],
                                    "product_model": result["model"],
                                    "product_name": result["product_name"],
                                    "status": "added",
                                    "markdown_length": len(markdown_content),
                                }
                            )

                            logger.debug(f"产品 {result['model']} 已添加到确认队列")

                        except Exception as e:
                            failed_count += 1
                            details.append(
                                {
                                    "row_number": result.get("row_number", 0),
                                    "product_model": result.get("model", ""),
                                    "status": "failed",
                                    "error": f"处理异常: {str(e)}",
                                }
                            )
                            logger.error(f"添加产品到确认队列失败: {e}")

                    conn.commit()

            result_summary = {
                "batch_id": batch_id,
                "total_count": total_count,
                "success_count": success_count,
                "failed_count": failed_count,
                "success_rate": (
                    round((success_count / total_count) * 100, 2)
                    if total_count > 0
                    else 0
                ),
                "details": details,
            }

            logger.info(
                f"Markdown队列添加完成: 总数 {total_count}, 成功 {success_count}, 失败 {failed_count}"
            )

            return success_count > 0, result_summary

        except Exception as e:
            logger.error(f"添加到Markdown确认队列失败: {e}")
            return False, {"error": str(e)}

    def get_pending_confirmations(
        self, batch_id: str = "", limit: int = 50, offset: int = 0
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        获取待确认的产品列表

        Args:
            batch_id: 批次ID（可选）
            limit: 限制条数
            offset: 偏移量

        Returns:
            (成功标志, 待确认产品列表)
        """
        try:
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    # 构建查询条件
                    where_clause = "WHERE status = 'pending'"
                    params = []

                    if batch_id:
                        where_clause += " AND batch_id = %s"
                        params.append(batch_id)

                    # 获取总数
                    cursor.execute(
                        f"SELECT COUNT(*) FROM product_markdown_queue {where_clause}",
                        params,
                    )
                    total_count = cursor.fetchone()[0]

                    # 获取列表数据
                    params.extend([limit, offset])
                    cursor.execute(
                        f"""
                        SELECT id, batch_id, row_number, product_model, product_id,
                               product_name, markdown_content, dataset_id, status,
                               created_at, created_by
                        FROM product_markdown_queue 
                        {where_clause}
                        ORDER BY created_at DESC, row_number ASC
                        LIMIT %s OFFSET %s
                    """,
                        params,
                    )

                    items = []
                    for row in cursor.fetchall():
                        items.append(
                            {
                                "id": row[0],
                                "batch_id": row[1],
                                "row_number": row[2],
                                "product_model": row[3],
                                "product_id": row[4],
                                "product_name": row[5],
                                "markdown_content": row[6],
                                "dataset_id": row[7],
                                "status": row[8],
                                "created_at": row[9].isoformat() if row[9] else None,
                                "created_by": row[10],
                                "markdown_preview": (
                                    row[6][:200] + "..."
                                    if len(row[6]) > 200
                                    else row[6]
                                ),
                            }
                        )

                    return True, {
                        "total_count": total_count,
                        "items": items,
                        "has_more": (offset + limit) < total_count,
                        "current_page": (offset // limit) + 1,
                        "total_pages": (total_count + limit - 1) // limit,
                    }

        except Exception as e:
            logger.error(f"获取待确认产品列表失败: {e}")
            return False, {"error": str(e)}

    def confirm_product(
        self, product_id: int, action: str, user_id: str = "system"
    ) -> Tuple[bool, str]:
        """
        确认或拒绝单个产品

        Args:
            product_id: 产品队列ID
            action: 操作类型（confirm/reject）
            user_id: 用户ID

        Returns:
            (成功标志, 消息)
        """
        try:
            if action not in ["confirm", "reject"]:
                return False, "无效的操作类型"

            new_status = "confirmed" if action == "confirm" else "rejected"

            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(
                        """
                        UPDATE product_markdown_queue 
                        SET status = %s, updated_by = %s, updated_at = CURRENT_TIMESTAMP
                        WHERE id = %s AND status = 'pending'
                    """,
                        (new_status, user_id, product_id),
                    )

                    if cursor.rowcount == 0:
                        return False, "产品不存在或状态不正确"

                    conn.commit()

            logger.info(f"产品 {product_id} 已{action}")
            return True, f"产品已{'确认' if action == 'confirm' else '拒绝'}"

        except Exception as e:
            logger.error(f"确认产品失败: {e}")
            return False, f"操作失败: {str(e)}"

    def batch_confirm_products(
        self, product_ids: List[int], action: str, user_id: str = "system"
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        批量确认或拒绝产品

        Args:
            product_ids: 产品队列ID列表
            action: 操作类型（confirm/reject）
            user_id: 用户ID

        Returns:
            (成功标志, 操作结果)
        """
        try:
            if action not in ["confirm", "reject"]:
                return False, {"error": "无效的操作类型"}

            new_status = "confirmed" if action == "confirm" else "rejected"
            success_count = 0

            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    for pid in product_ids:
                        cursor.execute(
                            """
                            UPDATE product_markdown_queue 
                            SET status = %s, updated_by = %s, updated_at = CURRENT_TIMESTAMP
                            WHERE id = %s AND status = 'pending'
                        """,
                            (new_status, user_id, pid),
                        )

                        if cursor.rowcount > 0:
                            success_count += 1

                    conn.commit()

            result = {
                "total_count": len(product_ids),
                "success_count": success_count,
                "failed_count": len(product_ids) - success_count,
                "action": action,
            }

            logger.info(
                f"批量{action}完成: 总数 {len(product_ids)}, 成功 {success_count}"
            )
            return True, result

        except Exception as e:
            logger.error(f"批量确认产品失败: {e}")
            return False, {"error": str(e)}

    def upload_confirmed_to_fastgpt(
        self, batch_id: str = "", dataset_id: str = "", user_id: str = "system"
    ) -> Tuple[bool, Dict[str, Any]]:
        """
        将已确认的产品上传到FastGPT

        Args:
            batch_id: 批次ID（可选）
            dataset_id: FastGPT数据集ID（可选，优先使用记录中的dataset_id）
            user_id: 用户ID

        Returns:
            (成功标志, 上传结果)
        """
        try:
            # 获取已确认的产品
            confirmed_products = self._get_confirmed_products(batch_id)

            if not confirmed_products:
                return False, {"error": "没有找到已确认的产品"}

            total_count = len(confirmed_products)
            success_count = 0
            failed_count = 0
            upload_details = []

            logger.info(f"开始上传 {total_count} 个已确认产品到FastGPT")

            for product in confirmed_products:
                try:
                    # 使用产品记录中的dataset_id，如果没有则使用参数中的
                    target_dataset_id = product["dataset_id"] or dataset_id

                    if not target_dataset_id:
                        failed_count += 1
                        upload_details.append(
                            {
                                "id": product["id"],
                                "product_model": product["product_model"],
                                "status": "failed",
                                "error": "缺少dataset_id",
                            }
                        )
                        continue

                    # 上传到FastGPT
                    success, data_id, response = self.fastgpt_service.upload_text_data(
                        dataset_id=target_dataset_id,
                        content=product["markdown_content"],
                        title=f"产品资料-{product['product_model']}",
                    )

                    if success:
                        # 更新状态为已上传
                        self._update_upload_status(
                            product["id"], "uploaded", data_id, "", user_id
                        )

                        success_count += 1
                        upload_details.append(
                            {
                                "id": product["id"],
                                "product_model": product["product_model"],
                                "status": "uploaded",
                                "fastgpt_data_id": data_id,
                            }
                        )

                        logger.debug(f"产品 {product['product_model']} 上传成功")

                    else:
                        # 更新失败状态
                        error_msg = response.get("error", "上传失败")
                        self._update_upload_status(
                            product["id"], "confirmed", "", error_msg, user_id
                        )

                        failed_count += 1
                        upload_details.append(
                            {
                                "id": product["id"],
                                "product_model": product["product_model"],
                                "status": "failed",
                                "error": error_msg,
                            }
                        )

                        logger.error(
                            f"产品 {product['product_model']} 上传失败: {error_msg}"
                        )

                except Exception as e:
                    failed_count += 1
                    error_msg = f"处理异常: {str(e)}"

                    upload_details.append(
                        {
                            "id": product.get("id", 0),
                            "product_model": product.get("product_model", ""),
                            "status": "failed",
                            "error": error_msg,
                        }
                    )

                    logger.error(f"上传产品异常: {e}")

            # 返回结果
            result = {
                "batch_id": batch_id,
                "total_count": total_count,
                "success_count": success_count,
                "failed_count": failed_count,
                "success_rate": (
                    round((success_count / total_count) * 100, 2)
                    if total_count > 0
                    else 0
                ),
                "upload_details": upload_details,
            }

            logger.info(
                f"FastGPT上传完成: 总数 {total_count}, 成功 {success_count}, 失败 {failed_count}"
            )

            return success_count > 0, result

        except Exception as e:
            logger.error(f"上传到FastGPT失败: {e}")
            return False, {"error": str(e)}

    def get_queue_status(self, batch_id: str = "") -> Dict[str, Any]:
        """
        获取确认队列状态统计

        Args:
            batch_id: 批次ID（可选）

        Returns:
            队列状态统计
        """
        try:
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    where_clause = ""
                    params = []

                    if batch_id:
                        where_clause = "WHERE batch_id = %s"
                        params.append(batch_id)

                    cursor.execute(
                        f"""
                        SELECT 
                            COUNT(*) as total,
                            SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                            SUM(CASE WHEN status = 'confirmed' THEN 1 ELSE 0 END) as confirmed,
                            SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected,
                            SUM(CASE WHEN status = 'uploaded' THEN 1 ELSE 0 END) as uploaded
                        FROM product_markdown_queue 
                        {where_clause}
                    """,
                        params,
                    )

                    stats = cursor.fetchone()

                    return {
                        "batch_id": batch_id,
                        "total": stats[0] if stats else 0,
                        "pending": stats[1] if stats else 0,
                        "confirmed": stats[2] if stats else 0,
                        "rejected": stats[3] if stats else 0,
                        "uploaded": stats[4] if stats else 0,
                        "pending_rate": (
                            round((stats[1] / stats[0]) * 100, 2)
                            if stats and stats[0] > 0
                            else 0
                        ),
                        "confirmed_rate": (
                            round((stats[2] / stats[0]) * 100, 2)
                            if stats and stats[0] > 0
                            else 0
                        ),
                        "upload_rate": (
                            round((stats[4] / stats[0]) * 100, 2)
                            if stats and stats[0] > 0
                            else 0
                        ),
                    }

        except Exception as e:
            logger.error(f"获取队列状态失败: {e}")
            return {"error": str(e)}

    def _get_complete_product_data(
        self, cursor, product_id: int
    ) -> Optional[Dict[str, Any]]:
        """获取完整的产品数据"""
        try:
            cursor.execute(
                """
                SELECT p.id, p.name, p.spec, p.introduction, p.details,
                       p.param_info, p.use_to, p.other, p.price,
                       p.small_img, p.banner,
                       c.name as category_name, b.name as brand_name
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                LEFT JOIN brands b ON p.brand_id = b.id
                WHERE p.id = %s
            """,
                (product_id,),
            )

            row = cursor.fetchone()
            if not row:
                return None

            return {
                "id": row[0],
                "name": row[1],
                "spec": row[2],
                "introduction": row[3],
                "details": row[4],
                "param_info": row[5],
                "use_to": row[6],
                "other": row[7],
                "price": row[8],
                "small_img": row[9],
                "banner": row[10],
                "category_name": row[11],
                "brand_name": row[12],
            }

        except Exception as e:
            logger.error(f"获取产品数据失败: {e}")
            return None

    def _convert_product_to_markdown(
        self, product_data: Dict[str, Any], matched_model: str
    ) -> str:
        """将产品数据转换为Markdown格式"""
        try:
            # 使用FastGPT服务中的转换方法
            return self.fastgpt_service._product_to_markdown(
                product_data, matched_model
            )
        except Exception as e:
            logger.error(f"转换Markdown失败: {e}")
            return f"# {product_data.get('name', matched_model)}\n\n**型号**: {matched_model}\n\n转换失败：{str(e)}"

    def _get_confirmed_products(self, batch_id: str = "") -> List[Dict[str, Any]]:
        """获取已确认的产品列表"""
        try:
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    where_clause = "WHERE status = 'confirmed'"
                    params = []

                    if batch_id:
                        where_clause += " AND batch_id = %s"
                        params.append(batch_id)

                    cursor.execute(
                        f"""
                        SELECT id, batch_id, product_model, product_name,
                               markdown_content, dataset_id
                        FROM product_markdown_queue 
                        {where_clause}
                        ORDER BY created_at ASC
                    """,
                        params,
                    )

                    products = []
                    for row in cursor.fetchall():
                        products.append(
                            {
                                "id": row[0],
                                "batch_id": row[1],
                                "product_model": row[2],
                                "product_name": row[3],
                                "markdown_content": row[4],
                                "dataset_id": row[5],
                            }
                        )

                    return products

        except Exception as e:
            logger.error(f"获取已确认产品失败: {e}")
            return []

    def _update_upload_status(
        self,
        product_id: int,
        status: str,
        fastgpt_data_id: str,
        error_message: str,
        user_id: str,
    ) -> None:
        """更新上传状态"""
        try:
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(
                        """
                        UPDATE product_markdown_queue 
                        SET status = %s, fastgpt_data_id = %s, error_message = %s,
                            updated_by = %s, updated_at = CURRENT_TIMESTAMP
                        WHERE id = %s
                    """,
                        (status, fastgpt_data_id, error_message, user_id, product_id),
                    )

                    conn.commit()

        except Exception as e:
            logger.error(f"更新上传状态失败: {e}")
