{"api_status": {"auth": true, "modules": {"products": {"status": "success", "count": 2, "sample_data": {"createBy": null, "createdAt": "2022-08-29 08:44:21", "updateBy": null, "updatedAt": "2025-06-06 10:24:14", "params": {}, "id": 31, "status": "0", "thumbnail": "https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/82b0dc48-5a5d-428b-842c-43b0c7d64e5e.png?file=屏幕快照 2022-12-16 下午6.08.28.png", "bannerImage": "https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/2f186b73-2b84-4d51-be92-dbdb117f234e.png?file=屏幕快照 2022-12-16 下午6.08.25.png,https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/91720920-f402-4c46-8eba-b62a8526a2ec.png?file=屏幕快照 2022-12-16 下午6.08.28.png,https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/531167ff-f3d6-4988-9f2e-46f19d813903.png?file=屏幕快照 2022-12-16 下午6.08.32.png", "name": "指纹考勤机", "attribute": "0", "categoryId": "110", "labelId": "17", "specification": "ZK3969", "introduction": "ZK3969采用了ZKFace7.0面部识别算法及ZKFinger10.0指纹算法，能够快速准确识别您的面部、指纹，双识别模式，自由选择考勤方式。", "visibility": null, "parameterInfo": "<table><tbody><tr><td><p><font>用户数</font></p></td><td><p><font>200</font><font>人</font></p></td></tr><tr><td><p><font>指纹容量</font></p></td><td><p><font>400</font><font>枚</font></p></td></tr><tr><td><p><font>面部容量</font></p></td><td><p><font>200张</font></p></td></tr><tr><td><p><font>记录容量</font></p></td><td><p><font>5万</font><font>条</font></p></td></tr><tr><td><p><font>显示屏</font></p></td><td><p><font>2.</font><font>8</font><font>寸高清彩屏</font></p></td></tr><tr><td><p><font>算法版本</font></p></td><td><p><font>ZKFinger&nbsp;</font><font>10.0</font><font>最新高速双引擎指纹识别算法</font></p></td></tr><tr><td><p><font>通讯方式</font></p></td><td><p><font>TCP/IP、USB-host</font></p></td></tr><tr><td><p><font>U盘功能</font></p></td><td><p><font>支持</font><font>U盘高速上传下载</font></p></td></tr><tr><td><p><font>其它功能</font></p></td><td><p><font>T9输入法，</font><font>简单门禁，</font><font>定时响铃</font><font>，记录查询</font></p></td></tr><tr><td><p><font>使用温度</font></p></td><td><p><font>0℃～45℃</font></p></td></tr><tr><td><p><font>使用湿度</font></p></td><td><p><font>20%～80%</font></p></td></tr><tr><td><p><font>电源规格</font></p></td><td><p><font>DC&nbsp;5V&nbsp;1A</font></p></td></tr><tr><td><p><font>尺寸</font></p></td><td><p><font>162*153*30 mm</font></p></td></tr></tbody></table>", "newParam": "[{\"content\":\"200\",\"id\":\"008554f4-d8b6-45ab-bb88-3120f0d793e6\",\"params\":\"用户数\"},{\"content\":\"400\",\"id\":\"f811ec24-3ffa-4f0d-bb71-8139aa9ac585\",\"params\":\"指纹容量\"},{\"content\":\"200\",\"id\":\"f9741238-a471-4e97-8050-3f9633b669e8\",\"params\":\"面部容量\"},{\"content\":\"5万条\",\"id\":\"da48713e-4b57-481b-896b-26cde86e0450\",\"params\":\"记录容量\"},{\"content\":\"2.8寸高清彩屏\",\"id\":\"cbe07f9b-5b6d-44b7-8ecd-c6c060277b93\",\"params\":\"显示屏\"},{\"content\":\"ZKFinger 10.0最新高速双引擎指纹识别算法\",\"id\":\"486adc50-11ab-4742-89ba-ee8497605166\",\"params\":\"算法版本\"},{\"content\":\"TCP/IP、USB-host\",\"id\":\"adae0100-8d96-4712-a72c-13a0ca82b88b\",\"params\":\"通讯方式\"},{\"content\":\"支持U盘高速上传下载\",\"id\":\"22933647-508d-40b3-8eb1-5a7117450103\",\"params\":\"U盘功能\"},{\"content\":\"T9输入法，简单门禁，定时响铃，记录查询\",\"id\":\"7aa3ff8b-7c46-49ee-8bfc-d0c0254614f1\",\"params\":\"其它功能\"},{\"content\":\"0℃～45℃\",\"id\":\"bfe52f08-1faa-4230-a1a6-cb96749bb483\",\"params\":\"使用温度\"},{\"content\":\"20%～80%\",\"id\":\"a552dbc7-43ce-4c2a-a20f-7ba7f07b713b\",\"params\":\"使用湿度\"},{\"content\":\"DC 5V 1A\",\"id\":\"efdf688a-5e88-4308-8885-11830beb68de\",\"params\":\"电源规格\"},{\"content\":\"162*153*30 mm\",\"id\":\"cc4cdf67-f932-4648-89af-17643af75b9c\",\"params\":\"尺寸\"}]", "usage": "写字楼、连锁商超、学校、工厂、工地、酒店", "details": "<p><img src=\"https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/cdb92413-6c83-4f16-a650-5a97f3ca266f.png?file=【宣传彩页】熵基彩屏指纹考勤终端ZK3969_00.png\" style=\"max-width:100%;\" contenteditable=\"false\"/><br/></p>", "qualificationUrl": null, "instructionIds": "14", "showTime": "2022-08-29T08:44:09.778Z", "attachments": "https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/b8279695-0220-409e-bb54-e0b4e509a9d2.pdf?file=【宣传彩页】熵基彩屏指纹考勤终端ZK3969.pdf", "operationGuide": "<p><br/></p>", "commonIssues": "36", "price": 0.0, "isNew": 1, "categoryName": "指纹识别考勤机", "labels": [{"createBy": "", "createTime": "2022-08-27 13:33:25", "updateBy": "", "updateTime": "2022-08-27 13:34:18", "remark": null, "params": {}, "isApp": null, "id": 17, "status": "0", "name": "渠道", "showDay": null}], "labelName": "渠道", "videoExplanation": null, "videoInstallation": null, "videoTroubleshooting": null, "showForCompany": null, "viewCount": 4145, "sizeImg": null, "specList": [], "productId": 31, "accessory": null, "showForCompanyName": null, "unit": 2, "productGenListHandle": [], "productGenListInstall": [], "productGenListFault": [], "productGenListLearning": [], "allProductGenListHandle": [], "allProductGenListInstall": [], "allProductGenListFault": [], "allProductGenListLearning": [], "showForCompanyFilter": false, "thirdTypeSearch": false, "showForCompanyList": [], "paramInfoList": [{"id": "008554f4-d8b6-45ab-bb88-3120f0d793e6", "params": "用户数", "content": "200"}, {"id": "f811ec24-3ffa-4f0d-bb71-8139aa9ac585", "params": "指纹容量", "content": "400"}, {"id": "f9741238-a471-4e97-8050-3f9633b669e8", "params": "面部容量", "content": "200"}, {"id": "da48713e-4b57-481b-896b-26cde86e0450", "params": "记录容量", "content": "5万条"}, {"id": "cbe07f9b-5b6d-44b7-8ecd-c6c060277b93", "params": "显示屏", "content": "2.8寸高清彩屏"}, {"id": "486adc50-11ab-4742-89ba-ee8497605166", "params": "算法版本", "content": "ZKFinger 10.0最新高速双引擎指纹识别算法"}, {"id": "adae0100-8d96-4712-a72c-13a0ca82b88b", "params": "通讯方式", "content": "TCP/IP、USB-host"}, {"id": "22933647-508d-40b3-8eb1-5a7117450103", "params": "U盘功能", "content": "支持U盘高速上传下载"}, {"id": "7aa3ff8b-7c46-49ee-8bfc-d0c0254614f1", "params": "其它功能", "content": "T9输入法，简单门禁，定时响铃，记录查询"}, {"id": "bfe52f08-1faa-4230-a1a6-cb96749bb483", "params": "使用温度", "content": "0℃～45℃"}, {"id": "a552dbc7-43ce-4c2a-a20f-7ba7f07b713b", "params": "使用湿度", "content": "20%～80%"}, {"id": "efdf688a-5e88-4308-8885-11830beb68de", "params": "电源规格", "content": "DC 5V 1A"}, {"id": "cc4cdf67-f932-4648-89af-17643af75b9c", "params": "尺寸", "content": "162*153*30 mm"}]}}, "cases": {"status": "success", "count": 2, "sample_data": {"createBy": null, "createdAt": "2023-03-01 12:08:38", "updateBy": null, "updatedAt": "2024-05-30 18:13:02", "remark": null, "params": {}, "id": 109, "status": "0", "bannerImage": "https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/0eff81bb-9c29-430e-a45d-8f0e17215a29.jpg?file=tmp_8d9cc2f488897fe88f03d92f04d13453d235764cabfcfc5e.jpg,https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/f83300da-88bf-47d0-a11d-34d94439cc8b.jpg?file=tmp_da6646d7540a240a9560a2162013b0854703519b994b906e.jpg,https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/9335375c-3c4d-413a-8221-0814e22b1bd3.jpg?file=tmp_cf7a6ff84c608f858273e9b41200d8bb505c603e13a714c9.jpg,https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/de379e82-422b-46e5-937b-0336fd5caf79.jpg?file=tmp_bc770bd19a3f0f08bafc75fc9a510cb8589058d8a17e5770.jpg,https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/ec5a89b8-c024-4461-8c94-c67c05fe8d92.jpg?file=tmp_d22a84a1e8d9ffa4ad039244ef256f7b273fc22224698f68.jpg,https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/1fd4274a-572f-4d51-9d2b-8da54d661c8c.jpg?file=tmp_616a268480a98f2129d4348e66c772da576645abe517b329.jpg", "categoryId": 224, "categoryName": "人行通道", "title": "人脸设备配闸机", "summary": "人脸配闸机，写字楼场景出入口管理最佳组合。", "description": "<p>&nbsp;</p>", "videoUrl": null, "publisher": "文元彪", "publisherId": 62, "companyId": 24, "companyName": "熵基科技股份有限公司", "viewCount": 544, "approvalStatus": "1", "isRecommended": "0", "isAuthenticated": 1, "isPush": 0, "city": "厦门市", "province": "福建省", "isShare": 0, "parentId": -1, "top": "0", "thumbnail": "https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/0eff81bb-9c29-430e-a45d-8f0e17215a29.jpg?file=tmp_8d9cc2f488897fe88f03d92f04d13453d235764cabfcfc5e.jpg", "productList": [{"createBy": null, "createTime": null, "updateBy": null, "updateTime": null, "remark": null, "params": {}, "isApp": null, "id": 4750, "status": "0", "smallImg": "https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/zkteco/applications/sj/abc51ff6-e5b0-49d5-9c6e-251d2a3d2223.jpg?file=产品主图_01.jpg", "banner": null, "name": "面部识别门禁机  ", "attribute": "0", "category": null, "label": null, "spec": "TDB08", "introduction": "TDB08是熵基科技可见光产品系列的全面升级版本，适用各种场景。TDB08搭配定制的CPU，用于运行面部识别算法，以提高其各方面的性能。 \nTDB08配备强大的CPU和深度自主学习面部识别算法，揭开生物识别领域新篇章。此版本支持最多50,000 张面部数据库，面部识别速度小于0.3秒，采用双目光电硬件+活体识别算法，对打印的高清面部图片、照片、 面部图像视频以及各种材料面具的攻击，具有较强的防伪能力。 ", "showFor": null, "paramInfo": null, "newParam": null, "useTo": null, "details": null, "qualifications": null, "instructions": null, "showTime": null, "upTime": null, "sort": null, "isHot": null, "other": null, "guide": null, "commonProblem": null, "price": null, "isNew": null, "commonProblemList": null, "instructionsList": null, "categoryName": null, "quantity": 1, "labelList": null, "labelName": null, "videoExplanation": null, "videoInstallation": null, "videoTroubleshooting": null, "showForCompany": null, "count": null, "sizeImg": null, "specList": null, "rmIds": null, "productId": 294, "listTag": null, "labelListForSelect": null, "size": 1, "accessory": null, "accessoryList": null, "showForCompanyName": null, "unit": null, "unitName": null, "productZoneForSelect": null, "productGenMap": null, "productGenListHandle": null, "productGenListInstall": null, "productGenListFault": null, "productGenListLearning": null, "questionList": null, "allProductGenListHandle": null, "allProductGenListInstall": null, "allProductGenListFault": null, "allProductGenListLearning": null, "showForCompanyFilter": false, "thirdTypeSearch": false, "showForCompanyList": null, "isFilterShowFor": null, "companyId": null, "specSearchList": null, "paramInfoList": null, "productIdList": null}, {"createBy": null, "createTime": null, "updateBy": null, "updateTime": null, "remark": null, "params": {}, "isApp": null, "id": 4751, "status": "1", "smallImg": "https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/zkteco/applications/sj/94ff2ffe-cac3-47db-a3bd-4d3ddfd2a261.jpg?file=产品主图_01.jpg", "banner": null, "name": "面部识别门禁机 ", "attribute": "0", "category": null, "label": null, "spec": "TDB08-3D", "introduction": "TDB08-3D是熵基科技推出的视觉革命级产品，区别于以往传统双目光电摄像头，产品搭载高端3D结构 光模组，使用红外点阵立体成像技术，着重提升设备防伪能力。主动发射光学特征点，抗干扰能力更强，达高 防伪活体检测水平，在安全性、识别精度、识别速度方面都大幅度提升。 \n3D结构光可向面部投射数万个红外点阵，形成散斑图案，通过IR摄像头拍摄光斑的形变，基于三角测量原理计算被测物深度图，通过深度图可转换成点云图，进行三维实时重建，配合RGB摄像头，完美还原面部细节，精准判断人员面部信息。适用于对安全性要求较高的场景及领域。 ", "showFor": null, "paramInfo": null, "newParam": null, "useTo": null, "details": null, "qualifications": null, "instructions": null, "showTime": null, "upTime": null, "sort": null, "isHot": null, "other": null, "guide": null, "commonProblem": null, "price": null, "isNew": null, "commonProblemList": null, "instructionsList": null, "categoryName": null, "quantity": 1, "labelList": null, "labelName": null, "videoExplanation": null, "videoInstallation": null, "videoTroubleshooting": null, "showForCompany": null, "count": null, "sizeImg": null, "specList": null, "rmIds": null, "productId": 297, "listTag": null, "labelListForSelect": null, "size": 1, "accessory": null, "accessoryList": null, "showForCompanyName": null, "unit": null, "unitName": null, "productZoneForSelect": null, "productGenMap": null, "productGenListHandle": null, "productGenListInstall": null, "productGenListFault": null, "productGenListLearning": null, "questionList": null, "allProductGenListHandle": null, "allProductGenListInstall": null, "allProductGenListFault": null, "allProductGenListLearning": null, "showForCompanyFilter": false, "thirdTypeSearch": false, "showForCompanyList": null, "isFilterShowFor": null, "companyId": null, "specSearchList": null, "paramInfoList": null, "productIdList": null}, {"createBy": null, "createTime": null, "updateBy": null, "updateTime": null, "remark": null, "params": {}, "isApp": null, "id": 4752, "status": "0", "smallImg": "https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/zkteco/applications/sj/e183a8ae-c2db-4f76-8256-6c0afccb447b.jpg?file=产品主图_01.jpg", "banner": null, "name": "面部识别门禁机", "attribute": "0", "category": null, "label": null, "spec": "TDB09", "introduction": "TDB09M/TDB09Z是一款为面部通行场景设计的高性能面部识别智能终端，配置8英寸IPS高清彩屏，搭载深度学习面部识别算法，满足高端园区、智慧大厦等场景便捷通行的需求。 ", "showFor": null, "paramInfo": null, "newParam": null, "useTo": null, "details": null, "qualifications": null, "instructions": null, "showTime": null, "upTime": null, "sort": null, "isHot": null, "other": null, "guide": null, "commonProblem": null, "price": null, "isNew": null, "commonProblemList": null, "instructionsList": null, "categoryName": null, "quantity": 1, "labelList": null, "labelName": null, "videoExplanation": null, "videoInstallation": null, "videoTroubleshooting": null, "showForCompany": null, "count": null, "sizeImg": null, "specList": null, "rmIds": null, "productId": 305, "listTag": null, "labelListForSelect": null, "size": 1, "accessory": null, "accessoryList": null, "showForCompanyName": null, "unit": null, "unitName": null, "productZoneForSelect": null, "productGenMap": null, "productGenListHandle": null, "productGenListInstall": null, "productGenListFault": null, "productGenListLearning": null, "questionList": null, "allProductGenListHandle": null, "allProductGenListInstall": null, "allProductGenListFault": null, "allProductGenListLearning": null, "showForCompanyFilter": false, "thirdTypeSearch": false, "showForCompanyList": null, "isFilterShowFor": null, "companyId": null, "specSearchList": null, "paramInfoList": null, "productIdList": null}, {"createBy": null, "createTime": null, "updateBy": null, "updateTime": null, "remark": null, "params": {}, "isApp": null, "id": 4753, "status": "1", "smallImg": "https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/bf3474ae-da27-4ea6-9849-0c32cb80410c.png?file=SBTL2000&2200PLUS双通道渲染图2.png", "banner": null, "name": "摆闸", "attribute": "0", "category": null, "label": null, "spec": "SBTL2000PLUS&SBTL2200PLUS", "introduction": "设备外形采用不锈钢板冲压成型，造型 美观大方、防锈、耐用，并且对外采用标准电气接口，能方便地将面部、指纹、掌纹、虹膜、RFID、二维 码等多种验证集成在本设备上，为出入人员提供文明、有序的通行方式，同时又可杜绝非法人员出入；另 外系统 还专门设计了消防要求的功能，在出现紧急情况时，保证通道畅通无阻，方便人员及时疏散。", "showFor": null, "paramInfo": null, "newParam": null, "useTo": null, "details": null, "qualifications": null, "instructions": null, "showTime": null, "upTime": null, "sort": null, "isHot": null, "other": null, "guide": null, "commonProblem": null, "price": null, "isNew": null, "commonProblemList": null, "instructionsList": null, "categoryName": null, "quantity": 1, "labelList": null, "labelName": null, "videoExplanation": null, "videoInstallation": null, "videoTroubleshooting": null, "showForCompany": null, "count": null, "sizeImg": null, "specList": null, "rmIds": null, "productId": 581, "listTag": null, "labelListForSelect": null, "size": 1, "accessory": null, "accessoryList": null, "showForCompanyName": null, "unit": null, "unitName": null, "productZoneForSelect": null, "productGenMap": null, "productGenListHandle": null, "productGenListInstall": null, "productGenListFault": null, "productGenListLearning": null, "questionList": null, "allProductGenListHandle": null, "allProductGenListInstall": null, "allProductGenListFault": null, "allProductGenListLearning": null, "showForCompanyFilter": false, "thirdTypeSearch": false, "showForCompanyList": null, "isFilterShowFor": null, "companyId": null, "specSearchList": null, "paramInfoList": null, "productIdList": null}], "approveTime": "2025-04-11 15:39:48", "companyInfoCity": "厦门市", "companyInfoProvince": "福建省", "pushStatus": "0", "industry": "523,529", "industryName": "智慧园区,智慧社区", "scene": "524,528", "sceneName": "门禁,人行", "newPunterName": "厦门软件3期写字楼场景出入口", "createFrom": 0}}, "programmes": {"status": "no_data", "count": 0, "sample_data": null}, "information": {"status": "success", "count": 2, "sample_data": {"createBy": "62", "createdAt": "2022-09-22 18:39:52", "updateBy": "32871", "updatedAt": "2025-05-26 14:14:54", "remark": null, "params": {}, "id": 24, "status": "1", "name": "熵基科技股份有限公司", "address": "集美区", "addressDetails": "软件园三期D09", "realName": "陈小椰", "mobile": "***********", "type": "熵基分公司", "telephone": "**********", "businessLicense": "https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/b985ef2c-6340-40f1-8720-a1650fe5ffef.jpg?file=tmp_59caab60f15ee3ed54ed27379b334bfb42eb0ea688b62c3d.jpg", "authorization": "ZKD00000", "authorizationImg": "https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/zkteco/applications/sj/e230b4d0-a10d-49d1-816e-5e3c6d942c6b.png?file=中性授权牌.png", "parentCompanyName": null, "loginName": "***********", "code": null, "pwd": "$2a$10$TRUsWQpYSbqkIKHRmh74WeA6yVBu7UDXtV97QiWOl.VqWL8EXtaIu", "province": "福建省", "city": "厦门市", "authName": "***********", "size": 408, "integral": 15749, "modifyTimes": 4, "isAuth": 1, "isLast": 1, "position": "销售代表", "frozen": 0, "admin": {"createBy": "3001", "createTime": "2024-08-29 09:37:53", "updateBy": "", "updateTime": "2024-11-27 11:59:07", "remark": null, "params": {}, "isApp": null, "id": 659, "status": "1", "name": "熵基科技股份有限公司", "code": null, "realName": "陈小椰", "position": "销售代表", "jobNo": null, "loginName": "***********", "userId": 3001, "companyId": 24, "isAdmin": 0, "headImg": null, "isAssistant": 1, "logoutTime": null, "shareUserId": null, "integral": null, "integralToday": null, "exceedLimit": null, "companyType": null, "mobile": "***********", "pwd": "$2a$10$TRUsWQpYSbqkIKHRmh74WeA6yVBu7UDXtV97QiWOl.VqWL8EXtaIu"}, "approveTime": "2025-05-26 14:14:53", "approveUserName": "总平台", "approveUserId": -1, "serviceIdList": [], "location": "24.609012,118.020864", "isCreate": 1, "companyIntroduction": "<p>熵基科技成立于2007年，是一家以AI认知为核心驱动的全球领先的智能空间进化服务商。我们运用多模态BioCV（计算机视觉与生物识别）与AI 认知空间计算技术，构建全要素感知体系，推动空间从静态管理迈向自主决策及进化，在智慧空间、智慧办公、数字身份认证及智慧商业四大战略领域深度布局，提供AI赋能的端云一体化解决方案，助力客户在数字时代实现效率跃升与价值重塑。</p>", "companyIntroductionVideo": "https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/zkteco/applications/sj/db04054f-63e6-43f7-8c27-4dd190f266a6.mp4?file=tmp_1e87dc934a3d176aedd59a58610e5911.mp4", "businessIntroduction": "<p><img class=\"rich-img\"  class=\"rich-img\"  class=\"rich-img\"  src=\"https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/zkteco/applications/sj/4a44aeb3-e764-42d8-b5a3-c4617e9a3889.jpg?file=tmp_fda0785898b24978245b383361a22761.jpg\"></p><p><br></p>", "businessIntroductionVideo": null, "logo": "https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/zkteco/applications/sj/15236dc6-0d79-48fd-aba5-aaec8d10db71.jpg?file=tmp_8704e55b80797c2aa6fd4d1edef7c805.jpg"}}, "distribution_orders": {"status": "success", "count": 2, "sample_data": {"createBy": null, "createdAt": "2023-02-03 08:53:35", "updateBy": null, "updatedAt": "2024-03-01 10:44:37", "params": {}, "id": 11, "status": "0", "banner": "https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/zkteco/applications/sj/a27629c6-5ca2-462b-bb1c-6df705c96573.jpg?file=方案配单配图_01.jpg", "title": "射频卡门禁控制器方案", "primaryCategoryId": 302, "primaryCategoryName": "政企单位", "secondaryCategoryId": 310, "secondaryCategoryName": "考勤门禁", "price": 0.0, "introduction": "门禁安全管理在近些年的现代企业管理中越来越受到管理者的关注。我司推出ZKAccess3.5门禁软件在结合生物识别等新技术下，在最大程度上保护了小区业主及住户、中小型企业、工厂员工等场所的安全，记录人员进出记录，更加方便管理人员信息。实现了门禁管理统一化、流程化，并帮助客户实现安全运营", "other": "https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/zkteco/applications/sj/2c0cba31-e114-4267-88ed-0e389c435887.pdf?file=射频卡识别控制器系统介绍方案.pdf", "details": "<p><b><font>射频卡识别控制器系统介绍方案</font></b><b></b></p><p><b></b></p><p><!--[if--><span>1.<span>&nbsp;</span></span><!--[endif]--><b><font>行业介绍：</font></b><b></b></p><p><!--[if--><span>1.1.<span>&nbsp;</span></span><!--[endif]--><font>行业背景：</font><b><br/></b><b>&nbsp;&nbsp;&nbsp;</b><font>随着科学技术不断发展，各类安全管理系统层出不穷，智能门禁系统作为现代安防行业不可缺少的一部分，在确保人身、财产</font><font>、信息</font><font>安全等方面发挥积极的作用。</font></p><p><!--[if--><span>1.2.<span>&nbsp;</span></span><!--[endif]--><font>客户需求：</font></p><p><b>&nbsp;&nbsp;&nbsp;</b><font>目前市场上</font><font>“智能门禁”概念各式各样，人们对工作、生活的自动化水平也提出了越来越高的要求，“智能门禁管理系统”就是为了满足人们对现代办公和生活场所安全管理的需求。门前管理系统广泛应用于中小型企业、工厂、写字楼、物业小区等场所，应用后大大提高了整体的工作效率以及系统安全管理需求。</font></p><p><!--[if--><span>2.<span>&nbsp;</span></span><!--[endif]--><b><font>门禁系统介绍</font></b><b></b></p><p><!--[if--><span>2.1.<span>&nbsp;</span></span><!--[endif]--><font>门禁系统概述：</font></p><p><font>门禁安全管理在近些年的现代企业管理中越来越受到管理者的关注。我司推出</font>ZKAccess3.5<font>门禁</font><font>软件</font><font>在结合生物识别等新技术下，在最大程度上保护了小区业主及住户、中小型企业、工厂员工</font><font>等场所</font><font>的安全，记录人员进出记录，更加方便管理人员信息</font><font>。</font><font>实现了门禁管理统一化、流程化</font><font>，</font><font>并帮助客户实现安全运营。</font></p><p><b></b></p><p><!--[if--><span>2.2.<span>&nbsp;</span></span><!--[endif]--><font>门禁系统特点：</font></p><p><!--[if--><span>Ø<span>&nbsp;</span></span><!--[endif]--><font>强大的数据处理能力，能管理</font>30000<font>个人员的门禁数据，能连接</font>100<font>台</font><font>硬件</font><font>设备，</font></p><p><!--[if--><span>Ø<span>&nbsp;</span></span><!--[endif]--><font>形象而合理的操作流程融合了多年的门禁经验。</font></p><p><!--[if--><span>Ø<span>&nbsp;</span></span><!--[endif]--><font>自动化的用户名单管理，使得管理更科学、高效。</font></p><p><!--[if--><span>Ø<span>&nbsp;</span></span><!--[endif]--><font>建立在多级管理角色上的权限管理，能保证用户数据的保密性</font><font>。</font></p><p><!--[if--><span>2.3.<span>&nbsp;</span></span><!--[endif]--><font>工作流程图：</font></p><p><img src=\"https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/493a8ad1-4391-41b1-854f-151d0edb9078.png?file=C3工作流程图.png\" style=\"max-width:100%;\" contenteditable=\"false\"/><br/><b></b></p><p><!--[if--><span>2.4.<span>&nbsp;</span></span><!--[endif]--><font>工作流程介绍：</font></p><p><!--[if--><span>a.<span>&nbsp;</span></span><!--[endif]--><font>管理员给每个人员发卡，并授权门禁权限</font><font>；</font></p><p><!--[if--><span>b.<span>&nbsp;</span></span><!--[endif]--><font>进门时员工刷卡验证，已授权的人员验证后开锁，验证不成功不开锁；</font></p><p><!--[if--><span>c.<span>&nbsp;</span></span><!--[endif]--><font>出门时单向门直接按出门开关出门，双向按照设置要求刷卡验证出门。</font></p><p><!--[if--><span>d.<span>&nbsp;</span></span><!--[endif]--><font>非法闯入后联动报警，胁迫报警直接输入反胁迫密码，输入后门打开并触发报警。</font> </p><p><!--[if--><span>e.<span>&nbsp;</span></span><!--[endif]--><font>当发生火灾时，浓烟触发烟感器，控制器触发联动报警，同时使门</font><font>保持常开状态</font><font>。</font>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<b>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</b><b></b></p><p><!--[if--><span>3.<span>&nbsp;</span></span><!--[endif]--><b><font>解决方案拓扑图及产品介绍：</font></b><b></b></p><p><!--[if--><span>3.1.<span>&nbsp;</span></span><!--[endif]--><font>基础拓扑图（射频卡控制器单门单向）</font><font>：</font></p><p><img src=\"https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/feb3374e-b576-4375-8497-390183f1cb30.png?file=C3铁箱A拓扑图.png\" style=\"max-width:100%;\" contenteditable=\"false\"/><br/><b></b></p><p><!--[if--><span>3.2.<span>&nbsp;</span></span><!--[endif]--><font>产品介绍：</font></p><p><!--[if--><span>3.2.1.<span>&nbsp;</span></span><!--[endif]--><font>控制器铁箱：</font><font>C3</font><font>控制器铁箱</font><font>A</font></p><p><font><img src=\"https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/e760d4fd-bea0-4d0c-8a52-cfc957b72a89.png?file=图片.png\" style=\"max-width:100%;\" contenteditable=\"false\"/><br/></font></p><p><!--[if--><span>a.<span>&nbsp;</span></span><!--[endif]--><font>控制器：</font><font>C3</font><font>控制器</font></p><p><font><img src=\"https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/f624074b-3aee-4454-a6aa-58294cc8e6f7.png?file=图片.png\" style=\"max-width:100%;\" contenteditable=\"false\"/><br/></font></p><p><b></b></p><p><font>C3系列单门╱双门╱四门控制器，采用32位400MHZ高速CPU,配合32MBits RAM,256MBits Flash, &nbsp;嵌入式Linux操作系统。采用以太网加RS485工业总线双通讯技术，连接后台管理系统。通过控制器将读卡器和输入设备、输出设备连接，组成一个完整的控制系统，实现高安全级门禁控制、出入口智能化管理。</font></p><p><b><font>产品特点：</font></b><b></b></p><p><!--[if--><span>Ø<span>&nbsp;</span></span><!--[endif]--><font>支持</font><font>TCP/IP、RS485工业总线双重通讯技术，保证通讯的可靠性；</font></p><p><!--[if--><span>Ø<span>&nbsp;</span></span><!--[endif]--><font>控制器内置硬件看门狗，杜绝死机</font></p><p><!--[if--><span>Ø<span>&nbsp;</span></span><!--[endif]--><font>控制器电源输入具有过流、过压、反压保护</font></p><p><!--[if--><span>Ø<span>&nbsp;</span></span><!--[endif]--><font>提供给读卡器的电源具有过流保护</font></p><p><!--[if--><span>Ø<span>&nbsp;</span></span><!--[endif]--><font>所有输入</font><font>/输出端口/通信端口具有瞬间过压保护</font></p><p><!--[if--><span>Ø<span>&nbsp;</span></span><!--[endif]--><font>支持多种</font><font>Wiegand卡格式，支持密码键盘，兼容各种卡片</font></p><p><!--[if--><span>Ø<span>&nbsp;</span></span><!--[endif]--><font>具有反潜、互锁、联动、多卡开门及远程开关门等功能；</font></p><p><!--[if--><span>Ø<span>&nbsp;</span></span><!--[endif]--><font>强大的联动功能：支持硬件触发及事件触发，如门状态、卡状态、输入输出点和卡号的组合联动；</font></p><p><!--[if--><span>Ø<span>&nbsp;</span></span><!--[endif]--><font>多人多组刷卡：支持人员组内多卡开门设置</font><font>(A组任意两人组合开门)和跨组多卡开门设置(A组1人+B组 1人组合开门);</font></p><p><!--[if--><span>Ø<span>&nbsp;</span></span><!--[endif]--><font>首卡常开功能：在设置的时间段内，第一张刷卡后保持门常开；</font></p><p><!--[if--><span>Ø<span>&nbsp;</span></span><!--[endif]--><font>防尾随：支持</font><font>APB(防尾随)功能，支持双向与跨门点的区域APB;</font></p><p><!--[if--><span>Ø<span>&nbsp;</span></span><!--[endif]--><font>互锁功能：支持多门控制器任意组合的互锁功能，任何时候仅能打开一个门；</font></p><p><!--[if--><span>Ø<span>&nbsp;</span></span><!--[endif]--><font>C/S管理系统：提供ZKAccess3.5门禁管理软件,集人事、门禁、考勤于一体，专业门禁管理系统。</font></p><p><!--[if--><span>b.<span>&nbsp;</span></span><!--[endif]--><font>铁箱：</font><font>CASE01+</font><font>门禁开关电源套包</font><font>030</font></p><p><img src=\"https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/927174e9-e8c8-468a-9b24-f4959b391101.png?file=图片.png\" style=\"max-width:100%;\" contenteditable=\"false\"/><br/><img src=\"https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/efaebc8d-66ae-4b27-ac48-fb9c330caa85.png?file=图片.png\" style=\"max-width:100%;\" contenteditable=\"false\"/></p><p><font>适合于</font><font>C3/C4</font><font>单门、双门、四门控制器的安装，可以安装一台控制器及一台</font><font>3A</font><font>电源或</font><font>5A</font><font>电源（可以安装蓄电池）。</font><br/></p><p><!--[if--><span>3.2.2.<span>&nbsp;</span></span><!--[endif]--><font>发卡器：</font><font>CR20E&amp;M</font></p><p><img src=\"https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/678e8271-9fbd-4729-acf2-4f6aaf263302.png?file=图片.png\" style=\"max-width:100%;\" contenteditable=\"false\"/><br/></p><p><font>非接触式读卡器，一个</font><font>LED指示灯和一个蜂鸣器，读卡时，指示灯闪一下，蜂鸣器响一下。标准USB键盘接口，支持Windows95\\98\\2000\\XP\\win7。支持ID\\IC发卡。</font></p><p><b></b></p><p><!--[if--><span>3.2.3.<span>&nbsp;</span></span><!--[endif]--><font>读头：</font><font>KR102E&amp;W</font></p><p><img src=\"https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/9f973e36-183b-4a97-8e3c-d12b668f1514.png?file=图片.png\" style=\"max-width:100%;\" contenteditable=\"false\"/><br/></p><p><font>KR102</font><font>系列是炳基科技推出的非接触式智能卡序列号读头，本门禁读头具有集成度高、抗干扰能力强、体积小、性能稳定，性价比高的特点。适用于门禁、考勤、收费、防盗、巡更等各种射频识别应用</font><font>领域</font><font>。</font></p><p><b></b></p><p><!--[if--><span>3.2.4.<span>&nbsp;</span></span><!--[endif]--><font>电锁：</font><font>ZL-280</font></p><p><img src=\"https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/1b415a84-1ce7-479e-9a56-85e9ee1345df.png?file=图片.png\" style=\"max-width:100%;\" contenteditable=\"false\"/><br/></p><p><font>单门五线拉丝磁力锁，支持带锁状态信号反馈，支持通电上锁，断电开锁的安全方式，适用于木门、防盗门、金属门、玻璃门等。</font></p><p><!--[if--><span>3.2.5.<span>&nbsp;</span></span><!--[endif]--><font>出门开关：</font><font>K1-1DR</font></p><p><img src=\"https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/80f32b4b-3b29-4df1-90c1-effbb2da54a9.png?file=图片.png\" style=\"max-width:100%;\" contenteditable=\"false\"/> <img src=\"https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/152c0324-3e8c-4592-8767-7c64b8dee732.png?file=图片.png\" style=\"max-width:100%;\" contenteditable=\"false\"/></p><p><font>支持红外感应开门，通过门禁专用电源或门禁一体机控制开门信号，支持遥控器控制开门。</font></p><p><b></b></p><p><!--[if--><span>3.3.<span>&nbsp;</span></span><!--[endif]--><font>产品列表：</font></p><table width=\"100%\" cellspacing=\"0\" cellpadding=\"0\" border=\"0\"><tbody><tr><td><p><b><font>产品名称</font></b><b></b></p></td><td><p><b><font>产品型号</font></b><b></b></p></td><td><p><b><font size=\"3\">容量</font></b><b></b></p></td><td><font size=\"3\"><b><font>基础功能</font></b></font></td></tr><tr><td><p><font>控制器</font></p></td><td><p><font>C3-100</font></p></td><td rowspan=\"3\"><p><font>卡：</font><font>30000</font><font>张</font></p><p><font>记录：</font><font>10</font><font>万</font></p></td><td><font size=\"3\">单个门进出门刷卡或进门刷卡，出门按钮</font></td></tr><tr><td><p><font>双门控制器</font></p></td><td><p><font>C3-200</font></p></td><td><p><font>两个门进出门刷卡或进门刷卡，出门按钮</font></p></td></tr><tr><td><p><font>四门控制器</font></p></td><td><p><font>C3-300</font></p></td><td><p><font>四个门进门刷卡或出门按钮</font></p></td></tr><tr><td><p><font>铁箱</font></p></td><td><p><font>CASE01</font></p></td><td colspan=\"2\"><p><font>安装一台控制器及一台</font><font>3A</font><font>电源或</font><font>5A</font><font>电源（可以安装蓄电池）</font></p></td></tr><tr><td><p><font>电源</font></p></td><td><p><font>门禁开关电源</font></p></td><td colspan=\"2\"><p><font>适用于</font><font>CASE01</font></p></td></tr><tr><td><p><font>发卡器</font></p></td><td><p><font>CR20</font></p></td><td colspan=\"2\"><p><font>类型</font><font>:E</font><font>（</font><font>ID</font><font>）、</font><font>M</font><font>（</font><font>IC</font><font>）</font><br/><font>工作时间</font> 1<font>（</font><font>s</font><font>）</font><br/><font>感应距离</font> <font>≤&nbsp;5</font><font>（</font><font>cm</font><font>）</font></p></td></tr><tr><td><p><font>读头</font></p></td><td><p><font>KR102E&amp;M</font></p></td><td colspan=\"2\"><p><font>外形尺寸：</font><font>86*86*22.5mm</font></p><p><font>工作温度：</font><font>-20~60</font><font>℃</font></p><p><font>卡类型：</font><font>E</font><font>（</font><font>ID</font><font>）、</font><font>M</font><font>（</font><font>IC</font><font>）</font></p><p><font>通讯格式：</font><font>Wiegand34\\26=</font></p></td></tr><tr><td><p><font>出门开关</font></p></td><td><p><font>K1-1DR</font></p></td><td colspan=\"2\"><p><font>红外感应距离：</font><font>10-25cm</font><font>（可调节）</font></p><p><font>不锈钢面板开关</font><font>+</font><font>金属遥控手柄</font></p></td></tr></tbody></table><p><b></b></p><p><!--[if--><span>4.<span>&nbsp;</span></span><!--[endif]--><b>ZKAccess3.5</b><b><font>门禁软件介绍：</font></b><b></b></p><p><!--[if--><span>4.1.<span>&nbsp;</span></span><!--[endif]--><font>软件界面图：</font></p><p><img src=\"https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/37894e5b-23b4-495a-84de-ede0a2bbcb6a.png?file=图片.png\" style=\"max-width:100%;\" contenteditable=\"false\"/><br/></p><p><!--[if--><span>4.2.<span>&nbsp;</span></span><!--[endif]--><font>服务器硬件要求及运行环境：</font></p><table><tbody><tr><td><p><b><font>CPU</font></b><font>：</font><b></b></p></td><td><p><font>主频</font><font>2.0G以上</font><b></b></p></td></tr><tr><td><p><b><font>内存</font></b><font>：</font><b></b></p></td><td><p><font>1G及以上</font><b></b></p></td></tr><tr><td><p><b><font>硬盘</font></b><font>：</font><b></b></p></td><td><p><font>可用空间</font><font>10G及以上，推荐使用NTFS的硬盘分区作为系统安装目录（NTFS硬盘分区能提供更好的性能和更高的安全性）</font><b></b></p></td></tr><tr><td><p><b><font>系统运行环境</font></b><b></b></p></td><td><p>Windows XP/7/8/10<font>、</font>Windows Server 2003/2008/2012/2016/2019<b></b></p></td></tr><tr><td><p><b><font>数据库</font></b><b></b></p></td><td><p>Access<font>、</font>SQL Server 2005/2008/2012/2014/2016/2017/2019<b></b></p></td></tr></tbody></table><p><!--[if--><span>4.3.<span>&nbsp;</span></span><!--[endif]--><font>名词解释：</font></p><p><b><font>超级用户</font></b><font>：拥有系统全部操作权限的用户，可以在系统中分配新用户（如公司管理人员、登记</font> <font>员、门禁管理员等）并配置相应用户的角色。</font></p><p><b><font>角色</font></b><font>：在日常使用过程中，超级用户需要分配一些具有不同权限级别的新用户，为避免对每个用户一一进行设置，可以在角色管理中设置一类具有一定权限的角色，在新增用户时直接将合适的角色分配给用户。</font></p><p><b><font>门禁时间段</font></b><font>：用于门的时间设置，可以设置读头仅在指定门的有效时间段可用，其他时间段不可用，也可以用于设置门的常开时间段；时间段可以用于设置门禁权限，指定用户只能在指定时间段访问指定门（含门禁权限组和首卡常开设置）。</font></p><p><b><font>门磁延时</font></b><font>：门被打开后，延迟检查门磁的时间。如果门在非</font>\"<font>常开</font>\"<font>时段处于开状态，则开始计时，</font> <font>当达到门磁延时设置的时间，则发生报警，如果将门关闭，则报警停止。门磁延时需大于锁驱动时长。</font></p><p><b><font>闭门回锁</font></b><font>：设置闭门之后是否回锁。</font></p><p><b><font>锁驱动时长</font></b><font>：用户验证通过后</font><font>，</font><font>电锁打开到关闭的时长（门未打开的情况下）。</font></p><p><b><font>首卡常开</font></b><font>：在指定时间段内，当有首卡常开权限的人员第一次刷卡后，门将常开，有效时间段</font> <font>结束后门将自动恢复关闭。</font></p><p><b><font>多卡开门</font></b><font>：某些特定门禁场合需要启用该功能：要求同一个开门的组合中指定人数同时到场，</font> <font>并依次刷卡才能验证通过，通过验证前，不能插入其他卡（即使是该门其他组合的有效卡），否则要等待</font>10<font>秒后重新验证。</font></p><p><b><font>互锁</font></b><font>：可设置一个控制器的两扇（或多扇）门之间的互锁管制，当要开启一扇门时，其他对应的门必须都是关闭的，否则无法开门。</font></p><p><b><font>反潜</font></b><font>：特殊场合对安全性要求高，要求出入一一对应。例如，</font>1-2<font>门反潜：如果人员从</font>1<font>门入，</font> <font>必须从</font>2<font>门出；如果人员从</font>2<font>门入，必须从</font>1<font>门出。</font></p><p><b><font>联动设置</font></b><font>：在门禁系统中的某个输入点触发某个特定事件后，会在指定的输出点产生一个联动动作，用来</font><font>控制系统中的验证、</font><font>开门、报警、异常等事件，并在监控事件列表中显示。</font></p><p><!--[if--><span>4.4.<span>&nbsp;</span></span><!--[endif]--><font>系统功能模块介绍（本系统主要分为四大功能模块）：</font></p><p><!--[if--><span>4.4.1.<span>&nbsp;</span></span><!--[endif]--><font>人事：</font><font>主要包括两部分，一是部门管理设置，即设置公司的主要架构；二是人员管理设置，为系统录入人员，分配部门，然后进行人员维护管理。</font></p><p><!--[if--><span>4.4.2.<span>&nbsp;</span></span><!--[endif]--><font>设备管理：本系统需连接门禁一体机或门禁控制器，才能实现门禁系统功能。为实现通过系统管理设备，须先安装设备并将设备连至网络，其次设置系统参数。通过系统管理设备，输出各类报表，实现企业的数字化管理。</font> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p><p><!--[if--><span>4.4.3.<span>&nbsp;</span></span><!--[endif]--><font>门禁管理：本门禁系统是基于</font>C/S<font>架构的管理系统，能够实现普通门禁功能，通过计算机对网络门禁控制器进行管理，实现对人员进出的统一管理。门禁系统对已登记用户的开门权限进行设置，即在某个时间段内，在某些门上，允许某些人员可以验证开门。系统支持多种数据库，包括</font>Access<font>、</font>SQL Server<font>等；基于多业务融合设计，支持业务扩展，如考勤；支持多国语言。</font></p><p><!--[if--><span>4.5.<span>&nbsp;</span></span><!--[endif]--><font>系统功能模块介绍（本系统主要分为四大功能模块）：</font></p><p><!--[if--><span>4.5.1.<span>&nbsp;</span></span><!--[endif]--><font>人事：</font><font>主要包括两部分，一是部门管理设置，即设置公司的主要架构；二是人员管理设置，为系统录入人员，分配部门，然后进行人员维护管理。</font></p><p><!--[if--><span>4.5.2.<span>&nbsp;</span></span><!--[endif]--><font>设备管理：本系统需连接门禁一体机或门禁控制器，才能实现门禁系统功能。为实现通过系统管理设备，须先安装设备并将设备连至网络，其次设置系统参数。通过系统管理设备，输出各类报表，实现企业的数字化管理。</font> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p><p><!--[if--><span>4.5.3.<span>&nbsp;</span></span><!--[endif]--><font>门禁管理：本门禁系统是基于</font>C/S<font>架构的管理系统，能够实现普通门禁功能，通过计算机对网络门禁控制器进行管理，实现对人员进出的统一管理。门禁系统对已登记用户的开门权限进行设置，即在某个时间段内，在某些门上，允许某些人员可以验证开门。系统支持多种数据库，包括</font>Access<font>、</font>SQL Server<font>等；基于多业务融合设计，支持业务扩展，如考勤；支持多国语言。</font></p><p><!--[if--><span>a.<span>&nbsp;</span></span><!--[endif]--><font>门禁管理参数：</font></p><p><!--[if--><span>Ø<span>&nbsp;</span></span><!--[endif]-->256<font>个时间段</font></p><p><!--[if--><span>Ø<span>&nbsp;</span></span><!--[endif]-->256<font>个门禁权限组</font></p><p><!--[if--><span>Ø<span>&nbsp;</span></span><!--[endif]--><font>三种假日类型共</font>96<font>个假日</font></p><p><!--[if--><span>Ø<span>&nbsp;</span></span><!--[endif]--><font>反潜功能</font></p><p><!--[if--><span>Ø<span>&nbsp;</span></span><!--[endif]--><font>韦根格式</font></p><p><!--[if--><span>Ø<span>&nbsp;</span></span><!--[endif]--><font>互锁功能</font></p><p><!--[if--><span>Ø<span>&nbsp;</span></span><!--[endif]--><font>联动功能</font></p><p><!--[if--><span>Ø<span>&nbsp;</span></span><!--[endif]--><font>首卡常开功能</font></p><p><!--[if--><span>Ø<span>&nbsp;</span></span><!--[endif]--><font>多卡开门功能</font></p><p><!--[if--><span>Ø<span>&nbsp;</span></span><!--[endif]--><font>远程开门、关门</font></p><p><!--[if--><span>Ø<span>&nbsp;</span></span><!--[endif]--><font>实时监控</font></p><p><!--[if--><span>b.<span>&nbsp;</span></span><!--[endif]--><font>门禁管理功能：</font><font>门禁时间段、门禁节假日、门管理、门禁权限、实时监控等。</font></p><p><!--[if--><span>c.<span>&nbsp;</span></span><!--[endif]--><font>门禁报表：包括【当天门禁事件】、【最近三天门禁事件】、【最近一周门禁事件】、【上周门禁事件】、【门禁异常事件】报表。可以选择全部导出或查询后导出。用户可以根据报表统计相关设备数据，包括卡验证信息、门操作信息、正常刷卡信息等。</font></p><p><font>备注：只有用户使用紧急状态密码开门时产生的事件记录，才包含【仅密码】的验证方式。</font></p><p><!--[if--><span>4.5.4.<span>&nbsp;</span></span><!--[endif]--><font>系统设置：系统设置功能，主要是分配系统用户（如公司管理人员、登记员、门禁管理员等）并配置相应用户的角色；管理数据库，如备份、初始化等；设置系统参数，管理系统操作日志等。</font></p><p><b><font>注：登录系统：进入系统需要进行身份验证，输入用户名和密码以保证系统安全，我们会为初次使用本系统的用户提供一个超级用户（拥有全部操作权限），初次用户名：</font><font>admin、密码：admin。初次登录后可【系统】</font></b><b><font>-</font></b><b><font>【修改密码】修改密码。</font></b></p><p><b></b></p><p><span style=\"font-size: 14px; font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, &quot;Helvetica Neue&quot;, Arial, &quot;Noto Sans&quot;, sans-serif, &quot;Apple Color Emoji&quot;, &quot;Segoe UI Emoji&quot;, &quot;Segoe UI Symbol&quot;, &quot;Noto Color Emoji&quot;;\"><br/></span></p>", "isPriceHidden": 0, "productList": [{"createBy": "", "createTime": "2024-03-01 02:44:36", "updateBy": "", "updateTime": null, "remark": null, "params": {}, "isApp": null, "id": 851, "status": "0", "smallImg": "https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/d4d471c3-0afe-450c-8fe2-7fa1e0d8069e.png?file=屏幕快照 2022-12-19 上午9.46.09.png", "banner": null, "name": "门禁控制器系列", "attribute": "0", "category": null, "label": null, "spec": "C3-100/200/400 ", "introduction": "C3系列是熵基科技推出的单门/双门/四门控制器，采用32位400MHZ高速CPU，配合32MBits RAM，256MBits Flash,嵌入式Linux操作系统。", "showFor": null, "paramInfo": null, "newParam": null, "useTo": null, "details": null, "qualifications": null, "instructions": null, "showTime": null, "upTime": null, "sort": null, "isHot": null, "other": null, "guide": null, "commonProblem": null, "price": 0.0, "isNew": null, "commonProblemList": null, "instructionsList": null, "categoryName": null, "quantity": 1, "labelList": null, "labelName": null, "videoExplanation": null, "videoInstallation": null, "videoTroubleshooting": null, "showForCompany": null, "count": null, "sizeImg": null, "specList": null, "rmIds": null, "productId": 88, "listTag": null, "labelListForSelect": null, "size": null, "accessory": null, "accessoryList": null, "showForCompanyName": null, "unit": null, "unitName": null, "productZoneForSelect": null, "productGenMap": null, "productGenListHandle": null, "productGenListInstall": null, "productGenListFault": null, "productGenListLearning": null, "questionList": null, "allProductGenListHandle": null, "allProductGenListInstall": null, "allProductGenListFault": null, "allProductGenListLearning": null, "showForCompanyFilter": false, "thirdTypeSearch": false, "showForCompanyList": null, "isFilterShowFor": null, "companyId": null, "specSearchList": null, "paramInfoList": null, "productIdList": null, "distributionOrderPlatformId": 11}, {"createBy": "", "createTime": "2024-03-01 02:44:36", "updateBy": "", "updateTime": null, "remark": null, "params": {}, "isApp": null, "id": 852, "status": "0", "smallImg": "https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/8a752d91-0333-43e6-bb05-c1bc2378044b.png?file=066-1.png", "banner": null, "name": "三合一读卡器", "attribute": "0", "category": null, "label": null, "spec": "ZR601-T", "introduction": "ZR601-T读卡器外观采用ABS耐高温材质，嵌入式LED灯光带设计，密封灌胶防水工艺；可同时支持非接触式射频ID、IC、身份证物理卡号三合一读卡；具有集成度高、抗干扰能力强、体积小、性能稳定，性价比高的特点；广泛应用于在门禁、巡更、防盗等各种射频识别应用场景。", "showFor": null, "paramInfo": null, "newParam": null, "useTo": null, "details": null, "qualifications": null, "instructions": null, "showTime": null, "upTime": null, "sort": null, "isHot": null, "other": null, "guide": null, "commonProblem": null, "price": 0.0, "isNew": null, "commonProblemList": null, "instructionsList": null, "categoryName": null, "quantity": 1, "labelList": null, "labelName": null, "videoExplanation": null, "videoInstallation": null, "videoTroubleshooting": null, "showForCompany": null, "count": null, "sizeImg": null, "specList": null, "rmIds": null, "productId": 266, "listTag": null, "labelListForSelect": null, "size": null, "accessory": null, "accessoryList": null, "showForCompanyName": null, "unit": null, "unitName": null, "productZoneForSelect": null, "productGenMap": null, "productGenListHandle": null, "productGenListInstall": null, "productGenListFault": null, "productGenListLearning": null, "questionList": null, "allProductGenListHandle": null, "allProductGenListInstall": null, "allProductGenListFault": null, "allProductGenListLearning": null, "showForCompanyFilter": false, "thirdTypeSearch": false, "showForCompanyList": null, "isFilterShowFor": null, "companyId": null, "specSearchList": null, "paramInfoList": null, "productIdList": null, "distributionOrderPlatformId": 11}, {"createBy": "", "createTime": "2024-03-01 02:44:36", "updateBy": "", "updateTime": null, "remark": null, "params": {}, "isApp": null, "id": 853, "status": "0", "smallImg": "https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/4c4ec0cc-d523-4f2e-b380-bb625016bcc6.png?file=屏幕快照 2022-12-14 上午11.17.41.png", "banner": null, "name": "单门两线拉丝磁力锁", "attribute": "0", "category": null, "label": null, "spec": "ZL-280", "introduction": " ", "showFor": null, "paramInfo": null, "newParam": null, "useTo": null, "details": null, "qualifications": null, "instructions": null, "showTime": null, "upTime": null, "sort": null, "isHot": null, "other": null, "guide": null, "commonProblem": null, "price": 0.0, "isNew": null, "commonProblemList": null, "instructionsList": null, "categoryName": null, "quantity": 1, "labelList": null, "labelName": null, "videoExplanation": null, "videoInstallation": null, "videoTroubleshooting": null, "showForCompany": null, "count": null, "sizeImg": null, "specList": null, "rmIds": null, "productId": 401, "listTag": null, "labelListForSelect": null, "size": null, "accessory": null, "accessoryList": null, "showForCompanyName": null, "unit": null, "unitName": null, "productZoneForSelect": null, "productGenMap": null, "productGenListHandle": null, "productGenListInstall": null, "productGenListFault": null, "productGenListLearning": null, "questionList": null, "allProductGenListHandle": null, "allProductGenListInstall": null, "allProductGenListFault": null, "allProductGenListLearning": null, "showForCompanyFilter": false, "thirdTypeSearch": false, "showForCompanyList": null, "isFilterShowFor": null, "companyId": null, "specSearchList": null, "paramInfoList": null, "productIdList": null, "distributionOrderPlatformId": 11}, {"createBy": "", "createTime": "2024-03-01 02:44:36", "updateBy": "", "updateTime": null, "remark": null, "params": {}, "isApp": null, "id": 854, "status": "0", "smallImg": "https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/5eb22880-e4d4-4655-b5d3-6385f853df3a.png?file=产品图_85.png", "banner": null, "name": "门禁发卡器", "attribute": "0", "category": null, "label": null, "spec": "CR20E", "introduction": " ", "showFor": null, "paramInfo": null, "newParam": null, "useTo": null, "details": null, "qualifications": null, "instructions": null, "showTime": null, "upTime": null, "sort": null, "isHot": null, "other": null, "guide": null, "commonProblem": null, "price": 0.0, "isNew": null, "commonProblemList": null, "instructionsList": null, "categoryName": null, "quantity": 1, "labelList": null, "labelName": null, "videoExplanation": null, "videoInstallation": null, "videoTroubleshooting": null, "showForCompany": null, "count": null, "sizeImg": null, "specList": null, "rmIds": null, "productId": 461, "listTag": null, "labelListForSelect": null, "size": null, "accessory": null, "accessoryList": null, "showForCompanyName": null, "unit": null, "unitName": null, "productZoneForSelect": null, "productGenMap": null, "productGenListHandle": null, "productGenListInstall": null, "productGenListFault": null, "productGenListLearning": null, "questionList": null, "allProductGenListHandle": null, "allProductGenListInstall": null, "allProductGenListFault": null, "allProductGenListLearning": null, "showForCompanyFilter": false, "thirdTypeSearch": false, "showForCompanyList": null, "isFilterShowFor": null, "companyId": null, "specSearchList": null, "paramInfoList": null, "productIdList": null, "distributionOrderPlatformId": 11}, {"createBy": "", "createTime": "2024-03-01 02:44:36", "updateBy": "", "updateTime": null, "remark": null, "params": {}, "isApp": null, "id": 855, "status": "0", "smallImg": "https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/53b552dc-833d-47ce-97aa-6c255f3a9209.png?file=产品图_115.png", "banner": null, "name": "射频卡读卡器", "attribute": "0", "category": null, "label": null, "spec": "KR602E", "introduction": "基于ISO/IEC14443A国际标准协议开发的非接触式智能卡序列号读头", "showFor": null, "paramInfo": null, "newParam": null, "useTo": null, "details": null, "qualifications": null, "instructions": null, "showTime": null, "upTime": null, "sort": null, "isHot": null, "other": null, "guide": null, "commonProblem": null, "price": 0.0, "isNew": null, "commonProblemList": null, "instructionsList": null, "categoryName": null, "quantity": 1, "labelList": null, "labelName": null, "videoExplanation": null, "videoInstallation": null, "videoTroubleshooting": null, "showForCompany": null, "count": null, "sizeImg": null, "specList": null, "rmIds": null, "productId": 482, "listTag": null, "labelListForSelect": null, "size": null, "accessory": null, "accessoryList": null, "showForCompanyName": null, "unit": null, "unitName": null, "productZoneForSelect": null, "productGenMap": null, "productGenListHandle": null, "productGenListInstall": null, "productGenListFault": null, "productGenListLearning": null, "questionList": null, "allProductGenListHandle": null, "allProductGenListInstall": null, "allProductGenListFault": null, "allProductGenListLearning": null, "showForCompanyFilter": false, "thirdTypeSearch": false, "showForCompanyList": null, "isFilterShowFor": null, "companyId": null, "specSearchList": null, "paramInfoList": null, "productIdList": null, "distributionOrderPlatformId": 11}, {"createBy": "", "createTime": "2024-03-01 02:44:36", "updateBy": "", "updateTime": null, "remark": null, "params": {}, "isApp": null, "id": 856, "status": "0", "smallImg": "https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/d62d3b1f-2155-4c93-ae8f-878c8c7b82f3.png?file=产品图_373.png", "banner": null, "name": "出门开关", "attribute": "0", "category": null, "label": null, "spec": "K1-1DR", "introduction": " ", "showFor": null, "paramInfo": null, "newParam": null, "useTo": null, "details": null, "qualifications": null, "instructions": null, "showTime": null, "upTime": null, "sort": null, "isHot": null, "other": null, "guide": null, "commonProblem": null, "price": 0.0, "isNew": null, "commonProblemList": null, "instructionsList": null, "categoryName": null, "quantity": 1, "labelList": null, "labelName": null, "videoExplanation": null, "videoInstallation": null, "videoTroubleshooting": null, "showForCompany": null, "count": null, "sizeImg": null, "specList": null, "rmIds": null, "productId": 516, "listTag": null, "labelListForSelect": null, "size": null, "accessory": null, "accessoryList": null, "showForCompanyName": null, "unit": null, "unitName": null, "productZoneForSelect": null, "productGenMap": null, "productGenListHandle": null, "productGenListInstall": null, "productGenListFault": null, "productGenListLearning": null, "questionList": null, "allProductGenListHandle": null, "allProductGenListInstall": null, "allProductGenListFault": null, "allProductGenListLearning": null, "showForCompanyFilter": false, "thirdTypeSearch": false, "showForCompanyList": null, "isFilterShowFor": null, "companyId": null, "specSearchList": null, "paramInfoList": null, "productIdList": null, "distributionOrderPlatformId": 11}, {"createBy": "", "createTime": "2024-03-01 02:44:36", "updateBy": "", "updateTime": null, "remark": null, "params": {}, "isApp": null, "id": 857, "status": "0", "smallImg": "https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/1199eafe-c4b0-4872-a4c1-2f28c2f79a8e.png?file=方案背景图_04.png", "banner": null, "name": "门禁管理系统", "attribute": "1", "category": null, "label": null, "spec": "ZKAccess3.5", "introduction": "本系统实现门禁系统管理统一化、流程化，并帮助客户实现运营安全。", "showFor": null, "paramInfo": null, "newParam": null, "useTo": null, "details": null, "qualifications": null, "instructions": null, "showTime": null, "upTime": null, "sort": null, "isHot": null, "other": null, "guide": null, "commonProblem": null, "price": 0.0, "isNew": null, "commonProblemList": null, "instructionsList": null, "categoryName": null, "quantity": 1, "labelList": null, "labelName": null, "videoExplanation": null, "videoInstallation": null, "videoTroubleshooting": null, "showForCompany": null, "count": null, "sizeImg": null, "specList": null, "rmIds": null, "productId": 322, "listTag": null, "labelListForSelect": null, "size": null, "accessory": null, "accessoryList": null, "showForCompanyName": null, "unit": null, "unitName": null, "productZoneForSelect": null, "productGenMap": null, "productGenListHandle": null, "productGenListInstall": null, "productGenListFault": null, "productGenListLearning": null, "questionList": null, "allProductGenListHandle": null, "allProductGenListInstall": null, "allProductGenListFault": null, "allProductGenListLearning": null, "showForCompanyFilter": false, "thirdTypeSearch": false, "showForCompanyList": null, "isFilterShowFor": null, "companyId": null, "specSearchList": null, "paramInfoList": null, "productIdList": null, "distributionOrderPlatformId": 11}]}}}}, "database_status": {"table_stats": {"products": -1, "cases": -1, "programmes": -1, "information": -1, "distribution_orders": -1, "attachments": -1}}, "knowledge_content_analysis": {"error": "0"}, "attachment_analysis": {"error": "0"}, "recommendations": [{"category": "数据获取", "priority": "low", "issue": "programmes模块无数据", "solution": "确认programmes模块在云商平台中是否有数据"}, {"category": "附件处理", "priority": "medium", "issue": "附件下载率低(0.0%)", "solution": "检查附件下载服务和网络连接，优化下载逻辑"}]}