2025-06-19 13:25:03,739 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 13:27:52,729 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 13:27:52,729 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 13:28:10,544 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 13:28:10,544 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 13:28:10,544 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 13:28:10,748 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 13:28:10,748 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 13:28:10,748 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 13:28:10,748 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 13:28:10,786 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 13:28:10,786 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 13:28:10,786 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 13:28:10,786 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 13:28:10,786 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 13:28:14,860 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 13:28:14,860 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 13:28:14,860 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 13:28:14,860 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 13:28:14,860 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 13:28:14,860 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 13:28:22,615 - services.sync_service - ERROR - get_sync_status:515 - 获取同步状态失败: relation "sync_status" does not exist
LINE 1: SELECT * FROM sync_status
                      ^

2025-06-19 13:28:22,615 - services.sync_service - ERROR - get_sync_status:515 - 获取同步状态失败: relation "sync_status" does not exist
LINE 1: SELECT * FROM sync_status
                      ^

2025-06-19 13:28:22,615 - services.sync_service - ERROR - get_sync_status:515 - 获取同步状态失败: relation "sync_status" does not exist
LINE 1: SELECT * FROM sync_status
                      ^

2025-06-19 13:28:22,615 - services.sync_service - ERROR - get_sync_status:515 - 获取同步状态失败: relation "sync_status" does not exist
LINE 1: SELECT * FROM sync_status
                      ^

2025-06-19 13:28:22,615 - services.sync_service - ERROR - get_sync_status:515 - 获取同步状态失败: relation "sync_status" does not exist
LINE 1: SELECT * FROM sync_status
                      ^

2025-06-19 13:28:22,615 - services.sync_service - ERROR - get_sync_status:515 - 获取同步状态失败: relation "sync_status" does not exist
LINE 1: SELECT * FROM sync_status
                      ^

2025-06-19 13:28:26,055 - services.db_init_service - INFO - _create_products_table:120 - 产品表创建成功
2025-06-19 13:28:26,055 - services.db_init_service - INFO - _create_products_table:120 - 产品表创建成功
2025-06-19 13:28:26,055 - services.db_init_service - INFO - _create_products_table:120 - 产品表创建成功
2025-06-19 13:28:26,055 - services.db_init_service - INFO - _create_products_table:120 - 产品表创建成功
2025-06-19 13:28:26,055 - services.db_init_service - INFO - _create_products_table:120 - 产品表创建成功
2025-06-19 13:28:26,055 - services.db_init_service - INFO - _create_products_table:120 - 产品表创建成功
2025-06-19 13:28:26,172 - services.db_init_service - INFO - _create_categories_table:147 - 分类表创建成功
2025-06-19 13:28:26,172 - services.db_init_service - INFO - _create_categories_table:147 - 分类表创建成功
2025-06-19 13:28:26,172 - services.db_init_service - INFO - _create_categories_table:147 - 分类表创建成功
2025-06-19 13:28:26,172 - services.db_init_service - INFO - _create_categories_table:147 - 分类表创建成功
2025-06-19 13:28:26,172 - services.db_init_service - INFO - _create_categories_table:147 - 分类表创建成功
2025-06-19 13:28:26,172 - services.db_init_service - INFO - _create_categories_table:147 - 分类表创建成功
2025-06-19 13:28:26,476 - services.db_init_service - INFO - _create_labels_table:173 - 标签表创建成功
2025-06-19 13:28:26,476 - services.db_init_service - INFO - _create_labels_table:173 - 标签表创建成功
2025-06-19 13:28:26,476 - services.db_init_service - INFO - _create_labels_table:173 - 标签表创建成功
2025-06-19 13:28:26,476 - services.db_init_service - INFO - _create_labels_table:173 - 标签表创建成功
2025-06-19 13:28:26,476 - services.db_init_service - INFO - _create_labels_table:173 - 标签表创建成功
2025-06-19 13:28:26,476 - services.db_init_service - INFO - _create_labels_table:173 - 标签表创建成功
2025-06-19 13:28:26,545 - services.db_init_service - INFO - _create_brands_table:199 - 品牌表创建成功
2025-06-19 13:28:26,545 - services.db_init_service - INFO - _create_brands_table:199 - 品牌表创建成功
2025-06-19 13:28:26,545 - services.db_init_service - INFO - _create_brands_table:199 - 品牌表创建成功
2025-06-19 13:28:26,545 - services.db_init_service - INFO - _create_brands_table:199 - 品牌表创建成功
2025-06-19 13:28:26,545 - services.db_init_service - INFO - _create_brands_table:199 - 品牌表创建成功
2025-06-19 13:28:26,545 - services.db_init_service - INFO - _create_brands_table:199 - 品牌表创建成功
2025-06-19 13:28:26,614 - services.db_init_service - INFO - _create_cases_table:249 - 案例表创建成功
2025-06-19 13:28:26,614 - services.db_init_service - INFO - _create_cases_table:249 - 案例表创建成功
2025-06-19 13:28:26,614 - services.db_init_service - INFO - _create_cases_table:249 - 案例表创建成功
2025-06-19 13:28:26,614 - services.db_init_service - INFO - _create_cases_table:249 - 案例表创建成功
2025-06-19 13:28:26,614 - services.db_init_service - INFO - _create_cases_table:249 - 案例表创建成功
2025-06-19 13:28:26,614 - services.db_init_service - INFO - _create_cases_table:249 - 案例表创建成功
2025-06-19 13:28:26,679 - services.db_init_service - INFO - _create_product_cases_table:269 - 产品案例关联表创建成功
2025-06-19 13:28:26,679 - services.db_init_service - INFO - _create_product_cases_table:269 - 产品案例关联表创建成功
2025-06-19 13:28:26,679 - services.db_init_service - INFO - _create_product_cases_table:269 - 产品案例关联表创建成功
2025-06-19 13:28:26,679 - services.db_init_service - INFO - _create_product_cases_table:269 - 产品案例关联表创建成功
2025-06-19 13:28:26,679 - services.db_init_service - INFO - _create_product_cases_table:269 - 产品案例关联表创建成功
2025-06-19 13:28:26,679 - services.db_init_service - INFO - _create_product_cases_table:269 - 产品案例关联表创建成功
2025-06-19 13:28:26,744 - services.db_init_service - INFO - _create_programmes_table:316 - 方案表创建成功
2025-06-19 13:28:26,744 - services.db_init_service - INFO - _create_programmes_table:316 - 方案表创建成功
2025-06-19 13:28:26,744 - services.db_init_service - INFO - _create_programmes_table:316 - 方案表创建成功
2025-06-19 13:28:26,744 - services.db_init_service - INFO - _create_programmes_table:316 - 方案表创建成功
2025-06-19 13:28:26,744 - services.db_init_service - INFO - _create_programmes_table:316 - 方案表创建成功
2025-06-19 13:28:26,744 - services.db_init_service - INFO - _create_programmes_table:316 - 方案表创建成功
2025-06-19 13:28:26,808 - services.db_init_service - INFO - _create_programme_products_table:338 - 方案产品关联表创建成功
2025-06-19 13:28:26,808 - services.db_init_service - INFO - _create_programme_products_table:338 - 方案产品关联表创建成功
2025-06-19 13:28:26,808 - services.db_init_service - INFO - _create_programme_products_table:338 - 方案产品关联表创建成功
2025-06-19 13:28:26,808 - services.db_init_service - INFO - _create_programme_products_table:338 - 方案产品关联表创建成功
2025-06-19 13:28:26,808 - services.db_init_service - INFO - _create_programme_products_table:338 - 方案产品关联表创建成功
2025-06-19 13:28:26,808 - services.db_init_service - INFO - _create_programme_products_table:338 - 方案产品关联表创建成功
2025-06-19 13:28:26,892 - services.db_init_service - INFO - _create_information_table:378 - 资讯表创建成功
2025-06-19 13:28:26,892 - services.db_init_service - INFO - _create_information_table:378 - 资讯表创建成功
2025-06-19 13:28:26,892 - services.db_init_service - INFO - _create_information_table:378 - 资讯表创建成功
2025-06-19 13:28:26,892 - services.db_init_service - INFO - _create_information_table:378 - 资讯表创建成功
2025-06-19 13:28:26,892 - services.db_init_service - INFO - _create_information_table:378 - 资讯表创建成功
2025-06-19 13:28:26,892 - services.db_init_service - INFO - _create_information_table:378 - 资讯表创建成功
2025-06-19 13:28:26,962 - services.db_init_service - INFO - _create_distribution_orders_table:421 - 配单表创建成功
2025-06-19 13:28:26,962 - services.db_init_service - INFO - _create_distribution_orders_table:421 - 配单表创建成功
2025-06-19 13:28:26,962 - services.db_init_service - INFO - _create_distribution_orders_table:421 - 配单表创建成功
2025-06-19 13:28:26,962 - services.db_init_service - INFO - _create_distribution_orders_table:421 - 配单表创建成功
2025-06-19 13:28:26,962 - services.db_init_service - INFO - _create_distribution_orders_table:421 - 配单表创建成功
2025-06-19 13:28:26,962 - services.db_init_service - INFO - _create_distribution_orders_table:421 - 配单表创建成功
2025-06-19 13:28:27,310 - services.db_init_service - INFO - _create_sync_status_table:470 - 同步状态表创建成功
2025-06-19 13:28:27,310 - services.db_init_service - INFO - _create_sync_status_table:470 - 同步状态表创建成功
2025-06-19 13:28:27,310 - services.db_init_service - INFO - _create_sync_status_table:470 - 同步状态表创建成功
2025-06-19 13:28:27,310 - services.db_init_service - INFO - _create_sync_status_table:470 - 同步状态表创建成功
2025-06-19 13:28:27,310 - services.db_init_service - INFO - _create_sync_status_table:470 - 同步状态表创建成功
2025-06-19 13:28:27,310 - services.db_init_service - INFO - _create_sync_status_table:470 - 同步状态表创建成功
2025-06-19 13:28:27,313 - services.db_init_service - INFO - create_tables:39 - 所有数据表创建成功
2025-06-19 13:28:27,313 - services.db_init_service - INFO - create_tables:39 - 所有数据表创建成功
2025-06-19 13:28:27,313 - services.db_init_service - INFO - create_tables:39 - 所有数据表创建成功
2025-06-19 13:28:27,313 - services.db_init_service - INFO - create_tables:39 - 所有数据表创建成功
2025-06-19 13:28:27,313 - services.db_init_service - INFO - create_tables:39 - 所有数据表创建成功
2025-06-19 13:28:27,313 - services.db_init_service - INFO - create_tables:39 - 所有数据表创建成功
2025-06-19 13:28:35,366 - services.sync_service - INFO - sync_products:58 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 13:28:35,366 - services.sync_service - INFO - sync_products:58 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 13:28:35,366 - services.sync_service - INFO - sync_products:58 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 13:28:35,366 - services.sync_service - INFO - sync_products:58 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 13:28:35,366 - services.sync_service - INFO - sync_products:58 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 13:28:35,366 - services.sync_service - INFO - sync_products:58 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 13:28:36,357 - services.sync_service - INFO - sync_cases:185 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 13:28:36,357 - services.sync_service - INFO - sync_cases:185 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 13:28:36,357 - services.sync_service - INFO - sync_cases:185 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 13:28:36,357 - services.sync_service - INFO - sync_cases:185 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 13:28:36,357 - services.sync_service - INFO - sync_cases:185 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 13:28:36,357 - services.sync_service - INFO - sync_cases:185 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 13:28:41,212 - services.sync_service - INFO - sync_programmes:264 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 13:28:41,212 - services.sync_service - INFO - sync_programmes:264 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 13:28:41,212 - services.sync_service - INFO - sync_programmes:264 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 13:28:41,212 - services.sync_service - INFO - sync_programmes:264 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 13:28:41,212 - services.sync_service - INFO - sync_programmes:264 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 13:28:41,212 - services.sync_service - INFO - sync_programmes:264 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 13:28:41,703 - services.sync_service - INFO - sync_information:345 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 13:28:41,703 - services.sync_service - INFO - sync_information:345 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 13:28:41,703 - services.sync_service - INFO - sync_information:345 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 13:28:41,703 - services.sync_service - INFO - sync_information:345 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 13:28:41,703 - services.sync_service - INFO - sync_information:345 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 13:28:41,703 - services.sync_service - INFO - sync_information:345 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 13:28:43,654 - services.sync_service - INFO - sync_distribution_orders:426 - 获取配单数据，页码: 1, 每页数量: 100
2025-06-19 13:28:43,654 - services.sync_service - INFO - sync_distribution_orders:426 - 获取配单数据，页码: 1, 每页数量: 100
2025-06-19 13:28:43,654 - services.sync_service - INFO - sync_distribution_orders:426 - 获取配单数据，页码: 1, 每页数量: 100
2025-06-19 13:28:43,654 - services.sync_service - INFO - sync_distribution_orders:426 - 获取配单数据，页码: 1, 每页数量: 100
2025-06-19 13:28:43,654 - services.sync_service - INFO - sync_distribution_orders:426 - 获取配单数据，页码: 1, 每页数量: 100
2025-06-19 13:28:43,654 - services.sync_service - INFO - sync_distribution_orders:426 - 获取配单数据，页码: 1, 每页数量: 100
2025-06-19 13:28:44,040 - utils.api_client - ERROR - _handle_response:54 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 13:28:44,040 - utils.api_client - ERROR - _handle_response:54 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 13:28:44,040 - utils.api_client - ERROR - _handle_response:54 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 13:28:44,040 - utils.api_client - ERROR - _handle_response:54 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 13:28:44,040 - utils.api_client - ERROR - _handle_response:54 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 13:28:44,040 - utils.api_client - ERROR - _handle_response:54 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 18:41:26,722 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 18:41:51,622 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 18:41:51,622 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 18:41:53,841 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 18:41:53,841 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 18:41:53,841 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 18:41:55,216 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 18:41:55,216 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 18:41:55,216 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 18:41:55,216 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 18:41:56,307 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 18:41:56,307 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 18:41:56,307 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 18:41:56,307 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 18:41:56,307 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 18:41:58,828 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 18:41:58,828 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 18:41:58,828 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 18:41:58,828 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 18:41:58,828 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 18:41:58,828 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 18:41:59,366 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 18:41:59,366 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 18:41:59,366 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 18:41:59,366 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 18:41:59,366 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 18:41:59,366 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 18:41:59,366 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 21:55:54,421 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 21:56:33,542 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 21:56:33,542 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 21:56:40,726 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 21:56:40,726 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 21:56:40,726 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 21:56:40,945 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 21:56:40,945 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 21:56:40,945 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 21:56:40,945 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 21:56:40,996 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 21:56:40,996 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 21:56:40,996 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 21:56:40,996 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 21:56:40,996 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 21:56:45,171 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 21:56:45,171 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 21:56:45,171 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 21:56:45,171 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 21:56:45,171 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 21:56:45,171 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 21:56:45,607 - services.sync_service - ERROR - get_sync_status:545 - 获取同步状态失败: 错误:  关系 "sync_status" 不存在
LINE 1: SELECT * FROM sync_status
                      ^

2025-06-19 21:56:45,607 - services.sync_service - ERROR - get_sync_status:545 - 获取同步状态失败: 错误:  关系 "sync_status" 不存在
LINE 1: SELECT * FROM sync_status
                      ^

2025-06-19 21:56:45,607 - services.sync_service - ERROR - get_sync_status:545 - 获取同步状态失败: 错误:  关系 "sync_status" 不存在
LINE 1: SELECT * FROM sync_status
                      ^

2025-06-19 21:56:45,607 - services.sync_service - ERROR - get_sync_status:545 - 获取同步状态失败: 错误:  关系 "sync_status" 不存在
LINE 1: SELECT * FROM sync_status
                      ^

2025-06-19 21:56:45,607 - services.sync_service - ERROR - get_sync_status:545 - 获取同步状态失败: 错误:  关系 "sync_status" 不存在
LINE 1: SELECT * FROM sync_status
                      ^

2025-06-19 21:56:45,607 - services.sync_service - ERROR - get_sync_status:545 - 获取同步状态失败: 错误:  关系 "sync_status" 不存在
LINE 1: SELECT * FROM sync_status
                      ^

2025-06-19 21:56:49,313 - services.db_init_service - INFO - _create_products_table:120 - 产品表创建成功
2025-06-19 21:56:49,313 - services.db_init_service - INFO - _create_products_table:120 - 产品表创建成功
2025-06-19 21:56:49,313 - services.db_init_service - INFO - _create_products_table:120 - 产品表创建成功
2025-06-19 21:56:49,313 - services.db_init_service - INFO - _create_products_table:120 - 产品表创建成功
2025-06-19 21:56:49,313 - services.db_init_service - INFO - _create_products_table:120 - 产品表创建成功
2025-06-19 21:56:49,313 - services.db_init_service - INFO - _create_products_table:120 - 产品表创建成功
2025-06-19 21:56:49,381 - services.db_init_service - INFO - _create_categories_table:147 - 分类表创建成功
2025-06-19 21:56:49,381 - services.db_init_service - INFO - _create_categories_table:147 - 分类表创建成功
2025-06-19 21:56:49,381 - services.db_init_service - INFO - _create_categories_table:147 - 分类表创建成功
2025-06-19 21:56:49,381 - services.db_init_service - INFO - _create_categories_table:147 - 分类表创建成功
2025-06-19 21:56:49,381 - services.db_init_service - INFO - _create_categories_table:147 - 分类表创建成功
2025-06-19 21:56:49,381 - services.db_init_service - INFO - _create_categories_table:147 - 分类表创建成功
2025-06-19 21:56:49,403 - services.db_init_service - INFO - _create_labels_table:173 - 标签表创建成功
2025-06-19 21:56:49,403 - services.db_init_service - INFO - _create_labels_table:173 - 标签表创建成功
2025-06-19 21:56:49,403 - services.db_init_service - INFO - _create_labels_table:173 - 标签表创建成功
2025-06-19 21:56:49,403 - services.db_init_service - INFO - _create_labels_table:173 - 标签表创建成功
2025-06-19 21:56:49,403 - services.db_init_service - INFO - _create_labels_table:173 - 标签表创建成功
2025-06-19 21:56:49,403 - services.db_init_service - INFO - _create_labels_table:173 - 标签表创建成功
2025-06-19 21:56:49,438 - services.db_init_service - INFO - _create_brands_table:199 - 品牌表创建成功
2025-06-19 21:56:49,438 - services.db_init_service - INFO - _create_brands_table:199 - 品牌表创建成功
2025-06-19 21:56:49,438 - services.db_init_service - INFO - _create_brands_table:199 - 品牌表创建成功
2025-06-19 21:56:49,438 - services.db_init_service - INFO - _create_brands_table:199 - 品牌表创建成功
2025-06-19 21:56:49,438 - services.db_init_service - INFO - _create_brands_table:199 - 品牌表创建成功
2025-06-19 21:56:49,438 - services.db_init_service - INFO - _create_brands_table:199 - 品牌表创建成功
2025-06-19 21:56:49,472 - services.db_init_service - INFO - _create_cases_table:249 - 案例表创建成功
2025-06-19 21:56:49,472 - services.db_init_service - INFO - _create_cases_table:249 - 案例表创建成功
2025-06-19 21:56:49,472 - services.db_init_service - INFO - _create_cases_table:249 - 案例表创建成功
2025-06-19 21:56:49,472 - services.db_init_service - INFO - _create_cases_table:249 - 案例表创建成功
2025-06-19 21:56:49,472 - services.db_init_service - INFO - _create_cases_table:249 - 案例表创建成功
2025-06-19 21:56:49,472 - services.db_init_service - INFO - _create_cases_table:249 - 案例表创建成功
2025-06-19 21:56:49,495 - services.db_init_service - INFO - _create_product_cases_table:269 - 产品案例关联表创建成功
2025-06-19 21:56:49,495 - services.db_init_service - INFO - _create_product_cases_table:269 - 产品案例关联表创建成功
2025-06-19 21:56:49,495 - services.db_init_service - INFO - _create_product_cases_table:269 - 产品案例关联表创建成功
2025-06-19 21:56:49,495 - services.db_init_service - INFO - _create_product_cases_table:269 - 产品案例关联表创建成功
2025-06-19 21:56:49,495 - services.db_init_service - INFO - _create_product_cases_table:269 - 产品案例关联表创建成功
2025-06-19 21:56:49,495 - services.db_init_service - INFO - _create_product_cases_table:269 - 产品案例关联表创建成功
2025-06-19 21:56:49,529 - services.db_init_service - INFO - _create_programmes_table:316 - 方案表创建成功
2025-06-19 21:56:49,529 - services.db_init_service - INFO - _create_programmes_table:316 - 方案表创建成功
2025-06-19 21:56:49,529 - services.db_init_service - INFO - _create_programmes_table:316 - 方案表创建成功
2025-06-19 21:56:49,529 - services.db_init_service - INFO - _create_programmes_table:316 - 方案表创建成功
2025-06-19 21:56:49,529 - services.db_init_service - INFO - _create_programmes_table:316 - 方案表创建成功
2025-06-19 21:56:49,529 - services.db_init_service - INFO - _create_programmes_table:316 - 方案表创建成功
2025-06-19 21:56:49,597 - services.db_init_service - INFO - _create_programme_products_table:338 - 方案产品关联表创建成功
2025-06-19 21:56:49,597 - services.db_init_service - INFO - _create_programme_products_table:338 - 方案产品关联表创建成功
2025-06-19 21:56:49,597 - services.db_init_service - INFO - _create_programme_products_table:338 - 方案产品关联表创建成功
2025-06-19 21:56:49,597 - services.db_init_service - INFO - _create_programme_products_table:338 - 方案产品关联表创建成功
2025-06-19 21:56:49,597 - services.db_init_service - INFO - _create_programme_products_table:338 - 方案产品关联表创建成功
2025-06-19 21:56:49,597 - services.db_init_service - INFO - _create_programme_products_table:338 - 方案产品关联表创建成功
2025-06-19 21:56:49,631 - services.db_init_service - INFO - _create_information_table:378 - 资讯表创建成功
2025-06-19 21:56:49,631 - services.db_init_service - INFO - _create_information_table:378 - 资讯表创建成功
2025-06-19 21:56:49,631 - services.db_init_service - INFO - _create_information_table:378 - 资讯表创建成功
2025-06-19 21:56:49,631 - services.db_init_service - INFO - _create_information_table:378 - 资讯表创建成功
2025-06-19 21:56:49,631 - services.db_init_service - INFO - _create_information_table:378 - 资讯表创建成功
2025-06-19 21:56:49,631 - services.db_init_service - INFO - _create_information_table:378 - 资讯表创建成功
2025-06-19 21:56:49,666 - services.db_init_service - INFO - _create_distribution_orders_table:421 - 配单表创建成功
2025-06-19 21:56:49,666 - services.db_init_service - INFO - _create_distribution_orders_table:421 - 配单表创建成功
2025-06-19 21:56:49,666 - services.db_init_service - INFO - _create_distribution_orders_table:421 - 配单表创建成功
2025-06-19 21:56:49,666 - services.db_init_service - INFO - _create_distribution_orders_table:421 - 配单表创建成功
2025-06-19 21:56:49,666 - services.db_init_service - INFO - _create_distribution_orders_table:421 - 配单表创建成功
2025-06-19 21:56:49,666 - services.db_init_service - INFO - _create_distribution_orders_table:421 - 配单表创建成功
2025-06-19 21:56:49,708 - services.db_init_service - INFO - _create_sync_status_table:470 - 同步状态表创建成功
2025-06-19 21:56:49,708 - services.db_init_service - INFO - _create_sync_status_table:470 - 同步状态表创建成功
2025-06-19 21:56:49,708 - services.db_init_service - INFO - _create_sync_status_table:470 - 同步状态表创建成功
2025-06-19 21:56:49,708 - services.db_init_service - INFO - _create_sync_status_table:470 - 同步状态表创建成功
2025-06-19 21:56:49,708 - services.db_init_service - INFO - _create_sync_status_table:470 - 同步状态表创建成功
2025-06-19 21:56:49,708 - services.db_init_service - INFO - _create_sync_status_table:470 - 同步状态表创建成功
2025-06-19 21:56:49,710 - services.db_init_service - INFO - create_tables:39 - 所有数据表创建成功
2025-06-19 21:56:49,710 - services.db_init_service - INFO - create_tables:39 - 所有数据表创建成功
2025-06-19 21:56:49,710 - services.db_init_service - INFO - create_tables:39 - 所有数据表创建成功
2025-06-19 21:56:49,710 - services.db_init_service - INFO - create_tables:39 - 所有数据表创建成功
2025-06-19 21:56:49,710 - services.db_init_service - INFO - create_tables:39 - 所有数据表创建成功
2025-06-19 21:56:49,710 - services.db_init_service - INFO - create_tables:39 - 所有数据表创建成功
2025-06-19 21:56:51,430 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 21:56:51,430 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 21:56:51,430 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 21:56:51,430 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 21:56:51,430 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 21:56:51,430 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 21:56:54,922 - services.sync_service - INFO - sync_cases:186 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 21:56:54,922 - services.sync_service - INFO - sync_cases:186 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 21:56:54,922 - services.sync_service - INFO - sync_cases:186 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 21:56:54,922 - services.sync_service - INFO - sync_cases:186 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 21:56:54,922 - services.sync_service - INFO - sync_cases:186 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 21:56:54,922 - services.sync_service - INFO - sync_cases:186 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 21:56:58,338 - services.sync_service - INFO - sync_programmes:265 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 21:56:58,338 - services.sync_service - INFO - sync_programmes:265 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 21:56:58,338 - services.sync_service - INFO - sync_programmes:265 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 21:56:58,338 - services.sync_service - INFO - sync_programmes:265 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 21:56:58,338 - services.sync_service - INFO - sync_programmes:265 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 21:56:58,338 - services.sync_service - INFO - sync_programmes:265 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 21:56:59,042 - services.sync_service - INFO - sync_information:346 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 21:56:59,042 - services.sync_service - INFO - sync_information:346 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 21:56:59,042 - services.sync_service - INFO - sync_information:346 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 21:56:59,042 - services.sync_service - INFO - sync_information:346 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 21:56:59,042 - services.sync_service - INFO - sync_information:346 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 21:56:59,042 - services.sync_service - INFO - sync_information:346 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 21:57:02,269 - services.sync_service - INFO - sync_distribution_orders:429 - 获取配单数据，页码: 1, 每页数量: 100
2025-06-19 21:57:02,269 - services.sync_service - INFO - sync_distribution_orders:429 - 获取配单数据，页码: 1, 每页数量: 100
2025-06-19 21:57:02,269 - services.sync_service - INFO - sync_distribution_orders:429 - 获取配单数据，页码: 1, 每页数量: 100
2025-06-19 21:57:02,269 - services.sync_service - INFO - sync_distribution_orders:429 - 获取配单数据，页码: 1, 每页数量: 100
2025-06-19 21:57:02,269 - services.sync_service - INFO - sync_distribution_orders:429 - 获取配单数据，页码: 1, 每页数量: 100
2025-06-19 21:57:02,269 - services.sync_service - INFO - sync_distribution_orders:429 - 获取配单数据，页码: 1, 每页数量: 100
2025-06-19 21:57:02,917 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 21:57:02,917 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 21:57:02,917 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 21:57:02,917 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 21:57:02,917 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 21:57:02,917 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 21:57:02,920 - utils.api_client - WARNING - get:93 - 认证失败，尝试第1次重试...
2025-06-19 21:57:02,920 - utils.api_client - WARNING - get:93 - 认证失败，尝试第1次重试...
2025-06-19 21:57:02,920 - utils.api_client - WARNING - get:93 - 认证失败，尝试第1次重试...
2025-06-19 21:57:02,920 - utils.api_client - WARNING - get:93 - 认证失败，尝试第1次重试...
2025-06-19 21:57:02,920 - utils.api_client - WARNING - get:93 - 认证失败，尝试第1次重试...
2025-06-19 21:57:02,920 - utils.api_client - WARNING - get:93 - 认证失败，尝试第1次重试...
2025-06-19 21:57:03,335 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 21:57:03,335 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 21:57:03,335 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 21:57:03,335 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 21:57:03,335 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 21:57:03,335 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 21:57:03,338 - utils.api_client - WARNING - get:93 - 认证失败，尝试第2次重试...
2025-06-19 21:57:03,338 - utils.api_client - WARNING - get:93 - 认证失败，尝试第2次重试...
2025-06-19 21:57:03,338 - utils.api_client - WARNING - get:93 - 认证失败，尝试第2次重试...
2025-06-19 21:57:03,338 - utils.api_client - WARNING - get:93 - 认证失败，尝试第2次重试...
2025-06-19 21:57:03,338 - utils.api_client - WARNING - get:93 - 认证失败，尝试第2次重试...
2025-06-19 21:57:03,338 - utils.api_client - WARNING - get:93 - 认证失败，尝试第2次重试...
2025-06-19 21:57:03,979 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 21:57:03,979 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 21:57:03,979 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 21:57:03,979 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 21:57:03,979 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 21:57:03,979 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 21:57:40,820 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 21:57:40,820 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 21:57:40,820 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 21:57:40,820 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 21:57:40,820 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 21:57:40,820 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 22:03:44,458 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:04:01,221 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:04:01,221 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:04:08,749 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:04:08,749 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:04:08,749 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:04:08,980 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:04:08,980 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:04:08,980 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:04:08,980 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:04:09,027 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:04:09,027 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:04:09,027 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:04:09,027 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:04:09,027 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:04:14,120 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:04:14,120 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:04:14,120 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:04:14,120 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:04:14,120 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:04:14,120 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:04:17,637 - services.db_init_service - INFO - _create_products_table:75 - 产品表已存在，跳过创建
2025-06-19 22:04:17,637 - services.db_init_service - INFO - _create_products_table:75 - 产品表已存在，跳过创建
2025-06-19 22:04:17,637 - services.db_init_service - INFO - _create_products_table:75 - 产品表已存在，跳过创建
2025-06-19 22:04:17,637 - services.db_init_service - INFO - _create_products_table:75 - 产品表已存在，跳过创建
2025-06-19 22:04:17,637 - services.db_init_service - INFO - _create_products_table:75 - 产品表已存在，跳过创建
2025-06-19 22:04:17,637 - services.db_init_service - INFO - _create_products_table:75 - 产品表已存在，跳过创建
2025-06-19 22:04:17,640 - services.db_init_service - INFO - _create_categories_table:125 - 分类表已存在，跳过创建
2025-06-19 22:04:17,640 - services.db_init_service - INFO - _create_categories_table:125 - 分类表已存在，跳过创建
2025-06-19 22:04:17,640 - services.db_init_service - INFO - _create_categories_table:125 - 分类表已存在，跳过创建
2025-06-19 22:04:17,640 - services.db_init_service - INFO - _create_categories_table:125 - 分类表已存在，跳过创建
2025-06-19 22:04:17,640 - services.db_init_service - INFO - _create_categories_table:125 - 分类表已存在，跳过创建
2025-06-19 22:04:17,640 - services.db_init_service - INFO - _create_categories_table:125 - 分类表已存在，跳过创建
2025-06-19 22:04:17,643 - services.db_init_service - INFO - _create_labels_table:152 - 标签表已存在，跳过创建
2025-06-19 22:04:17,643 - services.db_init_service - INFO - _create_labels_table:152 - 标签表已存在，跳过创建
2025-06-19 22:04:17,643 - services.db_init_service - INFO - _create_labels_table:152 - 标签表已存在，跳过创建
2025-06-19 22:04:17,643 - services.db_init_service - INFO - _create_labels_table:152 - 标签表已存在，跳过创建
2025-06-19 22:04:17,643 - services.db_init_service - INFO - _create_labels_table:152 - 标签表已存在，跳过创建
2025-06-19 22:04:17,643 - services.db_init_service - INFO - _create_labels_table:152 - 标签表已存在，跳过创建
2025-06-19 22:04:17,646 - services.db_init_service - INFO - _create_brands_table:178 - 品牌表已存在，跳过创建
2025-06-19 22:04:17,646 - services.db_init_service - INFO - _create_brands_table:178 - 品牌表已存在，跳过创建
2025-06-19 22:04:17,646 - services.db_init_service - INFO - _create_brands_table:178 - 品牌表已存在，跳过创建
2025-06-19 22:04:17,646 - services.db_init_service - INFO - _create_brands_table:178 - 品牌表已存在，跳过创建
2025-06-19 22:04:17,646 - services.db_init_service - INFO - _create_brands_table:178 - 品牌表已存在，跳过创建
2025-06-19 22:04:17,646 - services.db_init_service - INFO - _create_brands_table:178 - 品牌表已存在，跳过创建
2025-06-19 22:04:17,650 - services.db_init_service - INFO - _create_cases_table:204 - 案例表已存在，跳过创建
2025-06-19 22:04:17,650 - services.db_init_service - INFO - _create_cases_table:204 - 案例表已存在，跳过创建
2025-06-19 22:04:17,650 - services.db_init_service - INFO - _create_cases_table:204 - 案例表已存在，跳过创建
2025-06-19 22:04:17,650 - services.db_init_service - INFO - _create_cases_table:204 - 案例表已存在，跳过创建
2025-06-19 22:04:17,650 - services.db_init_service - INFO - _create_cases_table:204 - 案例表已存在，跳过创建
2025-06-19 22:04:17,650 - services.db_init_service - INFO - _create_cases_table:204 - 案例表已存在，跳过创建
2025-06-19 22:04:17,665 - services.db_init_service - INFO - _create_product_cases_table:254 - 产品案例关联表已存在，跳过创建
2025-06-19 22:04:17,665 - services.db_init_service - INFO - _create_product_cases_table:254 - 产品案例关联表已存在，跳过创建
2025-06-19 22:04:17,665 - services.db_init_service - INFO - _create_product_cases_table:254 - 产品案例关联表已存在，跳过创建
2025-06-19 22:04:17,665 - services.db_init_service - INFO - _create_product_cases_table:254 - 产品案例关联表已存在，跳过创建
2025-06-19 22:04:17,665 - services.db_init_service - INFO - _create_product_cases_table:254 - 产品案例关联表已存在，跳过创建
2025-06-19 22:04:17,665 - services.db_init_service - INFO - _create_product_cases_table:254 - 产品案例关联表已存在，跳过创建
2025-06-19 22:04:17,671 - services.db_init_service - INFO - _create_programmes_table:274 - 方案表已存在，跳过创建
2025-06-19 22:04:17,671 - services.db_init_service - INFO - _create_programmes_table:274 - 方案表已存在，跳过创建
2025-06-19 22:04:17,671 - services.db_init_service - INFO - _create_programmes_table:274 - 方案表已存在，跳过创建
2025-06-19 22:04:17,671 - services.db_init_service - INFO - _create_programmes_table:274 - 方案表已存在，跳过创建
2025-06-19 22:04:17,671 - services.db_init_service - INFO - _create_programmes_table:274 - 方案表已存在，跳过创建
2025-06-19 22:04:17,671 - services.db_init_service - INFO - _create_programmes_table:274 - 方案表已存在，跳过创建
2025-06-19 22:04:17,674 - services.db_init_service - INFO - _create_programme_products_table:321 - 方案产品关联表已存在，跳过创建
2025-06-19 22:04:17,674 - services.db_init_service - INFO - _create_programme_products_table:321 - 方案产品关联表已存在，跳过创建
2025-06-19 22:04:17,674 - services.db_init_service - INFO - _create_programme_products_table:321 - 方案产品关联表已存在，跳过创建
2025-06-19 22:04:17,674 - services.db_init_service - INFO - _create_programme_products_table:321 - 方案产品关联表已存在，跳过创建
2025-06-19 22:04:17,674 - services.db_init_service - INFO - _create_programme_products_table:321 - 方案产品关联表已存在，跳过创建
2025-06-19 22:04:17,674 - services.db_init_service - INFO - _create_programme_products_table:321 - 方案产品关联表已存在，跳过创建
2025-06-19 22:04:17,677 - services.db_init_service - INFO - _create_information_table:343 - 资讯表已存在，跳过创建
2025-06-19 22:04:17,677 - services.db_init_service - INFO - _create_information_table:343 - 资讯表已存在，跳过创建
2025-06-19 22:04:17,677 - services.db_init_service - INFO - _create_information_table:343 - 资讯表已存在，跳过创建
2025-06-19 22:04:17,677 - services.db_init_service - INFO - _create_information_table:343 - 资讯表已存在，跳过创建
2025-06-19 22:04:17,677 - services.db_init_service - INFO - _create_information_table:343 - 资讯表已存在，跳过创建
2025-06-19 22:04:17,677 - services.db_init_service - INFO - _create_information_table:343 - 资讯表已存在，跳过创建
2025-06-19 22:04:17,695 - services.db_init_service - INFO - _create_distribution_orders_table:383 - 配单表已存在，跳过创建
2025-06-19 22:04:17,695 - services.db_init_service - INFO - _create_distribution_orders_table:383 - 配单表已存在，跳过创建
2025-06-19 22:04:17,695 - services.db_init_service - INFO - _create_distribution_orders_table:383 - 配单表已存在，跳过创建
2025-06-19 22:04:17,695 - services.db_init_service - INFO - _create_distribution_orders_table:383 - 配单表已存在，跳过创建
2025-06-19 22:04:17,695 - services.db_init_service - INFO - _create_distribution_orders_table:383 - 配单表已存在，跳过创建
2025-06-19 22:04:17,695 - services.db_init_service - INFO - _create_distribution_orders_table:383 - 配单表已存在，跳过创建
2025-06-19 22:04:17,702 - services.db_init_service - INFO - _create_sync_status_table:426 - 同步状态表已存在，跳过创建
2025-06-19 22:04:17,702 - services.db_init_service - INFO - _create_sync_status_table:426 - 同步状态表已存在，跳过创建
2025-06-19 22:04:17,702 - services.db_init_service - INFO - _create_sync_status_table:426 - 同步状态表已存在，跳过创建
2025-06-19 22:04:17,702 - services.db_init_service - INFO - _create_sync_status_table:426 - 同步状态表已存在，跳过创建
2025-06-19 22:04:17,702 - services.db_init_service - INFO - _create_sync_status_table:426 - 同步状态表已存在，跳过创建
2025-06-19 22:04:17,702 - services.db_init_service - INFO - _create_sync_status_table:426 - 同步状态表已存在，跳过创建
2025-06-19 22:04:17,704 - services.db_init_service - INFO - create_tables:39 - 所有数据表创建成功
2025-06-19 22:04:17,704 - services.db_init_service - INFO - create_tables:39 - 所有数据表创建成功
2025-06-19 22:04:17,704 - services.db_init_service - INFO - create_tables:39 - 所有数据表创建成功
2025-06-19 22:04:17,704 - services.db_init_service - INFO - create_tables:39 - 所有数据表创建成功
2025-06-19 22:04:17,704 - services.db_init_service - INFO - create_tables:39 - 所有数据表创建成功
2025-06-19 22:04:17,704 - services.db_init_service - INFO - create_tables:39 - 所有数据表创建成功
2025-06-19 22:04:30,363 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 22:04:30,363 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 22:04:30,363 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 22:04:30,363 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 22:04:30,363 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 22:04:30,363 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 22:04:37,393 - services.sync_service - INFO - sync_cases:186 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 22:04:37,393 - services.sync_service - INFO - sync_cases:186 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 22:04:37,393 - services.sync_service - INFO - sync_cases:186 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 22:04:37,393 - services.sync_service - INFO - sync_cases:186 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 22:04:37,393 - services.sync_service - INFO - sync_cases:186 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 22:04:37,393 - services.sync_service - INFO - sync_cases:186 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 22:04:42,682 - services.sync_service - INFO - sync_programmes:265 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 22:04:42,682 - services.sync_service - INFO - sync_programmes:265 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 22:04:42,682 - services.sync_service - INFO - sync_programmes:265 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 22:04:42,682 - services.sync_service - INFO - sync_programmes:265 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 22:04:42,682 - services.sync_service - INFO - sync_programmes:265 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 22:04:42,682 - services.sync_service - INFO - sync_programmes:265 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 22:04:43,129 - services.sync_service - INFO - sync_information:346 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 22:04:43,129 - services.sync_service - INFO - sync_information:346 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 22:04:43,129 - services.sync_service - INFO - sync_information:346 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 22:04:43,129 - services.sync_service - INFO - sync_information:346 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 22:04:43,129 - services.sync_service - INFO - sync_information:346 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 22:04:43,129 - services.sync_service - INFO - sync_information:346 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 22:04:46,059 - services.sync_service - INFO - sync_distribution_orders:429 - 获取配单数据，页码: 1, 每页数量: 100
2025-06-19 22:04:46,059 - services.sync_service - INFO - sync_distribution_orders:429 - 获取配单数据，页码: 1, 每页数量: 100
2025-06-19 22:04:46,059 - services.sync_service - INFO - sync_distribution_orders:429 - 获取配单数据，页码: 1, 每页数量: 100
2025-06-19 22:04:46,059 - services.sync_service - INFO - sync_distribution_orders:429 - 获取配单数据，页码: 1, 每页数量: 100
2025-06-19 22:04:46,059 - services.sync_service - INFO - sync_distribution_orders:429 - 获取配单数据，页码: 1, 每页数量: 100
2025-06-19 22:04:46,059 - services.sync_service - INFO - sync_distribution_orders:429 - 获取配单数据，页码: 1, 每页数量: 100
2025-06-19 22:04:46,527 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:04:46,527 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:04:46,527 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:04:46,527 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:04:46,527 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:04:46,527 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:04:46,531 - utils.api_client - WARNING - get:93 - 认证失败，尝试第1次重试...
2025-06-19 22:04:46,531 - utils.api_client - WARNING - get:93 - 认证失败，尝试第1次重试...
2025-06-19 22:04:46,531 - utils.api_client - WARNING - get:93 - 认证失败，尝试第1次重试...
2025-06-19 22:04:46,531 - utils.api_client - WARNING - get:93 - 认证失败，尝试第1次重试...
2025-06-19 22:04:46,531 - utils.api_client - WARNING - get:93 - 认证失败，尝试第1次重试...
2025-06-19 22:04:46,531 - utils.api_client - WARNING - get:93 - 认证失败，尝试第1次重试...
2025-06-19 22:04:47,189 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:04:47,189 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:04:47,189 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:04:47,189 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:04:47,189 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:04:47,189 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:04:47,192 - utils.api_client - WARNING - get:93 - 认证失败，尝试第2次重试...
2025-06-19 22:04:47,192 - utils.api_client - WARNING - get:93 - 认证失败，尝试第2次重试...
2025-06-19 22:04:47,192 - utils.api_client - WARNING - get:93 - 认证失败，尝试第2次重试...
2025-06-19 22:04:47,192 - utils.api_client - WARNING - get:93 - 认证失败，尝试第2次重试...
2025-06-19 22:04:47,192 - utils.api_client - WARNING - get:93 - 认证失败，尝试第2次重试...
2025-06-19 22:04:47,192 - utils.api_client - WARNING - get:93 - 认证失败，尝试第2次重试...
2025-06-19 22:04:47,896 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:04:47,896 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:04:47,896 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:04:47,896 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:04:47,896 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:04:47,896 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:13:18,410 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:13:29,151 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:13:29,151 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:13:40,657 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:13:40,657 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:13:40,657 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:13:40,905 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:13:40,905 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:13:40,905 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:13:40,905 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:13:40,963 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:13:40,963 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:13:40,963 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:13:40,963 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:13:40,963 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:13:43,041 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:13:43,041 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:13:43,041 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:13:43,041 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:13:43,041 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:13:43,041 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:13:47,132 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 22:13:47,132 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 22:13:47,132 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 22:13:47,132 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 22:13:47,132 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 22:13:47,132 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 22:13:48,431 - services.sync_service - INFO - sync_cases:186 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 22:13:48,431 - services.sync_service - INFO - sync_cases:186 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 22:13:48,431 - services.sync_service - INFO - sync_cases:186 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 22:13:48,431 - services.sync_service - INFO - sync_cases:186 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 22:13:48,431 - services.sync_service - INFO - sync_cases:186 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 22:13:48,431 - services.sync_service - INFO - sync_cases:186 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 22:13:53,675 - services.sync_service - INFO - sync_programmes:265 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 22:13:53,675 - services.sync_service - INFO - sync_programmes:265 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 22:13:53,675 - services.sync_service - INFO - sync_programmes:265 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 22:13:53,675 - services.sync_service - INFO - sync_programmes:265 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 22:13:53,675 - services.sync_service - INFO - sync_programmes:265 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 22:13:53,675 - services.sync_service - INFO - sync_programmes:265 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 22:13:54,287 - services.sync_service - INFO - sync_information:346 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 22:13:54,287 - services.sync_service - INFO - sync_information:346 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 22:13:54,287 - services.sync_service - INFO - sync_information:346 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 22:13:54,287 - services.sync_service - INFO - sync_information:346 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 22:13:54,287 - services.sync_service - INFO - sync_information:346 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 22:13:54,287 - services.sync_service - INFO - sync_information:346 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 22:13:56,667 - services.sync_service - INFO - sync_distribution_orders:431 - 开始同步配单数据，每页20条，请求间隔2秒
2025-06-19 22:13:56,667 - services.sync_service - INFO - sync_distribution_orders:431 - 开始同步配单数据，每页20条，请求间隔2秒
2025-06-19 22:13:56,667 - services.sync_service - INFO - sync_distribution_orders:431 - 开始同步配单数据，每页20条，请求间隔2秒
2025-06-19 22:13:56,667 - services.sync_service - INFO - sync_distribution_orders:431 - 开始同步配单数据，每页20条，请求间隔2秒
2025-06-19 22:13:56,667 - services.sync_service - INFO - sync_distribution_orders:431 - 开始同步配单数据，每页20条，请求间隔2秒
2025-06-19 22:13:56,667 - services.sync_service - INFO - sync_distribution_orders:431 - 开始同步配单数据，每页20条，请求间隔2秒
2025-06-19 22:13:56,672 - services.sync_service - INFO - sync_distribution_orders:436 - 获取配单数据，页码: 1, 每页数量: 20
2025-06-19 22:13:56,672 - services.sync_service - INFO - sync_distribution_orders:436 - 获取配单数据，页码: 1, 每页数量: 20
2025-06-19 22:13:56,672 - services.sync_service - INFO - sync_distribution_orders:436 - 获取配单数据，页码: 1, 每页数量: 20
2025-06-19 22:13:56,672 - services.sync_service - INFO - sync_distribution_orders:436 - 获取配单数据，页码: 1, 每页数量: 20
2025-06-19 22:13:56,672 - services.sync_service - INFO - sync_distribution_orders:436 - 获取配单数据，页码: 1, 每页数量: 20
2025-06-19 22:13:56,672 - services.sync_service - INFO - sync_distribution_orders:436 - 获取配单数据，页码: 1, 每页数量: 20
2025-06-19 22:13:56,673 - utils.api_client - INFO - get_distribution_orders:384 - 配单API调用参数: {'current': 1, 'pageSize': 20}
2025-06-19 22:13:56,673 - utils.api_client - INFO - get_distribution_orders:384 - 配单API调用参数: {'current': 1, 'pageSize': 20}
2025-06-19 22:13:56,673 - utils.api_client - INFO - get_distribution_orders:384 - 配单API调用参数: {'current': 1, 'pageSize': 20}
2025-06-19 22:13:56,673 - utils.api_client - INFO - get_distribution_orders:384 - 配单API调用参数: {'current': 1, 'pageSize': 20}
2025-06-19 22:13:56,673 - utils.api_client - INFO - get_distribution_orders:384 - 配单API调用参数: {'current': 1, 'pageSize': 20}
2025-06-19 22:13:56,673 - utils.api_client - INFO - get_distribution_orders:384 - 配单API调用参数: {'current': 1, 'pageSize': 20}
2025-06-19 22:13:56,676 - utils.api_client - WARNING - get_distribution_orders:389 - Token为空，尝试重新登录
2025-06-19 22:13:56,676 - utils.api_client - WARNING - get_distribution_orders:389 - Token为空，尝试重新登录
2025-06-19 22:13:56,676 - utils.api_client - WARNING - get_distribution_orders:389 - Token为空，尝试重新登录
2025-06-19 22:13:56,676 - utils.api_client - WARNING - get_distribution_orders:389 - Token为空，尝试重新登录
2025-06-19 22:13:56,676 - utils.api_client - WARNING - get_distribution_orders:389 - Token为空，尝试重新登录
2025-06-19 22:13:56,676 - utils.api_client - WARNING - get_distribution_orders:389 - Token为空，尝试重新登录
2025-06-19 22:13:57,267 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:13:57,267 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:13:57,267 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:13:57,267 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:13:57,267 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:13:57,267 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:13:57,269 - utils.api_client - WARNING - get:99 - 认证失败，尝试第1次重试...
2025-06-19 22:13:57,269 - utils.api_client - WARNING - get:99 - 认证失败，尝试第1次重试...
2025-06-19 22:13:57,269 - utils.api_client - WARNING - get:99 - 认证失败，尝试第1次重试...
2025-06-19 22:13:57,269 - utils.api_client - WARNING - get:99 - 认证失败，尝试第1次重试...
2025-06-19 22:13:57,269 - utils.api_client - WARNING - get:99 - 认证失败，尝试第1次重试...
2025-06-19 22:13:57,269 - utils.api_client - WARNING - get:99 - 认证失败，尝试第1次重试...
2025-06-19 22:13:57,273 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:13:57,273 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:13:57,273 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:13:57,273 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:13:57,273 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:13:57,273 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:13:57,278 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:13:57,278 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:13:57,278 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:13:57,278 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:13:57,278 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:13:57,278 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:13:57,851 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:13:57,851 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:13:57,851 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:13:57,851 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:13:57,851 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:13:57,851 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:13:57,854 - utils.api_client - WARNING - get:99 - 认证失败，尝试第2次重试...
2025-06-19 22:13:57,854 - utils.api_client - WARNING - get:99 - 认证失败，尝试第2次重试...
2025-06-19 22:13:57,854 - utils.api_client - WARNING - get:99 - 认证失败，尝试第2次重试...
2025-06-19 22:13:57,854 - utils.api_client - WARNING - get:99 - 认证失败，尝试第2次重试...
2025-06-19 22:13:57,854 - utils.api_client - WARNING - get:99 - 认证失败，尝试第2次重试...
2025-06-19 22:13:57,854 - utils.api_client - WARNING - get:99 - 认证失败，尝试第2次重试...
2025-06-19 22:13:57,861 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:13:57,861 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:13:57,861 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:13:57,861 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:13:57,861 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:13:57,861 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:13:57,864 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:13:57,864 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:13:57,864 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:13:57,864 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:13:57,864 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:13:57,864 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:13:58,437 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:13:58,437 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:13:58,437 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:13:58,437 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:13:58,437 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:13:58,437 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:13:58,442 - utils.api_client - ERROR - get:122 - 认证失败，已达最大重试次数
2025-06-19 22:13:58,442 - utils.api_client - ERROR - get:122 - 认证失败，已达最大重试次数
2025-06-19 22:13:58,442 - utils.api_client - ERROR - get:122 - 认证失败，已达最大重试次数
2025-06-19 22:13:58,442 - utils.api_client - ERROR - get:122 - 认证失败，已达最大重试次数
2025-06-19 22:13:58,442 - utils.api_client - ERROR - get:122 - 认证失败，已达最大重试次数
2025-06-19 22:13:58,442 - utils.api_client - ERROR - get:122 - 认证失败，已达最大重试次数
2025-06-19 22:13:58,447 - utils.api_client - ERROR - get_distribution_orders:411 - 配单API调用失败: {'code': 401, 'msg': '认证失败，已达最大重试次数', 'data': None}
2025-06-19 22:13:58,447 - utils.api_client - ERROR - get_distribution_orders:411 - 配单API调用失败: {'code': 401, 'msg': '认证失败，已达最大重试次数', 'data': None}
2025-06-19 22:13:58,447 - utils.api_client - ERROR - get_distribution_orders:411 - 配单API调用失败: {'code': 401, 'msg': '认证失败，已达最大重试次数', 'data': None}
2025-06-19 22:13:58,447 - utils.api_client - ERROR - get_distribution_orders:411 - 配单API调用失败: {'code': 401, 'msg': '认证失败，已达最大重试次数', 'data': None}
2025-06-19 22:13:58,447 - utils.api_client - ERROR - get_distribution_orders:411 - 配单API调用失败: {'code': 401, 'msg': '认证失败，已达最大重试次数', 'data': None}
2025-06-19 22:13:58,447 - utils.api_client - ERROR - get_distribution_orders:411 - 配单API调用失败: {'code': 401, 'msg': '认证失败，已达最大重试次数', 'data': None}
2025-06-19 22:13:58,471 - services.sync_service - INFO - sync_distribution_orders:451 - 成功获取配单数据，页码1，数量: 0
2025-06-19 22:13:58,471 - services.sync_service - INFO - sync_distribution_orders:451 - 成功获取配单数据，页码1，数量: 0
2025-06-19 22:13:58,471 - services.sync_service - INFO - sync_distribution_orders:451 - 成功获取配单数据，页码1，数量: 0
2025-06-19 22:13:58,471 - services.sync_service - INFO - sync_distribution_orders:451 - 成功获取配单数据，页码1，数量: 0
2025-06-19 22:13:58,471 - services.sync_service - INFO - sync_distribution_orders:451 - 成功获取配单数据，页码1，数量: 0
2025-06-19 22:13:58,471 - services.sync_service - INFO - sync_distribution_orders:451 - 成功获取配单数据，页码1，数量: 0
2025-06-19 22:13:58,474 - services.sync_service - INFO - sync_distribution_orders:474 - 配单数据获取完毕，无更多数据
2025-06-19 22:13:58,474 - services.sync_service - INFO - sync_distribution_orders:474 - 配单数据获取完毕，无更多数据
2025-06-19 22:13:58,474 - services.sync_service - INFO - sync_distribution_orders:474 - 配单数据获取完毕，无更多数据
2025-06-19 22:13:58,474 - services.sync_service - INFO - sync_distribution_orders:474 - 配单数据获取完毕，无更多数据
2025-06-19 22:13:58,474 - services.sync_service - INFO - sync_distribution_orders:474 - 配单数据获取完毕，无更多数据
2025-06-19 22:13:58,474 - services.sync_service - INFO - sync_distribution_orders:474 - 配单数据获取完毕，无更多数据
2025-06-19 22:13:58,478 - services.sync_service - INFO - sync_distribution_orders:493 - 配单数据获取完成，总计: 0 条
2025-06-19 22:13:58,478 - services.sync_service - INFO - sync_distribution_orders:493 - 配单数据获取完成，总计: 0 条
2025-06-19 22:13:58,478 - services.sync_service - INFO - sync_distribution_orders:493 - 配单数据获取完成，总计: 0 条
2025-06-19 22:13:58,478 - services.sync_service - INFO - sync_distribution_orders:493 - 配单数据获取完成，总计: 0 条
2025-06-19 22:13:58,478 - services.sync_service - INFO - sync_distribution_orders:493 - 配单数据获取完成，总计: 0 条
2025-06-19 22:13:58,478 - services.sync_service - INFO - sync_distribution_orders:493 - 配单数据获取完成，总计: 0 条
2025-06-19 22:20:01,518 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:31:51,294 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:37:20,033 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:37:20,033 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:37:37,333 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:37:37,333 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:37:37,333 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:37:37,550 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:37:37,550 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:37:37,550 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:37:37,550 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:37:37,603 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:37:37,603 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:37:37,603 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:37:37,603 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:37:37,603 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:37:41,717 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:37:41,717 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:37:41,717 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:37:41,717 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:37:41,717 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:37:41,717 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:37:44,649 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 22:37:44,649 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 22:37:44,649 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 22:37:44,649 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 22:37:44,649 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 22:37:44,649 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 22:37:50,784 - services.sync_service - INFO - sync_cases:186 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 22:37:50,784 - services.sync_service - INFO - sync_cases:186 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 22:37:50,784 - services.sync_service - INFO - sync_cases:186 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 22:37:50,784 - services.sync_service - INFO - sync_cases:186 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 22:37:50,784 - services.sync_service - INFO - sync_cases:186 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 22:37:50,784 - services.sync_service - INFO - sync_cases:186 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 22:37:55,626 - services.sync_service - INFO - sync_programmes:265 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 22:37:55,626 - services.sync_service - INFO - sync_programmes:265 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 22:37:55,626 - services.sync_service - INFO - sync_programmes:265 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 22:37:55,626 - services.sync_service - INFO - sync_programmes:265 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 22:37:55,626 - services.sync_service - INFO - sync_programmes:265 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 22:37:55,626 - services.sync_service - INFO - sync_programmes:265 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 22:37:56,053 - services.sync_service - INFO - sync_information:346 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 22:37:56,053 - services.sync_service - INFO - sync_information:346 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 22:37:56,053 - services.sync_service - INFO - sync_information:346 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 22:37:56,053 - services.sync_service - INFO - sync_information:346 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 22:37:56,053 - services.sync_service - INFO - sync_information:346 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 22:37:56,053 - services.sync_service - INFO - sync_information:346 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 22:37:58,951 - services.sync_service - INFO - sync_distribution_orders:431 - 开始同步配单数据，每页20条，请求间隔2秒
2025-06-19 22:37:58,951 - services.sync_service - INFO - sync_distribution_orders:431 - 开始同步配单数据，每页20条，请求间隔2秒
2025-06-19 22:37:58,951 - services.sync_service - INFO - sync_distribution_orders:431 - 开始同步配单数据，每页20条，请求间隔2秒
2025-06-19 22:37:58,951 - services.sync_service - INFO - sync_distribution_orders:431 - 开始同步配单数据，每页20条，请求间隔2秒
2025-06-19 22:37:58,951 - services.sync_service - INFO - sync_distribution_orders:431 - 开始同步配单数据，每页20条，请求间隔2秒
2025-06-19 22:37:58,951 - services.sync_service - INFO - sync_distribution_orders:431 - 开始同步配单数据，每页20条，请求间隔2秒
2025-06-19 22:37:58,954 - services.sync_service - INFO - sync_distribution_orders:436 - 获取配单数据，页码: 1, 每页数量: 20
2025-06-19 22:37:58,954 - services.sync_service - INFO - sync_distribution_orders:436 - 获取配单数据，页码: 1, 每页数量: 20
2025-06-19 22:37:58,954 - services.sync_service - INFO - sync_distribution_orders:436 - 获取配单数据，页码: 1, 每页数量: 20
2025-06-19 22:37:58,954 - services.sync_service - INFO - sync_distribution_orders:436 - 获取配单数据，页码: 1, 每页数量: 20
2025-06-19 22:37:58,954 - services.sync_service - INFO - sync_distribution_orders:436 - 获取配单数据，页码: 1, 每页数量: 20
2025-06-19 22:37:58,954 - services.sync_service - INFO - sync_distribution_orders:436 - 获取配单数据，页码: 1, 每页数量: 20
2025-06-19 22:37:58,956 - utils.api_client - INFO - get_distribution_orders:384 - 配单API调用参数: {'current': 1, 'pageSize': 20}
2025-06-19 22:37:58,956 - utils.api_client - INFO - get_distribution_orders:384 - 配单API调用参数: {'current': 1, 'pageSize': 20}
2025-06-19 22:37:58,956 - utils.api_client - INFO - get_distribution_orders:384 - 配单API调用参数: {'current': 1, 'pageSize': 20}
2025-06-19 22:37:58,956 - utils.api_client - INFO - get_distribution_orders:384 - 配单API调用参数: {'current': 1, 'pageSize': 20}
2025-06-19 22:37:58,956 - utils.api_client - INFO - get_distribution_orders:384 - 配单API调用参数: {'current': 1, 'pageSize': 20}
2025-06-19 22:37:58,956 - utils.api_client - INFO - get_distribution_orders:384 - 配单API调用参数: {'current': 1, 'pageSize': 20}
2025-06-19 22:37:58,959 - utils.api_client - WARNING - get_distribution_orders:389 - Token为空，尝试重新登录
2025-06-19 22:37:58,959 - utils.api_client - WARNING - get_distribution_orders:389 - Token为空，尝试重新登录
2025-06-19 22:37:58,959 - utils.api_client - WARNING - get_distribution_orders:389 - Token为空，尝试重新登录
2025-06-19 22:37:58,959 - utils.api_client - WARNING - get_distribution_orders:389 - Token为空，尝试重新登录
2025-06-19 22:37:58,959 - utils.api_client - WARNING - get_distribution_orders:389 - Token为空，尝试重新登录
2025-06-19 22:37:58,959 - utils.api_client - WARNING - get_distribution_orders:389 - Token为空，尝试重新登录
2025-06-19 22:37:59,371 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:37:59,371 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:37:59,371 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:37:59,371 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:37:59,371 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:37:59,371 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:37:59,373 - utils.api_client - WARNING - get:99 - 认证失败，尝试第1次重试...
2025-06-19 22:37:59,373 - utils.api_client - WARNING - get:99 - 认证失败，尝试第1次重试...
2025-06-19 22:37:59,373 - utils.api_client - WARNING - get:99 - 认证失败，尝试第1次重试...
2025-06-19 22:37:59,373 - utils.api_client - WARNING - get:99 - 认证失败，尝试第1次重试...
2025-06-19 22:37:59,373 - utils.api_client - WARNING - get:99 - 认证失败，尝试第1次重试...
2025-06-19 22:37:59,373 - utils.api_client - WARNING - get:99 - 认证失败，尝试第1次重试...
2025-06-19 22:37:59,376 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:37:59,376 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:37:59,376 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:37:59,376 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:37:59,376 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:37:59,376 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:37:59,378 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:37:59,378 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:37:59,378 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:37:59,378 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:37:59,378 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:37:59,378 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:37:59,789 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:37:59,789 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:37:59,789 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:37:59,789 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:37:59,789 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:37:59,789 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:37:59,792 - utils.api_client - WARNING - get:99 - 认证失败，尝试第2次重试...
2025-06-19 22:37:59,792 - utils.api_client - WARNING - get:99 - 认证失败，尝试第2次重试...
2025-06-19 22:37:59,792 - utils.api_client - WARNING - get:99 - 认证失败，尝试第2次重试...
2025-06-19 22:37:59,792 - utils.api_client - WARNING - get:99 - 认证失败，尝试第2次重试...
2025-06-19 22:37:59,792 - utils.api_client - WARNING - get:99 - 认证失败，尝试第2次重试...
2025-06-19 22:37:59,792 - utils.api_client - WARNING - get:99 - 认证失败，尝试第2次重试...
2025-06-19 22:37:59,797 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:37:59,797 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:37:59,797 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:37:59,797 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:37:59,797 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:37:59,797 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:37:59,809 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:37:59,809 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:37:59,809 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:37:59,809 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:37:59,809 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:37:59,809 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:38:00,223 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:38:00,223 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:38:00,223 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:38:00,223 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:38:00,223 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:38:00,223 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:38:00,226 - utils.api_client - ERROR - get:122 - 认证失败，已达最大重试次数
2025-06-19 22:38:00,226 - utils.api_client - ERROR - get:122 - 认证失败，已达最大重试次数
2025-06-19 22:38:00,226 - utils.api_client - ERROR - get:122 - 认证失败，已达最大重试次数
2025-06-19 22:38:00,226 - utils.api_client - ERROR - get:122 - 认证失败，已达最大重试次数
2025-06-19 22:38:00,226 - utils.api_client - ERROR - get:122 - 认证失败，已达最大重试次数
2025-06-19 22:38:00,226 - utils.api_client - ERROR - get:122 - 认证失败，已达最大重试次数
2025-06-19 22:38:00,234 - utils.api_client - ERROR - get_distribution_orders:411 - 配单API调用失败: {'code': 401, 'msg': '认证失败，已达最大重试次数', 'data': None}
2025-06-19 22:38:00,234 - utils.api_client - ERROR - get_distribution_orders:411 - 配单API调用失败: {'code': 401, 'msg': '认证失败，已达最大重试次数', 'data': None}
2025-06-19 22:38:00,234 - utils.api_client - ERROR - get_distribution_orders:411 - 配单API调用失败: {'code': 401, 'msg': '认证失败，已达最大重试次数', 'data': None}
2025-06-19 22:38:00,234 - utils.api_client - ERROR - get_distribution_orders:411 - 配单API调用失败: {'code': 401, 'msg': '认证失败，已达最大重试次数', 'data': None}
2025-06-19 22:38:00,234 - utils.api_client - ERROR - get_distribution_orders:411 - 配单API调用失败: {'code': 401, 'msg': '认证失败，已达最大重试次数', 'data': None}
2025-06-19 22:38:00,234 - utils.api_client - ERROR - get_distribution_orders:411 - 配单API调用失败: {'code': 401, 'msg': '认证失败，已达最大重试次数', 'data': None}
2025-06-19 22:38:00,247 - services.sync_service - INFO - sync_distribution_orders:451 - 成功获取配单数据，页码1，数量: 0
2025-06-19 22:38:00,247 - services.sync_service - INFO - sync_distribution_orders:451 - 成功获取配单数据，页码1，数量: 0
2025-06-19 22:38:00,247 - services.sync_service - INFO - sync_distribution_orders:451 - 成功获取配单数据，页码1，数量: 0
2025-06-19 22:38:00,247 - services.sync_service - INFO - sync_distribution_orders:451 - 成功获取配单数据，页码1，数量: 0
2025-06-19 22:38:00,247 - services.sync_service - INFO - sync_distribution_orders:451 - 成功获取配单数据，页码1，数量: 0
2025-06-19 22:38:00,247 - services.sync_service - INFO - sync_distribution_orders:451 - 成功获取配单数据，页码1，数量: 0
2025-06-19 22:38:00,250 - services.sync_service - INFO - sync_distribution_orders:474 - 配单数据获取完毕，无更多数据
2025-06-19 22:38:00,250 - services.sync_service - INFO - sync_distribution_orders:474 - 配单数据获取完毕，无更多数据
2025-06-19 22:38:00,250 - services.sync_service - INFO - sync_distribution_orders:474 - 配单数据获取完毕，无更多数据
2025-06-19 22:38:00,250 - services.sync_service - INFO - sync_distribution_orders:474 - 配单数据获取完毕，无更多数据
2025-06-19 22:38:00,250 - services.sync_service - INFO - sync_distribution_orders:474 - 配单数据获取完毕，无更多数据
2025-06-19 22:38:00,250 - services.sync_service - INFO - sync_distribution_orders:474 - 配单数据获取完毕，无更多数据
2025-06-19 22:38:00,252 - services.sync_service - INFO - sync_distribution_orders:493 - 配单数据获取完成，总计: 0 条
2025-06-19 22:38:00,252 - services.sync_service - INFO - sync_distribution_orders:493 - 配单数据获取完成，总计: 0 条
2025-06-19 22:38:00,252 - services.sync_service - INFO - sync_distribution_orders:493 - 配单数据获取完成，总计: 0 条
2025-06-19 22:38:00,252 - services.sync_service - INFO - sync_distribution_orders:493 - 配单数据获取完成，总计: 0 条
2025-06-19 22:38:00,252 - services.sync_service - INFO - sync_distribution_orders:493 - 配单数据获取完成，总计: 0 条
2025-06-19 22:38:00,252 - services.sync_service - INFO - sync_distribution_orders:493 - 配单数据获取完成，总计: 0 条
2025-06-19 22:38:33,070 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:38:39,263 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:38:39,263 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:38:40,937 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:38:40,937 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:38:40,937 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:38:41,115 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:38:41,115 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:38:41,115 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:38:41,115 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:38:41,157 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:38:41,157 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:38:41,157 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:38:41,157 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:38:41,157 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:38:43,099 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:38:43,099 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:38:43,099 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:38:43,099 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:38:43,099 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:38:43,099 - yunshang - INFO - setup_logging:58 - 日志系统初始化完成
2025-06-19 22:38:45,657 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 22:38:45,657 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 22:38:45,657 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 22:38:45,657 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 22:38:45,657 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 22:38:45,657 - services.sync_service - INFO - sync_products:59 - 获取产品数据，页码: 1, 每页数量: 100
2025-06-19 22:38:46,895 - services.sync_service - INFO - sync_cases:186 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 22:38:46,895 - services.sync_service - INFO - sync_cases:186 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 22:38:46,895 - services.sync_service - INFO - sync_cases:186 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 22:38:46,895 - services.sync_service - INFO - sync_cases:186 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 22:38:46,895 - services.sync_service - INFO - sync_cases:186 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 22:38:46,895 - services.sync_service - INFO - sync_cases:186 - 获取案例数据，页码: 1, 每页数量: 100
2025-06-19 22:38:51,770 - services.sync_service - INFO - sync_programmes:265 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 22:38:51,770 - services.sync_service - INFO - sync_programmes:265 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 22:38:51,770 - services.sync_service - INFO - sync_programmes:265 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 22:38:51,770 - services.sync_service - INFO - sync_programmes:265 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 22:38:51,770 - services.sync_service - INFO - sync_programmes:265 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 22:38:51,770 - services.sync_service - INFO - sync_programmes:265 - 获取方案数据，页码: 1, 每页数量: 100
2025-06-19 22:38:52,151 - services.sync_service - INFO - sync_information:346 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 22:38:52,151 - services.sync_service - INFO - sync_information:346 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 22:38:52,151 - services.sync_service - INFO - sync_information:346 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 22:38:52,151 - services.sync_service - INFO - sync_information:346 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 22:38:52,151 - services.sync_service - INFO - sync_information:346 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 22:38:52,151 - services.sync_service - INFO - sync_information:346 - 获取资讯数据，页码: 1, 每页数量: 100
2025-06-19 22:38:54,503 - services.sync_service - INFO - sync_distribution_orders:431 - 开始同步配单数据，每页20条，请求间隔2秒
2025-06-19 22:38:54,503 - services.sync_service - INFO - sync_distribution_orders:431 - 开始同步配单数据，每页20条，请求间隔2秒
2025-06-19 22:38:54,503 - services.sync_service - INFO - sync_distribution_orders:431 - 开始同步配单数据，每页20条，请求间隔2秒
2025-06-19 22:38:54,503 - services.sync_service - INFO - sync_distribution_orders:431 - 开始同步配单数据，每页20条，请求间隔2秒
2025-06-19 22:38:54,503 - services.sync_service - INFO - sync_distribution_orders:431 - 开始同步配单数据，每页20条，请求间隔2秒
2025-06-19 22:38:54,503 - services.sync_service - INFO - sync_distribution_orders:431 - 开始同步配单数据，每页20条，请求间隔2秒
2025-06-19 22:38:54,506 - services.sync_service - INFO - sync_distribution_orders:436 - 获取配单数据，页码: 1, 每页数量: 20
2025-06-19 22:38:54,506 - services.sync_service - INFO - sync_distribution_orders:436 - 获取配单数据，页码: 1, 每页数量: 20
2025-06-19 22:38:54,506 - services.sync_service - INFO - sync_distribution_orders:436 - 获取配单数据，页码: 1, 每页数量: 20
2025-06-19 22:38:54,506 - services.sync_service - INFO - sync_distribution_orders:436 - 获取配单数据，页码: 1, 每页数量: 20
2025-06-19 22:38:54,506 - services.sync_service - INFO - sync_distribution_orders:436 - 获取配单数据，页码: 1, 每页数量: 20
2025-06-19 22:38:54,506 - services.sync_service - INFO - sync_distribution_orders:436 - 获取配单数据，页码: 1, 每页数量: 20
2025-06-19 22:38:54,512 - utils.api_client - INFO - get_distribution_orders:384 - 配单API调用参数: {'current': 1, 'pageSize': 20}
2025-06-19 22:38:54,512 - utils.api_client - INFO - get_distribution_orders:384 - 配单API调用参数: {'current': 1, 'pageSize': 20}
2025-06-19 22:38:54,512 - utils.api_client - INFO - get_distribution_orders:384 - 配单API调用参数: {'current': 1, 'pageSize': 20}
2025-06-19 22:38:54,512 - utils.api_client - INFO - get_distribution_orders:384 - 配单API调用参数: {'current': 1, 'pageSize': 20}
2025-06-19 22:38:54,512 - utils.api_client - INFO - get_distribution_orders:384 - 配单API调用参数: {'current': 1, 'pageSize': 20}
2025-06-19 22:38:54,512 - utils.api_client - INFO - get_distribution_orders:384 - 配单API调用参数: {'current': 1, 'pageSize': 20}
2025-06-19 22:38:54,516 - utils.api_client - WARNING - get_distribution_orders:389 - Token为空，尝试重新登录
2025-06-19 22:38:54,516 - utils.api_client - WARNING - get_distribution_orders:389 - Token为空，尝试重新登录
2025-06-19 22:38:54,516 - utils.api_client - WARNING - get_distribution_orders:389 - Token为空，尝试重新登录
2025-06-19 22:38:54,516 - utils.api_client - WARNING - get_distribution_orders:389 - Token为空，尝试重新登录
2025-06-19 22:38:54,516 - utils.api_client - WARNING - get_distribution_orders:389 - Token为空，尝试重新登录
2025-06-19 22:38:54,516 - utils.api_client - WARNING - get_distribution_orders:389 - Token为空，尝试重新登录
2025-06-19 22:38:54,915 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:38:54,915 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:38:54,915 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:38:54,915 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:38:54,915 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:38:54,915 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:38:54,918 - utils.api_client - WARNING - get:99 - 认证失败，尝试第1次重试...
2025-06-19 22:38:54,918 - utils.api_client - WARNING - get:99 - 认证失败，尝试第1次重试...
2025-06-19 22:38:54,918 - utils.api_client - WARNING - get:99 - 认证失败，尝试第1次重试...
2025-06-19 22:38:54,918 - utils.api_client - WARNING - get:99 - 认证失败，尝试第1次重试...
2025-06-19 22:38:54,918 - utils.api_client - WARNING - get:99 - 认证失败，尝试第1次重试...
2025-06-19 22:38:54,918 - utils.api_client - WARNING - get:99 - 认证失败，尝试第1次重试...
2025-06-19 22:38:54,921 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:38:54,921 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:38:54,921 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:38:54,921 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:38:54,921 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:38:54,921 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:38:54,924 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:38:54,924 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:38:54,924 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:38:54,924 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:38:54,924 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:38:54,924 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:38:55,334 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:38:55,334 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:38:55,334 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:38:55,334 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:38:55,334 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:38:55,334 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:38:55,338 - utils.api_client - WARNING - get:99 - 认证失败，尝试第2次重试...
2025-06-19 22:38:55,338 - utils.api_client - WARNING - get:99 - 认证失败，尝试第2次重试...
2025-06-19 22:38:55,338 - utils.api_client - WARNING - get:99 - 认证失败，尝试第2次重试...
2025-06-19 22:38:55,338 - utils.api_client - WARNING - get:99 - 认证失败，尝试第2次重试...
2025-06-19 22:38:55,338 - utils.api_client - WARNING - get:99 - 认证失败，尝试第2次重试...
2025-06-19 22:38:55,338 - utils.api_client - WARNING - get:99 - 认证失败，尝试第2次重试...
2025-06-19 22:38:55,341 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:38:55,341 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:38:55,341 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:38:55,341 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:38:55,341 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:38:55,341 - utils.api_client - WARNING - get:100 - 当前token: None...
2025-06-19 22:38:55,344 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:38:55,344 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:38:55,344 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:38:55,344 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:38:55,344 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:38:55,344 - utils.api_client - ERROR - get:119 - 缺少用户名或密码环境变量
2025-06-19 22:38:55,745 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:38:55,745 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:38:55,745 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:38:55,745 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:38:55,745 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:38:55,745 - utils.api_client - ERROR - _handle_response:59 - API错误: 请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源
2025-06-19 22:38:55,749 - utils.api_client - ERROR - get:122 - 认证失败，已达最大重试次数
2025-06-19 22:38:55,749 - utils.api_client - ERROR - get:122 - 认证失败，已达最大重试次数
2025-06-19 22:38:55,749 - utils.api_client - ERROR - get:122 - 认证失败，已达最大重试次数
2025-06-19 22:38:55,749 - utils.api_client - ERROR - get:122 - 认证失败，已达最大重试次数
2025-06-19 22:38:55,749 - utils.api_client - ERROR - get:122 - 认证失败，已达最大重试次数
2025-06-19 22:38:55,749 - utils.api_client - ERROR - get:122 - 认证失败，已达最大重试次数
2025-06-19 22:38:55,753 - utils.api_client - ERROR - get_distribution_orders:411 - 配单API调用失败: {'code': 401, 'msg': '认证失败，已达最大重试次数', 'data': None}
2025-06-19 22:38:55,753 - utils.api_client - ERROR - get_distribution_orders:411 - 配单API调用失败: {'code': 401, 'msg': '认证失败，已达最大重试次数', 'data': None}
2025-06-19 22:38:55,753 - utils.api_client - ERROR - get_distribution_orders:411 - 配单API调用失败: {'code': 401, 'msg': '认证失败，已达最大重试次数', 'data': None}
2025-06-19 22:38:55,753 - utils.api_client - ERROR - get_distribution_orders:411 - 配单API调用失败: {'code': 401, 'msg': '认证失败，已达最大重试次数', 'data': None}
2025-06-19 22:38:55,753 - utils.api_client - ERROR - get_distribution_orders:411 - 配单API调用失败: {'code': 401, 'msg': '认证失败，已达最大重试次数', 'data': None}
2025-06-19 22:38:55,753 - utils.api_client - ERROR - get_distribution_orders:411 - 配单API调用失败: {'code': 401, 'msg': '认证失败，已达最大重试次数', 'data': None}
2025-06-19 22:38:55,766 - services.sync_service - INFO - sync_distribution_orders:451 - 成功获取配单数据，页码1，数量: 0
2025-06-19 22:38:55,766 - services.sync_service - INFO - sync_distribution_orders:451 - 成功获取配单数据，页码1，数量: 0
2025-06-19 22:38:55,766 - services.sync_service - INFO - sync_distribution_orders:451 - 成功获取配单数据，页码1，数量: 0
2025-06-19 22:38:55,766 - services.sync_service - INFO - sync_distribution_orders:451 - 成功获取配单数据，页码1，数量: 0
2025-06-19 22:38:55,766 - services.sync_service - INFO - sync_distribution_orders:451 - 成功获取配单数据，页码1，数量: 0
2025-06-19 22:38:55,766 - services.sync_service - INFO - sync_distribution_orders:451 - 成功获取配单数据，页码1，数量: 0
2025-06-19 22:38:55,769 - services.sync_service - INFO - sync_distribution_orders:474 - 配单数据获取完毕，无更多数据
2025-06-19 22:38:55,769 - services.sync_service - INFO - sync_distribution_orders:474 - 配单数据获取完毕，无更多数据
2025-06-19 22:38:55,769 - services.sync_service - INFO - sync_distribution_orders:474 - 配单数据获取完毕，无更多数据
2025-06-19 22:38:55,769 - services.sync_service - INFO - sync_distribution_orders:474 - 配单数据获取完毕，无更多数据
2025-06-19 22:38:55,769 - services.sync_service - INFO - sync_distribution_orders:474 - 配单数据获取完毕，无更多数据
2025-06-19 22:38:55,769 - services.sync_service - INFO - sync_distribution_orders:474 - 配单数据获取完毕，无更多数据
2025-06-19 22:38:55,772 - services.sync_service - INFO - sync_distribution_orders:493 - 配单数据获取完成，总计: 0 条
2025-06-19 22:38:55,772 - services.sync_service - INFO - sync_distribution_orders:493 - 配单数据获取完成，总计: 0 条
2025-06-19 22:38:55,772 - services.sync_service - INFO - sync_distribution_orders:493 - 配单数据获取完成，总计: 0 条
2025-06-19 22:38:55,772 - services.sync_service - INFO - sync_distribution_orders:493 - 配单数据获取完成，总计: 0 条
2025-06-19 22:38:55,772 - services.sync_service - INFO - sync_distribution_orders:493 - 配单数据获取完成，总计: 0 条
2025-06-19 22:38:55,772 - services.sync_service - INFO - sync_distribution_orders:493 - 配单数据获取完成，总计: 0 条
