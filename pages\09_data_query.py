"""
数据查询页面
查询和显示数据库中同步的云商数据
"""

import streamlit as st
import logging
import pandas as pd
from typing import Dict, Any, List, Optional
import json
from utils.session import SessionManager
from utils.auth import require_auth
from utils.database import get_db_connection
from utils.api_client import ZKMallClient

logger = logging.getLogger(__name__)


@require_auth
def main():
    """数据查询页面"""
    # 页面配置
    st.set_page_config(
        page_title="数据查询",
        page_icon="🔍",
        layout="wide",
        initial_sidebar_state="expanded",
    )

    # 初始化session状态
    initialize_session()

    # 渲染页面
    render_header()
    render_sidebar()
    render_content()
    render_footer()


def initialize_session():
    """初始化session状态"""
    defaults = {
        "selected_entity": "products",
        "entity_filters": {},
        "query_results": [],
        "selected_record": None,
        "page_size": 20,
        "current_page": 1,
        "total_records": 0,
    }

    for key, value in defaults.items():
        if key not in st.session_state:
            SessionManager.set(key, value)


def render_header():
    """渲染页面头部"""
    st.title("🔍 数据查询")
    st.markdown("查询和显示从云商API同步的数据库数据")

    # 显示数据库连接状态
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        db_status = get_database_status()
        st.metric("数据库状态", db_status)

    with col2:
        total_tables = get_total_tables()
        st.metric("数据表总数", total_tables)

    with col3:
        total_records = get_total_records()
        st.metric("总记录数", f"{total_records:,}")

    with col4:
        last_sync = get_last_sync_time()
        st.metric("最后同步", last_sync)


def render_sidebar():
    """渲染侧边栏"""
    with st.sidebar:
        st.subheader("🔧 查询设置")

        # 选择数据类型
        st.markdown("#### 📊 数据类型")
        entity_types = [
            ("products", "产品数据"),
            ("categories", "分类数据"),
            ("brands", "品牌数据"),
            ("suppliers", "供应商数据"),
            ("orders", "订单数据"),
            ("customers", "客户数据"),
            ("inventory", "库存数据"),
            ("prices", "价格数据"),
            ("promotions", "促销数据"),
            ("reviews", "评价数据"),
            ("attachments", "附件数据"),
            ("sync_status", "同步状态"),
        ]

        selected_entity = st.selectbox(
            "选择要查询的数据类型",
            [key for key, _ in entity_types],
            format_func=lambda x: next(
                (name for key, name in entity_types if key == x), x
            ),
            index=0,
        )

        SessionManager.set("selected_entity", selected_entity)

        # 分页设置
        st.markdown("#### 📄 分页设置")
        page_size = st.selectbox("每页显示数量", [10, 20, 50, 100], index=1)
        SessionManager.set("page_size", page_size)

        # 筛选条件
        st.markdown("#### 🔍 筛选条件")
        render_filters(selected_entity)

        # 排序选项
        st.markdown("#### 📈 排序选项")
        sort_fields = get_sort_fields(selected_entity)
        sort_field = st.selectbox("排序字段", sort_fields)
        sort_order = st.selectbox("排序方式", ["降序", "升序"])

        # 查询按钮
        st.markdown("---")
        if st.button("🔍 执行查询", type="primary", use_container_width=True):
            execute_query()

        if st.button("🔄 重置条件", use_container_width=True):
            reset_filters()

        # 导出选项
        st.markdown("#### 📁 数据导出")
        export_format = st.selectbox("导出格式", ["CSV", "Excel", "JSON"])

        if st.button("📁 导出数据", use_container_width=True):
            export_data(export_format)


def render_filters(entity_type: str):
    """渲染筛选条件"""
    filters = SessionManager.get("entity_filters", {})

    if entity_type == "products":
        # 产品筛选条件
        filters["name"] = st.text_input("产品名称包含", value=filters.get("name", ""))
        filters["category_id"] = st.text_input(
            "分类ID", value=filters.get("category_id", "")
        )
        filters["brand_id"] = st.text_input("品牌ID", value=filters.get("brand_id", ""))
        filters["status"] = st.selectbox(
            "产品状态",
            ["全部", "正常", "停用"],
            index=["全部", "正常", "停用"].index(filters.get("status", "全部")),
        )

        # 数值范围筛选
        col1, col2 = st.columns(2)
        with col1:
            filters["min_price"] = st.number_input(
                "最低价格", min_value=0.0, value=filters.get("min_price", 0.0)
            )
        with col2:
            filters["max_price"] = st.number_input(
                "最高价格", min_value=0.0, value=filters.get("max_price", 0.0)
            )

        # 时间范围筛选
        filters["date_range"] = st.date_input(
            "创建时间范围", value=filters.get("date_range", [])
        )

    elif entity_type == "orders":
        # 订单筛选条件
        filters["order_id"] = st.text_input("订单ID", value=filters.get("order_id", ""))
        filters["customer_id"] = st.text_input(
            "客户ID", value=filters.get("customer_id", "")
        )
        filters["status"] = st.selectbox(
            "订单状态",
            ["全部", "待付款", "待发货", "已发货", "已完成", "已取消"],
            index=["全部", "待付款", "待发货", "已发货", "已完成", "已取消"].index(
                filters.get("status", "全部")
            ),
        )

        # 金额范围
        col1, col2 = st.columns(2)
        with col1:
            filters["min_amount"] = st.number_input(
                "最低金额", min_value=0.0, value=filters.get("min_amount", 0.0)
            )
        with col2:
            filters["max_amount"] = st.number_input(
                "最高金额", min_value=0.0, value=filters.get("max_amount", 0.0)
            )

    elif entity_type == "customers":
        # 客户筛选条件
        filters["customer_name"] = st.text_input(
            "客户姓名包含", value=filters.get("customer_name", "")
        )
        filters["phone"] = st.text_input("手机号码", value=filters.get("phone", ""))
        filters["email"] = st.text_input("邮箱地址", value=filters.get("email", ""))
        filters["level"] = st.selectbox(
            "客户等级",
            ["全部", "普通", "VIP", "钻石"],
            index=["全部", "普通", "VIP", "钻石"].index(filters.get("level", "全部")),
        )

    else:
        # 通用筛选条件
        filters["name"] = st.text_input("名称包含", value=filters.get("name", ""))
        filters["status"] = st.selectbox(
            "状态",
            ["全部", "正常", "停用"],
            index=["全部", "正常", "停用"].index(filters.get("status", "全部")),
        )

    SessionManager.set("entity_filters", filters)


def render_content():
    """渲染主要内容"""
    # 创建选项卡
    tab1, tab2, tab3, tab4 = st.tabs(
        ["📊 数据表格", "📈 统计分析", "🔍 详细查看", "📋 SQL查询"]
    )

    with tab1:
        render_data_table()

    with tab2:
        render_statistics()

    with tab3:
        render_detail_view()

    with tab4:
        render_sql_query()


def render_data_table():
    """渲染数据表格"""
    st.subheader("📊 数据表格")

    selected_entity = SessionManager.get("selected_entity", "products")
    filters = SessionManager.get("entity_filters", {})

    # 显示当前查询条件
    if filters:
        filter_texts = []
        for key, value in filters.items():
            if value and value != "全部" and value != 0.0:
                filter_texts.append(f"{get_field_name(key)}: {value}")

        if filter_texts:
            st.caption(f"筛选条件: {', '.join(filter_texts)}")

    # 执行查询
    results = query_data(selected_entity, filters)

    if results["success"] and results["data"]:
        data = results["data"]
        total_count = results["total_count"]

        # 创建DataFrame
        df = pd.DataFrame(data)

        # 格式化数据显示
        df = format_dataframe(df, selected_entity)

        # 显示分页信息
        page_size = SessionManager.get("page_size", 20)
        current_page = SessionManager.get("current_page", 1)
        total_pages = (total_count + page_size - 1) // page_size

        col1, col2, col3 = st.columns([1, 2, 1])

        with col1:
            if current_page > 1:
                if st.button("⬅️ 上一页"):
                    SessionManager.set("current_page", current_page - 1)
                    st.rerun()

        with col2:
            st.markdown(
                f"第 {current_page} 页，共 {total_pages} 页 (总计 {total_count:,} 条记录)"
            )

        with col3:
            if current_page < total_pages:
                if st.button("下一页 ➡️"):
                    SessionManager.set("current_page", current_page + 1)
                    st.rerun()

        # 显示数据表格
        st.dataframe(
            df,
            use_container_width=True,
            column_config=get_column_config(selected_entity),
        )

        # 选择记录查看详情
        if not df.empty:
            selected_id = st.selectbox(
                "选择记录ID查看详情",
                df.iloc[:, 0].tolist(),  # 假设第一列是ID
                key="selected_record_id",
            )

            if st.button("🔍 查看详情"):
                SessionManager.set("selected_record", selected_id)
                st.rerun()

    elif results["success"] and not results["data"]:
        st.info(f"没有找到符合条件的{get_entity_name(selected_entity)}数据")

    else:
        st.error(f"查询失败: {results.get('error', '未知错误')}")


def render_statistics():
    """渲染统计分析"""
    st.subheader("📈 统计分析")

    selected_entity = SessionManager.get("selected_entity", "products")

    # 获取统计信息
    stats = get_entity_statistics(selected_entity)

    if stats:
        # 基本统计指标
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("总记录数", f"{stats.get('total_count', 0):,}")

        with col2:
            st.metric("活跃记录", f"{stats.get('active_count', 0):,}")

        with col3:
            st.metric("本月新增", f"{stats.get('monthly_new', 0):,}")

        with col4:
            st.metric("增长率", f"{stats.get('growth_rate', 0):+.1f}%")

        # 图表分析
        if selected_entity == "products":
            render_product_charts(stats)
        elif selected_entity == "orders":
            render_order_charts(stats)
        elif selected_entity == "customers":
            render_customer_charts(stats)
        else:
            render_general_charts(stats)

    else:
        st.info("暂无统计数据")


def render_detail_view():
    """渲染详细查看"""
    st.subheader("🔍 详细查看")

    selected_record = SessionManager.get("selected_record")
    selected_entity = SessionManager.get("selected_entity", "products")

    if selected_record:
        # 获取记录详情
        detail = get_record_detail(selected_entity, selected_record)

        if detail:
            # 显示详细信息
            st.json(detail)

            # 如果是产品，显示相关附件
            if selected_entity == "products":
                render_product_attachments(selected_record)

        else:
            st.error("未找到记录详情")

    else:
        st.info("请在数据表格中选择一条记录查看详情")


def render_sql_query():
    """渲染SQL查询"""
    st.subheader("📋 自定义SQL查询")

    st.warning("⚠️ 请谨慎使用SQL查询功能，只允许SELECT语句")

    # SQL输入框
    sql_query = st.text_area(
        "输入SQL查询语句", height=150, placeholder="SELECT * FROM products LIMIT 10;"
    )

    col1, col2 = st.columns(2)

    with col1:
        if st.button("▶️ 执行查询", type="primary"):
            execute_sql_query(sql_query)

    with col2:
        if st.button("📋 查看表结构"):
            show_table_schema()

    # 显示查询结果
    if "sql_result" in st.session_state:
        result = st.session_state["sql_result"]

        if result["success"]:
            if result["data"]:
                df = pd.DataFrame(result["data"])
                st.dataframe(df, use_container_width=True)
                st.caption(f"查询返回 {len(df)} 条记录")
            else:
                st.info("查询无结果")
        else:
            st.error(f"查询失败: {result['error']}")


def execute_query():
    """执行查询"""
    selected_entity = SessionManager.get("selected_entity", "products")
    filters = SessionManager.get("entity_filters", {})

    # 重置页码
    SessionManager.set("current_page", 1)

    # 执行查询
    results = query_data(selected_entity, filters)

    if results["success"]:
        st.success(f"查询完成，找到 {results['total_count']} 条记录")
    else:
        st.error(f"查询失败: {results.get('error', '未知错误')}")


def reset_filters():
    """重置筛选条件"""
    SessionManager.set("entity_filters", {})
    SessionManager.set("current_page", 1)
    st.success("筛选条件已重置")
    st.rerun()


def export_data(format: str):
    """导出数据"""
    selected_entity = SessionManager.get("selected_entity", "products")
    filters = SessionManager.get("entity_filters", {})

    # 获取所有数据（不分页）
    all_filters = filters.copy()
    all_filters["no_pagination"] = True

    results = query_data(selected_entity, all_filters)

    if results["success"] and results["data"]:
        data = results["data"]
        df = pd.DataFrame(data)
        df = format_dataframe(df, selected_entity)

        try:
            if format == "CSV":
                csv = df.to_csv(index=False, encoding="utf-8-sig")
                st.download_button(
                    label="📁 下载CSV文件",
                    data=csv,
                    file_name=f"{selected_entity}_data.csv",
                    mime="text/csv",
                )

            elif format == "Excel":
                # 注意：这需要安装openpyxl
                excel_buffer = df.to_excel(index=False, engine="openpyxl")
                st.download_button(
                    label="📁 下载Excel文件",
                    data=excel_buffer,
                    file_name=f"{selected_entity}_data.xlsx",
                    mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                )

            elif format == "JSON":
                json_data = df.to_json(orient="records", ensure_ascii=False, indent=2)
                st.download_button(
                    label="📁 下载JSON文件",
                    data=json_data,
                    file_name=f"{selected_entity}_data.json",
                    mime="application/json",
                )

            st.success(f"导出 {format} 格式数据成功！")

        except Exception as e:
            st.error(f"导出失败: {str(e)}")

    else:
        st.error("没有数据可以导出")


def query_data(entity_type: str, filters: Dict[str, Any]) -> Dict[str, Any]:
    """查询数据"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 构建基础查询
        base_query = f"SELECT * FROM {entity_type}"
        count_query = f"SELECT COUNT(*) FROM {entity_type}"

        # 构建WHERE条件
        where_conditions = []
        params = []

        # 根据不同实体类型构建筛选条件
        if entity_type == "products":
            if filters.get("name"):
                where_conditions.append("name ILIKE %s")
                params.append(f"%{filters['name']}%")

            if filters.get("category_id"):
                where_conditions.append("category_id = %s")
                params.append(filters["category_id"])

            if filters.get("brand_id"):
                where_conditions.append("brand_id = %s")
                params.append(filters["brand_id"])

            if filters.get("status") and filters["status"] != "全部":
                status_value = "0" if filters["status"] == "正常" else "1"
                where_conditions.append("status = %s")
                params.append(status_value)

            if filters.get("min_price", 0) > 0:
                where_conditions.append("price >= %s")
                params.append(filters["min_price"])

            if filters.get("max_price", 0) > 0:
                where_conditions.append("price <= %s")
                params.append(filters["max_price"])

        elif entity_type == "orders":
            if filters.get("order_id"):
                where_conditions.append("order_id ILIKE %s")
                params.append(f"%{filters['order_id']}%")

            if filters.get("customer_id"):
                where_conditions.append("customer_id = %s")
                params.append(filters["customer_id"])

            if filters.get("status") and filters["status"] != "全部":
                where_conditions.append("status = %s")
                params.append(filters["status"])

        else:
            # 通用筛选
            if filters.get("name"):
                where_conditions.append("name ILIKE %s")
                params.append(f"%{filters['name']}%")

            if filters.get("status") and filters["status"] != "全部":
                status_value = "0" if filters["status"] == "正常" else "1"
                where_conditions.append("status = %s")
                params.append(status_value)

        # 组装WHERE子句
        where_clause = ""
        if where_conditions:
            where_clause = " WHERE " + " AND ".join(where_conditions)

        # 获取总数
        cursor.execute(count_query + where_clause, params)
        total_count = cursor.fetchone()[0]

        # 构建完整查询（包含分页）
        full_query = base_query + where_clause

        # 添加排序
        full_query += " ORDER BY id DESC"

        # 添加分页（如果需要）
        if not filters.get("no_pagination"):
            page_size = SessionManager.get("page_size", 20)
            current_page = SessionManager.get("current_page", 1)
            offset = (current_page - 1) * page_size

            full_query += f" LIMIT {page_size} OFFSET {offset}"

        # 执行查询
        cursor.execute(full_query, params)

        # 获取列名
        columns = [desc[0] for desc in cursor.description]

        # 获取数据
        rows = cursor.fetchall()
        data = [dict(zip(columns, row)) for row in rows]

        cursor.close()
        conn.close()

        return {"success": True, "data": data, "total_count": total_count}

    except Exception as e:
        logger.error(f"查询数据失败: {e}")
        return {"success": False, "error": str(e), "data": [], "total_count": 0}


def format_dataframe(df: pd.DataFrame, entity_type: str) -> pd.DataFrame:
    """格式化DataFrame显示"""
    if df.empty:
        return df

    # 通用格式化
    for col in df.columns:
        if col.endswith("_time") or col.endswith("time"):
            df[col] = pd.to_datetime(df[col], errors="coerce").dt.strftime(
                "%Y-%m-%d %H:%M:%S"
            )

    # 根据实体类型特殊格式化
    if entity_type == "products":
        if "status" in df.columns:
            df["status"] = df["status"].apply(
                lambda x: "正常" if str(x) == "0" else "停用"
            )

        # 重命名列
        column_rename = {
            "id": "ID",
            "name": "产品名称",
            "spec": "规格",
            "category_id": "分类ID",
            "brand_id": "品牌ID",
            "price": "价格",
            "status": "状态",
            "create_time": "创建时间",
            "last_sync_time": "同步时间",
        }

        df = df.rename(columns=column_rename)

    elif entity_type == "orders":
        column_rename = {
            "id": "ID",
            "order_id": "订单号",
            "customer_id": "客户ID",
            "total_amount": "总金额",
            "status": "状态",
            "create_time": "创建时间",
        }

        df = df.rename(columns=column_rename)

    return df


def get_column_config(entity_type: str) -> Dict[str, Any]:
    """获取列配置"""
    config = {}

    if entity_type == "products":
        config = {
            "ID": st.column_config.NumberColumn("ID", width="small"),
            "产品名称": st.column_config.TextColumn("产品名称", width="large"),
            "价格": st.column_config.NumberColumn("价格", format="¥%.2f"),
            "状态": st.column_config.TextColumn("状态", width="small"),
        }

    return config


def get_database_status() -> str:
    """获取数据库状态"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT 1")
        cursor.close()
        conn.close()
        return "🟢 正常"
    except:
        return "🔴 异常"


def get_total_tables() -> int:
    """获取数据表总数"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute(
            """
            SELECT COUNT(*) FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
        """
        )
        count = cursor.fetchone()[0]
        cursor.close()
        conn.close()
        return count
    except:
        return 0


def get_total_records() -> int:
    """获取总记录数"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        tables = ["products", "categories", "brands", "orders", "customers"]
        total = 0

        for table in tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                total += count
            except:
                continue

        cursor.close()
        conn.close()
        return total
    except:
        return 0


def get_last_sync_time() -> str:
    """获取最后同步时间"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute(
            """
            SELECT MAX(last_sync_time) FROM (
                SELECT MAX(last_sync_time) as last_sync_time FROM products
                UNION ALL
                SELECT MAX(last_sync_time) as last_sync_time FROM categories
                UNION ALL
                SELECT MAX(last_sync_time) as last_sync_time FROM brands
            ) t
        """
        )
        result = cursor.fetchone()[0]
        cursor.close()
        conn.close()

        if result:
            return result.strftime("%m-%d %H:%M")
        else:
            return "未同步"
    except:
        return "未知"


def get_sort_fields(entity_type: str) -> List[str]:
    """获取排序字段"""
    common_fields = ["id", "create_time", "last_sync_time"]

    if entity_type == "products":
        return common_fields + ["name", "price", "category_id"]
    elif entity_type == "orders":
        return common_fields + ["order_id", "total_amount", "customer_id"]
    else:
        return common_fields + ["name"]


def get_entity_statistics(entity_type: str) -> Optional[Dict[str, Any]]:
    """获取实体统计信息"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 基础统计
        cursor.execute(f"SELECT COUNT(*) FROM {entity_type}")
        total_count = cursor.fetchone()[0]

        # 活跃数量（状态为0的记录）
        try:
            cursor.execute(f"SELECT COUNT(*) FROM {entity_type} WHERE status = '0'")
            active_count = cursor.fetchone()[0]
        except:
            active_count = total_count

        # 本月新增
        cursor.execute(
            f"""
            SELECT COUNT(*) FROM {entity_type} 
            WHERE create_time >= date_trunc('month', CURRENT_DATE)
        """
        )
        monthly_new = cursor.fetchone()[0]

        cursor.close()
        conn.close()

        return {
            "total_count": total_count,
            "active_count": active_count,
            "monthly_new": monthly_new,
            "growth_rate": (monthly_new / max(total_count - monthly_new, 1)) * 100,
        }

    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        return None


def render_product_charts(stats: Dict[str, Any]):
    """渲染产品图表"""
    if not stats:
        st.info("暂无产品数据可展示")
        return
        
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 产品分类分布图
        cursor.execute("""
            SELECT c.name as category_name, COUNT(p.id) as product_count
            FROM categories c
            LEFT JOIN products p ON c.id = p.category_id
            GROUP BY c.id, c.name
            ORDER BY product_count DESC
            LIMIT 10
        """)
        
        category_data = cursor.fetchall()
        if category_data:
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader("📊 产品分类分布")
                category_df = pd.DataFrame(category_data, columns=['分类', '产品数量'])
                st.bar_chart(category_df.set_index('分类'))
            
            with col2:
                st.subheader("🥧 分类占比")
                # 使用plotly创建饼图
                import plotly.express as px
                fig = px.pie(category_df, values='产品数量', names='分类', title="产品分类占比")
                st.plotly_chart(fig, use_container_width=True)
        
        # 产品价格分布
        cursor.execute("""
            SELECT 
                CASE 
                    WHEN price < 100 THEN '<100'
                    WHEN price < 500 THEN '100-500'
                    WHEN price < 1000 THEN '500-1000'
                    WHEN price < 5000 THEN '1000-5000'
                    ELSE '>5000'
                END as price_range,
                COUNT(*) as count
            FROM products 
            WHERE price IS NOT NULL
            GROUP BY 
                CASE 
                    WHEN price < 100 THEN '<100'
                    WHEN price < 500 THEN '100-500'
                    WHEN price < 1000 THEN '500-1000'
                    WHEN price < 5000 THEN '1000-5000'
                    ELSE '>5000'
                END
            ORDER BY 
                CASE 
                    WHEN price < 100 THEN 1
                    WHEN price < 500 THEN 2
                    WHEN price < 1000 THEN 3
                    WHEN price < 5000 THEN 4
                    ELSE 5
                END
        """)
        
        price_data = cursor.fetchall()
        if price_data:
            st.subheader("💰 价格分布")
            price_df = pd.DataFrame(price_data, columns=['价格区间', '产品数量'])
            st.bar_chart(price_df.set_index('价格区间'))
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        logger.error(f"渲染产品图表失败: {e}")
        st.error(f"图表加载失败: {str(e)}")


def render_order_charts(stats: Dict[str, Any]):
    """渲染订单图表"""
    if not stats:
        st.info("暂无订单数据可展示")
        return
        
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 订单状态分布
        cursor.execute("""
            SELECT status, COUNT(*) as count
            FROM orders
            GROUP BY status
            ORDER BY count DESC
        """)
        
        status_data = cursor.fetchall()
        if status_data:
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader("📈 订单状态分布")
                status_df = pd.DataFrame(status_data, columns=['订单状态', '数量'])
                st.bar_chart(status_df.set_index('订单状态'))
            
            with col2:
                st.subheader("🔄 状态占比")
                import plotly.express as px
                fig = px.pie(status_df, values='数量', names='订单状态', title="订单状态占比")
                st.plotly_chart(fig, use_container_width=True)
        
        # 近30天订单趋势
        cursor.execute("""
            SELECT DATE(created_at) as order_date, COUNT(*) as daily_orders
            FROM orders
            WHERE created_at >= NOW() - INTERVAL '30 days'
            GROUP BY DATE(created_at)
            ORDER BY order_date
        """)
        
        trend_data = cursor.fetchall()
        if trend_data:
            st.subheader("📅 近30天订单趋势")
            trend_df = pd.DataFrame(trend_data, columns=['日期', '订单数量'])
            st.line_chart(trend_df.set_index('日期'))
        
        # 订单金额分布
        cursor.execute("""
            SELECT 
                CASE 
                    WHEN total_amount < 100 THEN '<100'
                    WHEN total_amount < 500 THEN '100-500'
                    WHEN total_amount < 1000 THEN '500-1000'
                    WHEN total_amount < 5000 THEN '1000-5000'
                    ELSE '>5000'
                END as amount_range,
                COUNT(*) as count
            FROM orders 
            WHERE total_amount IS NOT NULL
            GROUP BY 
                CASE 
                    WHEN total_amount < 100 THEN '<100'
                    WHEN total_amount < 500 THEN '100-500'
                    WHEN total_amount < 1000 THEN '500-1000'
                    WHEN total_amount < 5000 THEN '1000-5000'
                    ELSE '>5000'
                END
        """)
        
        amount_data = cursor.fetchall()
        if amount_data:
            st.subheader("💵 订单金额分布")
            amount_df = pd.DataFrame(amount_data, columns=['金额区间', '订单数量'])
            st.bar_chart(amount_df.set_index('金额区间'))
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        logger.error(f"渲染订单图表失败: {e}")
        st.error(f"图表加载失败: {str(e)}")


def render_customer_charts(stats: Dict[str, Any]):
    """渲染客户图表"""
    if not stats:
        st.info("暂无客户数据可展示")
        return
        
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 客户等级分布
        cursor.execute("""
            SELECT level, COUNT(*) as count
            FROM customers
            WHERE level IS NOT NULL
            GROUP BY level
            ORDER BY count DESC
        """)
        
        level_data = cursor.fetchall()
        if level_data:
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader("🏆 客户等级分布")
                level_df = pd.DataFrame(level_data, columns=['客户等级', '数量'])
                st.bar_chart(level_df.set_index('客户等级'))
            
            with col2:
                st.subheader("👑 等级占比")
                import plotly.express as px
                fig = px.pie(level_df, values='数量', names='客户等级', title="客户等级占比")
                st.plotly_chart(fig, use_container_width=True)
        
        # 客户注册趋势
        cursor.execute("""
            SELECT DATE(created_at) as reg_date, COUNT(*) as daily_regs
            FROM customers
            WHERE created_at >= NOW() - INTERVAL '30 days'
            GROUP BY DATE(created_at)
            ORDER BY reg_date
        """)
        
        reg_data = cursor.fetchall()
        if reg_data:
            st.subheader("📈 近30天客户注册趋势")
            reg_df = pd.DataFrame(reg_data, columns=['日期', '注册数量'])
            st.line_chart(reg_df.set_index('日期'))
        
        # 客户订单数分布
        cursor.execute("""
            SELECT 
                CASE 
                    WHEN order_count = 0 THEN '无订单'
                    WHEN order_count = 1 THEN '1单'
                    WHEN order_count <= 5 THEN '2-5单'
                    WHEN order_count <= 10 THEN '6-10单'
                    ELSE '>10单'
                END as order_range,
                COUNT(*) as customer_count
            FROM (
                SELECT c.id, COUNT(o.id) as order_count
                FROM customers c
                LEFT JOIN orders o ON c.id = o.customer_id
                GROUP BY c.id
            ) customer_orders
            GROUP BY 
                CASE 
                    WHEN order_count = 0 THEN '无订单'
                    WHEN order_count = 1 THEN '1单'
                    WHEN order_count <= 5 THEN '2-5单'
                    WHEN order_count <= 10 THEN '6-10单'
                    ELSE '>10单'
                END
        """)
        
        order_dist_data = cursor.fetchall()
        if order_dist_data:
            st.subheader("🛒 客户订单数分布")
            order_dist_df = pd.DataFrame(order_dist_data, columns=['订单数范围', '客户数量'])
            st.bar_chart(order_dist_df.set_index('订单数范围'))
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        logger.error(f"渲染客户图表失败: {e}")
        st.error(f"图表加载失败: {str(e)}")


def render_general_charts(stats: Dict[str, Any]):
    """渲染通用图表"""
    if not stats:
        st.info("暂无数据可展示")
        return
        
    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        
        # 数据表记录数统计
        tables = ['products', 'categories', 'brands', 'suppliers', 'orders', 'customers']
        table_names = ['产品', '分类', '品牌', '供应商', '订单', '客户']
        table_stats = []
        
        for table, name in zip(tables, table_names):
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0] or 0
                table_stats.append({'表名': name, '记录数': count})
            except Exception as e:
                logger.warning(f"获取{name}表统计失败: {e}")
                table_stats.append({'表名': name, '记录数': 0})
        
        if table_stats:
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader("📊 数据表记录统计")
                table_df = pd.DataFrame(table_stats)
                st.bar_chart(table_df.set_index('表名'))
            
            with col2:
                st.subheader("📈 数据分布")
                import plotly.express as px
                fig = px.pie(table_df, values='记录数', names='表名', title="数据分布")
                st.plotly_chart(fig, use_container_width=True)
        
        # 数据增长趋势（最近7天）
        cursor.execute("""
            SELECT date_part, table_name, daily_count
            FROM (
                SELECT DATE(created_at) as date_part, 'products' as table_name, COUNT(*) as daily_count
                FROM products
                WHERE created_at >= NOW() - INTERVAL '7 days'
                GROUP BY DATE(created_at)
                
                UNION ALL
                
                SELECT DATE(created_at) as date_part, 'orders' as table_name, COUNT(*) as daily_count
                FROM orders
                WHERE created_at >= NOW() - INTERVAL '7 days'
                GROUP BY DATE(created_at)
                
                UNION ALL
                
                SELECT DATE(created_at) as date_part, 'customers' as table_name, COUNT(*) as daily_count
                FROM customers
                WHERE created_at >= NOW() - INTERVAL '7 days'
                GROUP BY DATE(created_at)
            ) combined_data
            ORDER BY date_part, table_name
        """)
        
        growth_data = cursor.fetchall()
        if growth_data:
            st.subheader("📈 近7天数据增长趋势")
            growth_df = pd.DataFrame(growth_data, columns=['日期', '数据表', '新增数量'])
            
            # 透视表格式以便于图表展示
            pivot_df = growth_df.pivot(index='日期', columns='数据表', values='新增数量').fillna(0)
            st.line_chart(pivot_df)
        
        # 数据质量指标
        quality_stats = []
        
        # 产品数据质量
        cursor.execute("SELECT COUNT(*) FROM products WHERE name IS NOT NULL AND name != ''")
        valid_products = cursor.fetchone()[0] or 0
        cursor.execute("SELECT COUNT(*) FROM products")
        total_products = cursor.fetchone()[0] or 1
        product_quality = (valid_products / total_products) * 100
        
        quality_stats.append({'数据类型': '产品数据', '完整度(%)': round(product_quality, 1)})
        
        # 订单数据质量
        cursor.execute("SELECT COUNT(*) FROM orders WHERE customer_id IS NOT NULL AND total_amount > 0")
        valid_orders = cursor.fetchone()[0] or 0
        cursor.execute("SELECT COUNT(*) FROM orders")
        total_orders = cursor.fetchone()[0] or 1
        order_quality = (valid_orders / total_orders) * 100
        
        quality_stats.append({'数据类型': '订单数据', '完整度(%)': round(order_quality, 1)})
        
        if quality_stats:
            st.subheader("✅ 数据质量指标")
            quality_df = pd.DataFrame(quality_stats)
            st.bar_chart(quality_df.set_index('数据类型'))
        
        cursor.close()
        conn.close()
        
    except Exception as e:
        logger.error(f"渲染通用图表失败: {e}")
        st.error(f"图表加载失败: {str(e)}")


def get_record_detail(entity_type: str, record_id: str) -> Optional[Dict[str, Any]]:
    """获取记录详情"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute(f"SELECT * FROM {entity_type} WHERE id = %s", (record_id,))

        columns = [desc[0] for desc in cursor.description]
        row = cursor.fetchone()

        cursor.close()
        conn.close()

        if row:
            return dict(zip(columns, row))
        else:
            return None

    except Exception as e:
        logger.error(f"获取记录详情失败: {e}")
        return None


def render_product_attachments(product_id: str):
    """渲染产品附件"""
    st.subheader("📎 相关附件")

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute(
            """
            SELECT file_name, file_size, file_type, download_url, local_path
            FROM attachments 
            WHERE item_id = %s AND item_type = 'product'
        """,
            (product_id,),
        )

        attachments = cursor.fetchall()
        cursor.close()
        conn.close()

        if attachments:
            for attachment in attachments:
                col1, col2, col3 = st.columns([2, 1, 1])

                with col1:
                    st.text(attachment[0])  # file_name

                with col2:
                    file_size = attachment[1] / 1024 / 1024 if attachment[1] else 0
                    st.text(f"{file_size:.2f} MB")

                with col3:
                    if attachment[4]:  # local_path
                        st.success("已下载")
                    else:
                        st.warning("未下载")
        else:
            st.info("暂无附件")

    except Exception as e:
        st.error(f"获取附件失败: {str(e)}")


def execute_sql_query(sql: str):
    """执行SQL查询"""
    if not sql.strip():
        st.warning("请输入SQL查询语句")
        return

    # 安全检查：只允许SELECT语句
    sql_lower = sql.strip().lower()
    if not sql_lower.startswith("select"):
        st.error("只允许执行SELECT查询语句")
        return

    # 检查是否包含危险关键词
    dangerous_keywords = [
        "delete",
        "update",
        "insert",
        "drop",
        "create",
        "alter",
        "truncate",
    ]
    if any(keyword in sql_lower for keyword in dangerous_keywords):
        st.error("SQL语句包含不允许的关键词")
        return

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        cursor.execute(sql)

        if cursor.description:
            columns = [desc[0] for desc in cursor.description]
            rows = cursor.fetchall()
            data = [dict(zip(columns, row)) for row in rows]

            st.session_state["sql_result"] = {"success": True, "data": data}
        else:
            st.session_state["sql_result"] = {"success": True, "data": []}

        cursor.close()
        conn.close()

    except Exception as e:
        st.session_state["sql_result"] = {"success": False, "error": str(e)}


def show_table_schema():
    """显示表结构"""
    st.subheader("📋 数据库表结构")

    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 获取所有表
        cursor.execute(
            """
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
            ORDER BY table_name
        """
        )

        tables = [row[0] for row in cursor.fetchall()]

        selected_table = st.selectbox("选择表", tables)

        if selected_table:
            # 获取表结构
            cursor.execute(
                """
                SELECT column_name, data_type, is_nullable, column_default
                FROM information_schema.columns
                WHERE table_name = %s
                ORDER BY ordinal_position
            """,
                (selected_table,),
            )

            schema = cursor.fetchall()

            if schema:
                schema_df = pd.DataFrame(
                    schema, columns=["列名", "数据类型", "可为空", "默认值"]
                )
                st.dataframe(schema_df, use_container_width=True)

        cursor.close()
        conn.close()

    except Exception as e:
        st.error(f"获取表结构失败: {str(e)}")


def get_field_name(field: str) -> str:
    """获取字段显示名称"""
    field_names = {
        "name": "名称",
        "category_id": "分类ID",
        "brand_id": "品牌ID",
        "status": "状态",
        "min_price": "最低价格",
        "max_price": "最高价格",
        "order_id": "订单ID",
        "customer_id": "客户ID",
        "min_amount": "最低金额",
        "max_amount": "最高金额",
        "customer_name": "客户姓名",
        "phone": "手机号码",
        "email": "邮箱地址",
        "level": "客户等级",
    }
    return field_names.get(field, field)


def get_entity_name(entity_type: str) -> str:
    """获取实体类型显示名称"""
    entity_names = {
        "products": "产品",
        "categories": "分类",
        "brands": "品牌",
        "suppliers": "供应商",
        "orders": "订单",
        "customers": "客户",
        "inventory": "库存",
        "prices": "价格",
        "promotions": "促销",
        "reviews": "评价",
        "attachments": "附件",
        "sync_status": "同步状态",
    }
    return entity_names.get(entity_type, entity_type)


def render_footer():
    """渲染页面脚部"""
    st.markdown("---")
    st.markdown("© 2024 云商系统 - 数据查询模块")


if __name__ == "__main__":
    main()
