# 云商系统数据模型设计文档

## 📋 项目概述

基于熵基云商接口对接说明文档v1.0，设计适配Streamlit项目的数据模型和系统架构。

**测试环境地址**: https://zkmall.zktecoiot.com

## 🎯 核心业务分析

### 接口功能清单
1. **认证系统** - 登录获取token，支持多站点
2. **产品管理** - 产品列表、分类、标签、规格管理
3. **案例管理** - 产品关联案例，地区和公司信息
4. **方案管理** - 产品关联方案，软硬件清单
5. **资讯管理** - 产品关联资讯，多媒体内容
6. **配单管理** - 配单列表，分类和价格管理

### 业务流程图

```mermaid
graph TD
    A[用户登录] --> B[获取Token]
    B --> C{选择功能}
    C -->|产品| D[产品管理]
    C -->|案例| E[案例管理] 
    C -->|方案| F[方案管理]
    C -->|资讯| G[资讯管理]
    C -->|配单| H[配单管理]
    
    D --> I[产品列表]
    D --> J[产品详情]
    D --> K[产品分类]
    
    E --> L[案例列表]
    E --> M[案例详情]
    
    F --> N[方案列表]
    F --> O[方案详情]
    F --> P[软硬件清单]
    
    G --> Q[资讯列表]
    G --> R[资讯详情]
    
    H --> S[配单列表]
    H --> T[配单详情]
```

## 🗄️ 数据模型设计

### 1. 用户认证模块

#### Users - 用户表
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    real_name VARCHAR(100),
    phone VARCHAR(20),
    company_id INTEGER,
    site_id INTEGER DEFAULT 999,
    status VARCHAR(10) DEFAULT '0', -- 0正常 1停用
    role VARCHAR(50) DEFAULT 'user',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(100),
    update_by VARCHAR(100),
    last_login TIMESTAMP
);
```

#### Auth_Tokens - 认证令牌表
```sql
CREATE TABLE auth_tokens (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    token VARCHAR(500) NOT NULL,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT true,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used TIMESTAMP
);
```

### 2. 产品管理模块

#### Products - 产品表
```sql
CREATE TABLE products (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    spec VARCHAR(100), -- 产品规格如ZK3969
    introduction TEXT, -- 产品介绍
    details TEXT, -- 详情富文本
    small_img VARCHAR(500), -- 小图
    banner VARCHAR(500), -- 轮播图
    category_id INTEGER, -- 分类ID
    label_id INTEGER, -- 标签ID
    attribute VARCHAR(10) DEFAULT '0', -- 产品属性
    show_for TEXT, -- 指定可见群体
    param_info TEXT, -- 参数表格
    use_to TEXT, -- 适用场景
    qualifications VARCHAR(500), -- 资质文件
    instructions TEXT, -- 说明书IDs
    other TEXT, -- 附件地址
    guide TEXT, -- 操作指南
    common_problem TEXT, -- 常见问题ID
    price DECIMAL(10,2) DEFAULT 0,
    is_suggest VARCHAR(10) DEFAULT '0', -- 是否推荐
    is_hot VARCHAR(10) DEFAULT '0', -- 是否热门
    is_new VARCHAR(10) DEFAULT '0', -- 是否新品
    count INTEGER DEFAULT 0, -- 点击量
    like_count INTEGER DEFAULT 0, -- 点赞数
    favorite_count INTEGER DEFAULT 0, -- 收藏数
    status VARCHAR(10) DEFAULT '0', -- 0正常 1停用
    site_id INTEGER DEFAULT 999,
    brand_id INTEGER, -- 品牌ID
    show_time TIMESTAMP,
    sort INTEGER DEFAULT 0,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(100),
    update_by VARCHAR(100)
);
```

#### Categories - 分类表
```sql
CREATE TABLE categories (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    parent_id INTEGER REFERENCES categories(id),
    level INTEGER DEFAULT 1, -- 分类层级
    sort INTEGER DEFAULT 0,
    status VARCHAR(10) DEFAULT '0',
    site_id INTEGER DEFAULT 999,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(100),
    update_by VARCHAR(100)
);
```

#### Labels - 标签表
```sql
CREATE TABLE labels (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    show_day INTEGER, -- 显示天数
    sort INTEGER DEFAULT 0,
    status VARCHAR(10) DEFAULT '0',
    site_id INTEGER DEFAULT 999,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(100),
    update_by VARCHAR(100)
);
```

#### Brands - 品牌表
```sql
CREATE TABLE brands (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    logo VARCHAR(500),
    description TEXT,
    status VARCHAR(10) DEFAULT '0',
    site_id INTEGER DEFAULT 999,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(100),
    update_by VARCHAR(100)
);
```

### 3. 案例管理模块

#### Cases - 案例表
```sql
CREATE TABLE cases (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    keywords VARCHAR(500), -- 关键词
    introduction TEXT, -- 简介
    content TEXT, -- 内容富文本
    img VARCHAR(500), -- 案例图片
    banner VARCHAR(500), -- 轮播图
    small_img VARCHAR(500), -- 小图
    video VARCHAR(500), -- 视频地址
    category_id INTEGER, -- 分类ID
    publish_name VARCHAR(100), -- 发布人
    real_name VARCHAR(100),
    publish_id INTEGER, -- 发布人ID
    company_id INTEGER, -- 公司ID
    company_name VARCHAR(255), -- 公司名称
    province VARCHAR(100), -- 省份
    city VARCHAR(100), -- 城市
    county VARCHAR(100), -- 区县
    count INTEGER DEFAULT 0, -- 浏览量
    like_count INTEGER DEFAULT 0, -- 点赞数
    favorite_count INTEGER DEFAULT 0, -- 收藏数
    approve_status VARCHAR(10) DEFAULT '0', -- 审核状态
    is_suggest VARCHAR(10) DEFAULT '0', -- 是否推荐
    is_auth INTEGER DEFAULT 0, -- 是否认证
    is_push INTEGER DEFAULT 0, -- 是否推送
    is_share INTEGER DEFAULT 1, -- 是否共享
    parent_id INTEGER DEFAULT -1,
    top VARCHAR(10) DEFAULT '0', -- 是否置顶
    status VARCHAR(10) DEFAULT '0',
    site_id INTEGER DEFAULT 999,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(100),
    update_by VARCHAR(100)
);
```

#### Product_Cases - 产品案例关联表
```sql
CREATE TABLE product_cases (
    id SERIAL PRIMARY KEY,
    product_id INTEGER REFERENCES products(id),
    case_id INTEGER REFERENCES cases(id),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 4. 方案管理模块

#### Programmes - 方案表
```sql
CREATE TABLE programmes (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    introduction TEXT, -- 简介
    content TEXT, -- 内容富文本
    banner VARCHAR(500), -- 轮播图
    small_img VARCHAR(500), -- 小图
    video VARCHAR(500), -- 视频地址
    other TEXT, -- 附件
    category_id INTEGER, -- 分类ID
    category_scene_id INTEGER, -- 场景分类ID
    company_id INTEGER, -- 公司ID
    province VARCHAR(100), -- 省份
    city VARCHAR(100), -- 城市
    county VARCHAR(100), -- 区县
    count INTEGER DEFAULT 0, -- 浏览量
    like_count INTEGER DEFAULT 0, -- 点赞数
    favorite_count INTEGER DEFAULT 0, -- 收藏数
    collect_size INTEGER DEFAULT 0, -- 收藏大小
    is_suggest INTEGER DEFAULT 0, -- 是否推荐
    is_hot VARCHAR(10) DEFAULT '0', -- 是否热门
    is_new INTEGER DEFAULT 0, -- 是否新品
    is_push INTEGER DEFAULT 0, -- 是否推送
    top VARCHAR(10) DEFAULT '0', -- 是否置顶
    type INTEGER DEFAULT 0, -- 类型
    sort INTEGER DEFAULT 0,
    status VARCHAR(10) DEFAULT '0',
    site_id INTEGER DEFAULT 999,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(100),
    update_by VARCHAR(100)
);
```

#### Programme_Products - 方案产品关联表
```sql
CREATE TABLE programme_products (
    id SERIAL PRIMARY KEY,
    programme_id INTEGER REFERENCES programmes(id),
    product_id INTEGER REFERENCES products(id),
    quantity INTEGER DEFAULT 1, -- 数量
    type VARCHAR(20) DEFAULT 'hardware', -- hardware/software/custom
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### Programme_Details - 方案明细表
```sql
CREATE TABLE programme_details (
    id SERIAL PRIMARY KEY,
    programme_id INTEGER REFERENCES programmes(id),
    product_id INTEGER REFERENCES products(id),
    quantity INTEGER DEFAULT 1,
    price DECIMAL(10,2),
    total_price DECIMAL(10,2),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 5. 资讯管理模块

#### Information - 资讯表
```sql
CREATE TABLE information (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    details TEXT, -- 详情富文本
    pic_video VARCHAR(500), -- 图片视频
    small_img VARCHAR(500), -- 小图
    images TEXT, -- 图片集
    other_url VARCHAR(500), -- 附件地址
    video_url VARCHAR(500), -- 视频地址
    category_id INTEGER, -- 分类ID
    product_id INTEGER, -- 关联产品ID
    belong_id INTEGER, -- 归属ID
    is_hot INTEGER DEFAULT 0, -- 是否热门
    is_suggest VARCHAR(10) DEFAULT '0', -- 是否推荐
    show_type INTEGER DEFAULT 0, -- 显示类型
    top VARCHAR(50) DEFAULT '0', -- 是否置顶/头条
    watch INTEGER DEFAULT 0, -- 浏览量
    like_count INTEGER DEFAULT 0, -- 点赞数
    favorite_count INTEGER DEFAULT 0, -- 收藏数
    status VARCHAR(10) DEFAULT '0',
    site_id INTEGER DEFAULT 999,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(100),
    update_by VARCHAR(100)
);
```

### 6. 配单管理模块

#### Distribution_Orders - 配单表
```sql
CREATE TABLE distribution_orders (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    customer_name VARCHAR(255), -- 客户名称
    phone VARCHAR(20), -- 联系电话
    contacts VARCHAR(255), -- 联系人
    company_id INTEGER, -- 公司ID
    user_id INTEGER REFERENCES users(id),
    price DECIMAL(12,2), -- 总价
    hide_price INTEGER DEFAULT 0, -- 是否隐藏价格
    fir_category_id INTEGER, -- 一级分类ID
    sec_category_id INTEGER, -- 二级分类ID
    category_scene_id INTEGER, -- 场景分类ID
    source_type INTEGER DEFAULT 0, -- 0产品配单 1方案配单
    from_user_id INTEGER DEFAULT -1, -- 来源用户ID
    from_user_name VARCHAR(100), -- 来源用户名
    platform_order_id VARCHAR(100), -- 平台订单ID
    like_count INTEGER DEFAULT 0, -- 点赞数
    favorite_count INTEGER DEFAULT 0, -- 收藏数
    has_expire BOOLEAN DEFAULT false, -- 是否有下架产品
    other TEXT, -- 其他信息
    details TEXT, -- 详情
    status VARCHAR(10) DEFAULT '0',
    site_id INTEGER DEFAULT 999,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(100),
    update_by VARCHAR(100)
);
```

#### Distribution_Order_Items - 配单明细表
```sql
CREATE TABLE distribution_order_items (
    id SERIAL PRIMARY KEY,
    order_id INTEGER REFERENCES distribution_orders(id),
    product_id INTEGER REFERENCES products(id),
    quantity INTEGER DEFAULT 1,
    unit_price DECIMAL(10,2),
    total_price DECIMAL(10,2),
    type VARCHAR(20) DEFAULT 'product', -- product/custom
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 7. 系统支撑模块

#### Companies - 公司表
```sql
CREATE TABLE companies (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(100) UNIQUE,
    type VARCHAR(50), -- 公司类型
    parent_id INTEGER REFERENCES companies(id),
    province VARCHAR(100),
    city VARCHAR(100),
    county VARCHAR(100),
    address TEXT,
    contact_person VARCHAR(100),
    contact_phone VARCHAR(20),
    contact_email VARCHAR(255),
    status VARCHAR(10) DEFAULT '0',
    site_id INTEGER DEFAULT 999,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(100),
    update_by VARCHAR(100)
);
```

#### Sites - 站点表
```sql
CREATE TABLE sites (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(100) UNIQUE,
    domain VARCHAR(255),
    region VARCHAR(100), -- 地区：中国、欧洲等
    api_base_url VARCHAR(500),
    status VARCHAR(10) DEFAULT '0',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(100),
    update_by VARCHAR(100)
);
```

#### User_Interactions - 用户交互表
```sql
CREATE TABLE user_interactions (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    target_type VARCHAR(50) NOT NULL, -- product/case/programme/information
    target_id INTEGER NOT NULL,
    interaction_type VARCHAR(20) NOT NULL, -- view/like/favorite
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

#### System_Config - 系统配置表
```sql
CREATE TABLE system_config (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT,
    config_type VARCHAR(50), -- string/integer/boolean/json
    description TEXT,
    site_id INTEGER DEFAULT 999,
    status VARCHAR(10) DEFAULT '0',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(100),
    update_by VARCHAR(100)
);
```

## 📊 数据关系图

```mermaid
erDiagram
    Users ||--o{ Auth_Tokens : has
    Users }o--|| Companies : belongs_to
    Users ||--o{ User_Interactions : creates
    
    Products ||--o{ Product_Cases : has
    Products }o--|| Categories : belongs_to
    Products }o--|| Labels : has
    Products }o--|| Brands : belongs_to
    
    Cases ||--o{ Product_Cases : belongs_to
    Cases }o--|| Categories : belongs_to
    
    Programmes ||--o{ Programme_Products : contains
    Programmes ||--o{ Programme_Details : has
    Programmes }o--|| Categories : belongs_to
    
    Information }o--|| Categories : belongs_to
    Information }o--|| Products : relates_to
    
    Distribution_Orders ||--o{ Distribution_Order_Items : contains
    Distribution_Orders }o--|| Users : created_by
    Distribution_Orders }o--|| Companies : belongs_to
    Distribution_Orders }o--|| Categories : classified_by
    
    Companies ||--o{ Users : employs
    Sites ||--o{ Users : hosts
    Sites ||--o{ System_Config : configures
```

## 🔧 技术实现规范

### 数据库设计原则
1. **多租户支持**: 通过site_id实现多站点数据隔离
2. **软删除**: 使用status字段而非物理删除
3. **审计追踪**: create_by, update_by, timestamps
4. **用户交互**: 统一的点赞、收藏、浏览计数
5. **关联完整性**: 外键约束保证数据一致性

### 索引策略
```sql
-- 核心业务索引
CREATE INDEX idx_products_category ON products(category_id);
CREATE INDEX idx_products_status ON products(status);
CREATE INDEX idx_products_site ON products(site_id);

-- 用户交互索引
CREATE INDEX idx_user_interactions_user ON user_interactions(user_id);
CREATE INDEX idx_user_interactions_target ON user_interactions(target_type, target_id);

-- 时间相关索引
CREATE INDEX idx_products_create_time ON products(create_time);
CREATE INDEX idx_cases_create_time ON cases(create_time);
```

### 数据验证规则
1. **必填字段验证**: name, username等关键字段
2. **状态值验证**: status只能是'0'或'1'
3. **外键完整性**: 确保关联数据存在
4. **业务逻辑验证**: 价格非负、数量大于0等
5. **数据格式验证**: 邮箱、电话号码格式

## 📝 使用说明

### 数据初始化
1. 创建基础站点数据
2. 设置默认分类和标签
3. 初始化系统配置
4. 创建管理员账户

### API集成
1. 使用token认证机制
2. 实现数据同步策略
3. 错误处理和重试机制
4. 数据缓存优化

### 维护建议
1. 定期清理过期token
2. 统计数据定期归档
3. 监控数据库性能
4. 备份策略制定

---

**文档版本**: v1.0  
**最后更新**: 2024-12-19  
**维护者**: 开发团队 