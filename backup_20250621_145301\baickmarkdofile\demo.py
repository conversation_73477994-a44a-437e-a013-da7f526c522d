# Copyright (c) Opendatalab. All rights reserved.
import copy
import json
import os
from pathlib import Path

from loguru import logger

from mineru.cli.common import convert_pdf_bytes_to_bytes_by_pypdfium2, prepare_env, read_fn
from mineru.data.data_reader_writer import FileBasedDataWriter
from mineru.utils.draw_bbox import draw_layout_bbox, draw_span_bbox
from mineru.utils.enum_class import MakeMode
from mineru.backend.vlm.vlm_analyze import doc_analyze as vlm_doc_analyze
from mineru.backend.pipeline.pipeline_analyze import doc_analyze as pipeline_doc_analyze
from mineru.backend.pipeline.pipeline_middle_json_mkcontent import union_make as pipeline_union_make
from mineru.backend.pipeline.model_json_to_middle_json import result_to_middle_json as pipeline_result_to_middle_json
from mineru.backend.vlm.vlm_middle_json_mkcontent import union_make as vlm_union_make
from mineru.utils.models_download_utils import auto_download_and_get_model_root_path


def do_parse(
    output_dir,  # 用于存储解析结果的输出目录
    pdf_file_names: list[str],  # 要解析的PDF文件名列表
    pdf_bytes_list: list[bytes],  # 要解析的PDF字节列表
    p_lang_list: list[str],  # 每个PDF的语言列表，默认为'ch'（中文）
    backend="pipeline",  # 解析PDF的后端，默认为'pipeline'
    parse_method="auto",  # 解析PDF的方法，默认为'auto'
    p_formula_enable=True,  # 启用公式解析
    p_table_enable=True,  # 启用表格解析
    server_url=None,  # vlm-sglang-client后端的服务器URL
    f_draw_layout_bbox=True,  # 是否绘制布局边界框
    f_draw_span_bbox=True,  # 是否绘制跨度边界框
    f_dump_md=True,  # 是否导出markdown文件
    f_dump_middle_json=True,  # 是否导出中间JSON文件
    f_dump_model_output=True,  # 是否导出模型输出文件
    f_dump_orig_pdf=True,  # 是否导出原始PDF文件
    f_dump_content_list=True,  # 是否导出内容列表文件
    f_make_md_mode=MakeMode.MM_MD,  # 制作markdown内容的模式，默认为MM_MD
    start_page_id=0,  # 解析的起始页ID，默认为0
    end_page_id=None,  # 解析的结束页ID，默认为None（解析到文档末尾的所有页面）
):

    if backend == "pipeline":
        for idx, pdf_bytes in enumerate(pdf_bytes_list):
            new_pdf_bytes = convert_pdf_bytes_to_bytes_by_pypdfium2(pdf_bytes, start_page_id, end_page_id)
            pdf_bytes_list[idx] = new_pdf_bytes

        infer_results, all_image_lists, all_pdf_docs, lang_list, ocr_enabled_list = pipeline_doc_analyze(pdf_bytes_list, p_lang_list, parse_method=parse_method, formula_enable=p_formula_enable,table_enable=p_table_enable)

        for idx, model_list in enumerate(infer_results):
            model_json = copy.deepcopy(model_list)
            pdf_file_name = pdf_file_names[idx]
            local_image_dir, local_md_dir = prepare_env(output_dir, pdf_file_name, parse_method)
            image_writer, md_writer = FileBasedDataWriter(local_image_dir), FileBasedDataWriter(local_md_dir)

            images_list = all_image_lists[idx]
            pdf_doc = all_pdf_docs[idx]
            _lang = lang_list[idx]
            _ocr_enable = ocr_enabled_list[idx]
            middle_json = pipeline_result_to_middle_json(model_list, images_list, pdf_doc, image_writer, _lang, _ocr_enable, p_formula_enable)

            pdf_info = middle_json["pdf_info"]

            pdf_bytes = pdf_bytes_list[idx]
            if f_draw_layout_bbox:
                draw_layout_bbox(pdf_info, pdf_bytes, local_md_dir, f"{pdf_file_name}_layout.pdf")

            if f_draw_span_bbox:
                draw_span_bbox(pdf_info, pdf_bytes, local_md_dir, f"{pdf_file_name}_span.pdf")

            if f_dump_orig_pdf:
                md_writer.write(
                    f"{pdf_file_name}_origin.pdf",
                    pdf_bytes,
                )

            if f_dump_md:
                image_dir = str(os.path.basename(local_image_dir))
                md_content_str = pipeline_union_make(pdf_info, f_make_md_mode, image_dir)
                md_writer.write_string(
                    f"{pdf_file_name}.md",
                    md_content_str,
                )

            if f_dump_content_list:
                image_dir = str(os.path.basename(local_image_dir))
                content_list = pipeline_union_make(pdf_info, MakeMode.CONTENT_LIST, image_dir)
                md_writer.write_string(
                    f"{pdf_file_name}_content_list.json",
                    json.dumps(content_list, ensure_ascii=False, indent=4),
                )

            if f_dump_middle_json:
                md_writer.write_string(
                    f"{pdf_file_name}_middle.json",
                    json.dumps(middle_json, ensure_ascii=False, indent=4),
                )

            if f_dump_model_output:
                md_writer.write_string(
                    f"{pdf_file_name}_model.json",
                    json.dumps(model_json, ensure_ascii=False, indent=4),
                )

            logger.info(f"local output dir is {local_md_dir}")
    else:
        if backend.startswith("vlm-"):
            backend = backend[4:]

        f_draw_span_bbox = False
        parse_method = "vlm"
        for idx, pdf_bytes in enumerate(pdf_bytes_list):
            try:
                pdf_file_name = pdf_file_names[idx]
                pdf_bytes = convert_pdf_bytes_to_bytes_by_pypdfium2(pdf_bytes, start_page_id, end_page_id)
                local_image_dir, local_md_dir = prepare_env(output_dir, pdf_file_name, parse_method)
                image_writer, md_writer = FileBasedDataWriter(local_image_dir), FileBasedDataWriter(local_md_dir)
                middle_json, infer_result = vlm_doc_analyze(pdf_bytes, image_writer=image_writer, backend=backend, server_url=server_url)

                pdf_info = middle_json["pdf_info"]

                if f_draw_layout_bbox:
                    draw_layout_bbox(pdf_info, pdf_bytes, local_md_dir, f"{pdf_file_name}_layout.pdf")

                if f_draw_span_bbox:
                    draw_span_bbox(pdf_info, pdf_bytes, local_md_dir, f"{pdf_file_name}_span.pdf")

                if f_dump_orig_pdf:
                    md_writer.write(
                        f"{pdf_file_name}_origin.pdf",
                        pdf_bytes,
                    )

                if f_dump_md:
                    image_dir = str(os.path.basename(local_image_dir))
                    md_content_str = vlm_union_make(pdf_info, f_make_md_mode, image_dir)
                    md_writer.write_string(
                        f"{pdf_file_name}.md",
                        md_content_str,
                    )

                if f_dump_content_list:
                    image_dir = str(os.path.basename(local_image_dir))
                    content_list = vlm_union_make(pdf_info, MakeMode.CONTENT_LIST, image_dir)
                    md_writer.write_string(
                        f"{pdf_file_name}_content_list.json",
                        json.dumps(content_list, ensure_ascii=False, indent=4),
                    )

                if f_dump_middle_json:
                    md_writer.write_string(
                        f"{pdf_file_name}_middle.json",
                        json.dumps(middle_json, ensure_ascii=False, indent=4),
                    )

                if f_dump_model_output:
                    model_output = ("\n" + "-" * 50 + "\n").join(infer_result)
                    md_writer.write_string(
                        f"{pdf_file_name}_model_output.txt",
                        model_output,
                    )

                logger.info(f"local output dir is {local_md_dir}")
            except IndexError as e:
                logger.error(f"处理文件 {pdf_file_names[idx]} 时发生索引错误: {e}")
                logger.error("这可能是由于VLM模型输出格式不一致导致的，请尝试使用pipeline模式")
                continue  # 跳过当前文件继续处理下一个
            except Exception as e:
                logger.exception(f"处理文件 {pdf_file_names[idx]} 时发生错误: {e}")
                continue  # 跳过当前文件继续处理下一个


def parse_doc(
        path_list: list[Path],
        output_dir,
        lang="ch",
        backend="pipeline",
        method="auto",
        server_url=None,
        start_page_id=0,  # 解析的起始页ID，默认为0
        end_page_id=None  # 解析的结束页ID，默认为None（解析到文档末尾的所有页面）
):
    """
        参数说明:
        path_list: 要解析的文档路径列表，可以是PDF或图像文件。
        output_dir: 用于存储解析结果的输出目录。
        lang: 语言选项，默认为'ch'，可选值包括['ch', 'ch_server', 'ch_lite', 'en', 'korean', 'japan', 'chinese_cht', 'ta', 'te', 'ka']。
            输入PDF中的语言（如果已知）以提高OCR准确性。可选。
            仅适用于后端设置为"pipeline"的情况
        backend: 解析PDF的后端:
            pipeline: 更通用。
            vlm-transformers: 更通用。
            vlm-sglang-engine: 更快(引擎)。
            vlm-sglang-client: 更快(客户端)。
            如果未指定方法，默认将使用pipeline。
        method: 解析PDF的方法:
            auto: 根据文件类型自动确定方法。
            txt: 使用文本提取方法。
            ocr: 对基于图像的PDF使用OCR方法。
            如果未指定方法，默认将使用'auto'。
            仅适用于后端设置为"pipeline"的情况。
        server_url: 当后端为`sglang-client`时，需要指定server_url，例如:`http://127.0.0.1:30000`
    """
    try:
        file_name_list = []
        pdf_bytes_list = []
        lang_list = []
        for path in path_list:
            # 检查文件是否存在且大小不为0
            if not path.exists():
                logger.warning(f"文件不存在: {path}")
                continue
                
            if path.stat().st_size == 0:
                logger.warning(f"跳过空文件: {path}")
                continue
                
            file_name = str(Path(path).stem)
            pdf_bytes = read_fn(path)
            file_name_list.append(file_name)
            pdf_bytes_list.append(pdf_bytes)
            lang_list.append(lang)
            
        if not pdf_bytes_list:
            logger.warning("没有有效的文件可以处理")
            return
            
        do_parse(
            output_dir=output_dir,
            pdf_file_names=file_name_list,
            pdf_bytes_list=pdf_bytes_list,
            p_lang_list=lang_list,
            backend=backend,
            parse_method=method,
            server_url=server_url,
            start_page_id=start_page_id,
            end_page_id=end_page_id
        )
    except Exception as e:
        logger.exception(e)


def find_all_pdf_files(directory):
    """递归查找目录中的所有PDF文件"""
    pdf_files = []
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.lower().endswith('.pdf'):
                pdf_files.append(os.path.join(root, file))
    return pdf_files


def filter_unprocessed_files(pdf_files, output_dir):
    """过滤掉已经处理过的PDF文件"""
    # 获取已处理的文件夹名称
    processed_folders = set()
    if os.path.exists(output_dir):
        for item in os.listdir(output_dir):
            item_path = os.path.join(output_dir, item)
            if os.path.isdir(item_path):
                processed_folders.add(item)
    
    # 过滤未处理的文件
    unprocessed_files = []
    for pdf_file in pdf_files:
        # 获取文件名（不包含扩展名）
        file_name = os.path.splitext(os.path.basename(pdf_file))[0]
        
        # 检查是否已经处理过
        if file_name not in processed_folders:
            unprocessed_files.append(pdf_file)
        else:
            logger.info(f"跳过已处理的文件: {file_name}")
    
    return unprocessed_files


if __name__ == '__main__':
    # 参数
    __dir__ = os.path.dirname(os.path.abspath(__file__))
    # 修改为从门禁产品目录读取PDF文件
    pdf_files_dir = os.path.join(__dir__, "门禁产品")
    output_dir = os.path.join(__dir__, "output")
    pdf_suffixes = [".pdf"]
    image_suffixes = [".png", ".jpeg", ".jpg"]

    # 递归查找所有PDF文件
    all_pdf_files = find_all_pdf_files(pdf_files_dir)
    
    # 过滤掉已经处理过的文件
    unprocessed_pdf_files = filter_unprocessed_files(all_pdf_files, output_dir)
    doc_path_list = [Path(pdf_file) for pdf_file in unprocessed_pdf_files]
    
    # 打印文件数量信息
    logger.info(f"找到 {len(all_pdf_files)} 个PDF文件")
    logger.info(f"已处理 {len(all_pdf_files) - len(unprocessed_pdf_files)} 个文件")
    logger.info(f"待处理 {len(doc_path_list)} 个PDF文件")
    
    # 如果没有待处理的文件，则退出
    if len(doc_path_list) == 0:
        logger.info("所有PDF文件都已处理完成，无需重新处理")
        exit(0)

    """如果您由于网络问题无法下载模型，可以设置环境变量MINERU_MODEL_SOURCE为modelscope使用免代理仓库下载模型"""
    # os.environ['MINERU_MODEL_SOURCE'] = "modelscope"

    """如果您的环境不支持VLM，请使用pipeline模式"""
    # parse_doc(doc_path_list, output_dir, backend="pipeline")

    """要启用VLM模式，请将后端更改为'vlm-xxx'"""
    # parse_doc(doc_path_list, output_dir, backend="vlm-transformers")  # 更通用。
    # parse_doc(doc_path_list, output_dir, backend="vlm-sglang-engine")  # 更快(引擎)。
    parse_doc(doc_path_list, output_dir, backend="vlm-sglang-client", server_url="http://************:30000")  # 更快(客户端)。