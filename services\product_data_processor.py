#!/usr/bin/env python3
"""
产品数据处理器

负责解析API返回的产品数据，进行数据清洗、验证和存储
"""

import json
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple
import psycopg2
from psycopg2.extras import RealDictCursor
from utils.database import DatabaseManager

logger = logging.getLogger(__name__)


class ProductDataProcessor:
    """产品数据处理器"""

    def __init__(self):
        """初始化处理器"""
        self.db_manager = DatabaseManager()
        self.processed_count = 0
        self.error_count = 0
        self.skipped_count = 0

    def process_products_batch(
        self, products_data: List[Dict[str, Any]]
    ) -> Dict[str, int]:
        """
        批量处理产品数据

        Args:
            products_data: 产品数据列表

        Returns:
            处理结果统计
        """
        logger.info(f"开始处理 {len(products_data)} 条产品数据")

        self.processed_count = 0
        self.error_count = 0
        self.skipped_count = 0

        for product_raw in products_data:
            try:
                # 解析和验证产品数据
                product_normalized = self._normalize_product_data(product_raw)

                if not product_normalized:
                    self.skipped_count += 1
                    continue

                # 保存到数据库
                success = self._save_product_to_db(product_normalized)

                if success:
                    self.processed_count += 1
                else:
                    self.error_count += 1

            except Exception as e:
                logger.error(f"处理产品数据失败: {e}", exc_info=True)
                self.error_count += 1

        result = {
            "processed": self.processed_count,
            "errors": self.error_count,
            "skipped": self.skipped_count,
            "total": len(products_data),
        }

        logger.info(f"产品数据处理完成: {result}")
        return result

    def _normalize_product_data(
        self, product_raw: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        标准化产品数据

        Args:
            product_raw: 原始产品数据

        Returns:
            标准化后的产品数据，如果数据无效则返回None
        """
        try:
            # 基本字段验证
            product_id = product_raw.get("productId") or product_raw.get("id")
            if not product_id:
                logger.warning("产品ID为空，跳过处理")
                return None

            product_name = product_raw.get("name") or product_raw.get("productName", "")
            if not product_name.strip():
                logger.warning(f"产品ID {product_id} 名称为空，跳过处理")
                return None

            # 处理参数信息
            param_info_list = product_raw.get("paramInfoList")
            param_info_json = None
            product_specs = {}

            if param_info_list is not None and isinstance(param_info_list, list):
                # 转换参数列表为JSON格式存储
                param_info_json = json.dumps(param_info_list, ensure_ascii=False)

                # 提取关键规格参数
                for param in param_info_list:
                    if param and isinstance(param, dict):
                        param_name = param.get("params", "")
                        param_content = param.get("content", "")
                        if param_name and param_content:
                            product_specs[param_name] = param_content

            # 处理标签信息
            label_list = product_raw.get("labelList") or []
            label_name = product_raw.get("labelName", "")
            if label_list and isinstance(label_list, list) and len(label_list) > 0:
                if not label_name:
                    label_name = (
                        label_list[0].get("name", "")
                        if isinstance(label_list[0], dict)
                        else ""
                    )

            # 标准化数据结构
            normalized_data = {
                # 基本信息
                "id": int(product_id),
                "name": product_name.strip(),
                "spec": product_raw.get("spec", "").strip(),
                "introduction": (product_raw.get("introduction") or "").strip(),
                "details": product_raw.get("details", ""),
                # 图片信息
                "small_img": product_raw.get("smallImg", ""),
                "banner": product_raw.get("banner", ""),
                "size_img": product_raw.get("sizeImg", ""),
                # 分类和标签
                "category_id": self._safe_int(product_raw.get("categoryId")),
                "category_name": product_raw.get("categoryName", ""),
                "label_id": self._safe_int(product_raw.get("labelId")),
                "label_name": label_name,
                # 产品属性
                "attribute": product_raw.get("attribute", "0"),
                "show_for": product_raw.get("showFor", ""),
                "show_for_company": product_raw.get("showForCompany", ""),
                # 参数和规格
                "param_info": param_info_json,
                "product_specs": (
                    json.dumps(product_specs, ensure_ascii=False)
                    if product_specs
                    else None
                ),
                # 使用场景和说明
                "use_to": product_raw.get("useTo", ""),
                "qualifications": product_raw.get("qualifications", ""),
                "instructions": product_raw.get("instructions", ""),
                "other": product_raw.get("other", ""),
                "guide": product_raw.get("guide", ""),
                "common_problem": product_raw.get("commonProblem", ""),
                "accessory": product_raw.get("accessory", ""),
                # 价格和数量
                "price": self._safe_float(product_raw.get("price", 0)),
                "quantity": self._safe_int(product_raw.get("quantity", 1)),
                "unit": self._safe_int(product_raw.get("unit")),
                # 状态标识
                "is_suggest": product_raw.get("isSuggest", "0"),
                "is_hot": product_raw.get("isHot", "0"),
                "is_new": self._safe_int(product_raw.get("isNew", 0)),
                "count": self._safe_int(product_raw.get("count", 0)),
                "like_count": self._safe_int(product_raw.get("likeCount", 0)),
                "favorite_count": self._safe_int(product_raw.get("favoriteCount", 0)),
                "status": product_raw.get("status", "0"),
                # 系统字段
                "site_id": self._safe_int(product_raw.get("siteId", 999)),
                "brand_id": self._safe_int(product_raw.get("brandId")),
                "show_time": self._parse_datetime(product_raw.get("showTime")),
                "sort": self._safe_int(product_raw.get("sort", 0)),
                # 时间戳
                "create_time": self._parse_datetime(product_raw.get("createTime"))
                or datetime.now(),
                "update_time": self._parse_datetime(product_raw.get("updateTime"))
                or datetime.now(),
                "create_by": product_raw.get("createBy", ""),
                "update_by": product_raw.get("updateBy", ""),
                "last_sync_time": datetime.now(),
                # 视频和媒体
                "video_explanation": product_raw.get("videoExplanation", ""),
                "video_installation": product_raw.get("videoInstallation", ""),
                "video_troubleshooting": product_raw.get("videoTroubleshooting", ""),
                # 平台相关
                "distribution_order_platform_id": self._safe_int(
                    product_raw.get("distributionOrderPlatformId")
                ),
            }

            return normalized_data

        except Exception as e:
            logger.error(f"标准化产品数据失败: {e}", exc_info=True)
            return None

    def _save_product_to_db(self, product_data: Dict[str, Any]) -> bool:
        """
        保存产品数据到数据库

        Args:
            product_data: 标准化的产品数据

        Returns:
            是否保存成功
        """
        try:
            conn = self.db_manager.get_connection()

            try:
                with conn.cursor() as cursor:
                    # 检查产品是否已存在
                    cursor.execute(
                        "SELECT id FROM products WHERE id = %s", (product_data["id"],)
                    )
                    exists = cursor.fetchone()

                    if exists:
                        # 更新现有产品
                        update_sql = """
                            UPDATE products SET
                                name = %(name)s,
                                spec = %(spec)s,
                                introduction = %(introduction)s,
                                details = %(details)s,
                                small_img = %(small_img)s,
                                banner = %(banner)s,
                                category_id = %(category_id)s,
                                label_id = %(label_id)s,
                                attribute = %(attribute)s,
                                show_for = %(show_for)s,
                                param_info = %(param_info)s,
                                use_to = %(use_to)s,
                                qualifications = %(qualifications)s,
                                instructions = %(instructions)s,
                                other = %(other)s,
                                guide = %(guide)s,
                                common_problem = %(common_problem)s,
                                price = %(price)s,
                                is_suggest = %(is_suggest)s,
                                is_hot = %(is_hot)s,
                                is_new = %(is_new)s,
                                count = %(count)s,
                                like_count = %(like_count)s,
                                favorite_count = %(favorite_count)s,
                                status = %(status)s,
                                site_id = %(site_id)s,
                                brand_id = %(brand_id)s,
                                show_time = %(show_time)s,
                                sort = %(sort)s,
                                update_time = %(update_time)s,
                                update_by = %(update_by)s,
                                last_sync_time = %(last_sync_time)s
                            WHERE id = %(id)s
                        """
                        cursor.execute(update_sql, product_data)
                        logger.debug(
                            f"更新产品 {product_data['id']}: {product_data['name']}"
                        )
                    else:
                        # 插入新产品
                        insert_sql = """
                            INSERT INTO products (
                                id, name, spec, introduction, details, small_img, banner,
                                category_id, label_id, attribute, show_for, param_info, use_to,
                                qualifications, instructions, other, guide, common_problem, price,
                                is_suggest, is_hot, is_new, count, like_count, favorite_count,
                                status, site_id, brand_id, show_time, sort, create_time, update_time,
                                create_by, update_by, last_sync_time
                            ) VALUES (
                                %(id)s, %(name)s, %(spec)s, %(introduction)s, %(details)s, %(small_img)s, %(banner)s,
                                %(category_id)s, %(label_id)s, %(attribute)s, %(show_for)s, %(param_info)s, %(use_to)s,
                                %(qualifications)s, %(instructions)s, %(other)s, %(guide)s, %(common_problem)s, %(price)s,
                                %(is_suggest)s, %(is_hot)s, %(is_new)s, %(count)s, %(like_count)s, %(favorite_count)s,
                                %(status)s, %(site_id)s, %(brand_id)s, %(show_time)s, %(sort)s, %(create_time)s, %(update_time)s,
                                %(create_by)s, %(update_by)s, %(last_sync_time)s
                            )
                        """
                        cursor.execute(insert_sql, product_data)
                        logger.debug(
                            f"插入新产品 {product_data['id']}: {product_data['name']}"
                        )

                    conn.commit()
                    return True

            finally:
                self.db_manager.return_connection(conn)

        except Exception as e:
            logger.error(f"保存产品数据到数据库失败: {e}", exc_info=True)
            if conn:
                try:
                    conn.rollback()
                except:
                    pass
            return False

    def _safe_int(self, value: Any) -> Optional[int]:
        """安全转换为整数"""
        if value is None or value == "":
            return None
        try:
            return int(value)
        except (ValueError, TypeError):
            return None

    def _safe_float(self, value: Any) -> float:
        """安全转换为浮点数"""
        if value is None or value == "":
            return 0.0
        try:
            return float(value)
        except (ValueError, TypeError):
            return 0.0

    def _parse_datetime(self, value: Any) -> Optional[datetime]:
        """解析日期时间"""
        if not value:
            return None

        if isinstance(value, datetime):
            return value

        if isinstance(value, str):
            try:
                # 尝试解析常见的日期时间格式
                formats = [
                    "%Y-%m-%d %H:%M:%S",
                    "%Y-%m-%d %H:%M:%S.%f",
                    "%Y-%m-%dT%H:%M:%S",
                    "%Y-%m-%dT%H:%M:%S.%f",
                    "%Y-%m-%dT%H:%M:%SZ",
                    "%Y-%m-%d",
                ]

                for fmt in formats:
                    try:
                        return datetime.strptime(value, fmt)
                    except ValueError:
                        continue

                logger.warning(f"无法解析日期时间: {value}")
                return None

            except Exception as e:
                logger.warning(f"解析日期时间出错: {e}")
                return None

        return None

    def get_processing_stats(self) -> Dict[str, int]:
        """获取处理统计信息"""
        return {
            "processed": self.processed_count,
            "errors": self.error_count,
            "skipped": self.skipped_count,
        }

    def validate_product_data(
        self, product_data: Dict[str, Any]
    ) -> Tuple[bool, List[str]]:
        """
        验证产品数据完整性

        Args:
            product_data: 产品数据

        Returns:
            (是否有效, 错误信息列表)
        """
        errors = []

        # 必需字段检查
        required_fields = ["id", "name"]
        for field in required_fields:
            if not product_data.get(field):
                errors.append(f"缺少必需字段: {field}")

        # 数据类型检查
        if product_data.get("id") and not isinstance(product_data["id"], int):
            errors.append("产品ID必须是整数")

        if product_data.get("price") and not isinstance(
            product_data["price"], (int, float)
        ):
            errors.append("价格必须是数字")

        # 业务逻辑检查
        if product_data.get("price", 0) < 0:
            errors.append("价格不能为负数")

        return len(errors) == 0, errors
