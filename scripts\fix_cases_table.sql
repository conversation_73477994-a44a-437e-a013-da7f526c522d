-- 修复Cases表字段缺失问题
-- 添加product_id和product_name字段

-- 检查并添加product_id字段
DO $$
BEGIN
    -- 检查product_id字段是否存在
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'cases' 
        AND column_name = 'product_id' 
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE cases ADD COLUMN product_id INTEGER;
        RAISE NOTICE '✅ 成功添加product_id字段到cases表';
    ELSE
        RAISE NOTICE '✅ product_id字段已存在于cases表';
    END IF;
    
    -- 检查并添加product_name字段
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'cases' 
        AND column_name = 'product_name' 
        AND table_schema = 'public'
    ) THEN
        ALTER TABLE cases ADD COLUMN product_name VARCHAR(255);
        RAISE NOTICE '✅ 成功添加product_name字段到cases表';
    ELSE
        RAISE NOTICE '✅ product_name字段已存在于cases表';
    END IF;
    
    -- 添加索引优化查询性能
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'cases' 
        AND indexname = 'idx_cases_product_id'
    ) THEN
        CREATE INDEX idx_cases_product_id ON cases(product_id);
        RAISE NOTICE '✅ 成功创建product_id索引';
    ELSE
        RAISE NOTICE '✅ product_id索引已存在';
    END IF;
    
    -- 添加product_name索引
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE tablename = 'cases' 
        AND indexname = 'idx_cases_product_name'
    ) THEN
        CREATE INDEX idx_cases_product_name ON cases(product_name);
        RAISE NOTICE '✅ 成功创建product_name索引';
    ELSE
        RAISE NOTICE '✅ product_name索引已存在';
    END IF;
    
END$$;

-- 验证字段添加结果
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default
FROM information_schema.columns 
WHERE table_name = 'cases' 
AND column_name IN ('product_id', 'product_name')
ORDER BY column_name;

-- 显示cases表的完整结构
\d cases; 