"""
数据同步页面
从云商API获取所有数据并写入本地数据库
"""

import streamlit as st
import logging
import datetime
import time
import traceback
from typing import Dict, Any, List, Optional
import json
import pandas as pd
from utils.session import SessionManager
from utils.auth import require_auth
from services.db_init_service import DBInitService
from services.sync_service import SyncService
from utils.api_client import ZKMallClient
from utils.database import get_db_connection, return_db_connection

logger = logging.getLogger(__name__)


@require_auth
def main():
    """数据同步页面"""
    # 页面配置
    st.set_page_config(
        page_title="数据同步",
        page_icon="🔄",
        layout="wide",
        initial_sidebar_state="expanded",
    )

    # 初始化session状态
    initialize_session()

    # 渲染页面
    render_header()
    render_sidebar()
    render_content()
    render_footer()


def initialize_session():
    """初始化session状态"""
    defaults = {
        "sync_progress": {},
        "sync_status": "idle",
        "sync_logs": [],
        "last_sync_time": None,
        "auto_sync_enabled": False,
        "sync_interval": 3600,  # 默认1小时
        "selected_modules": [],
        "sync_statistics": {},
    }

    for key, value in defaults.items():
        if key not in st.session_state:
            SessionManager.set(key, value)


def render_header():
    """渲染页面头部"""
    st.title("🔄 数据同步")
    st.markdown("从云商API同步所有数据到本地数据库")

    # 显示同步状态
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        status = SessionManager.get("sync_status", "idle")
        if status == "running":
            st.success("🟢 同步进行中")
        elif status == "completed":
            st.info("🔵 同步完成")
        elif status == "error":
            st.error("🔴 同步错误")
        else:
            st.warning("⚪ 待同步")

    with col2:
        last_sync = SessionManager.get("last_sync_time")
        if last_sync:
            st.metric("上次同步", last_sync.strftime("%H:%M:%S"))
        else:
            st.metric("上次同步", "未同步")

    with col3:
        stats = SessionManager.get("sync_statistics", {})
        total_records = sum(stats.values()) if stats else 0
        st.metric("总记录数", f"{total_records:,}")

    with col4:
        auto_sync = SessionManager.get("auto_sync_enabled", False)
        st.metric("自动同步", "启用" if auto_sync else "禁用")


def render_sidebar():
    """渲染侧边栏"""
    with st.sidebar:
        st.subheader("🛠️ 同步设置")

        # 同步模块选择
        st.markdown("#### 📋 同步模块")
        modules = [
            ("products", "产品数据"),
            ("categories", "分类数据"),
            ("labels", "标签数据"),
            ("brands", "品牌数据"),
            ("cases", "关联案例"),
            ("programmes", "关联方案"),
            ("information", "关联资讯"),
            ("distribution_orders", "关联配单"),
        ]

        selected_modules = []
        for module_key, module_name in modules:
            if st.checkbox(module_name, key=f"module_{module_key}"):
                selected_modules.append(module_key)

        SessionManager.set("selected_modules", selected_modules)

        st.markdown("#### ⚙️ 同步选项")

        # 同步策略
        sync_strategy = st.selectbox(
            "同步策略",
            ["增量同步", "全量同步", "智能同步"],
            help="增量：只同步更新的数据；全量：同步所有数据；智能：根据变化自动选择",
        )

        # 批处理大小
        batch_size = st.number_input(
            "批处理大小",
            min_value=10,
            max_value=1000,
            value=100,
            help="每次API请求处理的记录数量",
        )

        # 并发线程数
        thread_count = st.number_input(
            "并发线程数",
            min_value=1,
            max_value=10,
            value=3,
            help="同步时的并发线程数量",
        )

        # 错误处理策略
        error_strategy = st.selectbox(
            "错误处理", ["继续同步", "停止同步", "重试3次"], help="遇到错误时的处理方式"
        )

        st.markdown("#### 🕒 自动同步")

        # 自动同步开关
        auto_sync = st.toggle(
            "启用自动同步", value=SessionManager.get("auto_sync_enabled", False)
        )
        SessionManager.set("auto_sync_enabled", auto_sync)

        if auto_sync:
            # 同步间隔
            interval_options = {
                "30分钟": 1800,
                "1小时": 3600,
                "2小时": 7200,
                "6小时": 21600,
                "12小时": 43200,
                "24小时": 86400,
            }

            interval_label = st.selectbox(
                "同步间隔", list(interval_options.keys()), index=1
            )
            SessionManager.set("sync_interval", interval_options[interval_label])

            # 同步时间段
            start_time = st.time_input("开始时间", datetime.time(9, 0))
            end_time = st.time_input("结束时间", datetime.time(18, 0))


def render_content():
    """渲染主要内容"""
    # 创建选项卡
    tab1, tab2, tab3, tab4 = st.tabs(
        ["🚀 执行同步", "📊 同步监控", "📋 同步日志", "📈 统计分析"]
    )

    with tab1:
        render_sync_execution()

    with tab2:
        render_sync_monitoring()

    with tab3:
        render_sync_logs()

    with tab4:
        render_sync_statistics()


def render_sync_execution():
    """渲染同步执行界面"""
    st.subheader("🚀 执行数据同步")

    # 同步控制按钮
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        if st.button("🔄 开始同步", type="primary", use_container_width=True):
            start_sync()

    with col2:
        if st.button("⏸️ 暂停同步", use_container_width=True):
            pause_sync()

    with col3:
        if st.button("⏹️ 停止同步", use_container_width=True):
            stop_sync()

    with col4:
        if st.button("🔄 重置状态", use_container_width=True):
            reset_sync_status()

    st.markdown("---")

    # 同步进度显示
    st.subheader("📈 同步进度")

    progress_data = SessionManager.get("sync_progress", {})
    if progress_data:
        for module, progress in progress_data.items():
            col1, col2 = st.columns([3, 1])
            with col1:
                progress_bar = st.progress(progress.get("percentage", 0) / 100)
                st.text(
                    f"{module}: {progress.get('current', 0)}/{progress.get('total', 0)}"
                )
            with col2:
                status = progress.get("status", "waiting")
                if status == "completed":
                    st.success("✅")
                elif status == "running":
                    st.info("🔄")
                elif status == "error":
                    st.error("❌")
                else:
                    st.warning("⏳")
    else:
        st.info("暂无同步任务")

    # 实时日志显示
    st.subheader("📝 实时日志")

    # 创建日志容器
    log_container = st.empty()

    # 显示最新日志
    logs = SessionManager.get("sync_logs", [])
    if logs:
        # 只显示最新的10条日志
        recent_logs = logs[-10:]
        log_text = "\n".join(
            [f"[{log['time']}] {log['level']}: {log['message']}" for log in recent_logs]
        )
        log_container.text_area("", value=log_text, height=200, disabled=True)
    else:
        log_container.info("暂无日志")


def render_sync_monitoring():
    """渲染同步监控界面"""
    st.subheader("📊 同步监控")

    # 系统状态监控
    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric("API连接状态", get_api_status())

    with col2:
        st.metric("数据库状态", get_database_status())

    with col3:
        st.metric("内存使用", get_memory_usage())

    # 同步性能监控
    st.subheader("⚡ 性能监控")

    # 获取真实性能监控数据
    performance_data = generate_performance_data()

    col1, col2 = st.columns(2)

    with col1:
        st.line_chart(performance_data["sync_speed"])

    with col2:
        st.bar_chart(performance_data["module_progress"])

    # 错误监控
    st.subheader("⚠️ 错误监控")

    error_data = get_sync_errors()
    if error_data:
        st.error(f"发现 {len(error_data)} 个错误")
        for error in error_data:
            with st.expander(f"错误: {error['module']} - {error['time']}"):
                st.code(error["message"])
    else:
        st.success("暂无错误")


def render_sync_logs():
    """渲染同步日志界面"""
    st.subheader("📋 同步日志")

    # 日志筛选选项
    col1, col2, col3 = st.columns(3)

    with col1:
        log_level = st.selectbox("日志级别", ["ALL", "INFO", "WARNING", "ERROR"])

    with col2:
        log_date = st.date_input("日期", datetime.date.today())

    with col3:
        log_module = st.selectbox("模块", ["ALL"] + [m[1] for m in get_sync_modules()])

    # 获取筛选后的日志
    filtered_logs = get_filtered_logs(log_level, log_date, log_module)

    if filtered_logs:
        # 创建日志DataFrame
        df = pd.DataFrame(filtered_logs)

        # 显示日志表格
        st.dataframe(
            df,
            use_container_width=True,
            column_config={
                "time": "时间",
                "level": "级别",
                "module": "模块",
                "message": "消息",
            },
        )

        # 导出日志按钮
        if st.button("📁 导出日志"):
            export_logs(filtered_logs)
    else:
        st.info("没有找到符合条件的日志")


def render_sync_statistics():
    """渲染统计分析界面"""
    st.subheader("📈 统计分析")

    # 同步统计概览
    stats = get_sync_statistics()

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("总同步次数", stats.get("total_syncs", 0))

    with col2:
        st.metric("成功率", f"{stats.get('success_rate', 0):.1f}%")

    with col3:
        st.metric("平均耗时", f"{stats.get('avg_duration', 0):.1f}秒")

    with col4:
        st.metric("数据增长", f"{stats.get('data_growth', 0):+.1f}%")

    # 数据量趋势图
    st.subheader("📊 数据量趋势")

    trend_data = get_data_trend()
    if trend_data:
        st.line_chart(trend_data)
    else:
        st.info("暂无趋势数据")

    # 模块同步统计
    st.subheader("📋 模块统计")

    module_stats = get_module_statistics()
    if module_stats:
        df = pd.DataFrame(module_stats)
        st.dataframe(
            df,
            use_container_width=True,
            column_config={
                "module": "模块",
                "total_records": "总记录数",
                "last_sync": "最后同步",
                "sync_count": "同步次数",
                "error_count": "错误次数",
            },
        )
    else:
        st.info("暂无模块统计数据")


def start_sync():
    """开始同步"""
    try:
        selected_modules = SessionManager.get("selected_modules", [])

        if not selected_modules:
            st.error("请至少选择一个同步模块")
            return

        # 更新同步状态
        SessionManager.set("sync_status", "running")

        # 添加日志
        add_sync_log("INFO", "开始数据同步", "system")

        # 初始化进度
        progress = {}
        for module in selected_modules:
            progress[module] = {
                "status": "waiting",
                "current": 0,
                "total": 0,
                "percentage": 0,
            }
        SessionManager.set("sync_progress", progress)

        # 这里应该调用真实的同步服务
        sync_service = SyncService()

        with st.spinner("正在同步数据..."):
            # 执行同步
            for module in selected_modules:
                add_sync_log("INFO", f"开始同步模块: {module}", module)

                # 更新进度
                progress[module]["status"] = "running"
                SessionManager.set("sync_progress", progress)

                try:
                    # 调用实际的同步方法
                    result = sync_module_data(module, sync_service)

                    if result["success"]:
                        progress[module]["status"] = "completed"
                        progress[module]["percentage"] = 100
                        add_sync_log(
                            "INFO",
                            f"模块 {module} 同步完成: {result.get('total', 0)} 条记录",
                            module,
                        )
                    else:
                        progress[module]["status"] = "error"
                        add_sync_log(
                            "ERROR",
                            f"模块 {module} 同步失败: {result['error']}",
                            module,
                        )

                except Exception as e:
                    progress[module]["status"] = "error"
                    add_sync_log("ERROR", f"模块 {module} 同步异常: {str(e)}", module)

                # 更新进度
                SessionManager.set("sync_progress", progress)
                time.sleep(0.1)  # 短暂延迟以显示进度

        # 更新同步状态
        SessionManager.set("sync_status", "completed")
        SessionManager.set("last_sync_time", datetime.datetime.now())

        add_sync_log("INFO", "数据同步完成", "system")
        st.success("数据同步完成！")
        st.rerun()

    except Exception as e:
        SessionManager.set("sync_status", "error")
        add_sync_log("ERROR", f"同步过程发生错误: {str(e)}", "system")
        st.error(f"同步失败: {str(e)}")
        logger.error(f"同步失败: {e}")


def sync_module_data(module: str, sync_service: SyncService) -> Dict[str, Any]:
    """同步指定模块的数据"""
    try:
        if module == "products":
            return sync_service.sync_products()
        elif module == "categories":
            return sync_service.sync_categories()
        elif module == "labels":
            return sync_service.sync_labels()
        elif module == "brands":
            return sync_service.sync_brands()
        elif module == "cases":
            return sync_service.sync_cases()
        elif module == "programmes":
            return sync_service.sync_programmes()
        elif module == "information":
            return sync_service.sync_information()
        elif module == "distribution_orders":
            return sync_service.sync_distribution_orders()
        else:
            return {"success": False, "error": f"未知模块: {module}"}

    except Exception as e:
        return {"success": False, "error": str(e)}


def pause_sync():
    """暂停同步"""
    SessionManager.set("sync_status", "paused")
    add_sync_log("WARNING", "同步已暂停", "system")
    st.warning("同步已暂停")


def stop_sync():
    """停止同步"""
    SessionManager.set("sync_status", "stopped")
    SessionManager.set("sync_progress", {})
    add_sync_log("WARNING", "同步已停止", "system")
    st.warning("同步已停止")


def reset_sync_status():
    """重置同步状态"""
    SessionManager.set("sync_status", "idle")
    SessionManager.set("sync_progress", {})
    SessionManager.set("sync_logs", [])
    add_sync_log("INFO", "同步状态已重置", "system")
    st.info("同步状态已重置")


def add_sync_log(level: str, message: str, module: str = "system"):
    """添加同步日志"""
    logs = SessionManager.get("sync_logs", [])
    logs.append(
        {
            "time": datetime.datetime.now().strftime("%H:%M:%S"),
            "level": level,
            "module": module,
            "message": message,
        }
    )

    # 保持最新1000条日志
    if len(logs) > 1000:
        logs = logs[-1000:]

    SessionManager.set("sync_logs", logs)


def get_api_status() -> str:
    """获取API连接状态"""
    try:
        client = ZKMallClient()
        # 尝试获取基本信息
        response = client.get_products(page=1, page_size=1)
        return "🟢 正常" if response else "🔴 异常"
    except:
        return "🔴 断开"


def get_database_status() -> str:
    """获取数据库状态"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            return "🟢 正常"
    except:
        return "🔴 异常"


def get_memory_usage() -> str:
    """获取内存使用情况"""
    import psutil

    memory = psutil.virtual_memory()
    return f"{memory.percent:.1f}%"


def generate_performance_data() -> Dict[str, Any]:
    """获取真实性能监控数据"""
    try:
        conn = get_db_connection()
        try:
            cursor = conn.cursor()

            # 获取最近10次同步的速度数据（每分钟处理的记录数）
            cursor.execute(
                """
                SELECT 
                    DATE_TRUNC('minute', create_time) as minute_time,
                    COUNT(*) as records_per_minute
                FROM (
                    SELECT create_time FROM products WHERE create_time >= NOW() - INTERVAL '10 minutes'
                    UNION ALL
                    SELECT create_time FROM categories WHERE create_time >= NOW() - INTERVAL '10 minutes'
                    UNION ALL
                    SELECT create_time FROM brands WHERE create_time >= NOW() - INTERVAL '10 minutes'
                ) combined_data
                GROUP BY minute_time
                ORDER BY minute_time DESC
                LIMIT 10
            """
            )

            sync_speed_data = cursor.fetchall()
            sync_speed = {}
            for i, (minute_time, count) in enumerate(sync_speed_data):
                sync_speed[f"时刻{i+1}"] = count or 0

            # 如果没有数据，提供默认值
            if not sync_speed:
                sync_speed = {f"时刻{i}": 0 for i in range(1, 11)}

            # 获取各模块的数据量作为进度指标
            modules_data = []
            module_queries = [
                ("产品", "SELECT COUNT(*) FROM products"),
                ("分类", "SELECT COUNT(*) FROM categories"),
                ("标签", "SELECT COUNT(*) FROM labels"),
                ("品牌", "SELECT COUNT(*) FROM brands"),
                ("案例", "SELECT COUNT(*) FROM cases"),
                ("方案", "SELECT COUNT(*) FROM programmes"),
                ("资讯", "SELECT COUNT(*) FROM information"),
                ("配单", "SELECT COUNT(*) FROM distribution_orders"),
            ]

            module_progress = {}
            for module_name, query in module_queries:
                try:
                    cursor.execute(query)
                    count = cursor.fetchone()[0] or 0
                    # 将记录数转换为进度百分比（假设目标是10000条记录）
                    progress = min(100, (count / 10000) * 100)
                    module_progress[module_name] = int(progress)
                except Exception as e:
                    logger.warning(f"获取{module_name}数据失败: {e}")
                    logger.debug(
                        f"获取{module_name}数据失败详细信息: {traceback.format_exc()}"
                    )
                    module_progress[module_name] = 0

            cursor.close()
            return {"sync_speed": sync_speed, "module_progress": module_progress}
        finally:
            return_db_connection(conn)

    except Exception as e:
        logger.error(f"获取性能监控数据失败: {e}")
        logger.debug(f"详细错误信息: {traceback.format_exc()}")
        # 返回默认数据而不是模拟数据
        return {
            "sync_speed": {f"时刻{i}": 0 for i in range(1, 11)},
            "module_progress": {"产品": 0, "分类": 0, "品牌": 0},
        }


def get_sync_errors() -> List[Dict[str, Any]]:
    """获取同步错误"""
    # 从日志中筛选错误
    logs = SessionManager.get("sync_logs", [])
    return [log for log in logs if log["level"] == "ERROR"]


def get_sync_modules() -> List[tuple]:
    """获取同步模块列表"""
    return [
        ("products", "产品数据"),
        ("categories", "分类数据"),
        ("labels", "标签数据"),
        ("brands", "品牌数据"),
        ("cases", "关联案例"),
        ("programmes", "关联方案"),
        ("information", "关联资讯"),
        ("distribution_orders", "关联配单"),
    ]


def get_filtered_logs(
    level: str, date: datetime.date, module: str
) -> List[Dict[str, Any]]:
    """获取筛选后的日志"""
    logs = SessionManager.get("sync_logs", [])

    # 这里应该根据筛选条件过滤日志
    # 简化处理，返回所有日志
    return logs


def export_logs(logs: List[Dict[str, Any]]):
    """导出日志"""
    try:
        df = pd.DataFrame(logs)
        csv = df.to_csv(index=False)

        st.download_button(
            label="📁 下载CSV文件",
            data=csv,
            file_name=f"sync_logs_{datetime.date.today()}.csv",
            mime="text/csv",
        )

        st.success("日志导出成功！")
    except Exception as e:
        st.error(f"日志导出失败: {str(e)}")


def get_sync_statistics() -> Dict[str, Any]:
    """获取真实同步统计信息"""
    try:
        conn = get_db_connection()
        try:
            cursor = conn.cursor()

            # 获取同步次数（基于日志表或创建时间）
            cursor.execute(
                """
                SELECT COUNT(DISTINCT DATE(create_time)) as total_sync_days
                FROM (
                    SELECT create_time FROM products WHERE create_time >= NOW() - INTERVAL '30 days'
                    UNION ALL
                    SELECT create_time FROM categories WHERE create_time >= NOW() - INTERVAL '30 days'
                ) sync_data
            """
            )

            total_syncs = cursor.fetchone()[0] or 0

            # 计算成功率（基于数据完整性）
            cursor.execute(
                "SELECT COUNT(*) FROM products WHERE name IS NOT NULL AND name != ''"
            )
            valid_products = cursor.fetchone()[0] or 0

            cursor.execute("SELECT COUNT(*) FROM products")
            total_products = cursor.fetchone()[0] or 1  # 避免除零

            success_rate = (
                (valid_products / total_products) * 100 if total_products > 0 else 0
            )

            # 计算平均同步时间（基于数据创建时间间隔）
            cursor.execute(
                """
                SELECT AVG(EXTRACT(EPOCH FROM (
                    MAX(create_time) - MIN(create_time)
                ))) / 60 as avg_duration_minutes
                FROM (
                    SELECT DATE(create_time) as sync_date, create_time
                    FROM products 
                    WHERE create_time >= NOW() - INTERVAL '7 days'
                ) daily_sync
                GROUP BY sync_date
            """
            )

            avg_duration_result = cursor.fetchone()
            avg_duration = avg_duration_result[0] or 0 if avg_duration_result else 0

            # 计算数据增长率
            cursor.execute(
                """
                SELECT 
                    COUNT(*) FILTER (WHERE create_time >= NOW() - INTERVAL '7 days') as recent_count,
                    COUNT(*) FILTER (WHERE create_time >= NOW() - INTERVAL '14 days' AND create_time < NOW() - INTERVAL '7 days') as previous_count
                FROM products
            """
            )

            growth_data = cursor.fetchone()
            recent_count = growth_data[0] or 0
            previous_count = growth_data[1] or 1  # 避免除零

            data_growth = (
                ((recent_count - previous_count) / previous_count) * 100
                if previous_count > 0
                else 0
            )

            cursor.close()

            return {
                "total_syncs": total_syncs,
                "success_rate": round(success_rate, 1),
                "avg_duration": round(avg_duration, 1),
                "data_growth": round(data_growth, 1),
            }
        finally:
            return_db_connection(conn)

    except Exception as e:
        logger.error(f"获取同步统计信息失败: {e}")
        return {
            "total_syncs": 0,
            "success_rate": 0.0,
            "avg_duration": 0.0,
            "data_growth": 0.0,
        }


def get_data_trend() -> Optional[Dict[str, int]]:
    """获取真实数据趋势"""
    try:
        conn = get_db_connection()
        try:
            cursor = conn.cursor()

            # 获取最近30天的数据增长趋势
            cursor.execute(
                """
                SELECT 
                    DATE(create_time) as date,
                    COUNT(*) as daily_count
                FROM (
                    SELECT create_time FROM products WHERE create_time >= NOW() - INTERVAL '30 days'
                    UNION ALL
                    SELECT create_time FROM categories WHERE create_time >= NOW() - INTERVAL '30 days'
                    UNION ALL
                    SELECT create_time FROM brands WHERE create_time >= NOW() - INTERVAL '30 days'
                ) combined_data
                GROUP BY DATE(create_time)
                ORDER BY date DESC
                LIMIT 30
            """
            )

            trend_data = cursor.fetchall()
            cursor.close()

            if trend_data:
                # 转换为字典格式
                trend_dict = {}
                for i, (date, count) in enumerate(reversed(trend_data)):
                    trend_dict[f"第{i+1}天"] = count or 0
                return trend_dict
            else:
                # 如果没有数据，返回空趋势
                return {f"第{i}天": 0 for i in range(1, 31)}
        finally:
            return_db_connection(conn)

    except Exception as e:
        logger.error(f"获取数据趋势失败: {e}")
        return None


def get_module_statistics() -> List[Dict[str, Any]]:
    """获取真实模块统计信息"""
    try:
        conn = get_db_connection()
        try:
            cursor = conn.cursor()

            modules = [
                ("products", "产品数据"),
                ("categories", "分类数据"),
                ("labels", "标签数据"),
                ("brands", "品牌数据"),
                ("cases", "关联案例"),
                ("programmes", "关联方案"),
                ("information", "关联资讯"),
                ("distribution_orders", "关联配单"),
            ]

            statistics = []

            for table_name, display_name in modules:
                try:
                    # 获取总记录数
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    total_records = cursor.fetchone()[0] or 0

                    # 获取最后同步时间
                    cursor.execute(f"SELECT MAX(create_time) FROM {table_name}")
                    last_sync_result = cursor.fetchone()
                    last_sync = (
                        last_sync_result[0]
                        if last_sync_result and last_sync_result[0]
                        else None
                    )

                    # 获取同步次数（基于创建时间的天数）
                    cursor.execute(
                        f"""
                        SELECT COUNT(DISTINCT DATE(create_time)) 
                        FROM {table_name} 
                        WHERE create_time >= NOW() - INTERVAL '30 days'
                    """
                    )
                    sync_count = cursor.fetchone()[0] or 0

                    # 模拟错误计数（可以基于数据质量检查）
                    cursor.execute(
                        f"""
                        SELECT COUNT(*) FROM {table_name} 
                        WHERE name IS NULL OR name = '' OR name = 'null'
                    """
                    )
                    error_count = cursor.fetchone()[0] or 0

                    statistics.append(
                        {
                            "module": display_name,
                            "total_records": total_records,
                            "last_sync": (
                                last_sync.strftime("%Y-%m-%d %H:%M:%S")
                                if last_sync
                                else "未同步"
                            ),
                            "sync_count": sync_count,
                            "error_count": error_count,
                        }
                    )

                except Exception as e:
                    logger.warning(f"获取{display_name}统计失败: {e}")
                    statistics.append(
                        {
                            "module": display_name,
                            "total_records": 0,
                            "last_sync": "获取失败",
                            "sync_count": 0,
                            "error_count": 0,
                        }
                    )

            cursor.close()
            return statistics
        finally:
            return_db_connection(conn)

    except Exception as e:
        logger.error(f"获取模块统计信息失败: {e}")
        return []


def render_footer():
    """渲染页面脚部"""
    st.markdown("---")
    st.markdown("© 2024 云商系统 - 数据同步模块")


if __name__ == "__main__":
    main()
