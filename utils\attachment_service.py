import os
import requests
import logging
import hashlib
import mimetypes
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
from urllib.parse import urlparse, unquote
import time
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class AttachmentInfo:
    """附件信息"""

    original_url: str
    local_path: str
    filename: str
    file_size: int
    file_type: str
    mime_type: str
    download_time: str
    md5_hash: str


class AttachmentService:
    """附件下载和管理服务"""

    def __init__(self, base_dir: str = "attachments"):
        """
        初始化附件服务

        Args:
            base_dir: 附件存储根目录
        """
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(exist_ok=True)

        # 按业务模块创建子目录
        self.module_dirs = {
            "products": self.base_dir / "products",
            "cases": self.base_dir / "cases",
            "programmes": self.base_dir / "programmes",
            "information": self.base_dir / "information",
            "distribution_orders": self.base_dir / "distribution_orders",
        }

        for module_dir in self.module_dirs.values():
            module_dir.mkdir(exist_ok=True)

        # 支持的文件类型
        self.supported_types = {
            "images": [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"],
            "videos": [".mp4", ".avi", ".mov", ".wmv", ".flv", ".mkv"],
            "documents": [
                ".pdf",
                ".doc",
                ".docx",
                ".xls",
                ".xlsx",
                ".ppt",
                ".pptx",
                ".txt",
            ],
            "archives": [".zip", ".rar", ".7z", ".tar", ".gz"],
        }

        # 下载配置
        self.timeout = 30
        self.max_retries = 3
        self.chunk_size = 8192

        logger.info(f"附件服务初始化完成 - 存储目录: {self.base_dir}")

    def _get_file_category(self, file_extension: str) -> str:
        """
        根据文件扩展名确定文件分类

        Args:
            file_extension: 文件扩展名

        Returns:
            文件分类
        """
        ext_lower = file_extension.lower()

        for category, extensions in self.supported_types.items():
            if ext_lower in extensions:
                return category

        return "others"

    def _generate_filename(
        self, url: str, original_filename: Optional[str] = None
    ) -> str:
        """
        生成安全的文件名

        Args:
            url: 原始URL
            original_filename: 原始文件名

        Returns:
            安全的文件名
        """
        if original_filename:
            # 清理原始文件名
            filename = "".join(
                c for c in original_filename if c.isalnum() or c in "._-"
            )
        else:
            # 从URL提取文件名
            parsed_url = urlparse(url)
            filename = os.path.basename(unquote(parsed_url.path))

            if not filename or "." not in filename:
                # 如果无法获取文件名，使用URL的MD5作为文件名
                url_hash = hashlib.md5(url.encode()).hexdigest()[:10]
                filename = f"attachment_{url_hash}"

        # 确保文件名不为空且包含扩展名
        if not filename:
            filename = f"attachment_{int(time.time())}"

        # 如果没有扩展名，尝试从Content-Type推断
        if "." not in filename:
            filename += ".bin"  # 默认扩展名

        return filename

    def _calculate_md5(self, file_path: Path) -> str:
        """
        计算文件的MD5值

        Args:
            file_path: 文件路径

        Returns:
            MD5哈希值
        """
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()

    def download_file(
        self,
        url: str,
        module: str,
        item_id: str,
        field_name: str,
        filename: Optional[str] = None,
    ) -> Optional[AttachmentInfo]:
        """
        下载单个文件

        Args:
            url: 文件URL
            module: 业务模块名 (products, cases, programmes, information, distribution_orders)
            item_id: 业务数据ID
            field_name: 字段名
            filename: 指定文件名

        Returns:
            附件信息，下载失败返回None
        """
        if not url or not url.startswith(("http://", "https://")):
            logger.warning(f"无效的URL: {url}")
            return None

        try:
            # 创建目标目录
            module_dir = self.module_dirs.get(module)
            if not module_dir:
                logger.error(f"不支持的模块: {module}")
                return None

            item_dir = module_dir / str(item_id)
            item_dir.mkdir(exist_ok=True)

            # 发送HEAD请求获取文件信息
            headers = {"User-Agent": "YunShang-AttachmentService/1.0"}

            head_response = requests.head(
                url, headers=headers, timeout=10, allow_redirects=True
            )
            content_type = head_response.headers.get(
                "content-type", "application/octet-stream"
            )
            content_length = head_response.headers.get("content-length")

            # 检查文件大小限制（100MB）
            if content_length and int(content_length) > 100 * 1024 * 1024:
                logger.warning(f"文件过大，跳过下载: {url} ({content_length} bytes)")
                return None

            # 生成文件名
            if not filename:
                content_disposition = head_response.headers.get(
                    "content-disposition", ""
                )
                if "filename=" in content_disposition:
                    filename = content_disposition.split("filename=")[1].strip("\"'")

            safe_filename = self._generate_filename(url, filename)

            # 根据字段名添加前缀
            safe_filename = f"{field_name}_{safe_filename}"

            # 确定文件扩展名和MIME类型
            file_extension = Path(safe_filename).suffix
            if not file_extension and content_type:
                # 从MIME类型推断扩展名
                ext = mimetypes.guess_extension(content_type.split(";")[0])
                if ext:
                    safe_filename += ext
                    file_extension = ext

            file_category = self._get_file_category(file_extension)

            # 创建分类子目录
            category_dir = item_dir / file_category
            category_dir.mkdir(exist_ok=True)

            # 目标文件路径
            target_path = category_dir / safe_filename

            # 如果文件已存在且大小匹配，跳过下载
            if target_path.exists():
                existing_size = target_path.stat().st_size
                if content_length and existing_size == int(content_length):
                    logger.info(f"文件已存在且大小匹配，跳过下载: {safe_filename}")
                    md5_hash = self._calculate_md5(target_path)
                    return AttachmentInfo(
                        original_url=url,
                        local_path=str(target_path.relative_to(self.base_dir)),
                        filename=safe_filename,
                        file_size=existing_size,
                        file_type=file_category,
                        mime_type=content_type,
                        download_time=time.strftime("%Y-%m-%d %H:%M:%S"),
                        md5_hash=md5_hash,
                    )

            # 下载文件
            logger.info(f"开始下载: {url} -> {target_path}")
            response = requests.get(url, headers=headers, timeout=30, stream=True)
            response.raise_for_status()

            # 写入文件
            with open(target_path, "wb") as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)

            # 验证下载完整性
            if not target_path.exists():
                logger.error(f"文件下载失败: {target_path}")
                return None

            actual_size = target_path.stat().st_size
            if actual_size == 0:
                logger.error(f"下载的文件为空: {target_path}")
                target_path.unlink()
                return None

            # 验证文件大小（如果有content-length）
            if content_length and actual_size != int(content_length):
                logger.warning(
                    f"文件大小不匹配: 期望 {content_length}, 实际 {actual_size}"
                )

            # 计算MD5哈希
            md5_hash = self._calculate_md5(target_path)

            # 创建附件信息
            attachment_info = AttachmentInfo(
                original_url=url,
                local_path=str(target_path.relative_to(self.base_dir)),
                filename=safe_filename,
                file_size=actual_size,
                file_type=file_category,
                mime_type=content_type,
                download_time=time.strftime("%Y-%m-%d %H:%M:%S"),
                md5_hash=md5_hash,
            )

            logger.info(f"附件下载成功: {safe_filename} ({actual_size} bytes)")
            return attachment_info

        except requests.exceptions.RequestException as e:
            logger.error(f"下载附件失败 {url}: {e}")
            return None
        except Exception as e:
            logger.error(f"附件处理异常 {url}: {e}")
            return None

    def download_attachments_for_item(
        self, item_data: Dict[str, Any], module: str, item_id: str
    ) -> Dict[str, AttachmentInfo]:
        """
        为单个业务数据下载所有附件

        Args:
            item_data: 业务数据字典
            module: 业务模块名
            item_id: 业务数据ID

        Returns:
            字段名到附件信息的映射
        """
        # 定义各模块的附件字段
        attachment_fields = {
            "products": ["other", "qualifications", "smallImg", "banner"],
            "cases": ["img", "banner", "smallImg", "video"],
            "programmes": ["other", "video", "smallImg", "banner"],
            "information": ["otherUrl", "videoUrl", "picVideo", "smallImg", "images"],
            "distribution_orders": ["other"],
        }

        fields = attachment_fields.get(module, [])
        attachment_info = {}

        for field in fields:
            if field in item_data and item_data[field]:
                url = item_data[field]
                if isinstance(url, str) and url.strip():
                    info = self.download_file(url.strip(), module, item_id, field)
                    if info:
                        attachment_info[field] = info

        logger.info(f"{module} {item_id} 附件下载完成: {len(attachment_info)} 个文件")
        return attachment_info

    def batch_download_attachments(
        self, items_data: List[Dict[str, Any]], module: str
    ) -> Dict[str, Dict[str, AttachmentInfo]]:
        """
        批量下载附件

        Args:
            items_data: 业务数据列表
            module: 业务模块名

        Returns:
            业务ID到附件信息映射的字典
        """
        result = {}
        total_items = len(items_data)

        logger.info(f"开始批量下载 {module} 模块附件，共 {total_items} 项")

        for i, item in enumerate(items_data):
            item_id = str(item.get("id", i))

            try:
                attachments = self.download_attachments_for_item(item, module, item_id)
                if attachments:
                    result[item_id] = attachments

                # 显示进度
                if (i + 1) % 10 == 0 or i == total_items - 1:
                    logger.info(f"批量下载进度: {i + 1}/{total_items}")

            except Exception as e:
                logger.error(f"下载 {module} {item_id} 附件时出错: {e}")
                continue

        logger.info(f"{module} 模块附件批量下载完成: {len(result)} 项成功")
        return result

    def get_attachment_stats(self) -> Dict[str, Any]:
        """
        获取附件统计信息

        Returns:
            统计信息字典
        """
        stats = {"total_files": 0, "total_size": 0, "by_module": {}, "by_type": {}}

        for module, module_dir in self.module_dirs.items():
            if not module_dir.exists():
                continue

            module_stats = {"files": 0, "size": 0, "by_type": {}}

            for file_path in module_dir.rglob("*"):
                if file_path.is_file():
                    file_size = file_path.stat().st_size
                    file_ext = file_path.suffix.lower()
                    file_type = self._get_file_category(file_ext)

                    module_stats["files"] += 1
                    module_stats["size"] += file_size

                    if file_type not in module_stats["by_type"]:
                        module_stats["by_type"][file_type] = {"files": 0, "size": 0}
                    module_stats["by_type"][file_type]["files"] += 1
                    module_stats["by_type"][file_type]["size"] += file_size

                    stats["total_files"] += 1
                    stats["total_size"] += file_size

                    if file_type not in stats["by_type"]:
                        stats["by_type"][file_type] = {"files": 0, "size": 0}
                    stats["by_type"][file_type]["files"] += 1
                    stats["by_type"][file_type]["size"] += file_size

            stats["by_module"][module] = module_stats

        return stats

    def cleanup_orphaned_files(self, valid_items: Dict[str, List[str]]) -> int:
        """
        清理孤立的附件文件

        Args:
            valid_items: 有效的业务数据ID映射 {module: [item_ids]}

        Returns:
            清理的文件数量
        """
        cleaned_count = 0

        for module, module_dir in self.module_dirs.items():
            if not module_dir.exists():
                continue

            valid_ids = set(valid_items.get(module, []))

            for item_dir in module_dir.iterdir():
                if item_dir.is_dir() and item_dir.name not in valid_ids:
                    logger.info(f"清理孤立目录: {item_dir}")
                    try:
                        # 计算文件数量
                        file_count = sum(1 for f in item_dir.rglob("*") if f.is_file())
                        cleaned_count += file_count

                        # 删除目录
                        import shutil

                        shutil.rmtree(item_dir)

                    except Exception as e:
                        logger.error(f"清理目录失败 {item_dir}: {e}")

        logger.info(f"附件清理完成，删除 {cleaned_count} 个文件")
        return cleaned_count

    def get_local_attachment_url(self, attachment_info: AttachmentInfo) -> str:
        """
        获取本地附件的访问URL

        Args:
            attachment_info: 附件信息

        Returns:
            本地访问URL
        """
        # 这里返回相对路径，前端可以通过静态文件服务访问
        return f"/attachments/{attachment_info.local_path}"

    def verify_file_integrity(self, attachment_info: AttachmentInfo) -> bool:
        """
        验证文件完整性

        Args:
            attachment_info: 附件信息

        Returns:
            文件是否完整
        """
        file_path = self.base_dir / attachment_info.local_path

        if not file_path.exists():
            return False

        try:
            current_md5 = self._calculate_md5(file_path)
            return current_md5 == attachment_info.md5_hash
        except Exception as e:
            logger.error(f"验证文件完整性失败 {file_path}: {e}")
            return False
