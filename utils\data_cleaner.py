#!/usr/bin/env python3
"""
数据清洗工具模块
处理云商接口数据的各种格式问题
"""

import logging
import re
from typing import Any, Dict, List, Optional, Union

logger = logging.getLogger(__name__)


def clean_integer_field(value: Any) -> Optional[int]:
    """
    清洗整数字段，处理逗号分隔的值
    
    Args:
        value: 原始值
        
    Returns:
        清洗后的整数值或None
    """
    if not value:
        return None
    
    if isinstance(value, (int, float)):
        return int(value)
    
    if isinstance(value, str):
        # 移除逗号、空格和其他非数字字符
        cleaned = re.sub(r'[^0-9.-]', '', value.strip())
        
        # 如果原值包含逗号，可能是多个ID，取第一个
        if ',' in value:
            parts = value.split(',')
            if parts:
                cleaned = re.sub(r'[^0-9.-]', '', parts[0].strip())
        
        try:
            return int(float(cleaned)) if cleaned else None
        except (ValueError, TypeError):
            logger.warning(f"无法转换为整数: {value}")
            return None
    
    return None


def clean_text_field(value: Any, max_length: Optional[int] = None) -> str:
    """
    清洗文本字段
    
    Args:
        value: 原始值
        max_length: 最大长度限制
        
    Returns:
        清洗后的文本
    """
    if not value:
        return ''
    
    if not isinstance(value, str):
        value = str(value)
    
    # 移除首尾空格
    cleaned = value.strip()
    
    # 移除特殊控制字符
    cleaned = re.sub(r'[ --]', '', cleaned)
    
    # 限制长度
    if max_length and len(cleaned) > max_length:
        cleaned = cleaned[:max_length]
    
    return cleaned


def clean_comma_separated_ids(value: Any) -> Optional[str]:
    """
    清洗逗号分隔的ID字符串
    
    Args:
        value: 原始值
        
    Returns:
        清洗后的ID字符串
    """
    if not value:
        return None
    
    if isinstance(value, (int, float)):
        return str(int(value))
    
    if isinstance(value, str):
        # 移除多余的空格和特殊字符
        cleaned = re.sub(r'[^0-9,]', '', value.strip())
        
        # 移除连续的逗号
        cleaned = re.sub(r',+', ',', cleaned)
        
        # 移除首尾逗号
        cleaned = cleaned.strip(',')
        
        return cleaned if cleaned else None
    
    return str(value) if value else None


def clean_product_data(product_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    清洗产品数据
    
    Args:
        product_data: 原始产品数据
        
    Returns:
        清洗后的产品数据
    """
    if not isinstance(product_data, dict):
        logger.warning(f"产品数据格式错误: {type(product_data)}")
        return {}
    
    cleaned = {}
    
    # 基本字段清洗
    cleaned['id'] = clean_integer_field(product_data.get('id'))
    cleaned['name'] = clean_text_field(product_data.get('name'), 255)
    cleaned['description'] = clean_text_field(product_data.get('description'))
    cleaned['details'] = clean_text_field(product_data.get('details'))
    
    # 处理分类ID - 可能是逗号分隔的
    category_id = product_data.get('categoryId') or product_data.get('category_id')
    cleaned['category_id'] = clean_comma_separated_ids(category_id)
    
    # 处理标签ID - 可能是逗号分隔的
    label_id = product_data.get('labelId') or product_data.get('label_id')
    cleaned['label_id'] = clean_comma_separated_ids(label_id)
    
    # 其他数值字段
    cleaned['brand_id'] = clean_integer_field(product_data.get('brandId'))
    cleaned['site_id'] = clean_integer_field(product_data.get('siteId'))
    
    # 文本字段
    cleaned['price'] = clean_text_field(product_data.get('price'), 50)
    cleaned['status'] = clean_text_field(product_data.get('status'), 10)
    cleaned['brand_name'] = clean_text_field(product_data.get('brandName'), 100)
    cleaned['category_name'] = clean_text_field(product_data.get('categoryName'), 100)
    
    # 时间字段
    cleaned['create_time'] = product_data.get('createTime')
    cleaned['update_time'] = product_data.get('updateTime')
    
    return cleaned


def extract_brands_from_products(products: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    从产品数据中提取品牌信息
    
    Args:
        products: 产品数据列表
        
    Returns:
        品牌数据列表
    """
    brands_dict = {}
    
    for product in products:
        brand_id = product.get('brandId')
        brand_name = product.get('brandName')
        
        if brand_id and brand_id not in brands_dict:
            brands_dict[brand_id] = {
                'id': brand_id,
                'name': brand_name or f'品牌_{brand_id}',
                'status': '1',
                'site_id': product.get('siteId', 999),
                'create_time': product.get('createTime'),
                'update_time': product.get('updateTime')
            }
    
    return list(brands_dict.values())


def extract_categories_from_products(products: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """
    从产品数据中提取分类信息
    
    Args:
        products: 产品数据列表
        
    Returns:
        分类数据列表
    """
    categories_dict = {}
    
    for product in products:
        category_id = product.get('categoryId')
        category_name = product.get('categoryName')
        
        if category_id:
            # 处理逗号分隔的分类ID
            if isinstance(category_id, str) and ',' in category_id:
                category_ids = category_id.split(',')
                category_names = category_name.split(',') if category_name else []
                
                for i, cat_id in enumerate(category_ids):
                    cat_id = cat_id.strip()
                    cat_name = category_names[i].strip() if i < len(category_names) else f'分类_{cat_id}'
                    
                    if cat_id and cat_id not in categories_dict:
                        categories_dict[cat_id] = {
                            'id': cat_id,
                            'name': cat_name,
                            'status': '1',
                            'site_id': product.get('siteId', 999),
                            'parent_id': None,
                            'create_time': product.get('createTime'),
                            'update_time': product.get('updateTime')
                        }
            else:
                if category_id not in categories_dict:
                    categories_dict[category_id] = {
                        'id': category_id,
                        'name': category_name or f'分类_{category_id}',
                        'status': '1',
                        'site_id': product.get('siteId', 999),
                        'parent_id': None,
                        'create_time': product.get('createTime'),
                        'update_time': product.get('updateTime')
                    }
    
    return list(categories_dict.values())


def safe_extract_api_data(api_response: Any, data_type: str = "unknown") -> List[Dict[str, Any]]:
    """
    安全地从API响应中提取数据
    
    Args:
        api_response: API响应
        data_type: 数据类型（用于日志）
        
    Returns:
        提取的数据列表
    """
    try:
        if not api_response:
            logger.warning(f"API响应为空: {data_type}")
            return []
        
        # 处理不同的响应格式
        if isinstance(api_response, list):
            return api_response
        
        if isinstance(api_response, dict):
            # 尝试不同的数据字段
            for field in ['data', 'rows', 'list', 'items', 'content', 'result']:
                if field in api_response:
                    data = api_response[field]
                    if isinstance(data, list):
                        return data
                    elif isinstance(data, dict):
                        if 'rows' in data:
                            return data['rows'] or []
                        elif 'list' in data:
                            return data['list'] or []
            
            # 如果没有找到列表数据，返回单个对象
            return [api_response] if api_response else []
        
        logger.warning(f"未知的API响应格式: {type(api_response)} for {data_type}")
        return []
        
    except Exception as e:
        logger.error(f"提取API数据失败 ({data_type}): {e}")
        return []
