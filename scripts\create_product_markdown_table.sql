-- 创建产品Markdown确认表
-- 该表用于存储Excel导入后转换为Markdown格式的产品信息，等待用户确认后再推送到FastGPT

CREATE TABLE IF NOT EXISTS product_markdown_queue (
    id SERIAL PRIMARY KEY,
    batch_id VARCHAR(50) NOT NULL,
    row_number INTEGER NOT NULL,
    product_model VARCHAR(255) NOT NULL,
    product_id INTEGER,
    product_name VARCHAR(500),
    product_data JSONB,
    markdown_content TEXT,
    dataset_id VARCHAR(100),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'confirmed', 'rejected', 'uploaded')),
    fastgpt_data_id VARCHAR(100),
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100) DEFAULT 'system',
    updated_by VARCHAR(100),
    
    -- 约束
    CONSTRAINT unique_batch_model UNIQUE (batch_id, product_model),
    
    -- 索引
    INDEX idx_product_markdown_batch (batch_id),
    INDEX idx_product_markdown_status (status),
    INDEX idx_product_markdown_model (product_model),
    INDEX idx_product_markdown_created (created_at),
    INDEX idx_product_markdown_dataset (dataset_id)
);

-- 添加表注释
COMMENT ON TABLE product_markdown_queue IS '产品Markdown确认队列表，存储Excel导入后待确认的产品信息';
COMMENT ON COLUMN product_markdown_queue.id IS '主键ID';
COMMENT ON COLUMN product_markdown_queue.batch_id IS '批次ID，与excel_import_records关联';
COMMENT ON COLUMN product_markdown_queue.row_number IS 'Excel行号';
COMMENT ON COLUMN product_markdown_queue.product_model IS '产品型号';
COMMENT ON COLUMN product_markdown_queue.product_id IS '关联的产品ID（如果匹配成功）';
COMMENT ON COLUMN product_markdown_queue.product_name IS '产品名称';
COMMENT ON COLUMN product_markdown_queue.product_data IS '完整的产品数据JSON';
COMMENT ON COLUMN product_markdown_queue.markdown_content IS 'Markdown格式的产品信息';
COMMENT ON COLUMN product_markdown_queue.dataset_id IS 'FastGPT数据集ID';
COMMENT ON COLUMN product_markdown_queue.status IS '状态：pending-待确认，confirmed-已确认，rejected-已拒绝，uploaded-已上传';
COMMENT ON COLUMN product_markdown_queue.fastgpt_data_id IS 'FastGPT数据ID（上传成功后）';
COMMENT ON COLUMN product_markdown_queue.error_message IS '错误信息';
COMMENT ON COLUMN product_markdown_queue.created_at IS '创建时间';
COMMENT ON COLUMN product_markdown_queue.updated_at IS '更新时间';
COMMENT ON COLUMN product_markdown_queue.created_by IS '创建者';
COMMENT ON COLUMN product_markdown_queue.updated_by IS '更新者';

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_product_markdown_queue_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER trigger_update_product_markdown_queue_updated_at
    BEFORE UPDATE ON product_markdown_queue
    FOR EACH ROW
    EXECUTE FUNCTION update_product_markdown_queue_updated_at();

-- 如果需要，添加外键约束（确保相关表存在）
-- ALTER TABLE product_markdown_queue 
-- ADD CONSTRAINT fk_product_markdown_batch 
-- FOREIGN KEY (batch_id) REFERENCES excel_import_records(batch_id) ON DELETE CASCADE;

-- ALTER TABLE product_markdown_queue 
-- ADD CONSTRAINT fk_product_markdown_product 
-- FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE SET NULL; 