#!/usr/bin/env python3
"""
测试所有字段解析
验证重要字段是否正确解析和存储
"""

import os
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 加载环境变量
from config import load_env_file
load_env_file()

from utils.api_client import ZKMallClient
from utils.auth import AuthManager
from utils.logging_config import get_logger
from services.complete_product_parser import CompleteProductParser

logger = get_logger()


def test_all_fields_parsing():
    """测试所有字段解析"""
    try:
        print("🚀 开始测试所有字段解析")
        
        # 1. 登录认证
        print("\n🔐 步骤1: 用户登录认证")
        success, user_info = AuthManager.login("18929343717", "Zk@123456")
        
        if not success:
            print("❌ 登录失败")
            return
            
        print(f"✅ 登录成功")
        
        # 2. 初始化服务
        print("\n🔧 步骤2: 初始化解析器")
        api_client = ZKMallClient()
        parser = CompleteProductParser()
        print("✅ 解析器初始化成功")
        
        # 3. 获取产品数据
        print("\n📦 步骤3: 获取产品数据")
        products = api_client.get_products(pageSize=5, current=1)
        
        if not products:
            print("❌ 未获取到产品数据")
            return
            
        print(f"✅ 获取到 {len(products)} 个产品")
        
        # 4. 测试重要字段解析
        print("\n🔍 步骤4: 测试重要字段解析")
        
        # 您提到的重要字段
        important_fields = [
            'use_to',  # 应用于
            'new_param',  # 设备参数介绍功能介绍新的
            'param_info',  # 参数信息
            'param_info_list',  # 参数信息列表
            'accessory_list',  # 配件列表
            'category_id',  # 分类ID
            'category_name',  # 分类名称
            'common_problem',  # 常见问题
            'is_hot',  # 是否热门
            'is_new',  # 是否新品
            'label_id',  # 标签ID
            'label_list',  # 标签列表
            'label_name',  # 标签名称
            'update_time',  # 更新时间
            'video_explanation',  # 说明视频
            'video_installation',  # 安装视频
            'video_troubleshooting',  # 故障排除视频
        ]
        
        for i, product in enumerate(products, 1):
            print(f"\n📋 产品 {i}: {product.get('name', 'Unknown')}")
            print(f"原始字段数量: {len(product)}")
            
            # 解析产品
            parsed_product = parser.parse_product(product)
            print(f"解析后字段数量: {len(parsed_product)}")
            
            # 检查重要字段
            print("重要字段检查:")
            found_fields = []
            missing_fields = []
            
            for field in important_fields:
                if field in parsed_product and parsed_product[field] is not None:
                    value = parsed_product[field]
                    if isinstance(value, str) and len(value) > 50:
                        display_value = value[:50] + "..."
                    else:
                        display_value = value
                    print(f"  ✅ {field}: {display_value}")
                    found_fields.append(field)
                else:
                    print(f"  ❌ {field}: 缺失或为空")
                    missing_fields.append(field)
            
            print(f"找到重要字段: {len(found_fields)}/{len(important_fields)}")
            
            # 显示原始数据中的对应字段
            print("\n原始数据中的对应字段:")
            original_field_mapping = {
                'use_to': 'useTo',
                'new_param': 'newParam', 
                'param_info': 'paramInfo',
                'param_info_list': 'paramInfoList',
                'accessory_list': 'accessoryList',
                'category_id': 'category',
                'category_name': 'categoryName',
                'common_problem': 'commonProblem',
                'is_hot': 'isHot',
                'is_new': 'isNew',
                'label_id': 'label',
                'label_list': 'labelList',
                'label_name': 'labelName',
                'update_time': 'updateTime',
                'video_explanation': 'videoExplanation',
                'video_installation': 'videoInstallation',
                'video_troubleshooting': 'videoTroubleshooting',
            }
            
            for parsed_field, original_field in original_field_mapping.items():
                original_value = product.get(original_field)
                if original_value is not None:
                    if isinstance(original_value, str) and len(original_value) > 50:
                        display_value = original_value[:50] + "..."
                    else:
                        display_value = original_value
                    print(f"  📄 {original_field} -> {parsed_field}: {display_value}")
                else:
                    print(f"  📄 {original_field} -> {parsed_field}: 原始数据中也为空")
            
            # 显示解析统计
            missing_fields_from_parser = parser.get_missing_fields()
            print(f"\n解析器报告的未解析字段数量: {len(missing_fields_from_parser)}")
            if missing_fields_from_parser:
                print(f"未解析字段: {missing_fields_from_parser[:10]}...")  # 只显示前10个
            
            if i >= 2:  # 只详细分析前2个产品
                break
        
        print("\n🎉 字段解析测试完成！")
        
    except Exception as e:
        logger.error(f"字段解析测试失败: {e}", exc_info=True)
        print(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    test_all_fields_parsing()
