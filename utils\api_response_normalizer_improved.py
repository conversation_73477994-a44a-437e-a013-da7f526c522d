"""
改进的API响应格式标准化工具

该模块提供更强大的API响应格式处理和字段映射功能，解决原版本的格式识别问题。
"""

import logging
from typing import Any, Dict, List, Optional, Union
from datetime import datetime

logger = logging.getLogger(__name__)


class ImprovedApiResponseNormalizer:
    """改进的API响应格式标准化器"""

    # 预定义的字段映射表
    FIELD_MAPPINGS = {
        "product": {
            "productName": "name",
            "category": "categoryId",
            "label": "labelId",
            "labelList": "labels",
            "smallImg": "thumbnail",
            "spec": "specification",
        },
        "case": {
            "caseName": "title",
            "name": "title",
            "category": "categoryId",
            "categoryName": "categoryName",
            "companyName": "companyName",
            "productList": "products",
            "introduction": "description",
            "content": "content",
        },
        "programme": {
            "programmeName": "title",
            "name": "title",
            "category": "categoryId",
        },
        "information": {
            "title": "title",
            "categoryId": "categoryId",
            "categoryName": "categoryName",
            "content": "content",
            "summary": "summary",
            "author": "author",
            "publishTime": "publishTime",
            "readCount": "readCount",
            "coverImage": "coverImage",
        },
        "distribution_order": {
            "firCategoryId": "primaryCategoryId",
            "firCategoryName": "primaryCategoryName",
            "secCategoryId": "secondaryCategoryId",
            "secCategoryName": "secondaryCategoryName",
            "hasExpire": "hasExpired",
        },
    }

    @staticmethod
    def normalize_list_response(
        response: Any,
        field_mapping: Optional[Dict[str, str]] = None,
        data_type: str = "generic",
    ) -> List[Dict[str, Any]]:
        """
        标准化列表响应 - 改进版本

        Args:
            response: 原始API响应
            field_mapping: 自定义字段映射
            data_type: 数据类型 (product, case, programme, etc.)

        Returns:
            标准化后的数据列表
        """
        try:
            # 1. 提取数据列表
            data_list = ImprovedApiResponseNormalizer._extract_list_data(response)

            if not data_list:
                logger.info(f"从响应中未提取到 {data_type} 类型的数据")
                return []

            # 2. 应用字段映射
            if (
                field_mapping
                or data_type in ImprovedApiResponseNormalizer.FIELD_MAPPINGS
            ):
                mapping = (
                    field_mapping
                    or ImprovedApiResponseNormalizer.FIELD_MAPPINGS.get(data_type, {})
                )
                data_list = [
                    ImprovedApiResponseNormalizer.apply_field_mapping(item, mapping)
                    for item in data_list
                ]

            # 3. 数据清洗和验证
            data_list = [
                ImprovedApiResponseNormalizer._clean_data_item(item)
                for item in data_list
                if item
            ]

            logger.info(
                f"成功标准化 {data_type} 类型列表响应，数据量: {len(data_list)}"
            )
            return data_list

        except Exception as e:
            logger.error(f"列表响应标准化失败: {e}")
            return []

    @staticmethod
    def _extract_list_data(response: Any) -> List[Dict[str, Any]]:
        """
        从响应中提取列表数据 - 改进版本

        Args:
            response: 原始响应

        Returns:
            数据列表
        """
        # 详细记录响应格式用于调试
        response_type = type(response).__name__
        logger.debug(f"处理响应类型: {response_type}")

        try:
            # 1. 标准格式: {"code": 200, "data": {"rows": [...]}}
            if isinstance(response, dict) and response.get("code") == 200:
                data = response.get("data", {})
                logger.debug(f"标准格式，data类型: {type(data).__name__}")

                if isinstance(data, dict):
                    # 尝试多种可能的列表字段名
                    list_fields = [
                        "rows",
                        "records",
                        "list",
                        "data",
                        "items",
                        "content",
                    ]
                    for field in list_fields:
                        if field in data and isinstance(data[field], list):
                            logger.debug(
                                f"找到列表字段: {field}，数量: {len(data[field])}"
                            )
                            return data[field]

                    # 如果没有找到列表字段，但data本身可能包含有用信息
                    logger.debug(f"data字段: {list(data.keys())}")

                elif isinstance(data, list):
                    logger.debug(f"data是列表，长度: {len(data)}")
                    return data

            # 2. 直接格式: {"rows": [...]} 或其他列表字段
            elif isinstance(response, dict):
                logger.debug(f"直接格式，响应字段: {list(response.keys())}")

                # 尝试多种可能的列表字段名
                list_fields = ["rows", "records", "list", "data", "items", "content"]
                for field in list_fields:
                    if field in response and isinstance(response[field], list):
                        logger.debug(
                            f"找到列表字段: {field}，数量: {len(response[field])}"
                        )
                        return response[field]

            # 3. 列表格式: [...]
            elif isinstance(response, list):
                logger.debug(f"直接列表格式，长度: {len(response)}")
                return response

            # 4. 其他格式 - 详细分析但不记录为警告
            logger.debug(f"尝试识别非标准响应格式:")
            logger.debug(f"  类型: {response_type}")

            if isinstance(response, dict):
                logger.debug(f"  字典键: {list(response.keys())}")
                # 尝试从字典中找到任何可能的列表
                for key, value in response.items():
                    if isinstance(value, list) and len(value) > 0:
                        # 验证列表内容是否为有效数据
                        if _is_valid_data_list(value):
                            logger.info(f"发现有效列表字段 {key}: {len(value)} 项")
                            return value
                        else:
                            logger.debug(f"字段 {key} 包含列表但验证失败")
                    elif isinstance(value, dict) and any(
                        isinstance(v, list) for v in value.values()
                    ):
                        logger.debug(f"字段 {key} 包含嵌套列表")
                        for nested_key, nested_value in value.items():
                            if isinstance(nested_value, list) and len(nested_value) > 0:
                                if _is_valid_data_list(nested_value):
                                    logger.info(
                                        f"使用嵌套列表 {nested_key}: {len(nested_value)} 项"
                                    )
                                    return nested_value

            logger.info("未找到任何可用的列表数据")
            return []

        except Exception as e:
            logger.error(f"数据提取异常: {e}")
            return []

    @staticmethod
    def apply_field_mapping(
        data: Dict[str, Any], field_mapping: Dict[str, str]
    ) -> Dict[str, Any]:
        """
        应用字段映射

        Args:
            data: 原始数据
            field_mapping: 字段映射表

        Returns:
            映射后的数据
        """
        if not isinstance(data, dict) or not field_mapping:
            return data

        mapped_data = {}

        # 保留未映射的字段
        for key, value in data.items():
            new_key = field_mapping.get(key, key)
            mapped_data[new_key] = value

        return mapped_data

    @staticmethod
    def _clean_data_item(data: Dict[str, Any]) -> Dict[str, Any]:
        """
        清洗数据项

        Args:
            data: 原始数据项

        Returns:
            清洗后的数据项
        """
        if not isinstance(data, dict):
            return data

        cleaned_data = {}

        for key, value in data.items():
            # 清理键名
            clean_key = key.strip() if isinstance(key, str) else key

            # 清理值
            if isinstance(value, str):
                cleaned_value = value.strip()
                # 处理空字符串
                cleaned_value = cleaned_value if cleaned_value else None
            elif isinstance(value, (int, float)):
                cleaned_value = value
            elif isinstance(value, list):
                # 清理列表中的空值
                cleaned_value = [
                    (
                        ImprovedApiResponseNormalizer._clean_data_item(item)
                        if isinstance(item, dict)
                        else item
                    )
                    for item in value
                    if item is not None
                ]
            elif isinstance(value, dict):
                # 递归清理嵌套字典
                cleaned_value = ImprovedApiResponseNormalizer._clean_data_item(value)
            else:
                cleaned_value = value

            cleaned_data[clean_key] = cleaned_value

        return cleaned_data

    @staticmethod
    def normalize_pagination_response(response: Any) -> Dict[str, Any]:
        """
        标准化分页响应

        Args:
            response: 原始API响应

        Returns:
            标准化的分页信息
        """
        pagination = {
            "total": 0,
            "current": 1,
            "size": 20,
            "pages": 0,
            "hasNext": False,
            "hasPrevious": False,
        }

        try:
            # 从标准格式提取
            if (
                isinstance(response, dict)
                and response.get("code") == 200
                and "data" in response
            ):
                data = response["data"]
                if isinstance(data, dict):
                    pagination.update(
                        {
                            "total": data.get("total", 0),
                            "current": data.get("current", 1),
                            "size": data.get("size", data.get("pageSize", 20)),
                            "pages": data.get("pages", 0),
                        }
                    )

            # 从直接格式提取
            elif isinstance(response, dict):
                pagination.update(
                    {
                        "total": response.get("total", 0),
                        "current": response.get("current", 1),
                        "size": response.get("size", response.get("pageSize", 20)),
                        "pages": response.get("pages", 0),
                    }
                )

            # 计算总页数和导航信息
            if pagination["total"] > 0 and pagination["size"] > 0:
                pagination["pages"] = (
                    pagination["total"] + pagination["size"] - 1
                ) // pagination["size"]
                pagination["hasNext"] = pagination["current"] < pagination["pages"]
                pagination["hasPrevious"] = pagination["current"] > 1

            logger.debug(f"分页信息: {pagination}")
            return pagination

        except Exception as e:
            logger.error(f"分页信息提取失败: {e}")
            return pagination

    @staticmethod
    def analyze_response_structure(response: Any) -> Dict[str, Any]:
        """
        分析响应结构

        Args:
            response: API响应

        Returns:
            结构分析结果
        """
        analysis = {
            "type": type(response).__name__,
            "is_standard_format": False,
            "has_data_field": False,
            "data_structure": {},
            "possible_list_fields": [],
            "total_items": 0,
        }

        try:
            if isinstance(response, dict):
                analysis["has_data_field"] = "data" in response
                analysis["is_standard_format"] = (
                    response.get("code") == 200 and "data" in response
                )

                # 分析数据结构
                if "data" in response:
                    data = response["data"]
                    analysis["data_structure"]["type"] = type(data).__name__

                    if isinstance(data, dict):
                        analysis["data_structure"]["fields"] = list(data.keys())
                        # 查找可能的列表字段
                        for key, value in data.items():
                            if isinstance(value, list):
                                analysis["possible_list_fields"].append(
                                    {"field": key, "count": len(value)}
                                )
                                analysis["total_items"] += len(value)
                    elif isinstance(data, list):
                        analysis["total_items"] = len(data)
                        analysis["possible_list_fields"].append(
                            {"field": "data", "count": len(data)}
                        )

                # 直接在根级别查找列表字段
                for key, value in response.items():
                    if isinstance(value, list):
                        analysis["possible_list_fields"].append(
                            {"field": key, "count": len(value)}
                        )

            elif isinstance(response, list):
                analysis["total_items"] = len(response)
                analysis["possible_list_fields"].append(
                    {"field": "root", "count": len(response)}
                )

            logger.debug(f"响应结构分析: {analysis}")
            return analysis

        except Exception as e:
            logger.error(f"响应结构分析失败: {e}")
            analysis["error"] = str(e)
            return analysis


# 便利函数
def normalize_api_response(
    response: Any, data_type: str = "generic"
) -> List[Dict[str, Any]]:
    """
    标准化API响应（便利函数）

    Args:
        response: API响应
        data_type: 数据类型

    Returns:
        标准化的数据列表
    """
    return ImprovedApiResponseNormalizer.normalize_list_response(
        response, data_type=data_type
    )


def analyze_response(response: Any) -> Dict[str, Any]:
    """
    分析响应结构（便利函数）

    Args:
        response: API响应

    Returns:
        分析结果
    """
    return ImprovedApiResponseNormalizer.analyze_response_structure(response)


# 新增从patch文件移植的函数
def improved_extract_list_data(response):
    """改进的列表数据提取函数"""
    try:
        # 详细日志记录
        logger.debug(f"处理响应类型: {type(response)}")

        # 处理空响应
        if not response:
            logger.warning("响应为空")
            return []

        # 1. 标准格式: {"code": 200, "data": {"rows": [...]}} 或 {"code": 200, "rows": [...]}
        if isinstance(response, dict) and response.get("code") == 200:
            # 首先检查根级别是否有列表字段
            priority_fields = [
                "rows",
                "records",
                "list",
                "items",
                "data",
                "results",
                "content",
            ]
            for field in priority_fields:
                if field in response and isinstance(response[field], list):
                    if _is_valid_data_list(response[field]):
                        logger.info(f"从根级{field}提取到{len(response[field])}条记录")
                        return response[field]

            # 然后检查data字段
            data = response.get("data", {})
            if isinstance(data, dict):
                # 尝试多种列表字段，按优先级排序
                for field in priority_fields:
                    if field in data and isinstance(data[field], list):
                        logger.info(f"从data.{field}提取到{len(data[field])}条记录")
                        return data[field]

                # 检查是否data本身包含列表数据
                logger.debug(f"data字段: {list(data.keys())}")

                # 深度搜索data内的列表字段
                for key, value in data.items():
                    if isinstance(value, list) and len(value) > 0:
                        # 验证列表内容是否为有效数据记录
                        if _is_valid_data_list(value):
                            logger.info(f"从data.{key}提取到{len(value)}条记录")
                            return value

            elif isinstance(data, list):
                if _is_valid_data_list(data):
                    logger.info(f"从data提取到{len(data)}条记录")
                    return data

        # 2. 其他成功状态码检查
        elif isinstance(response, dict) and str(response.get("code", "")).startswith(
            "2"
        ):
            data = response.get("data", response)
            if isinstance(data, list) and _is_valid_data_list(data):
                logger.info(f"从成功响应提取到{len(data)}条记录")
                return data

        # 3. 直接格式检查
        elif isinstance(response, dict):
            # 按优先级检查常见字段
            priority_fields = [
                "rows",
                "records",
                "list",
                "items",
                "data",
                "results",
                "content",
            ]
            for field in priority_fields:
                if field in response and isinstance(response[field], list):
                    if _is_valid_data_list(response[field]):
                        logger.info(f"从{field}提取到{len(response[field])}条记录")
                        return response[field]

            # 深度搜索列表数据 - 先检查一级嵌套
            for key, value in response.items():
                if isinstance(value, dict):
                    # 检查嵌套字典中的列表字段
                    for nested_field in priority_fields:
                        if nested_field in value and isinstance(
                            value[nested_field], list
                        ):
                            if _is_valid_data_list(value[nested_field]):
                                logger.info(
                                    f"从{key}.{nested_field}提取到{len(value[nested_field])}条记录"
                                )
                                return value[nested_field]
                elif isinstance(value, list) and len(value) > 0:
                    # 验证列表内容
                    if _is_valid_data_list(value):
                        logger.info(f"从{key}提取到{len(value)}条记录")
                        return value

        # 4. 直接列表
        elif isinstance(response, list):
            if _is_valid_data_list(response):
                logger.info(f"直接列表格式，{len(response)}条记录")
                return response

        logger.warning("未找到可用的列表数据")
        return []

    except Exception as e:
        logger.error(f"数据提取异常: {e}", exc_info=True)
        return []


def _is_valid_data_list(data_list):
    """验证列表是否为有效的数据记录列表"""
    if not isinstance(data_list, list) or len(data_list) == 0:
        return False

    # 检查第一个元素
    first_item = data_list[0]

    # 如果是字典，检查是否包含有意义的数据字段
    if isinstance(first_item, dict):
        # 排除只包含简单值的字典
        if len(first_item) < 1:
            return False

        # 检查是否包含常见的数据字段
        data_indicators = [
            "id",
            "name",
            "title",
            "code",
            "type",
            "status",
            "createTime",
            "updateTime",
            "productId",
            "productName",
            "caseId",
            "caseName",
            "programmeId",
            "programmeName",
            "categoryId",
            "categoryName",
            "description",
            "content",
        ]
        has_data_field = any(key in first_item for key in data_indicators)

        # 如果没有明显的数据字段，但字典结构复杂，也认为是有效的
        if not has_data_field:
            # 检查是否有复杂的嵌套结构
            complex_fields = sum(
                1
                for v in first_item.values()
                if isinstance(v, (dict, list)) or (isinstance(v, str) and len(v) > 20)
            )
            if complex_fields >= 1:
                return True

            # 检查字段数量 - 更宽松的判断
            if len(first_item) >= 2:
                return True

        return has_data_field

    # 如果是其他类型，需要进一步判断
    return isinstance(first_item, (str, int, float)) and len(data_list) > 0


def extract_list_data_with_fallback(response, fallback_extractors=None):
    """带回退机制的数据提取函数"""
    # 首先尝试主要提取函数
    result = improved_extract_list_data(response)
    if result:
        return result

    # 如果主要提取失败，尝试回退提取器
    if fallback_extractors:
        for extractor_name, extractor_func in fallback_extractors.items():
            try:
                logger.debug(f"尝试回退提取器: {extractor_name}")
                result = extractor_func(response)
                if result:
                    logger.info(
                        f"回退提取器 {extractor_name} 成功提取 {len(result)} 条记录"
                    )
                    return result
            except Exception as e:
                logger.warning(f"回退提取器 {extractor_name} 失败: {e}")

    logger.warning("所有提取方法都失败")
    return []
