---
description: 
globs: 
alwaysApply: true
---
# 文件命名规范

## 📋 概述

本项目需要遵循统一的文件命名规范，以确保代码的可维护性和可读性。特别是对于多语言环境下的开发，文件名应当使用英文而非中文或其他非ASCII字符。

## 🚫 禁止使用的命名方式

### 1. 中文文件名
- **禁止使用中文字符作为文件名**，如当前的 `01_📦_产品管理.py`、`02_📋_产品详情.py` 等
- 中文文件名在不同操作系统和环境下可能导致路径解析问题
- 不利于国际化协作和代码共享

### 2. 特殊字符
- **避免使用表情符号**，如 `📦`、`📋` 等
- 特殊字符可能在某些文件系统或工具中不被正确支持

## ✅ 推荐的命名方式

### 1. 英文命名
- 使用有意义的英文单词作为文件名
- 例如：`product_management.py` 替代 `01_📦_产品管理.py`
- 例如：`product_detail.py` 替代 `02_📋_产品详情.py`

### 2. 命名规范
- 使用小写字母
- 单词之间用下划线连接（snake_case）
- 可以使用数字前缀表示顺序，如 `01_product_management.py`

## 🔄 重命名计划

以下是需要重命名的文件列表：

1. `pages/01_📦_产品管理.py` → `pages/01_product_management.py`
2. `pages/02_📋_产品详情.py` → `pages/02_product_detail.py`
3. `pages/03_📋_案例管理.py` → `pages/03_case_management.py`
4. `pages/04_🎯_方案管理.py` → `pages/04_programme_management.py`
5. `pages/05_📰_资讯管理.py` → `pages/05_information_management.py`
6. `pages/06_📊_配单管理.py` → `pages/06_distribution_management.py`

## ⚠️ 注意事项

- 重命名文件后，需要更新所有引用这些文件的代码
- 特别注意 `st.switch_page()` 函数调用中的路径引用
- 确保在版本控制系统中正确记录文件重命名操作

## 📝 实施建议

建议分批进行文件重命名，每次重命名一个文件并立即测试，以确保系统功能正常：

1. 首先创建新文件（使用英文名称）
2. 复制原文件内容到新文件
3. 更新所有引用
4. 测试功能
5. 删除原文件

---

**文档版本**: v1.0  
**最后更新**: 2024-12-19  
**维护者**: 开发团队

