#!/usr/bin/env python3
"""
产品知识库功能测试脚本

测试产品知识库管理的各项功能：
1. 产品知识块创建
2. 文档处理功能
3. FastGPT同步功能
4. 类别映射功能

严格遵循开发规范，使用真实API接口，禁止模拟数据。
"""

import logging
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.product_knowledge_manager import ProductKnowledgeManager
from services.document_processor import DocumentProcessor
from utils.api_client import ZKMallClient
from config import config

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def test_product_knowledge_manager():
    """测试产品知识库管理器"""
    try:
        logger.info("开始测试产品知识库管理器")

        # 初始化管理器
        knowledge_manager = ProductKnowledgeManager()

        # 测试类别映射
        logger.info("测试类别映射功能")
        test_categories = ["门禁", "考勤", "人脸识别", "安检", "未知类别"]

        for category in test_categories:
            dataset_id = knowledge_manager.get_dataset_id_by_category(category)
            logger.info(f"类别 '{category}' -> 知识库ID: {dataset_id}")

        # 获取测试产品数据
        api_client = ZKMallClient()
        products = api_client.get_products(pageSize=5)

        if not products:
            logger.warning("未获取到产品数据，跳过产品知识块测试")
            return False

        # 测试创建产品知识块
        logger.info("测试创建产品知识块")
        test_product = products[0]
        product_model = test_product.get("model", "") or test_product.get("name", "")

        if product_model:
            logger.info(f"为产品 '{product_model}' 创建知识块")

            knowledge_block = knowledge_manager.create_product_knowledge_block(
                product_model, test_product
            )

            if knowledge_block:
                logger.info("产品知识块创建成功")
                logger.info(f"知识块标题: {knowledge_block.get('title', '')}")
                logger.info(
                    f"知识块摘要: {knowledge_block.get('summary', '')[:100]}..."
                )
                logger.info(f"标签数量: {len(knowledge_block.get('tags', []))}")
                logger.info(f"索引数量: {len(knowledge_block.get('indexes', []))}")
                logger.info(f"文档数量: {len(knowledge_block.get('documents', []))}")

                # 测试同步到FastGPT（如果配置了API密钥）
                fastgpt_config = config.get_fastgpt_config()
                if fastgpt_config.get("api_key"):
                    logger.info("测试同步到FastGPT")
                    success, data_id, response = (
                        knowledge_manager.sync_product_to_fastgpt(
                            product_model, knowledge_block
                        )
                    )

                    if success:
                        logger.info(f"同步到FastGPT成功，数据ID: {data_id}")
                    else:
                        logger.warning(f"同步到FastGPT失败: {response}")
                else:
                    logger.info("未配置FastGPT API密钥，跳过同步测试")

                return True
            else:
                logger.error("产品知识块创建失败")
                return False
        else:
            logger.warning("产品型号为空，跳过知识块创建测试")
            return False

    except Exception as e:
        logger.error(f"测试产品知识库管理器失败: {e}")
        return False


def test_document_processor():
    """测试文档处理器"""
    try:
        logger.info("开始测试文档处理器")

        # 初始化处理器
        doc_processor = DocumentProcessor()

        # 测试文档类型检测
        logger.info("测试文档类型检测")
        test_files = [
            "ZK3969_install.pdf",
            "MB460_user_manual.doc",
            "config_guide_ZK3969.docx",
            "spec_sheet_MB460.txt",
            "quick_start_guide.md",
            "troubleshooting_ZK3969.pdf",
            "firmware_update_MB460.doc",
        ]

        for filename in test_files:
            doc_type = doc_processor._classify_document_type(filename)
            priority = doc_processor._get_document_priority(doc_type)
            logger.info(f"文件 '{filename}' -> 类型: {doc_type} (优先级: {priority})")

        # 测试产品型号推断
        logger.info("\n测试产品型号推断")
        test_paths = [
            "documents/ZK3969/install_guide.pdf",
            "docs/products/MB460/user_manual.doc",
            "attachments/ZK-3969A_spec.txt",
            "files/general_config.md",
        ]

        for path in test_paths:
            inferred_model = doc_processor._infer_product_model_from_path(path)
            logger.info(f"路径 '{path}' -> 推断型号: '{inferred_model}'")

        # 测试相关性计算
        logger.info("\n测试文档相关性计算")
        product_model = "ZK3969"

        for filename in test_files:
            is_related = doc_processor._is_product_related_document(
                filename, product_model
            )
            relevance_score = doc_processor._calculate_relevance_score(
                filename, product_model
            )
            logger.info(
                f"文件 '{filename}' 与产品 '{product_model}' -> 相关: {is_related}, 得分: {relevance_score:.2f}"
            )

        # 测试全局文档扫描
        logger.info("\n测试全局文档扫描")
        all_docs = doc_processor.scan_all_product_documents()

        logger.info(f"扫描结果: 找到 {len(all_docs)} 种文档类型")
        for doc_type, docs in all_docs.items():
            logger.info(f"- {doc_type}: {len(docs)} 个文件")
            if docs:  # 显示前3个文件
                for doc in docs[:3]:
                    logger.info(
                        f"  * {doc['file_name']} (得分: {doc['relevance_score']:.2f})"
                    )

        # 测试获取产品文档类型
        logger.info(f"\n测试获取产品 '{product_model}' 的文档类型")
        doc_types = doc_processor.get_document_types_for_display(product_model)
        logger.info(f"找到 {len(doc_types)} 种文档类型")

        for doc_type_info in doc_types:
            logger.info(
                f"- {doc_type_info['document_type']}: {doc_type_info['file_count']} 个文件 (优先级: {doc_type_info['priority']})"
            )
            # 显示前2个文件
            for file_info in doc_type_info["files"][:2]:
                logger.info(
                    f"  * {file_info['file_name']} (相关性: {file_info['relevance_score']:.2f})"
                )

        # 测试根据产品型号获取文档
        logger.info(f"\n测试根据产品型号 '{product_model}' 获取相关文档")
        related_docs = doc_processor.get_documents_by_product_model(product_model)
        logger.info(f"找到 {len(related_docs)} 个相关文档")

        for doc in related_docs[:5]:  # 显示前5个
            logger.info(
                f"- {doc['file_name']} ({doc['document_type']}) 得分: {doc['relevance_score']:.2f}"
            )

        return True

    except Exception as e:
        logger.error(f"测试文档处理器失败: {e}")
        return False


def test_markdown_optimization():
    """测试Markdown格式优化"""
    try:
        logger.info("开始测试Markdown格式优化")

        doc_processor = DocumentProcessor()

        # 测试内容
        test_content = """
# 产品说明书

## 产品概述
这是一款门禁产品。

![产品图片](image1.jpg)

## 技术参数
- 识别方式：人脸识别
- 识别距离：0.3-1.5米

### 详细参数
工作温度：-20°C ~ +60°C

## 安装说明
请按照以下步骤安装...

### 步骤1
连接电源线

### 步骤2
配置网络参数
        """

        # 优化格式
        optimized_content = doc_processor._optimize_markdown_format(
            test_content, "测试文档"
        )
        logger.info("Markdown格式优化完成")

        # 按二级标题分段
        segments = doc_processor._segment_by_h2_headers(optimized_content, "测试文档")
        logger.info(f"按二级标题分段完成，共 {len(segments)} 个段落")

        for i, segment in enumerate(segments):
            first_line = segment.split("\n")[0]
            logger.info(f"段落 {i+1} 标题: {first_line}")

        return True

    except Exception as e:
        logger.error(f"测试Markdown格式优化失败: {e}")
        return False


def test_batch_processing():
    """测试批量处理功能"""
    try:
        logger.info("开始测试批量处理功能")

        knowledge_manager = ProductKnowledgeManager()

        # 测试小批量处理（只处理前3个产品）
        logger.info("执行小批量测试处理")

        # 获取少量产品进行测试
        api_client = ZKMallClient()
        test_products = api_client.get_products(pageSize=3)

        if not test_products:
            logger.warning("未获取到测试产品数据")
            return False

        # 模拟批量处理
        results = {
            "total": len(test_products),
            "success": 0,
            "failed": 0,
            "details": [],
            "errors": [],
        }

        for product in test_products:
            try:
                product_model = product.get("model", "") or product.get("name", "")
                if not product_model:
                    results["failed"] += 1
                    results["errors"].append("产品型号为空")
                    continue

                # 创建知识块
                knowledge_block = knowledge_manager.create_product_knowledge_block(
                    product_model, product
                )

                if knowledge_block:
                    results["success"] += 1
                    results["details"].append(
                        {
                            "product_model": product_model,
                            "status": "success",
                            "dataset_id": knowledge_block.get("dataset_id", ""),
                        }
                    )
                    logger.info(f"产品 '{product_model}' 处理成功")
                else:
                    results["failed"] += 1
                    results["errors"].append(f"产品 '{product_model}' 知识块创建失败")

            except Exception as e:
                results["failed"] += 1
                results["errors"].append(f"处理产品失败: {str(e)}")

        logger.info(
            f"批量处理测试完成: 成功 {results['success']}, 失败 {results['failed']}"
        )

        return results["success"] > 0

    except Exception as e:
        logger.error(f"测试批量处理功能失败: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    try:
        logger.info("=" * 60)
        logger.info("开始运行产品知识库功能测试")
        logger.info("=" * 60)

        test_results = {}

        # 运行各项测试
        test_results["产品知识库管理器"] = test_product_knowledge_manager()
        test_results["文档处理器"] = test_document_processor()
        test_results["Markdown优化"] = test_markdown_optimization()
        test_results["批量处理"] = test_batch_processing()

        # 汇总结果
        logger.info("=" * 60)
        logger.info("测试结果汇总")
        logger.info("=" * 60)

        passed = 0
        total = len(test_results)

        for test_name, result in test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            logger.info(f"{test_name}: {status}")
            if result:
                passed += 1

        logger.info("=" * 60)
        logger.info(f"测试完成: {passed}/{total} 项测试通过")

        if passed == total:
            logger.info("🎉 所有测试通过！产品知识库功能正常")
            return True
        else:
            logger.warning(f"⚠️  有 {total - passed} 项测试失败，请检查相关功能")
            return False

    except Exception as e:
        logger.error(f"运行测试时发生错误: {e}")
        return False


if __name__ == "__main__":
    # 设置环境变量（如果需要）
    if not os.getenv("ZKMALL_PASSWORD"):
        logger.warning("未设置ZKMALL_PASSWORD环境变量，部分测试可能失败")

    # 运行测试
    success = run_all_tests()

    # 退出码
    sys.exit(0 if success else 1)
