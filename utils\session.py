import streamlit as st
from typing import Any, Optional

class SessionManager:
    """Session状态管理器"""
    
    @staticmethod
    def get(key: str, default: Any = None) -> Any:
        """获取session值"""
        return st.session_state.get(key, default)
    
    @staticmethod
    def set(key: str, value: Any) -> None:
        """设置session值"""
        st.session_state[key] = value
    
    @staticmethod
    def delete(key: str) -> None:
        """删除session值"""
        if key in st.session_state:
            del st.session_state[key]
    
    @staticmethod
    def clear() -> None:
        """清空所有session"""
        st.session_state.clear()
    
    @staticmethod
    def has_key(key: str) -> bool:
        """检查session是否包含指定键"""
        return key in st.session_state 