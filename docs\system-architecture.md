# 云商系统架构设计文档

## 🏗️ 系统概述

基于熵基云商接口对接的Streamlit应用系统架构设计，采用现代化的分层架构模式，确保系统的可扩展性、可维护性和高性能。

## 🎯 设计原则

### 核心原则
1. **分层解耦** - 清晰的分层架构，降低模块间耦合
2. **数据驱动** - 以数据为中心的设计理念
3. **安全优先** - 全链路安全保障
4. **性能至上** - 高性能的缓存和优化策略
5. **可扩展性** - 支持水平和垂直扩展

### 开发规范
- 严格遵循Streamlit开发规范
- 禁止模拟数据，确保真实环境运行
- 完整的错误处理和日志记录
- 自动化测试覆盖

## 🏛️ 总体架构

```mermaid
graph TB
    subgraph "用户层"
        UI[Streamlit Web UI]
        Mobile[移动端界面]
    end
    
    subgraph "应用层"
        Auth[认证服务]
        Product[产品服务]
        Case[案例服务]
        Programme[方案服务]
        Info[资讯服务]
        Order[配单服务]
    end
    
    subgraph "业务逻辑层"
        BL[业务逻辑处理]
        Valid[数据验证]
        Cache[缓存管理]
        Search[搜索引擎]
    end
    
    subgraph "数据访问层"
        DAO[数据访问对象]
        Pool[连接池]
        Trans[事务管理]
    end
    
    subgraph "数据层"
        PG[(PostgreSQL)]
        Redis[(Redis缓存)]
        Files[文件存储]
    end
    
    subgraph "外部服务"
        ZKMall[熵基云商API]
        OSS[对象存储]
        CDN[内容分发网络]
    end
    
    UI --> Auth
    UI --> Product
    UI --> Case
    UI --> Programme
    UI --> Info
    UI --> Order
    
    Auth --> BL
    Product --> BL
    Case --> BL
    Programme --> BL
    Info --> BL
    Order --> BL
    
    BL --> Valid
    BL --> Cache
    BL --> Search
    
    Valid --> DAO
    Cache --> Redis
    Search --> DAO
    
    DAO --> Pool
    Pool --> Trans
    Trans --> PG
    
    BL --> ZKMall
    Files --> OSS
    UI --> CDN
```

## 📱 前端架构设计

### Streamlit应用结构
```
streamlit_app/
├── main.py                 # 应用入口
├── pages/                  # 页面模块
│   ├── 01_🏠_首页.py
│   ├── 02_📦_产品管理.py
│   ├── 03_📋_案例管理.py
│   ├── 04_🎯_方案管理.py
│   ├── 05_📰_资讯管理.py
│   └── 06_📊_配单管理.py
├── components/             # 可复用组件
│   ├── __init__.py
│   ├── auth.py            # 认证组件
│   ├── product.py         # 产品组件
│   ├── case.py            # 案例组件
│   ├── programme.py       # 方案组件
│   ├── information.py     # 资讯组件
│   ├── order.py           # 配单组件
│   ├── charts.py          # 图表组件
│   ├── forms.py           # 表单组件
│   └── widgets.py         # 自定义小部件
└── utils/                 # 工具函数
    ├── __init__.py
    ├── session.py         # 会话管理
    ├── api_client.py      # API客户端
    ├── data_processing.py # 数据处理
    ├── cache.py          # 缓存管理
    └── validators.py     # 数据验证
```

### 页面设计模式
```python
# 标准页面模板
import streamlit as st
from typing import Optional, Dict, Any
import logging
from components.auth import require_auth
from utils.session import SessionManager
from utils.api_client import ZKMallClient

logger = logging.getLogger(__name__)

@require_auth
def main():
    """页面主函数"""
    st.set_page_config(
        page_title="产品管理",
        page_icon="📦",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # 初始化session
    initialize_session()
    
    # 渲染页面
    render_header()
    render_sidebar()
    render_content()
    render_footer()

def initialize_session():
    """初始化页面session状态"""
    defaults = {
        'current_product': None,
        'search_filters': {},
        'pagination': {'page': 1, 'size': 20}
    }
    
    for key, value in defaults.items():
        if key not in st.session_state:
            SessionManager.set(key, value)

def render_header():
    """渲染页面头部"""
    st.title("📦 产品管理")
    st.markdown("管理和查看熵基云商产品信息")

def render_sidebar():
    """渲染侧边栏"""
    with st.sidebar:
        st.subheader("筛选条件")
        
        # 分类筛选
        category = st.selectbox("产品分类", options=get_categories())
        
        # 标签筛选
        labels = st.multiselect("产品标签", options=get_labels())
        
        # 更新筛选条件
        if st.button("应用筛选"):
            SessionManager.set('search_filters', {
                'category': category,
                'labels': labels
            })
            st.rerun()

def render_content():
    """渲染主要内容"""
    # 搜索栏
    search_term = st.text_input("搜索产品", placeholder="输入产品名称或关键词")
    
    # 数据加载
    with st.spinner("加载产品数据..."):
        products = load_products(search_term)
    
    # 数据展示
    if products:
        display_products(products)
    else:
        st.info("暂无产品数据")

@st.cache_data(ttl=300)
def load_products(search_term: str = "") -> List[Dict]:
    """加载产品数据"""
    try:
        client = ZKMallClient()
        filters = SessionManager.get('search_filters', {})
        pagination = SessionManager.get('pagination', {})
        
        return client.get_products(
            search=search_term,
            category=filters.get('category'),
            labels=filters.get('labels'),
            page=pagination['page'],
            size=pagination['size']
        )
    except Exception as e:
        logger.error(f"加载产品失败: {e}")
        st.error("产品数据加载失败，请稍后重试")
        return []

def display_products(products: List[Dict]):
    """展示产品列表"""
    # 网格布局展示
    cols = st.columns(3)
    
    for idx, product in enumerate(products):
        with cols[idx % 3]:
            with st.container():
                # 产品卡片
                if product.get('small_img'):
                    st.image(product['small_img'], use_column_width=True)
                
                st.subheader(product['name'])
                st.write(f"规格：{product.get('spec', 'N/A')}")
                st.write(f"分类：{product.get('categoryName', 'N/A')}")
                st.write(f"浏览：{product.get('count', 0)}")
                
                if st.button(f"查看详情", key=f"view_{product['id']}"):
                    SessionManager.set('current_product', product)
                    st.switch_page("pages/product_detail.py")

if __name__ == "__main__":
    main()
```

## 🔧 后端架构设计

### 服务层架构
```python
# services/base_service.py
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional
from utils.database import get_db_connection
from utils.cache import CacheManager
from utils.api_client import ZKMallClient
import logging

class BaseService(ABC):
    """基础服务类"""
    
    def __init__(self):
        self.db = get_db_connection()
        self.cache = CacheManager()
        self.api_client = ZKMallClient()
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @abstractmethod
    def get_list(self, **kwargs) -> List[Dict]:
        """获取列表数据"""
        pass
    
    @abstractmethod
    def get_detail(self, item_id: int) -> Optional[Dict]:
        """获取详情数据"""
        pass
    
    def sync_from_api(self, **kwargs) -> bool:
        """从API同步数据"""
        try:
            # 从云商API获取数据
            api_data = self._fetch_from_api(**kwargs)
            
            # 数据转换和验证
            validated_data = self._validate_data(api_data)
            
            # 保存到数据库
            self._save_to_database(validated_data)
            
            # 清理缓存
            self.cache.clear_pattern(self._get_cache_pattern())
            
            return True
        except Exception as e:
            self.logger.error(f"数据同步失败: {e}")
            return False
    
    @abstractmethod
    def _fetch_from_api(self, **kwargs) -> List[Dict]:
        """从API获取数据"""
        pass
    
    @abstractmethod
    def _validate_data(self, data: List[Dict]) -> List[Dict]:
        """验证数据"""
        pass
    
    @abstractmethod
    def _save_to_database(self, data: List[Dict]) -> None:
        """保存到数据库"""
        pass
    
    @abstractmethod
    def _get_cache_pattern(self) -> str:
        """获取缓存模式"""
        pass

# services/product_service.py
from .base_service import BaseService
from typing import Dict, List, Optional

class ProductService(BaseService):
    """产品服务"""
    
    def get_list(self, **kwargs) -> List[Dict]:
        """获取产品列表"""
        cache_key = f"products:list:{hash(str(kwargs))}"
        
        # 尝试从缓存获取
        cached_data = self.cache.get(cache_key)
        if cached_data:
            return cached_data
        
        # 从数据库查询
        query = """
            SELECT p.*, c.name as category_name, l.name as label_name, b.name as brand_name
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN labels l ON p.label_id = l.id
            LEFT JOIN brands b ON p.brand_id = b.id
            WHERE p.status = '0'
        """
        
        params = []
        
        # 添加筛选条件
        if kwargs.get('category_id'):
            query += " AND p.category_id = %s"
            params.append(kwargs['category_id'])
        
        if kwargs.get('search'):
            query += " AND (p.name ILIKE %s OR p.introduction ILIKE %s)"
            search_term = f"%{kwargs['search']}%"
            params.extend([search_term, search_term])
        
        # 排序和分页
        query += " ORDER BY p.sort DESC, p.create_time DESC"
        
        if kwargs.get('page') and kwargs.get('size'):
            offset = (kwargs['page'] - 1) * kwargs['size']
            query += f" LIMIT {kwargs['size']} OFFSET {offset}"
        
        with self.db.cursor() as cursor:
            cursor.execute(query, params)
            results = cursor.fetchall()
        
        # 缓存结果
        self.cache.set(cache_key, results, ttl=300)
        
        return results
    
    def get_detail(self, product_id: int) -> Optional[Dict]:
        """获取产品详情"""
        cache_key = f"product:detail:{product_id}"
        
        # 尝试从缓存获取
        cached_data = self.cache.get(cache_key)
        if cached_data:
            return cached_data
        
        query = """
            SELECT p.*, c.name as category_name, l.name as label_name, b.name as brand_name
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            LEFT JOIN labels l ON p.label_id = l.id
            LEFT JOIN brands b ON p.brand_id = b.id
            WHERE p.id = %s AND p.status = '0'
        """
        
        with self.db.cursor() as cursor:
            cursor.execute(query, [product_id])
            result = cursor.fetchone()
        
        if result:
            # 增加浏览量
            self._increment_view_count(product_id)
            
            # 缓存结果
            self.cache.set(cache_key, result, ttl=600)
        
        return result
    
    def _fetch_from_api(self, **kwargs) -> List[Dict]:
        """从云商API获取产品数据"""
        return self.api_client.get_products(**kwargs)
    
    def _validate_data(self, data: List[Dict]) -> List[Dict]:
        """验证产品数据"""
        from utils.data_processing import process_api_data
        
        # 首先进行HTML处理和数据清理
        processed_data = process_api_data(data, 'product')
        
        validated = []
        for item in processed_data:
            if self._validate_product_item(item):
                validated.append(item)
        return validated
    
    def _validate_product_item(self, item: Dict) -> bool:
        """验证单个产品数据"""
        required_fields = ['id', 'name']
        return all(field in item and item[field] for field in required_fields)
    
    def _save_to_database(self, data: List[Dict]) -> None:
        """保存产品数据到数据库"""
        insert_query = """
            INSERT INTO products (id, name, spec, introduction, details, small_img, banner,
                                category_id, label_id, attribute, show_for, param_info, use_to,
                                qualifications, instructions, other, guide, common_problem, price,
                                is_suggest, is_hot, is_new, count, like_count, favorite_count,
                                status, site_id, brand_id, show_time, sort, create_time, update_time)
            VALUES (%(id)s, %(name)s, %(spec)s, %(introduction)s, %(details)s, %(small_img)s, %(banner)s,
                   %(category_id)s, %(label_id)s, %(attribute)s, %(show_for)s, %(param_info)s, %(use_to)s,
                   %(qualifications)s, %(instructions)s, %(other)s, %(guide)s, %(common_problem)s, %(price)s,
                   %(is_suggest)s, %(is_hot)s, %(is_new)s, %(count)s, %(like_count)s, %(favorite_count)s,
                   %(status)s, %(site_id)s, %(brand_id)s, %(show_time)s, %(sort)s, %(create_time)s, %(update_time)s)
            ON CONFLICT (id) DO UPDATE SET
                name = EXCLUDED.name,
                spec = EXCLUDED.spec,
                introduction = EXCLUDED.introduction,
                details = EXCLUDED.details,
                small_img = EXCLUDED.small_img,
                banner = EXCLUDED.banner,
                category_id = EXCLUDED.category_id,
                label_id = EXCLUDED.label_id,
                attribute = EXCLUDED.attribute,
                show_for = EXCLUDED.show_for,
                param_info = EXCLUDED.param_info,
                use_to = EXCLUDED.use_to,
                qualifications = EXCLUDED.qualifications,
                instructions = EXCLUDED.instructions,
                other = EXCLUDED.other,
                guide = EXCLUDED.guide,
                common_problem = EXCLUDED.common_problem,
                price = EXCLUDED.price,
                is_suggest = EXCLUDED.is_suggest,
                is_hot = EXCLUDED.is_hot,
                is_new = EXCLUDED.is_new,
                count = EXCLUDED.count,
                like_count = EXCLUDED.like_count,
                favorite_count = EXCLUDED.favorite_count,
                status = EXCLUDED.status,
                site_id = EXCLUDED.site_id,
                brand_id = EXCLUDED.brand_id,
                show_time = EXCLUDED.show_time,
                sort = EXCLUDED.sort,
                update_time = EXCLUDED.update_time
        """
        
        with self.db.cursor() as cursor:
            cursor.executemany(insert_query, data)
            self.db.commit()
    
    def _get_cache_pattern(self) -> str:
        """获取产品缓存模式"""
        return "products:*"
    
    def _increment_view_count(self, product_id: int) -> None:
        """增加产品浏览量"""
        query = "UPDATE products SET count = count + 1 WHERE id = %s"
        with self.db.cursor() as cursor:
            cursor.execute(query, [product_id])
            self.db.commit()
```

## 🗄️ 数据访问层设计

### 数据库连接管理
```python
# utils/database.py
import psycopg2
from psycopg2.pool import ThreadedConnectionPool
from psycopg2.extras import RealDictCursor
import os
from typing import Optional
import logging

logger = logging.getLogger(__name__)

class DatabaseManager:
    """数据库管理器"""
    
    _instance: Optional['DatabaseManager'] = None
    _pool: Optional[ThreadedConnectionPool] = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._pool is None:
            self._initialize_pool()
    
    def _initialize_pool(self):
        """初始化连接池"""
        database_url = os.getenv('DATABASE_URL', '***********************************************/product')
        
        try:
            self._pool = ThreadedConnectionPool(
                minconn=5,
                maxconn=20,
                dsn=database_url,
                cursor_factory=RealDictCursor
            )
            logger.info("数据库连接池初始化成功")
        except Exception as e:
            logger.error(f"数据库连接池初始化失败: {e}")
            raise
    
    def get_connection(self):
        """获取数据库连接"""
        if self._pool is None:
            raise RuntimeError("数据库连接池未初始化")
        
        try:
            return self._pool.getconn()
        except Exception as e:
            logger.error(f"获取数据库连接失败: {e}")
            raise
    
    def return_connection(self, conn):
        """归还数据库连接"""
        if self._pool and conn:
            self._pool.putconn(conn)
    
    def close_all_connections(self):
        """关闭所有连接"""
        if self._pool:
            self._pool.closeall()
            self._pool = None

# 全局数据库管理器实例
db_manager = DatabaseManager()

def get_db_connection():
    """获取数据库连接"""
    return db_manager.get_connection()

def return_db_connection(conn):
    """归还数据库连接"""
    db_manager.return_connection(conn)
```

### 缓存管理
```python
# utils/cache.py
import redis
import json
import pickle
from typing import Any, Optional, Union
import os
import logging

logger = logging.getLogger(__name__)

class CacheManager:
    """缓存管理器"""
    
    def __init__(self):
        self.redis_client = self._initialize_redis()
    
    def _initialize_redis(self) -> Optional[redis.Redis]:
        """初始化Redis连接"""
        redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
        
        try:
            client = redis.from_url(redis_url, decode_responses=False)
            client.ping()  # 测试连接
            logger.info("Redis连接初始化成功")
            return client
        except Exception as e:
            logger.warning(f"Redis连接失败，使用内存缓存: {e}")
            return None
    
    def get(self, key: str) -> Any:
        """获取缓存值"""
        if not self.redis_client:
            return None
        
        try:
            value = self.redis_client.get(key)
            if value:
                return pickle.loads(value)
        except Exception as e:
            logger.error(f"获取缓存失败 {key}: {e}")
        
        return None
    
    def set(self, key: str, value: Any, ttl: int = 3600) -> bool:
        """设置缓存值"""
        if not self.redis_client:
            return False
        
        try:
            serialized_value = pickle.dumps(value)
            return self.redis_client.setex(key, ttl, serialized_value)
        except Exception as e:
            logger.error(f"设置缓存失败 {key}: {e}")
            return False
    
    def delete(self, key: str) -> bool:
        """删除缓存"""
        if not self.redis_client:
            return False
        
        try:
            return bool(self.redis_client.delete(key))
        except Exception as e:
            logger.error(f"删除缓存失败 {key}: {e}")
            return False
    
    def clear_pattern(self, pattern: str) -> int:
        """清理匹配模式的缓存"""
        if not self.redis_client:
            return 0
        
        try:
            keys = self.redis_client.keys(pattern)
            if keys:
                return self.redis_client.delete(*keys)
            return 0
        except Exception as e:
            logger.error(f"清理缓存失败 {pattern}: {e}")
            return 0
    
    def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        if not self.redis_client:
            return False
        
        try:
            return bool(self.redis_client.exists(key))
        except Exception as e:
            logger.error(f"检查缓存失败 {key}: {e}")
            return False
```

## 🔐 安全架构设计

### 认证和授权
```python
# utils/auth.py
import jwt
import hashlib
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
import streamlit as st
from functools import wraps
import os

SECRET_KEY = os.getenv('JWT_SECRET_KEY', 'your-secret-key-here')
ALGORITHM = 'HS256'
TOKEN_EXPIRE_HOURS = 24

class AuthManager:
    """认证管理器"""
    
    @staticmethod
    def hash_password(password: str) -> str:
        """密码哈希"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    @staticmethod
    def verify_password(password: str, hashed: str) -> bool:
        """验证密码"""
        return AuthManager.hash_password(password) == hashed
    
    @staticmethod
    def create_token(user_data: Dict[str, Any]) -> str:
        """创建JWT令牌"""
        payload = {
            'user_id': user_data['id'],
            'username': user_data['username'],
            'role': user_data.get('role', 'user'),
            'site_id': user_data.get('site_id', 999),
            'exp': datetime.utcnow() + timedelta(hours=TOKEN_EXPIRE_HOURS)
        }
        
        return jwt.encode(payload, SECRET_KEY, algorithm=ALGORITHM)
    
    @staticmethod
    def verify_token(token: str) -> Optional[Dict[str, Any]]:
        """验证JWT令牌"""
        try:
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            return payload
        except jwt.ExpiredSignatureError:
            return None
        except jwt.InvalidTokenError:
            return None
    
    @staticmethod
    def get_current_user() -> Optional[Dict[str, Any]]:
        """获取当前用户信息"""
        token = st.session_state.get('auth_token')
        if not token:
            return None
        
        return AuthManager.verify_token(token)
    
    @staticmethod
    def is_authenticated() -> bool:
        """检查是否已认证"""
        return AuthManager.get_current_user() is not None
    
    @staticmethod
    def has_permission(required_role: str = 'user') -> bool:
        """检查权限"""
        user = AuthManager.get_current_user()
        if not user:
            return False
        
        role_hierarchy = {
            'user': 0,
            'admin': 1,
            'super_admin': 2
        }
        
        user_role_level = role_hierarchy.get(user.get('role', 'user'), 0)
        required_role_level = role_hierarchy.get(required_role, 0)
        
        return user_role_level >= required_role_level

def require_auth(func):
    """认证装饰器"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        if not AuthManager.is_authenticated():
            st.error("请先登录")
            st.switch_page("pages/login.py")
            return
        return func(*args, **kwargs)
    return wrapper

def require_role(role: str):
    """角色权限装饰器"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            if not AuthManager.has_permission(role):
                st.error("权限不足")
                return
            return func(*args, **kwargs)
        return wrapper
    return decorator
```

## 📊 监控和日志架构

### 日志管理
```python
# utils/logging_config.py
import logging
import logging.handlers
import os
from datetime import datetime

def setup_logging():
    """配置日志系统"""
    
    # 创建日志目录
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 配置根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    
    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    console_handler.setFormatter(console_formatter)
    
    # 文件处理器（按日期轮转）
    file_handler = logging.handlers.TimedRotatingFileHandler(
        filename=os.path.join(log_dir, 'app.log'),
        when='midnight',
        interval=1,
        backupCount=30,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.INFO)
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    )
    file_handler.setFormatter(file_formatter)
    
    # 错误日志处理器
    error_handler = logging.handlers.TimedRotatingFileHandler(
        filename=os.path.join(log_dir, 'error.log'),
        when='midnight',
        interval=1,
        backupCount=30,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(file_formatter)
    
    # 添加处理器
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
    root_logger.addHandler(error_handler)
    
    return root_logger

# 初始化日志
logger = setup_logging()
```

## 🚀 部署架构设计

### Docker容器化
```dockerfile
# Dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# 安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建非root用户
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# 暴露端口
EXPOSE 8501

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8501/_stcore/health || exit 1

# 启动命令
CMD ["streamlit", "run", "main.py", "--server.port=8501", "--server.address=0.0.0.0"]
```

### Docker Compose配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "8501:8501"
    environment:
      - DATABASE_URL=**************************************/yunshang
      - REDIS_URL=redis://redis:6379/0
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
    depends_on:
      - db
      - redis
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/_stcore/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=yunshang
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

## 📈 性能优化策略

### 缓存策略
1. **多层缓存架构**
   - Streamlit原生缓存（@st.cache_data）
   - Redis分布式缓存
   - 数据库查询结果缓存

2. **缓存更新策略**
   - TTL过期机制
   - 主动失效策略
   - 版本化缓存键

### 数据库优化
1. **索引优化**
   - 核心查询字段建索引
   - 复合索引优化
   - 定期索引维护

2. **查询优化**
   - SQL查询优化
   - 批量操作
   - 连接池管理

### 前端优化
1. **组件优化**
   - 组件懒加载
   - 数据分页加载
   - 图片压缩和CDN

2. **状态管理**
   - 合理使用session_state
   - 避免不必要的重新渲染
   - 数据预加载

## 🔄 HTML内容处理架构

### HTML到Markdown转换
```python
# utils/html_processor.py
class HTMLProcessor:
    """HTML内容处理器"""
    
    def __init__(self):
        self.html2text_handler = html2text.HTML2Text()
        # 配置转换选项
    
    def process_html_content(self, content: str, conversion_type: str = "auto") -> str:
        """
        处理HTML内容并转换为Markdown
        
        转换类型:
        - auto: 自动检测转换方式
        - table: 专门处理表格
        - text: 纯文本转换
        - mixed: 混合内容转换
        """
        
    def _table_to_markdown(self, table_element) -> str:
        """将HTML表格转换为标准Markdown表格"""
        # 处理表头、表体、表格格式化
```

### 数据处理集成
```python
# utils/data_processing.py
class DataProcessor:
    """集成HTML处理的数据处理器"""
    
    def process_api_response(self, response_data, data_type: str = "generic"):
        """
        处理API响应数据流程:
        1. 基本数据清理
        2. HTML内容转换 ← 新增功能
        3. 数据类型特定处理
        """
        
    def _get_html_fields_by_type(self, data_type: str) -> List[str]:
        """
        根据数据类型确定需要HTML处理的字段:
        - product: details, param_info, specifications
        - case: content, details, introduction
        - programme: content, details, description
        - information: content, details, title
        """
```

### 在服务层的应用
```python
# services/base_service.py
def _validate_data(self, data: List[Dict]) -> List[Dict]:
    """验证数据时自动进行HTML处理"""
    from utils.data_processing import process_api_data
    
    # HTML处理和数据清理
    processed_data = process_api_data(data, self.data_type)
    
    # 验证处理后的数据
    validated = []
    for item in processed_data:
        if self._validate_item(item):
            validated.append(item)
    return validated
```

### Streamlit展示优化
```python
# 在页面组件中使用转换后的Markdown
def display_product_details(product):
    """展示产品详情"""
    # 参数信息（表格格式）
    if product.get('param_info'):
        st.subheader("技术参数")
        st.markdown(product['param_info'])  # 已转换为Markdown表格
    
    # 产品详情（富文本）
    if product.get('details'):
        st.subheader("产品详情")
        st.markdown(product['details'])  # 已转换为Markdown格式
```

## 🔧 开发和测试环境

### 开发环境配置
```python
# config/development.py
import os

class DevelopmentConfig:
    """开发环境配置"""
    DEBUG = True
    DATABASE_URL = os.getenv('DEV_DATABASE_URL', 'postgresql://postgres:123456@localhost:5432/yunshang_dev')
    REDIS_URL = os.getenv('DEV_REDIS_URL', 'redis://localhost:6379/1')
    LOG_LEVEL = 'DEBUG'
    CACHE_TTL = 60  # 开发环境短缓存时间
    JWT_SECRET_KEY = 'dev-secret-key'
    ZKMALL_API_BASE = 'https://zkmall.zktecoiot.com'
    ENABLE_MOCK_DATA = False  # 严格禁止模拟数据
```

### 测试架构
```python
# tests/conftest.py
import pytest
import psycopg2
from utils.database import DatabaseManager
from utils.cache import CacheManager
import tempfile
import os

@pytest.fixture(scope="session")
def test_db():
    """测试数据库固件"""
    # 创建临时测试数据库
    test_db_name = "test_yunshang"
    
    # 连接到默认数据库创建测试数据库
    conn = psycopg2.connect(
        host="localhost",
        port=5432,
        user="postgres",
        password="123456",
        database="postgres"
    )
    conn.autocommit = True
    
    with conn.cursor() as cursor:
        cursor.execute(f"DROP DATABASE IF EXISTS {test_db_name}")
        cursor.execute(f"CREATE DATABASE {test_db_name}")
    
    conn.close()
    
    # 设置测试数据库URL
    test_db_url = f"postgresql://postgres:123456@localhost:5432/{test_db_name}"
    os.environ['DATABASE_URL'] = test_db_url
    
    # 初始化数据库模式
    db_manager = DatabaseManager()
    # ... 初始化表结构
    
    yield test_db_url
    
    # 清理测试数据库
    conn = psycopg2.connect(
        host="localhost",
        port=5432,
        user="postgres",
        password="123456",
        database="postgres"
    )
    conn.autocommit = True
    
    with conn.cursor() as cursor:
        cursor.execute(f"DROP DATABASE {test_db_name}")
    
    conn.close()

@pytest.fixture
def test_cache():
    """测试缓存固件"""
    cache_manager = CacheManager()
    
    yield cache_manager
    
    # 清理测试缓存
    cache_manager.clear_pattern("test:*")
```

---

**文档版本**: v1.0  
**最后更新**: 2024-12-19  
**维护者**: 开发团队 