import psycopg2
from psycopg2.pool import ThreadedConnectionPool
from psycopg2.extras import RealDictCursor
import os
import threading
import time
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
import logging
from contextlib import contextmanager

logger = logging.getLogger(__name__)


class ConnectionPoolMonitor:
    """连接池监控器"""

    def __init__(self):
        self.connection_stats = {
            "total_connections_created": 0,
            "total_connections_returned": 0,
            "active_connections": 0,
            "failed_connections": 0,
            "connection_timeouts": 0,
            "health_check_failures": 0,
            "last_health_check": None,
            "pool_created_at": datetime.now(),
        }
        self.active_connections_tracker = {}  # 跟踪活跃连接
        self.connection_lease_times = {}  # 连接租借时间
        self.connection_usage_log = []  # 连接使用日志
        self._stats_lock = threading.Lock()

    def record_connection_acquired(self, conn_id: str):
        """记录连接获取"""
        with self._stats_lock:
            self.connection_stats["total_connections_created"] += 1
            self.connection_stats["active_connections"] += 1
            self.active_connections_tracker[conn_id] = datetime.now()
            self.connection_lease_times[conn_id] = datetime.now()

            # 记录连接使用日志
            self._log_connection_usage(conn_id, "acquired")

    def record_connection_returned(self, conn_id: str):
        """记录连接归还"""
        with self._stats_lock:
            self.connection_stats["total_connections_returned"] += 1
            self.connection_stats["active_connections"] = max(
                0, self.connection_stats["active_connections"] - 1
            )

            # 计算连接使用时长
            lease_time = self.connection_lease_times.get(conn_id)
            if lease_time:
                duration = (datetime.now() - lease_time).total_seconds()
                self._log_connection_usage(conn_id, "returned", duration)

            self.active_connections_tracker.pop(conn_id, None)
            self.connection_lease_times.pop(conn_id, None)

    def record_connection_failed(self):
        """记录连接失败"""
        with self._stats_lock:
            self.connection_stats["failed_connections"] += 1
            logger.warning(
                f"数据库连接失败，总失败次数: {self.connection_stats['failed_connections']}"
            )

    def record_connection_timeout(self):
        """记录连接超时"""
        with self._stats_lock:
            self.connection_stats["connection_timeouts"] += 1
            logger.warning(
                f"数据库连接超时，总超时次数: {self.connection_stats['connection_timeouts']}"
            )

    def record_health_check_failure(self):
        """记录健康检查失败"""
        with self._stats_lock:
            self.connection_stats["health_check_failures"] += 1
            logger.error(
                f"数据库健康检查失败，总失败次数: {self.connection_stats['health_check_failures']}"
            )

    def update_health_check_time(self):
        """更新最后健康检查时间"""
        with self._stats_lock:
            self.connection_stats["last_health_check"] = datetime.now()

    def get_long_running_connections(
        self, threshold_minutes: int = 30
    ) -> List[Dict[str, Any]]:
        """获取长时间运行的连接"""
        with self._stats_lock:
            threshold_time = datetime.now() - timedelta(minutes=threshold_minutes)
            long_running = []

            for conn_id, start_time in self.active_connections_tracker.items():
                if start_time < threshold_time:
                    duration = (datetime.now() - start_time).total_seconds()
                    long_running.append(
                        {
                            "connection_id": conn_id,
                            "start_time": start_time.isoformat(),
                            "duration_seconds": duration,
                            "duration_minutes": duration / 60,
                        }
                    )

            return long_running

    def _log_connection_usage(self, conn_id: str, action: str, duration: float = None):
        """记录连接使用日志"""
        log_entry = {
            "timestamp": datetime.now().isoformat(),
            "connection_id": conn_id,
            "action": action,
            "duration_seconds": duration,
        }

        # 保持日志条目数量在合理范围内
        if len(self.connection_usage_log) > 1000:
            self.connection_usage_log = self.connection_usage_log[
                -500:
            ]  # 保留最近500条

        self.connection_usage_log.append(log_entry)

        # 记录详细日志
        if action == "acquired":
            logger.debug(f"连接 {conn_id} 已获取")
        elif action == "returned" and duration:
            logger.debug(f"连接 {conn_id} 已归还，使用时长: {duration:.2f}秒")

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._stats_lock:
            stats = self.connection_stats.copy()
            stats["uptime_seconds"] = (
                datetime.now() - stats["pool_created_at"]
            ).total_seconds()

            # 添加连接使用率统计
            if stats["total_connections_created"] > 0:
                stats["connection_success_rate"] = (
                    (stats["total_connections_created"] - stats["failed_connections"])
                    / stats["total_connections_created"]
                    * 100
                )
            else:
                stats["connection_success_rate"] = 100.0

            return stats

    def get_connection_usage_log(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取连接使用日志"""
        with self._stats_lock:
            return (
                self.connection_usage_log[-limit:] if self.connection_usage_log else []
            )


class DatabaseManager:
    """数据库管理器（线程安全的单例模式）"""

    _instance: Optional["DatabaseManager"] = None
    _pool: Optional[ThreadedConnectionPool] = None
    _lock = threading.Lock()
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        # 双重检查锁定，确保只初始化一次
        if not self._initialized:
            with self._lock:
                if not self._initialized:
                    self.monitor = ConnectionPoolMonitor()
                    self._connection_map = {}  # 连接对象到ID的映射
                    self._initialize_pool()
                    self._initialized = True

    def _initialize_pool(self):
        """初始化数据库连接池"""
        if self._pool is not None:
            return  # 已经初始化，直接返回

        database_url = os.getenv(
            "DATABASE_URL", "***********************************************/product"
        )

        try:
            # 创建连接池
            self._pool = ThreadedConnectionPool(
                minconn=5, maxconn=20, dsn=database_url, cursor_factory=RealDictCursor
            )

            logger.info("数据库连接池初始化成功 (5-20 连接)")
        except Exception as e:
            logger.error(f"数据库连接池初始化失败: {e}")
            self.monitor.record_connection_failed()
            raise

    @contextmanager
    def get_connection_context(self, timeout: int = 30):
        """
        获取数据库连接的上下文管理器

        Args:
            timeout: 获取连接的超时时间（秒）

        Yields:
            数据库连接对象
        """
        conn = None
        try:
            conn = self.get_connection(timeout)
            yield conn
        except Exception as e:
            if conn:
                try:
                    conn.rollback()
                except:
                    pass
            raise
        finally:
            if conn:
                self.return_connection(conn)

    def get_connection(self, timeout: int = 30):
        """
        获取数据库连接

        Args:
            timeout: 获取连接的超时时间（秒）
        """
        if self._pool is None:
            raise RuntimeError("数据库连接池未初始化")

        try:
            start_time = time.time()
            conn = self._pool.getconn()

            # 记录连接获取
            conn_id = str(id(conn))
            self.monitor.record_connection_acquired(conn_id)

            # 使用字典映射而不是在连接对象上设置属性
            self._connection_map[conn] = conn_id

            # 检查获取连接的时间
            acquisition_time = time.time() - start_time
            if acquisition_time > timeout:
                self.monitor.record_connection_timeout()
                logger.warning(f"获取数据库连接超时: {acquisition_time:.2f}秒")

            # 验证连接有效性
            self._validate_connection(conn)

            return conn
        except Exception as e:
            logger.error(f"获取数据库连接失败: {e}")
            self.monitor.record_connection_failed()
            raise

    def _validate_connection(self, conn):
        """验证连接有效性"""
        try:
            with conn.cursor() as cursor:
                cursor.execute("SELECT 1")
                cursor.fetchone()
        except Exception as e:
            logger.error(f"连接验证失败: {e}")
            raise

    def return_connection(self, conn):
        """归还数据库连接"""
        if self._pool and conn:
            try:
                # 从映射中获取连接ID
                conn_id = self._connection_map.get(conn, "unknown")
                self.monitor.record_connection_returned(conn_id)

                # 清除连接映射
                if conn in self._connection_map:
                    del self._connection_map[conn]

                self._pool.putconn(conn)
            except Exception as e:
                logger.error(f"归还数据库连接失败: {e}")

    def get_pool_status(self) -> Dict[str, Any]:
        """获取连接池状态"""
        if not self._pool:
            return {"error": "连接池未初始化"}

        try:
            # 获取连接池基本信息
            pool_info = {
                "min_connections": self._pool.minconn,
                "max_connections": self._pool.maxconn,
                "current_connections": (
                    len(self._pool._pool) if hasattr(self._pool, "_pool") else 0
                ),
                "available_connections": (
                    len([c for c in self._pool._pool if c])
                    if hasattr(self._pool, "_pool")
                    else 0
                ),
            }

            # 合并监控统计信息
            pool_info.update(self.monitor.get_stats())

            return pool_info
        except Exception as e:
            logger.error(f"获取连接池状态失败: {e}")
            return {"error": str(e)}

    def health_check(self) -> Dict[str, Any]:
        """数据库健康检查"""
        health_result = {
            "timestamp": datetime.now().isoformat(),
            "healthy": False,
            "checks": {},
            "errors": [],
            "warnings": [],
        }

        try:
            # 测试连接获取和查询
            test_conn = None
            try:
                test_conn = self._pool.getconn()
                with test_conn.cursor() as cursor:
                    cursor.execute(
                        "SELECT NOW() as current_time, version() as db_version"
                    )
                    result = cursor.fetchone()
                    health_result["checks"]["database_connection"] = True
                    health_result["checks"]["current_time"] = result[
                        "current_time"
                    ].isoformat()
                    health_result["checks"]["db_version"] = result["db_version"]

            except Exception as e:
                health_result["checks"]["database_connection"] = False
                health_result["errors"].append(f"数据库连接测试失败: {str(e)}")
                self.monitor.record_health_check_failure()
            finally:
                if test_conn:
                    try:
                        self._pool.putconn(test_conn)
                    except Exception as e:
                        logger.error(f"健康检查连接归还失败: {e}")

            # 检查长时间运行的连接
            long_running = self.monitor.get_long_running_connections(
                threshold_minutes=30
            )
            if long_running:
                health_result["warnings"].append(
                    f"发现 {len(long_running)} 个长时间运行的连接"
                )
                health_result["checks"]["long_running_connections"] = len(long_running)
            else:
                health_result["checks"]["long_running_connections"] = 0

            # 检查连接池使用率
            pool_status = self.get_pool_status()
            if isinstance(pool_status, dict) and "max_connections" in pool_status:
                usage_rate = pool_status.get("active_connections", 0) / pool_status.get(
                    "max_connections", 1
                )
                health_result["checks"]["pool_usage_rate"] = round(usage_rate * 100, 2)

                if usage_rate > 0.8:  # 使用率超过80%认为警告
                    health_result["warnings"].append(
                        f"连接池使用率较高: {usage_rate*100:.1f}%"
                    )

            # 更新健康检查时间
            self.monitor.update_health_check_time()

            # 如果没有错误，标记为健康
            health_result["healthy"] = len(health_result["errors"]) == 0

        except Exception as e:
            health_result["errors"].append(f"健康检查执行失败: {str(e)}")
            logger.error(f"数据库健康检查失败: {e}")

        return health_result

    def get_connection_leaks(self) -> List[Dict[str, Any]]:
        """检测可能的连接泄漏"""
        return self.monitor.get_long_running_connections(threshold_minutes=60)

    def reset_stats(self):
        """重置统计信息"""
        self.monitor = ConnectionPoolMonitor()
        logger.info("连接池统计信息已重置")

    def close_all_connections(self):
        """关闭所有连接"""
        with self._lock:
            if self._pool:
                self._pool.closeall()
                self._pool = None
                self._initialized = False
                logger.info("所有数据库连接已关闭")


# 全局数据库管理器实例
_db_manager = None
_manager_lock = threading.Lock()


def get_database_manager() -> DatabaseManager:
    """获取数据库管理器实例（单例模式）"""
    global _db_manager
    if _db_manager is None:
        with _manager_lock:
            if _db_manager is None:
                _db_manager = DatabaseManager()
    return _db_manager


def get_db_connection(timeout: int = 30):
    """
    获取数据库连接（兼容旧接口）

    Args:
        timeout: 获取连接的超时时间（秒）

    Returns:
        数据库连接对象
    """
    manager = get_database_manager()
    return manager.get_connection(timeout)


def return_db_connection(conn):
    """
    归还数据库连接（兼容旧接口）

    Args:
        conn: 数据库连接对象
    """
    manager = get_database_manager()
    return manager.return_connection(conn)


@contextmanager
def get_db_connection_context(timeout: int = 30):
    """
    获取数据库连接的上下文管理器

    Args:
        timeout: 获取连接的超时时间（秒）

    Yields:
        数据库连接对象
    """
    manager = get_database_manager()
    with manager.get_connection_context(timeout) as conn:
        yield conn


def get_db_pool_status() -> Dict[str, Any]:
    """获取数据库连接池状态"""
    manager = get_database_manager()
    return manager.get_pool_status()


def perform_db_health_check() -> Dict[str, Any]:
    """执行数据库健康检查"""
    manager = get_database_manager()
    return manager.health_check()


def get_db_connection_leaks() -> List[Dict[str, Any]]:
    """获取数据库连接泄漏信息"""
    manager = get_database_manager()
    return manager.get_connection_leaks()


def reset_db_stats():
    """重置数据库统计信息"""
    manager = get_database_manager()
    manager.reset_stats()


def close_all_db_connections():
    """关闭所有数据库连接"""
    manager = get_database_manager()
    manager.close_all_connections()
