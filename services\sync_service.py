import logging
import json
import datetime
import time
import psycopg2
from psycopg2.extras import RealDictCursor
from urllib.parse import urlparse
import os
import traceback
from typing import List, Dict, Any, Optional, Tuple

# 使用统一的API客户端，避免代码冗余
from utils.api_client import ZKMallClient
from utils.auth import AuthManager
from utils.database import get_db_connection, return_db_connection
from utils.attachment_manager import AttachmentManager
from utils.database import DatabaseManager
from utils.api_response_normalizer import ApiResponseNormalizer
from utils.attachment_service import AttachmentService
from utils.sync_error_handler import sync_error_handler, log_sync_exception

logger = logging.getLogger(__name__)


class SyncService:
    """数据同步服务"""

    def _safe_get_api_data(self, api_method, *args, **kwargs):
        """
        安全地获取API数据，处理空响应和错误
        """
        try:
            response = api_method(*args, **kwargs)

            if not response:
                logger.warning(f"API方法 {api_method.__name__} 返回空响应")
                return []

            if isinstance(response, list):
                return response
            elif isinstance(response, dict):
                if "data" in response:
                    data = response["data"]
                    if isinstance(data, dict) and "rows" in data:
                        return data["rows"] or []
                    elif isinstance(data, list):
                        return data
                elif "rows" in response:
                    return response["rows"] or []
                else:
                    return [response] if response else []
            else:
                logger.warning(f"未知的响应格式: {type(response)}")
                return []

        except Exception as e:
            logger.error(f"获取API数据失败: {e}")
            return []

    def _safe_normalize_data(self, data, data_type="generic"):
        """
        安全地标准化数据
        """
        try:
            if not data:
                return []

            if isinstance(data, list):
                return ApiResponseNormalizer.normalize_list_response(
                    data, data_type=data_type
                )
            else:
                result = ApiResponseNormalizer.normalize_single_response(
                    data, data_type=data_type
                )
                return [result] if result else []

        except Exception as e:
            logger.error(f"数据标准化失败: {e}")
            return data if isinstance(data, list) else [data] if data else []

    def __init__(self):
        """初始化同步服务"""
        self.db_manager = DatabaseManager()
        self.api_client = ZKMallClient()
        self.normalizer = ApiResponseNormalizer()
        self.attachment_service = AttachmentService()
        logger.info("同步服务初始化完成")

    def _execute_with_connection(self, operation_func, *args, **kwargs):
        """
        使用连接池执行数据库操作的通用方法

        Args:
            operation_func: 要执行的操作函数
            *args: 传递给操作函数的参数
            **kwargs: 传递给操作函数的关键字参数

        Returns:
            操作函数的返回值
        """
        conn = None
        try:
            conn = get_db_connection()
            logger.debug("从连接池获取数据库连接成功")

            # 执行操作，传入连接对象
            result = operation_func(conn, *args, **kwargs)

            return result
        except Exception as e:
            if conn:
                try:
                    conn.rollback()
                    logger.debug("数据库事务回滚成功")
                except Exception as rollback_error:
                    logger.error(f"数据库事务回滚失败: {rollback_error}")
            logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            if conn:
                try:
                    return_db_connection(conn)
                    logger.debug("数据库连接已归还到连接池")
                except Exception as return_error:
                    logger.error(f"归还数据库连接失败: {return_error}")

    def sync_all(self) -> Dict[str, Any]:
        """
        同步所有数据

        Returns:
            Dict[str, Any]: 同步结果
        """
        results = {
            "overall_status": "starting",
            "start_time": datetime.now().isoformat(),
            "errors": [],
            "warnings": [],
            "success_count": 0,
            "total_operations": 0,
        }

        # 确保认证状态
        if not AuthManager.ensure_authenticated():
            logger.error("认证失败，无法进行数据同步")
            results.update(
                {
                    "overall_status": "failed",
                    "error": "认证失败",
                    "end_time": datetime.now().isoformat(),
                }
            )
            return results

        # 定义同步操作列表 - 只包含4个核心模块
        sync_operations = [
            ("products", self.sync_products),
            ("cases", self.sync_cases),
            ("programmes", self.sync_programmes),
            ("information", self.sync_information),
            ("distribution_orders", self.sync_distribution_orders),
        ]

        results["total_operations"] = len(sync_operations)

        # 逐个执行同步操作
        for operation_name, sync_func in sync_operations:
            try:
                logger.info(f"开始同步 {operation_name}...")
                operation_result = sync_func()

                # 检查操作结果
                if isinstance(operation_result, dict):
                    if operation_result.get("success", False):
                        results["success_count"] += 1
                        logger.info(f"{operation_name} 同步成功")
                    else:
                        error_msg = operation_result.get(
                            "error", f"{operation_name} 同步失败"
                        )
                        results["errors"].append(f"{operation_name}: {error_msg}")
                        logger.error(f"{operation_name} 同步失败: {error_msg}")
                else:
                    # 兼容旧的返回格式
                    results["success_count"] += 1
                    logger.info(f"{operation_name} 同步完成")

                results[operation_name] = operation_result

            except Exception as e:
                error_msg = f"{operation_name} 同步异常: {str(e)}"
                results["errors"].append(error_msg)
                logger.error(error_msg, exc_info=True)

                # 记录详细错误信息
                results[operation_name] = {
                    "success": False,
                    "error": str(e),
                    "error_type": type(e).__name__,
                }

        # 计算总体状态
        if results["success_count"] == results["total_operations"]:
            results["overall_status"] = "success"
        elif results["success_count"] > 0:
            results["overall_status"] = "partial_success"
        else:
            results["overall_status"] = "failed"

        results["end_time"] = datetime.now().isoformat()

        # 记录同步摘要
        logger.info(
            f"数据同步完成 - 状态: {results['overall_status']}, "
            f"成功: {results['success_count']}/{results['total_operations']}, "
            f"错误: {len(results['errors'])}"
        )

        return results

    @sync_error_handler("同步产品数据")
    def sync_products(self) -> Dict[str, Any]:
        """
        同步产品数据

        Returns:
            Dict[str, Any]: 同步结果
        """
        entity_type = "products"
        try:
            logger.info("开始同步产品数据")
            self._update_sync_status(
                entity_type, "running", 0, 0, 0, "正在同步产品数据"
            )

            # 获取API数据
            api_data = self.api_client.get_products()

            if not api_data:
                logger.warning("未获取到产品数据")
                self._update_sync_status(
                    entity_type, "completed", 0, 0, 0, "未获取到产品数据"
                )
                return {
                    "success": True,
                    "status": "success",
                    "total": 0,
                    "success_count": 0,
                    "error_count": 0,
                    "message": "未获取到产品数据",
                }

            total_count = len(api_data)
            success_count = 0
            error_count = 0

            # 初始化附件管理器
            attachment_manager = AttachmentManager()

            for i, item in enumerate(api_data):
                try:
                    # 标准化数据
                    normalized_data = self.normalizer.normalize_single_response(
                        item, data_type="product"
                    )

                    # 保存产品数据
                    self._save_product(normalized_data)
                    success_count += 1

                    # 下载附件
                    try:
                        attachments = attachment_manager.download_business_attachments(
                            entity_type, str(item.get("id", "")), item
                        )
                        if attachments:
                            logger.info(
                                f"产品 {item.get('id')} 下载了 {len(attachments)} 个附件"
                            )
                    except Exception as e:
                        logger.warning(f"产品附件下载失败 {item.get('id')}: {e}")

                    # 更新进度
                    progress = int((i + 1) / total_count * 100)
                    self._update_sync_status(
                        entity_type,
                        "running",
                        total_count,
                        success_count,
                        error_count,
                        f"正在同步产品数据 ({i + 1}/{total_count})",
                        progress,
                    )

                except Exception as e:
                    # 使用详细的错误处理
                    error_id = log_sync_exception(
                        e,
                        f"处理产品数据时发生错误",
                        {
                            "product_id": item.get("id", "unknown"),
                            "product_name": item.get(
                                "name", item.get("productName", "unknown")
                            ),
                            "item_type": type(item).__name__,
                            "data_keys": (
                                list(item.keys())
                                if isinstance(item, dict)
                                else "not_dict"
                            ),
                            "item_data_sample": (
                                str(item)[:300] + "..."
                                if len(str(item)) > 300
                                else str(item)
                            ),
                        },
                    )
                    logger.error(f"[{error_id}] 处理产品数据失败")
                    error_count += 1

            # 完成同步
            final_status = "completed" if error_count == 0 else "completed_with_errors"
            self._update_sync_status(
                entity_type,
                final_status,
                total_count,
                success_count,
                error_count,
                f"产品数据同步完成，成功: {success_count}, 失败: {error_count}",
                100,
            )

            logger.info(
                f"产品数据同步完成 - 总数: {total_count}, 成功: {success_count}, 失败: {error_count}"
            )

            return {
                "success": True,
                "status": "success",
                "total": total_count,
                "success_count": success_count,
                "error_count": error_count,
                "message": f"产品数据同步完成，成功: {success_count}, 失败: {error_count}",
            }

        except Exception as e:
            # 记录详细的异常信息
            error_id = log_sync_exception(
                e,
                "同步产品数据异常",
                {
                    "entity_type": entity_type,
                    "api_data_length": (
                        len(api_data) if "api_data" in locals() else "unknown"
                    ),
                    "method": "sync_products",
                },
            )
            logger.error(f"[{error_id}] 同步产品数据异常")
            self._update_sync_status(
                entity_type, "failed", 0, 0, 0, f"同步失败: {error_id}"
            )
            return {
                "success": False,
                "status": "error",
                "total": 0,
                "success_count": 0,
                "error_count": 1,
                "message": f"同步产品数据异常: {error_id}",
                "error_id": error_id,
            }

    def sync_categories(self) -> Dict[str, Any]:
        """
        同步分类数据

        Returns:
            Dict[str, Any]: 同步结果
        """
        entity_type = "categories"
        try:
            logger.info("开始同步分类数据")

            # 从API客户端获取分类数据
            categories = self.api_client.get_categories()

            if not categories:
                logger.warning("未获取到分类数据")
                self._update_sync_status(
                    entity_type, "completed", 0, 0, 0, "未获取到分类数据"
                )
                return {
                    "success": True,
                    "status": "success",
                    "message": "未获取到分类数据",
                    "total": 0,
                }

            # 保存分类数据
            success_count = 0
            error_count = 0

            for category in categories:
                try:
                    # 标准化数据
                    normalized_category = self.normalizer.normalize_single_response(
                        category, data_type="category"
                    )
                    self._save_category(normalized_category)
                    success_count += 1
                except Exception as e:
                    logger.error(f"保存分类失败: {e}")
                    error_count += 1

            # 更新同步状态
            self._update_sync_status(
                entity_type,
                "completed" if error_count == 0 else "partial",
                len(categories),
                success_count,
                error_count,
            )

            logger.info(
                f"分类同步完成 - 总数: {len(categories)}, 成功: {success_count}, 失败: {error_count}"
            )

            return {
                "success": True,
                "status": "success",
                "total": len(categories),
                "success_count": success_count,
                "error_count": error_count,
            }

        except Exception as e:
            logger.error(f"同步分类失败: {e}")
            self._update_sync_status(entity_type, "failed", 0, 0, 1, str(e))
            return {"success": False, "status": "error", "message": str(e)}

    def sync_labels(self) -> Dict[str, Any]:
        """
        同步标签数据

        Returns:
            Dict[str, Any]: 同步结果
        """
        entity_type = "labels"
        try:
            logger.info("开始同步标签数据")

            # 从API客户端获取标签数据
            labels = self.api_client.get_labels()

            if not labels:
                logger.warning("未获取到标签数据")
                self._update_sync_status(
                    entity_type, "completed", 0, 0, 0, "未获取到标签数据"
                )
                return {
                    "success": True,
                    "status": "success",
                    "message": "未获取到标签数据",
                    "total": 0,
                }

            # 保存标签数据
            success_count = 0
            error_count = 0

            for label in labels:
                try:
                    # 标准化数据
                    normalized_label = self.normalizer.normalize_single_response(
                        label, data_type="label"
                    )
                    self._save_label(normalized_label)
                    success_count += 1
                except Exception as e:
                    logger.error(f"保存标签失败: {e}")
                    error_count += 1

            # 更新同步状态
            self._update_sync_status(
                entity_type,
                "completed" if error_count == 0 else "partial",
                len(labels),
                success_count,
                error_count,
            )

            logger.info(
                f"标签同步完成 - 总数: {len(labels)}, 成功: {success_count}, 失败: {error_count}"
            )

            return {
                "status": "success",
                "total": len(labels),
                "success": success_count,
                "error": error_count,
            }

        except Exception as e:
            logger.error(f"同步标签失败: {e}")
            self._update_sync_status(entity_type, "failed", 0, 0, 1, str(e))
            return {"status": "error", "message": str(e)}

    def sync_cases(self) -> Dict[str, Any]:
        """
        同步案例数据

        Returns:
            Dict[str, Any]: 同步结果
        """
        entity_type = "cases"
        try:
            logger.info("开始同步案例数据")
            self._update_sync_status(
                entity_type, "running", 0, 0, 0, "正在同步案例数据"
            )

            # 获取API数据
            api_data = self.api_client.get_cases()

            if not api_data:
                logger.warning("未获取到案例数据")
                self._update_sync_status(
                    entity_type, "completed", 0, 0, 0, "未获取到案例数据"
                )
                return {
                    "status": "success",
                    "total": 0,
                    "success": 0,
                    "error": 0,
                    "message": "未获取到案例数据",
                }

            total_count = len(api_data)
            success_count = 0
            error_count = 0

            # 初始化附件管理器
            attachment_manager = AttachmentManager()

            for i, item in enumerate(api_data):
                try:
                    # 标准化数据
                    normalized_data = self.normalizer.normalize_single_response(
                        item, data_type="case"
                    )

                    # 保存案例数据
                    self._save_case(normalized_data)
                    success_count += 1

                    # 下载附件
                    try:
                        attachments = attachment_manager.download_business_attachments(
                            entity_type, str(item.get("id", "")), item
                        )
                        if attachments:
                            logger.info(
                                f"案例 {item.get('id')} 下载了 {len(attachments)} 个附件"
                            )
                    except Exception as e:
                        logger.warning(f"案例附件下载失败 {item.get('id')}: {e}")

                    # 更新进度
                    progress = int((i + 1) / total_count * 100)
                    self._update_sync_status(
                        entity_type,
                        "running",
                        total_count,
                        success_count,
                        error_count,
                        f"正在同步案例数据 ({i + 1}/{total_count})",
                        progress,
                    )

                except Exception as e:
                    logger.error(f"同步案例数据失败: {e}")
                    error_count += 1

            # 完成同步
            final_status = "completed" if error_count == 0 else "completed_with_errors"
            self._update_sync_status(
                entity_type,
                final_status,
                total_count,
                success_count,
                error_count,
                f"案例数据同步完成，成功: {success_count}, 失败: {error_count}",
                100,
            )

            logger.info(
                f"案例数据同步完成 - 总数: {total_count}, 成功: {success_count}, 失败: {error_count}"
            )

            return {
                "status": "success",
                "total": total_count,
                "success": success_count,
                "error": error_count,
                "message": f"案例数据同步完成，成功: {success_count}, 失败: {error_count}",
            }

        except Exception as e:
            logger.error(f"同步案例数据异常: {e}")
            self._update_sync_status(
                entity_type, "failed", 0, 0, 0, f"同步失败: {str(e)}"
            )
            return {
                "status": "error",
                "total": 0,
                "success": 0,
                "error": 0,
                "message": f"同步案例数据异常: {str(e)}",
            }

    def sync_programmes(self) -> Dict[str, Any]:
        """
        同步方案数据

        Returns:
            Dict[str, Any]: 同步结果
        """
        entity_type = "programmes"
        try:
            logger.info("开始同步方案数据")
            self._update_sync_status(
                entity_type, "running", 0, 0, 0, "正在同步方案数据"
            )

            # 获取API数据
            api_data = self.api_client.get_programmes()

            if not api_data:
                logger.warning("未获取到方案数据")
                self._update_sync_status(
                    entity_type, "completed", 0, 0, 0, "未获取到方案数据"
                )
                return {
                    "status": "success",
                    "total": 0,
                    "success": 0,
                    "error": 0,
                    "message": "未获取到方案数据",
                }

            total_count = len(api_data)
            success_count = 0
            error_count = 0

            # 初始化附件管理器
            attachment_manager = AttachmentManager()

            for i, item in enumerate(api_data):
                try:
                    # 标准化数据
                    normalized_data = self.normalizer.normalize_single_response(
                        item, data_type="programme"
                    )

                    # 保存方案数据
                    self._save_programme(normalized_data)
                    success_count += 1

                    # 下载附件
                    try:
                        attachments = attachment_manager.download_business_attachments(
                            entity_type, str(item.get("id", "")), item
                        )
                        if attachments:
                            logger.info(
                                f"方案 {item.get('id')} 下载了 {len(attachments)} 个附件"
                            )
                    except Exception as e:
                        logger.warning(f"方案附件下载失败 {item.get('id')}: {e}")

                    # 更新进度
                    progress = int((i + 1) / total_count * 100)
                    self._update_sync_status(
                        entity_type,
                        "running",
                        total_count,
                        success_count,
                        error_count,
                        f"正在同步方案数据 ({i + 1}/{total_count})",
                        progress,
                    )

                except Exception as e:
                    logger.error(f"同步方案数据失败: {e}")
                    error_count += 1

            # 完成同步
            final_status = "completed" if error_count == 0 else "completed_with_errors"
            self._update_sync_status(
                entity_type,
                final_status,
                total_count,
                success_count,
                error_count,
                f"方案数据同步完成，成功: {success_count}, 失败: {error_count}",
                100,
            )

            logger.info(
                f"方案数据同步完成 - 总数: {total_count}, 成功: {success_count}, 失败: {error_count}"
            )

            return {
                "status": "success",
                "total": total_count,
                "success": success_count,
                "error": error_count,
                "message": f"方案数据同步完成，成功: {success_count}, 失败: {error_count}",
            }

        except Exception as e:
            logger.error(f"同步方案数据异常: {e}")
            self._update_sync_status(
                entity_type, "failed", 0, 0, 0, f"同步失败: {str(e)}"
            )
            return {
                "status": "error",
                "total": 0,
                "success": 0,
                "error": 0,
                "message": f"同步方案数据异常: {str(e)}",
            }

    def sync_information(self) -> Dict[str, Any]:
        """
        同步资讯数据

        Returns:
            Dict[str, Any]: 同步结果
        """
        entity_type = "information"
        try:
            logger.info("开始同步资讯数据")
            self._update_sync_status(
                entity_type, "running", 0, 0, 0, "正在同步资讯数据"
            )

            # 获取API数据
            api_data = self.api_client.get_information()

            if not api_data:
                logger.warning("未获取到资讯数据")
                self._update_sync_status(
                    entity_type, "completed", 0, 0, 0, "未获取到资讯数据"
                )
                return {
                    "status": "success",
                    "total": 0,
                    "success": 0,
                    "error": 0,
                    "message": "未获取到资讯数据",
                }

            total_count = len(api_data)
            success_count = 0
            error_count = 0

            # 初始化附件管理器
            attachment_manager = AttachmentManager()

            for i, item in enumerate(api_data):
                try:
                    # 标准化数据
                    normalized_data = self.normalizer.normalize_single_response(
                        item, data_type="information"
                    )

                    # 保存资讯数据
                    self._save_information(normalized_data)
                    success_count += 1

                    # 下载附件
                    try:
                        attachments = attachment_manager.download_business_attachments(
                            entity_type, str(item.get("id", "")), item
                        )
                        if attachments:
                            logger.info(
                                f"资讯 {item.get('id')} 下载了 {len(attachments)} 个附件"
                            )
                    except Exception as e:
                        logger.warning(f"资讯附件下载失败 {item.get('id')}: {e}")

                    # 更新进度
                    progress = int((i + 1) / total_count * 100)
                    self._update_sync_status(
                        entity_type,
                        "running",
                        total_count,
                        success_count,
                        error_count,
                        f"正在同步资讯数据 ({i + 1}/{total_count})",
                        progress,
                    )

                except Exception as e:
                    logger.error(f"同步资讯数据失败: {e}")
                    error_count += 1

            # 完成同步
            final_status = "completed" if error_count == 0 else "completed_with_errors"
            self._update_sync_status(
                entity_type,
                final_status,
                total_count,
                success_count,
                error_count,
                f"资讯数据同步完成，成功: {success_count}, 失败: {error_count}",
                100,
            )

            logger.info(
                f"资讯数据同步完成 - 总数: {total_count}, 成功: {success_count}, 失败: {error_count}"
            )

            return {
                "status": "success",
                "total": total_count,
                "success": success_count,
                "error": error_count,
                "message": f"资讯数据同步完成，成功: {success_count}, 失败: {error_count}",
            }

        except Exception as e:
            logger.error(f"同步资讯数据异常: {e}")
            self._update_sync_status(
                entity_type, "failed", 0, 0, 0, f"同步失败: {str(e)}"
            )
            return {
                "status": "error",
                "total": 0,
                "success": 0,
                "error": 0,
                "message": f"同步资讯数据异常: {str(e)}",
            }

    def sync_distribution_orders(self) -> Dict[str, Any]:
        """
        同步配单数据

        Returns:
            Dict[str, Any]: 同步结果
        """
        entity_type = "distribution_orders"
        try:
            logger.info("开始同步配单数据")
            self._update_sync_status(
                entity_type, "running", 0, 0, 0, "正在同步配单数据"
            )

            # 获取API数据
            api_data = self.api_client.get_distribution_orders()

            if not api_data:
                logger.warning("未获取到配单数据")
                self._update_sync_status(
                    entity_type, "completed", 0, 0, 0, "未获取到配单数据"
                )
                return {
                    "status": "success",
                    "total": 0,
                    "success": 0,
                    "error": 0,
                    "message": "未获取到配单数据",
                }

            total_count = len(api_data)
            success_count = 0
            error_count = 0

            # 初始化附件管理器
            attachment_manager = AttachmentManager()

            for i, item in enumerate(api_data):
                try:
                    # 标准化数据
                    normalized_data = self.normalizer.normalize_single_response(
                        item, data_type="distribution_order"
                    )

                    # 保存配单数据
                    self._save_distribution_order(normalized_data)
                    success_count += 1

                    # 下载附件
                    try:
                        attachments = attachment_manager.download_business_attachments(
                            entity_type, str(item.get("id", "")), item
                        )
                        if attachments:
                            logger.info(
                                f"配单 {item.get('id')} 下载了 {len(attachments)} 个附件"
                            )
                    except Exception as e:
                        logger.warning(f"配单附件下载失败 {item.get('id')}: {e}")

                    # 更新进度
                    progress = int((i + 1) / total_count * 100)
                    self._update_sync_status(
                        entity_type,
                        "running",
                        total_count,
                        success_count,
                        error_count,
                        f"正在同步配单数据 ({i + 1}/{total_count})",
                        progress,
                    )

                except Exception as e:
                    logger.error(f"同步配单数据失败: {e}")
                    error_count += 1

            # 完成同步
            final_status = "completed" if error_count == 0 else "completed_with_errors"
            self._update_sync_status(
                entity_type,
                final_status,
                total_count,
                success_count,
                error_count,
                f"配单数据同步完成，成功: {success_count}, 失败: {error_count}",
                100,
            )

            logger.info(
                f"配单数据同步完成 - 总数: {total_count}, 成功: {success_count}, 失败: {error_count}"
            )

            return {
                "status": "success",
                "total": total_count,
                "success": success_count,
                "error": error_count,
                "message": f"配单数据同步完成，成功: {success_count}, 失败: {error_count}",
            }

        except Exception as e:
            logger.error(f"同步配单数据异常: {e}")
            self._update_sync_status(
                entity_type, "failed", 0, 0, 0, f"同步失败: {str(e)}"
            )
            return {
                "status": "error",
                "total": 0,
                "success": 0,
                "error": 0,
                "message": f"同步配单数据异常: {str(e)}",
            }

    def get_sync_status(
        self, entity_type: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        获取同步状态

        Args:
            entity_type: 实体类型，为空时获取所有

        Returns:
            List[Dict[str, Any]]: 同步状态列表
        """

        def _get_status_operation(conn, entity_type):
            with conn.cursor() as cursor:
                if entity_type:
                    cursor.execute(
                        """
                        SELECT * FROM sync_status
                        WHERE entity_type = %s
                        """,
                        (entity_type,),
                    )
                else:
                    cursor.execute("SELECT * FROM sync_status")

                return cursor.fetchall()

        try:
            return self._execute_with_connection(_get_status_operation, entity_type)
        except Exception as e:
            logger.error(f"获取同步状态失败: {e}")
            return []

    def _get_last_sync_time(self, entity_type: str) -> Optional[datetime.datetime]:
        """
        获取上次同步时间

        Args:
            entity_type: 实体类型

        Returns:
            Optional[datetime.datetime]: 上次同步时间
        """

        def _get_time_operation(conn, entity_type):
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT last_sync_time FROM sync_status
                    WHERE entity_type = %s
                    """,
                    (entity_type,),
                )

                result = cursor.fetchone()
                return result["last_sync_time"] if result else None

        try:
            return self._execute_with_connection(_get_time_operation, entity_type)
        except Exception as e:
            logger.error(f"获取上次同步时间失败: {e}")
            return None

    def _update_sync_status(
        self,
        entity_type: str,
        status: str,
        total_count: int = 0,
        success_count: int = 0,
        error_count: int = 0,
        error_message: str = "",
        progress: int = 0,
    ) -> None:
        """
        更新同步状态

        Args:
            entity_type: 实体类型
            status: 状态
            total_count: 总数
            success_count: 成功数
            error_count: 错误数
            error_message: 错误信息
            progress: 进度
        """

        def _update_operation(
            conn,
            entity_type,
            status,
            total_count,
            success_count,
            error_count,
            error_message,
            progress,
        ):
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    UPDATE sync_status SET
                        last_sync_time = CURRENT_TIMESTAMP,
                        status = %s,
                        total_count = %s,
                        success_count = %s,
                        error_count = %s,
                        error_message = %s,
                        progress = %s,
                        update_time = CURRENT_TIMESTAMP
                    WHERE entity_type = %s
                    """,
                    (
                        status,
                        total_count,
                        success_count,
                        error_count,
                        error_message,
                        progress,
                        entity_type,
                    ),
                )
                conn.commit()

        try:
            self._execute_with_connection(
                _update_operation,
                entity_type,
                status,
                total_count,
                success_count,
                error_count,
                error_message,
                progress,
            )
        except Exception as e:
            logger.error(f"更新同步状态失败: {e}")

    def _save_product(self, product: Dict[str, Any]) -> None:
        """
        保存产品数据

        Args:
            product: 产品数据
        """

        def _save_operation(conn, product):
            with conn.cursor() as cursor:
                # 处理可能的None值
                product_id = product.get("id")
                if not product_id:
                    logger.warning(f"产品ID为空，跳过保存")
                    return

                # 安全获取count字段值
                def safe_get_count(data):
                    """安全获取count字段值"""
                    try:
                        # 尝试从标准化后的字段获取
                        if "viewCount" in data:
                            count_val = data["viewCount"]
                        elif "count" in data:
                            count_val = data["count"]
                        else:
                            return 0

                        # 确保返回整数
                        if isinstance(count_val, (int, float)):
                            return int(count_val)
                        elif isinstance(count_val, str) and count_val.isdigit():
                            return int(count_val)
                        else:
                            logger.warning(
                                f"产品 {product_id} count字段值无效: {count_val}, 使用默认值0"
                            )
                            return 0
                    except Exception as e:
                        logger.warning(
                            f"产品 {product_id} 获取count字段失败: {e}, 使用默认值0"
                        )
                        return 0

                # 检查产品是否已存在
                cursor.execute(
                    "SELECT id FROM products WHERE id = %s",
                    (product_id,),
                )

                exists = cursor.fetchone()

                # 准备数据 - 优化知识内容字段处理
                data = {
                    "id": product_id,
                    "name": product.get("name", ""),
                    "spec": product.get("spec", ""),
                    "introduction": product.get("introduction", ""),
                    "details": product.get("details", ""),
                    "small_img": product.get("smallImg", ""),
                    "banner": product.get("banner", ""),
                    "category_id": self._parse_category_id(
                        product.get("category") or product.get("categoryId")
                    ),
                    "label_id": self._parse_label_id(
                        product.get("label") or product.get("labelId")
                    ),
                    "attribute": product.get("attribute", "0"),
                    "show_for": product.get("showFor", ""),
                    # 知识内容字段 - 完整处理
                    "param_info": product.get("paramInfo", ""),
                    "new_param": self._safe_json_serialize(product.get("newParam")),
                    "param_info_list": self._safe_json_serialize(
                        product.get("paramInfoList")
                    ),
                    "use_to": product.get("useTo", ""),
                    "qualifications": product.get("qualifications", ""),
                    "instructions": product.get("instructions", ""),
                    "other": product.get("other", ""),
                    "guide": product.get("guide", ""),
                    "common_problem": product.get("commonProblem", ""),
                    # 视频相关字段
                    "video_explanation": product.get("videoExplanation", ""),
                    "video_installation": product.get("videoInstallation", ""),
                    "video_troubleshooting": product.get("videoTroubleshooting", ""),
                    # 其他扩展字段
                    "accessory": product.get("accessory", ""),
                    "size_img": product.get("sizeImg", ""),
                    "unit": product.get("unit"),
                    "unit_name": product.get("unitName", ""),
                    # 基础字段
                    "price": product.get("price", 0),
                    "is_suggest": product.get("isSuggest", "0"),
                    "is_hot": product.get("isHot", "0"),
                    "is_new": product.get("isNew", "0"),
                    "count": safe_get_count(product),
                    "like_count": 0,  # 这个字段在API中不存在，设为0
                    "favorite_count": 0,  # 这个字段在API中不存在，设为0
                    "status": product.get("status", "0"),
                    "site_id": 999,  # 默认站点ID
                    "brand_id": None,  # 这个字段在API中不存在，设为None
                    "show_time": product.get("showTime"),
                    "sort": product.get("sort", 0),
                    "create_time": product.get("createTime"),
                    "update_time": product.get("updateTime"),
                    "create_by": product.get("createBy", ""),
                    "update_by": product.get("updateBy", ""),
                    "last_sync_time": datetime.datetime.now(),
                }

                if exists:
                    # 更新产品
                    cursor.execute(
                        """
                        UPDATE products SET
                            name = %(name)s,
                            spec = %(spec)s,
                            introduction = %(introduction)s,
                            details = %(details)s,
                            small_img = %(small_img)s,
                            banner = %(banner)s,
                            category_id = %(category_id)s,
                            label_id = %(label_id)s,
                            attribute = %(attribute)s,
                            show_for = %(show_for)s,
                            param_info = %(param_info)s,
                            new_param = %(new_param)s,
                            param_info_list = %(param_info_list)s,
                            use_to = %(use_to)s,
                            qualifications = %(qualifications)s,
                            instructions = %(instructions)s,
                            other = %(other)s,
                            guide = %(guide)s,
                            common_problem = %(common_problem)s,
                            video_explanation = %(video_explanation)s,
                            video_installation = %(video_installation)s,
                            video_troubleshooting = %(video_troubleshooting)s,
                            accessory = %(accessory)s,
                            size_img = %(size_img)s,
                            unit = %(unit)s,
                            unit_name = %(unit_name)s,
                            price = %(price)s,
                            is_suggest = %(is_suggest)s,
                            is_hot = %(is_hot)s,
                            is_new = %(is_new)s,
                            count = %(count)s,
                            like_count = %(like_count)s,
                            favorite_count = %(favorite_count)s,
                            status = %(status)s,
                            site_id = %(site_id)s,
                            brand_id = %(brand_id)s,
                            show_time = %(show_time)s,
                            sort = %(sort)s,
                            update_time = %(update_time)s,
                            update_by = %(update_by)s,
                            last_sync_time = %(last_sync_time)s
                        WHERE id = %(id)s
                        """,
                        data,
                    )
                else:
                    # 插入产品
                    cursor.execute(
                        """
                        INSERT INTO products (
                            id, name, spec, introduction, details, small_img, banner,
                            category_id, label_id, attribute, show_for, param_info, new_param, param_info_list, use_to,
                            qualifications, instructions, other, guide, common_problem,
                            video_explanation, video_installation, video_troubleshooting,
                            accessory, size_img, unit, unit_name, price,
                            is_suggest, is_hot, is_new, count, like_count, favorite_count,
                            status, site_id, brand_id, show_time, sort, create_time, update_time,
                            create_by, update_by, last_sync_time
                        ) VALUES (
                            %(id)s, %(name)s, %(spec)s, %(introduction)s, %(details)s, %(small_img)s, %(banner)s,
                            %(category_id)s, %(label_id)s, %(attribute)s, %(show_for)s, %(param_info)s, %(new_param)s, %(param_info_list)s, %(use_to)s,
                            %(qualifications)s, %(instructions)s, %(other)s, %(guide)s, %(common_problem)s,
                            %(video_explanation)s, %(video_installation)s, %(video_troubleshooting)s,
                            %(accessory)s, %(size_img)s, %(unit)s, %(unit_name)s, %(price)s,
                            %(is_suggest)s, %(is_hot)s, %(is_new)s, %(count)s, %(like_count)s, %(favorite_count)s,
                            %(status)s, %(site_id)s, %(brand_id)s, %(show_time)s, %(sort)s, %(create_time)s, %(update_time)s,
                            %(create_by)s, %(update_by)s, %(last_sync_time)s
                        )
                        """,
                        data,
                    )

                conn.commit()

        self._execute_with_connection(_save_operation, product)

    def _safe_json_serialize(self, data: Any) -> str:
        """
        安全地将数据序列化为JSON字符串

        Args:
            data: 要序列化的数据

        Returns:
            str: JSON字符串，如果序列化失败则返回空字符串
        """
        try:
            if data is None:
                return ""
            if isinstance(data, str):
                # 如果已经是字符串，检查是否是有效JSON
                try:
                    json.loads(data)
                    return data
                except:
                    # 不是有效JSON，直接返回
                    return data
            # 序列化为JSON
            return json.dumps(data, ensure_ascii=False)
        except Exception as e:
            logger.warning(f"JSON序列化失败: {e}, 数据: {str(data)[:100]}")
            return ""

    def _parse_label_id(self, label: str) -> Optional[int]:
        """
        解析标签ID，处理逗号分隔的多个标签ID，取第一个

        Args:
            label: 标签字符串，可能包含逗号分隔的多个ID

        Returns:
            int: 解析后的标签ID，如果为空或无效则返回None
        """
        if not label:
            return None

        try:
            # 如果是逗号分隔的多个ID，取第一个
            first_label = label.split(",")[0].strip()
            if first_label:
                return int(first_label)
        except (ValueError, AttributeError):
            logger.warning(f"无法解析标签ID: {label}")

        return None

    def _save_case(self, case: Dict[str, Any]) -> None:
        """
        保存案例数据到数据库

        Args:
            case: 案例数据
        """

        def _save_case_operation(conn, case):
            with conn.cursor() as cursor:
                # 处理可能的None值
                case_id = case.get("id")
                if not case_id:
                    logger.warning(f"案例ID为空，跳过保存")
                    return

                # 检查案例是否已存在
                cursor.execute(
                    "SELECT id FROM cases WHERE id = %s",
                    (case_id,),
                )
                exists = cursor.fetchone()

                # 准备数据
                data = {
                    "id": case_id,
                    "name": case.get("name", case.get("caseName", "")),
                    "introduction": case.get("introduction", ""),
                    "content": case.get("content", ""),
                    "details": case.get("details", ""),
                    "img": case.get("img", ""),
                    "banner": case.get("banner", ""),
                    "small_img": case.get("smallImg", ""),
                    "video": case.get("video", ""),
                    "images": case.get("images", ""),
                    "category_id": self._parse_category_id(
                        case.get("category") or case.get("categoryId")
                    ),
                    "product_id": case.get("productId"),
                    "product_name": case.get("productName", ""),
                    "keywords": case.get("keywords", ""),
                    "publish_name": case.get("publishName", ""),
                    "real_name": case.get("realName", ""),
                    "publish_id": case.get("publishId"),
                    "company_id": case.get("companyId"),
                    "company_name": case.get("companyName", ""),
                    "industry": case.get("industry", ""),
                    "province": case.get("province", ""),
                    "city": case.get("city", ""),
                    "county": case.get("county", ""),
                    "location": case.get("location", ""),
                    "count": case.get("viewCount", case.get("count", 0)),
                    "like_count": case.get("likeCount", 0),
                    "favorite_count": case.get("favoriteCount", 0),
                    "watch": case.get("watch", 0),
                    "approve_status": case.get("approveStatus", ""),
                    "is_suggest": case.get("isSuggest", "0"),
                    "is_auth": case.get("isAuth", "0"),
                    "is_push": case.get("isPush", "0"),
                    "is_share": case.get("isShare", "0"),
                    "is_hot": case.get("isHot", "0"),
                    "parent_id": case.get("parentId"),
                    "top": case.get("top", "0"),
                    "status": case.get("status", "0"),
                    "site_id": case.get("siteId", 999),
                    "belong_id": case.get("belongId"),
                    "type": case.get("type", 0),
                    "sort": case.get("sort", 0),
                    "create_time": case.get("createTime"),
                    "update_time": case.get("updateTime"),
                    "create_by": case.get("createBy", ""),
                    "update_by": case.get("updateBy", ""),
                    "last_sync_time": datetime.datetime.now(),
                }

                if exists:
                    # 更新案例
                    cursor.execute(
                        """
                        UPDATE cases SET
                            name = %(name)s,
                            introduction = %(introduction)s,
                            content = %(content)s,
                            details = %(details)s,
                            img = %(img)s,
                            banner = %(banner)s,
                            small_img = %(small_img)s,
                            video = %(video)s,
                            images = %(images)s,
                            category_id = %(category_id)s,
                            product_id = %(product_id)s,
                            product_name = %(product_name)s,
                            keywords = %(keywords)s,
                            publish_name = %(publish_name)s,
                            real_name = %(real_name)s,
                            publish_id = %(publish_id)s,
                            company_id = %(company_id)s,
                            company_name = %(company_name)s,
                            industry = %(industry)s,
                            province = %(province)s,
                            city = %(city)s,
                            county = %(county)s,
                            location = %(location)s,
                            count = %(count)s,
                            like_count = %(like_count)s,
                            favorite_count = %(favorite_count)s,
                            watch = %(watch)s,
                            approve_status = %(approve_status)s,
                            is_suggest = %(is_suggest)s,
                            is_auth = %(is_auth)s,
                            is_push = %(is_push)s,
                            is_share = %(is_share)s,
                            is_hot = %(is_hot)s,
                            parent_id = %(parent_id)s,
                            top = %(top)s,
                            status = %(status)s,
                            site_id = %(site_id)s,
                            belong_id = %(belong_id)s,
                            type = %(type)s,
                            sort = %(sort)s,
                            update_time = %(update_time)s,
                            update_by = %(update_by)s,
                            last_sync_time = %(last_sync_time)s
                        WHERE id = %(id)s
                        """,
                        data,
                    )
                else:
                    # 插入案例
                    cursor.execute(
                        """
                        INSERT INTO cases (
                            id, name, introduction, content, details, img, banner, small_img, video, images,
                            category_id, product_id, product_name, keywords, publish_name, real_name, publish_id,
                            company_id, company_name, industry, province, city, county, location,
                            count, like_count, favorite_count, watch, approve_status, is_suggest, is_auth,
                            is_push, is_share, is_hot, parent_id, top, status, site_id, belong_id, type, sort,
                            create_time, update_time, create_by, update_by, last_sync_time
                        ) VALUES (
                            %(id)s, %(name)s, %(introduction)s, %(content)s, %(details)s, %(img)s, %(banner)s, %(small_img)s, %(video)s, %(images)s,
                            %(category_id)s, %(product_id)s, %(product_name)s, %(keywords)s, %(publish_name)s, %(real_name)s, %(publish_id)s,
                            %(company_id)s, %(company_name)s, %(industry)s, %(province)s, %(city)s, %(county)s, %(location)s,
                            %(count)s, %(like_count)s, %(favorite_count)s, %(watch)s, %(approve_status)s, %(is_suggest)s, %(is_auth)s,
                            %(is_push)s, %(is_share)s, %(is_hot)s, %(parent_id)s, %(top)s, %(status)s, %(site_id)s, %(belong_id)s, %(type)s, %(sort)s,
                            %(create_time)s, %(update_time)s, %(create_by)s, %(update_by)s, %(last_sync_time)s
                        )
                        """,
                        data,
                    )

                conn.commit()

        self._execute_with_connection(_save_case_operation, case)

    def _save_programme(self, programme: Dict[str, Any]) -> None:
        """
        保存方案数据到数据库

        Args:
            programme: 方案数据
        """

        def _save_programme_operation(conn, programme):
            with conn.cursor() as cursor:
                # 处理可能的None值
                programme_id = programme.get("id")
                if not programme_id:
                    logger.warning(f"方案ID为空，跳过保存")
                    return

                # 检查方案是否已存在
                cursor.execute(
                    "SELECT id FROM programmes WHERE id = %s",
                    (programme_id,),
                )
                exists = cursor.fetchone()

                # 准备数据
                data = {
                    "id": programme_id,
                    "name": programme.get("name", programme.get("programmeName", "")),
                    "introduction": programme.get("introduction", ""),
                    "content": programme.get("content", ""),
                    "details": programme.get("details", ""),
                    "overview": programme.get("overview", ""),
                    "features": programme.get("features", ""),
                    "advantages": programme.get("advantages", ""),
                    "applications": programme.get("applications", ""),
                    "banner": programme.get("banner", ""),
                    "small_img": programme.get("smallImg", ""),
                    "video": programme.get("video", ""),
                    "images": programme.get("images", ""),
                    "other": programme.get("other", ""),
                    "category_id": self._parse_category_id(programme.get("categoryId")),
                    "category_scene_id": programme.get("categorySceneId"),
                    "product_id": programme.get("productId"),
                    "product_name": programme.get("productName", ""),
                    "product_list": programme.get(
                        "list", programme.get("productList", "")
                    ),
                    "hard_list": programme.get("hardList", ""),
                    "soft_list": programme.get("softList", ""),
                    "custom_list": programme.get("customList", ""),
                    "company_id": programme.get("companyId"),
                    "company_name": programme.get("companyName", ""),
                    "province": programme.get("province", ""),
                    "city": programme.get("city", ""),
                    "county": programme.get("county", ""),
                    "count": programme.get("count", 0),
                    "like_count": programme.get("likeCount", 0),
                    "favorite_count": programme.get("favoriteCount", 0),
                    "collect_size": programme.get("collectSize", 0),
                    "is_suggest": programme.get("isSuggest", 0),
                    "is_hot": programme.get("isHot", "0"),
                    "is_new": programme.get("isNew", 0),
                    "is_push": programme.get("isPush", 0),
                    "top": programme.get("top", "0"),
                    "status": programme.get("status", "0"),
                    "site_id": programme.get("siteId", 999),
                    "type": programme.get("type", 0),
                    "sort": programme.get("sort", 0),
                    "create_time": programme.get("createTime"),
                    "update_time": programme.get("updateTime"),
                    "create_by": programme.get("createBy", ""),
                    "update_by": programme.get("updateBy", ""),
                    "last_sync_time": datetime.datetime.now(),
                }

                if exists:
                    # 更新方案
                    cursor.execute(
                        """
                        UPDATE programmes SET
                            name = %(name)s,
                            introduction = %(introduction)s,
                            content = %(content)s,
                            banner = %(banner)s,
                            small_img = %(small_img)s,
                            video = %(video)s,
                            other = %(other)s,
                            category_id = %(category_id)s,
                            category_scene_id = %(category_scene_id)s,
                            company_id = %(company_id)s,
                            province = %(province)s,
                            city = %(city)s,
                            county = %(county)s,
                            count = %(count)s,
                            like_count = %(like_count)s,
                            favorite_count = %(favorite_count)s,
                            collect_size = %(collect_size)s,
                            is_suggest = %(is_suggest)s,
                            is_hot = %(is_hot)s,
                            is_new = %(is_new)s,
                            is_push = %(is_push)s,
                            top = %(top)s,
                            type = %(type)s,
                            sort = %(sort)s,
                            status = %(status)s,
                            site_id = %(site_id)s,
                            update_time = %(update_time)s,
                            update_by = %(update_by)s,
                            last_sync_time = %(last_sync_time)s
                        WHERE id = %(id)s
                        """,
                        data,
                    )
                else:
                    # 插入方案
                    cursor.execute(
                        """
                        INSERT INTO programmes (
                            id, name, introduction, content, banner, small_img, video, other,
                            category_id, category_scene_id, company_id, province, city, county,
                            count, like_count, favorite_count, collect_size, is_suggest, is_hot,
                            is_new, is_push, top, type, sort, status, site_id,
                            create_time, update_time, create_by, update_by, last_sync_time
                        ) VALUES (
                            %(id)s, %(name)s, %(introduction)s, %(content)s, %(banner)s, %(small_img)s, %(video)s, %(other)s,
                            %(category_id)s, %(category_scene_id)s, %(company_id)s, %(province)s, %(city)s, %(county)s,
                            %(count)s, %(like_count)s, %(favorite_count)s, %(collect_size)s, %(is_suggest)s, %(is_hot)s,
                            %(is_new)s, %(is_push)s, %(top)s, %(type)s, %(sort)s, %(status)s, %(site_id)s,
                            %(create_time)s, %(update_time)s, %(create_by)s, %(update_by)s, %(last_sync_time)s
                        )
                        """,
                        data,
                    )

                conn.commit()

        self._execute_with_connection(_save_programme_operation, programme)

    def _save_information(self, info: Dict[str, Any]) -> None:
        """
        保存资讯数据到数据库

        Args:
            info: 资讯数据
        """

        def _save_information_operation(conn, info):
            with conn.cursor() as cursor:
                # 处理可能的None值 - 首先检查info本身是否为None
                if info is None:
                    logger.warning("资讯数据为None，跳过保存")
                    return

                # 安全获取资讯数据的函数
                def safe_get_info_data(data, key, default_value=""):
                    """安全获取资讯数据字段值"""
                    try:
                        if data is None:
                            logger.warning(
                                f"资讯数据为None，字段 {key} 使用默认值: {default_value}"
                            )
                            return default_value

                        if not isinstance(data, dict):
                            logger.warning(
                                f"资讯数据不是字典类型: {type(data)}, 字段 {key} 使用默认值: {default_value}"
                            )
                            return default_value

                        value = data.get(key, default_value)
                        if value is None:
                            logger.debug(
                                f"资讯字段 {key} 为None，使用默认值: {default_value}"
                            )
                            return default_value

                        return value
                    except Exception as e:
                        logger.warning(
                            f"获取资讯字段 {key} 失败: {e}, 使用默认值: {default_value}"
                        )
                        return default_value

                info_id = safe_get_info_data(info, "id")
                if not info_id:
                    logger.warning(
                        f"资讯ID为空，跳过保存。原始数据: {str(info)[:200] if info else 'None'}"
                    )
                    return

                # 检查资讯是否已存在
                cursor.execute(
                    "SELECT id FROM information WHERE id = %s",
                    (info_id,),
                )
                exists = cursor.fetchone()

                # 准备数据 - 使用安全获取函数
                data = {
                    "id": info_id,
                    "title": safe_get_info_data(info, "title", ""),
                    "details": safe_get_info_data(
                        info, "details", safe_get_info_data(info, "content", "")
                    ),
                    "content": safe_get_info_data(info, "content", ""),
                    "summary": safe_get_info_data(info, "summary", ""),
                    "author": safe_get_info_data(info, "author", ""),
                    "pic_video": safe_get_info_data(info, "picVideo", ""),
                    "small_img": safe_get_info_data(info, "smallImg", ""),
                    "images": safe_get_info_data(info, "images", ""),
                    "other_url": safe_get_info_data(info, "otherUrl", ""),
                    "video_url": safe_get_info_data(info, "videoUrl", ""),
                    "cover_image": safe_get_info_data(info, "coverImage", ""),
                    "category_id": self._parse_category_id(
                        safe_get_info_data(info, "categoryId", None)
                    ),
                    "product_id": safe_get_info_data(info, "productId", None),
                    "product_name": safe_get_info_data(info, "productName", ""),
                    "belong_id": safe_get_info_data(info, "belongId", None),
                    "is_hot": safe_get_info_data(info, "isHot", 0),
                    "is_suggest": safe_get_info_data(info, "isSuggest", "0"),
                    "show_type": safe_get_info_data(info, "showType", 0),
                    "top": safe_get_info_data(info, "top", "0"),
                    "status": safe_get_info_data(info, "status", "0"),
                    "watch": safe_get_info_data(info, "watch", 0),
                    "read_count": safe_get_info_data(info, "readCount", 0),
                    "like_count": safe_get_info_data(info, "likeCount", 0),
                    "favorite_count": safe_get_info_data(info, "favoriteCount", 0),
                    "publish_time": safe_get_info_data(info, "publishTime", None),
                    "publish_name": safe_get_info_data(info, "publishName", ""),
                    "site_id": safe_get_info_data(info, "siteId", 999),
                    "type": safe_get_info_data(info, "type", 0),
                    "create_time": safe_get_info_data(info, "createTime", None),
                    "update_time": safe_get_info_data(info, "updateTime", None),
                    "create_by": safe_get_info_data(info, "createBy", ""),
                    "update_by": safe_get_info_data(info, "updateBy", ""),
                    "last_sync_time": datetime.datetime.now(),
                }

                if exists:
                    # 更新资讯
                    cursor.execute(
                        """
                        UPDATE information SET
                            title = %(title)s,
                            details = %(details)s,
                            pic_video = %(pic_video)s,
                            small_img = %(small_img)s,
                            images = %(images)s,
                            other_url = %(other_url)s,
                            video_url = %(video_url)s,
                            category_id = %(category_id)s,
                            product_id = %(product_id)s,
                            belong_id = %(belong_id)s,
                            is_hot = %(is_hot)s,
                            is_suggest = %(is_suggest)s,
                            show_type = %(show_type)s,
                            top = %(top)s,
                            watch = %(watch)s,
                            like_count = %(like_count)s,
                            favorite_count = %(favorite_count)s,
                            status = %(status)s,
                            site_id = %(site_id)s,
                            update_time = %(update_time)s,
                            update_by = %(update_by)s,
                            last_sync_time = %(last_sync_time)s
                        WHERE id = %(id)s
                        """,
                        data,
                    )
                else:
                    # 插入资讯
                    cursor.execute(
                        """
                        INSERT INTO information (
                            id, title, details, pic_video, small_img, images, other_url, video_url,
                            category_id, product_id, belong_id, is_hot, is_suggest, show_type, top,
                            watch, like_count, favorite_count, status, site_id,
                            create_time, update_time, create_by, update_by, last_sync_time
                        ) VALUES (
                            %(id)s, %(title)s, %(details)s, %(pic_video)s, %(small_img)s, %(images)s, %(other_url)s, %(video_url)s,
                            %(category_id)s, %(product_id)s, %(belong_id)s, %(is_hot)s, %(is_suggest)s, %(show_type)s, %(top)s,
                            %(watch)s, %(like_count)s, %(favorite_count)s, %(status)s, %(site_id)s,
                            %(create_time)s, %(update_time)s, %(create_by)s, %(update_by)s, %(last_sync_time)s
                        )
                        """,
                        data,
                    )

                conn.commit()

        self._execute_with_connection(_save_information_operation, info)

    def _save_distribution_order(self, order: Dict[str, Any]) -> None:
        """
        保存配单数据到数据库

        Args:
            order: 配单数据
        """

        def _save_order_operation(conn, order):
            with conn.cursor() as cursor:
                # 处理可能的None值
                order_id = order.get("id")
                if not order_id:
                    logger.warning(f"配单ID为空，跳过保存")
                    return

                # 检查配单是否已存在
                cursor.execute(
                    "SELECT id FROM distribution_orders WHERE id = %s",
                    (order_id,),
                )
                exists = cursor.fetchone()

                # 准备数据
                data = {
                    "id": order_id,
                    "name": order.get("name", ""),
                    "order_no": order.get("orderNo", ""),
                    "type": order.get("type", ""),
                    "status": order.get("status", "0"),
                    "customer_name": order.get("customerName", ""),
                    "phone": order.get("phone", ""),
                    "contacts": order.get("contacts", ""),
                    "company_id": order.get("companyId"),
                    "company_name": order.get("companyName", ""),
                    "user_id": order.get("userId"),
                    "product_list": order.get("list", order.get("productList", "")),
                    "del_list": order.get("delList", ""),
                    "hard_list": order.get("hardList", ""),
                    "soft_list": order.get("softList", ""),
                    "custom_list": order.get("customList", ""),
                    "user_list": order.get("userList", ""),
                    "from_type": order.get("fromType", ""),
                    "source_type": order.get("sourceType", 0),
                    "from_user_id": order.get("fromUserId", -1),
                    "from_user_name": order.get("fromUserName", ""),
                    "platform_order_id": order.get("platformOrderId", ""),
                    "price": order.get("price", 0),
                    "hide_price": order.get("hidePrice", 0),
                    "fir_category_id": self._parse_category_id(
                        order.get("firCategoryId")
                    ),
                    "fir_category_name": order.get("firCategoryName", ""),
                    "sec_category_id": self._parse_category_id(
                        order.get("secCategoryId")
                    ),
                    "sec_category_name": order.get("secCategoryName", ""),
                    "category_scene": order.get("categoryScene", ""),
                    "category_scene_name": order.get("categorySceneName", ""),
                    "like_count": order.get("likeCount", 0),
                    "favorite_count": order.get("favoriteCount", 0),
                    "other": order.get("other", ""),
                    "details": order.get("details", ""),
                    "has_expire": order.get("hasExpire", False),
                    "site_id": order.get("siteId", 999),
                    "create_time": order.get("createTime"),
                    "update_time": order.get("updateTime"),
                    "create_by": order.get("createBy", ""),
                    "update_by": order.get("updateBy", ""),
                    "last_sync_time": datetime.datetime.now(),
                }

                if exists:
                    # 更新配单
                    cursor.execute(
                        """
                        UPDATE distribution_orders SET
                            name = %(name)s,
                            type = %(type)s,
                            status = %(status)s,
                            customer_name = %(customer_name)s,
                            phone = %(phone)s,
                            contacts = %(contacts)s,
                            company_id = %(company_id)s,
                            company_name = %(company_name)s,
                            user_id = %(user_id)s,
                            hide_price = %(hide_price)s,
                            fir_category_id = %(fir_category_id)s,
                            fir_category_name = %(fir_category_name)s,
                            sec_category_id = %(sec_category_id)s,
                            sec_category_name = %(sec_category_name)s,
                            source_type = %(source_type)s,
                            from_user_id = %(from_user_id)s,
                            from_user_name = %(from_user_name)s,
                            platform_order_id = %(platform_order_id)s,
                            category_scene = %(category_scene)s,
                            like_count = %(like_count)s,
                            favorite_count = %(favorite_count)s,
                            other = %(other)s,
                            details = %(details)s,
                            has_expire = %(has_expire)s,
                            site_id = %(site_id)s,
                            update_time = %(update_time)s,
                            update_by = %(update_by)s,
                            last_sync_time = %(last_sync_time)s
                        WHERE id = %(id)s
                        """,
                        data,
                    )
                else:
                    # 插入配单
                    cursor.execute(
                        """
                        INSERT INTO distribution_orders (
                            id, status, name, type, customer_name, phone, contacts, company_id, company_name, user_id,
                            hide_price, fir_category_id, fir_category_name, sec_category_id, sec_category_name,
                            source_type, from_user_id, from_user_name, platform_order_id, category_scene,
                            like_count, favorite_count, other, details, has_expire, site_id,
                            create_time, update_time, create_by, update_by, last_sync_time
                        ) VALUES (
                            %(id)s, %(status)s, %(name)s, %(type)s, %(customer_name)s, %(phone)s, %(contacts)s, %(company_id)s, %(company_name)s, %(user_id)s,
                            %(hide_price)s, %(fir_category_id)s, %(fir_category_name)s, %(sec_category_id)s, %(sec_category_name)s,
                            %(source_type)s, %(from_user_id)s, %(from_user_name)s, %(platform_order_id)s, %(category_scene)s,
                            %(like_count)s, %(favorite_count)s, %(other)s, %(details)s, %(has_expire)s, %(site_id)s,
                            %(create_time)s, %(update_time)s, %(create_by)s, %(update_by)s, %(last_sync_time)s
                        )
                        """,
                        data,
                    )

                conn.commit()

        self._execute_with_connection(_save_order_operation, order)

    def _save_category(self, category: Dict[str, Any]) -> None:
        """
        保存分类数据到数据库

        Args:
            category: 分类数据
        """

        def _save_category_operation(conn, category):
            with conn.cursor() as cursor:
                # 使用INSERT ... ON CONFLICT DO UPDATE语法实现upsert
                sql = """
                INSERT INTO categories (
                    id, name, source, type, level, parent_id, 
                    create_time, update_time
                ) VALUES (
                    %(id)s, %(name)s, %(source)s, %(type)s, %(level)s, %(parent_id)s,
                    NOW(), NOW()
                )
                ON CONFLICT (id) DO UPDATE SET
                    name = EXCLUDED.name,
                    source = EXCLUDED.source,
                    type = EXCLUDED.type,
                    level = EXCLUDED.level,
                    parent_id = EXCLUDED.parent_id,
                    update_time = NOW()
                """

                cursor.execute(
                    sql,
                    {
                        "id": category.get("id"),
                        "name": category.get("name"),
                        "source": category.get("source"),
                        "type": category.get("type"),
                        "level": category.get("level"),
                        "parent_id": category.get("parent_id"),
                    },
                )

                conn.commit()
                logger.debug(f"保存分类成功: {category.get('name')}")

        self._execute_with_connection(_save_category_operation, category)

    def _save_label(self, label: Dict[str, Any]) -> None:
        """
        保存标签数据到数据库

        Args:
            label: 标签数据
        """

        def _save_label_operation(conn, label):
            with conn.cursor() as cursor:
                # 使用INSERT ... ON CONFLICT DO UPDATE语法实现upsert
                sql = """
                INSERT INTO labels (
                    id, name, status, created_at, updated_at
                ) VALUES (
                    %(id)s, %(name)s, %(status)s, NOW(), NOW()
                )
                ON CONFLICT (id) DO UPDATE SET
                    name = EXCLUDED.name,
                    status = EXCLUDED.status,
                    updated_at = NOW()
                """

                cursor.execute(
                    sql,
                    {
                        "id": label.get("id"),
                        "name": label.get("name"),
                        "status": label.get("status", "0"),
                    },
                )

                conn.commit()
                logger.debug(f"保存标签成功: {label.get('name')}")

        self._execute_with_connection(_save_label_operation, label)

    @sync_error_handler("同步品牌数据")
    def sync_brands(self) -> Dict[str, Any]:
        """
        同步品牌数据

        Returns:
            Dict[str, Any]: 同步结果
        """
        entity_type = "brands"
        try:
            logger.info("开始同步品牌数据")
            self._update_sync_status(
                entity_type, "running", 0, 0, 0, "正在同步品牌数据"
            )

            # 获取API数据
            api_data = self.api_client.get_brands()

            if not api_data:
                logger.warning("未获取到品牌数据")
                self._update_sync_status(
                    entity_type, "completed", 0, 0, 0, "未获取到品牌数据"
                )
                return {
                    "status": "success",
                    "total": 0,
                    "success": 0,
                    "error": 0,
                    "message": "未获取到品牌数据",
                }

            total_count = len(api_data)
            success_count = 0
            error_count = 0

            for brand in api_data:
                try:
                    # 数据清洗和验证
                    brand_data = self._clean_brand_data(brand)

                    # 数据库操作
                    self._upsert_brand(brand_data)
                    success_count += 1

                except Exception as e:
                    # 记录详细的异常信息
                    error_id = log_sync_exception(
                        e,
                        "处理品牌数据失败",
                        {
                            "brand_id": brand.get("id", "unknown"),
                            "brand_name": brand.get("name", "unknown"),
                            "brand_data": (
                                str(brand)[:300] + "..."
                                if len(str(brand)) > 300
                                else str(brand)
                            ),
                        },
                    )
                    logger.error(f"[{error_id}] 处理品牌数据失败")
                    error_count += 1

            # 更新同步状态
            status = "completed" if error_count == 0 else "partial"
            message = f"成功同步 {success_count} 条，失败 {error_count} 条"

            self._update_sync_status(
                entity_type, status, total_count, success_count, error_count, message
            )

            logger.info(f"品牌同步完成: {message}")
            return {
                "status": "success",
                "total": total_count,
                "success": success_count,
                "error": error_count,
                "message": message,
            }

        except Exception as e:
            # 记录详细的异常信息
            error_id = log_sync_exception(
                e,
                "同步品牌数据异常",
                {
                    "entity_type": entity_type,
                    "api_data_length": (
                        len(api_data) if "api_data" in locals() else "unknown"
                    ),
                    "method": "sync_brands",
                },
            )
            logger.error(f"[{error_id}] 同步品牌数据异常")
            self._update_sync_status(
                entity_type, "failed", 0, 0, 1, f"同步失败: {error_id}"
            )
            return {
                "status": "error",
                "message": f"同步品牌数据异常: {error_id}",
                "error_id": error_id,
            }

    def _clean_brand_data(self, brand: Dict[str, Any]) -> Dict[str, Any]:
        """
        清洗品牌数据

        Args:
            brand: 原始品牌数据

        Returns:
            Dict[str, Any]: 清洗后的数据
        """
        return {
            "id": brand.get("id"),
            "name": brand.get("name", "").strip(),
            "logo": brand.get("logoUrl", "").strip(),
            "description": brand.get("description", "").strip(),
            "status": brand.get("status", "0"),
            "site_id": brand.get("siteId"),
            "create_time": self._parse_time(brand.get("createdAt")),
            "update_time": self._parse_time(brand.get("updatedAt")),
            "created_by": brand.get("createdBy"),
            "updated_by": brand.get("updatedBy"),
        }

    def _upsert_brand(self, brand_data: Dict[str, Any]) -> None:
        """
        插入或更新品牌数据

        Args:
            brand_data: 品牌数据
        """
        try:
            with self.db.get_connection() as conn:
                cursor = conn.cursor()

                # 检查品牌是否已存在
                cursor.execute(
                    "SELECT id FROM brands WHERE id = %s", (brand_data["id"],)
                )
                exists = cursor.fetchone()

                if exists:
                    # 更新现有记录
                    sql = """
                        UPDATE brands SET 
                            name = %s, logo = %s, description = %s, status = %s,
                            site_id = %s, update_time = %s, updated_by = %s
                        WHERE id = %s
                    """
                    cursor.execute(
                        sql,
                        (
                            brand_data["name"],
                            brand_data["logo"],
                            brand_data["description"],
                            brand_data["status"],
                            brand_data["site_id"],
                            brand_data["update_time"],
                            brand_data["updated_by"],
                            brand_data["id"],
                        ),
                    )
                else:
                    # 插入新记录
                    sql = """
                        INSERT INTO brands 
                        (id, name, logo, description, status, site_id, create_time, update_time, created_by, updated_by)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """
                    cursor.execute(
                        sql,
                        (
                            brand_data["id"],
                            brand_data["name"],
                            brand_data["logo"],
                            brand_data["description"],
                            brand_data["status"],
                            brand_data["site_id"],
                            brand_data["create_time"],
                            brand_data["update_time"],
                            brand_data["created_by"],
                            brand_data["updated_by"],
                        ),
                    )

                conn.commit()

        except Exception as e:
            logger.error(f"数据库操作失败: {e}")
            raise

    def _parse_category_id(self, category_value: Any) -> Optional[int]:
        """
        解析分类ID，处理逗号分隔的多个分类ID，取第一个

        Args:
            category_value: 分类值，可能是整数、字符串或逗号分隔的多个ID

        Returns:
            int: 解析后的分类ID，如果为空或无效则返回None
        """
        if not category_value:
            return None

        try:
            # 如果已经是整数，直接返回
            if isinstance(category_value, int):
                return category_value

            # 如果是字符串，处理逗号分隔的情况
            if isinstance(category_value, str):
                # 移除空格并分割
                category_str = str(category_value).strip()
                if "," in category_str:
                    # 如果是逗号分隔的多个ID，取第一个有效的
                    first_category = category_str.split(",")[0].strip()
                    if first_category:
                        return int(first_category)
                else:
                    # 单个ID
                    if category_str:
                        return int(category_str)

            # 其他类型尝试直接转换
            return int(category_value)

        except (ValueError, AttributeError, TypeError):
            logger.warning(
                f"无法解析分类ID: {category_value} (类型: {type(category_value)})"
            )

        return None

    # 删除多余的同步方法，只保留4个核心模块的同步功能


if __name__ == "__main__":
    # 执行产品同步测试
    logging.basicConfig(level=logging.INFO)
    sync_service = SyncService()
    try:
        print("开始同步产品数据...")
        result = sync_service.sync_products()
        print(f"同步结果: {result}")
    finally:
        if sync_service.conn:
            sync_service.conn.close()
