#!/usr/bin/env python3
"""
附件和图片处理验证脚本

检查附件下载和本地存储功能，确保图片和文档正确保存到项目路径并在数据库中记录路径
"""

import sys
import os
import json
import logging
import requests
from pathlib import Path
from typing import Dict, Any, List, Optional
from urllib.parse import urlparse, unquote

# 添加项目路径
sys.path.append('.')

# 设置环境变量
os.environ['DATABASE_HOST'] = '***********'
os.environ['DATABASE_PORT'] = '5432'
os.environ['DATABASE_NAME'] = 'product'
os.environ['DATABASE_USER'] = 'username'
os.environ['DATABASE_PASSWORD'] = '123456'
os.environ['ZKMALL_API_BASE'] = 'https://zkmall.zktecoiot.com'
os.environ['ZKMALL_USERNAME'] = '18929343717'
os.environ['ZKMALL_PASSWORD'] = 'Zk@123456'

from utils.auth import AuthManager
from utils.api_client import ZKMallClient
from utils.database import DatabaseManager

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class AttachmentProcessingVerifier:
    """附件处理验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.client = None
        self.db_manager = None
        self.verification_results = {
            'api_attachment_analysis': {},
            'download_functionality': {},
            'storage_verification': {},
            'database_records': {},
            'issues_found': [],
            'recommendations': []
        }
        
        # 创建测试目录
        self.test_download_dir = Path("test_attachments")
        self.test_download_dir.mkdir(exist_ok=True)
    
    def verify_attachment_processing(self) -> Dict[str, Any]:
        """验证附件处理功能"""
        print("🔍 开始附件和图片处理验证...")
        print("=" * 60)
        
        try:
            # 1. 确保API认证
            if not AuthManager.ensure_authenticated():
                print("❌ API认证失败")
                return self.verification_results
            
            print("✅ API认证成功")
            self.client = ZKMallClient()
            self.db_manager = DatabaseManager()
            
            # 2. 分析API中的附件数据
            self._analyze_api_attachments()
            
            # 3. 测试下载功能
            self._test_download_functionality()
            
            # 4. 验证存储结构
            self._verify_storage_structure()
            
            # 5. 检查数据库记录
            self._check_database_records()
            
            # 6. 生成问题和建议
            self._generate_issues_and_recommendations()
            
            # 7. 输出验证报告
            self._print_verification_report()
            
            return self.verification_results
            
        except Exception as e:
            logger.error(f"验证过程出错: {e}")
            import traceback
            traceback.print_exc()
            return self.verification_results
    
    def _analyze_api_attachments(self):
        """分析API中的附件数据"""
        print("\n1. 📊 API附件数据分析")
        print("-" * 40)
        
        try:
            # 获取产品数据样本
            products = self.client.get_products(pageSize=3, current=1)
            if not products:
                print("❌ 未能获取产品数据")
                return
            
            print(f"✅ 获取到 {len(products)} 个产品样本")
            
            attachment_analysis = {
                'total_products': len(products),
                'products_with_attachments': 0,
                'attachment_types': {},
                'url_patterns': {},
                'file_extensions': {},
                'samples': []
            }
            
            for product in products:
                product_attachments = []
                
                # 检查各种附件字段
                attachment_fields = {
                    'other': '其他附件',
                    'smallImg': '缩略图',
                    'banner': '横幅图片',
                    'qualifications': '资质文件',
                    'videoExplanation': '说明视频',
                    'videoInstallation': '安装视频',
                    'videoTroubleshooting': '故障排除视频'
                }
                
                has_attachments = False
                
                for field, field_name in attachment_fields.items():
                    if field in product and product[field]:
                        has_attachments = True
                        urls = str(product[field]).split(',') if ',' in str(product[field]) else [str(product[field])]
                        
                        for url in urls:
                            url = url.strip()
                            if url:
                                # 分析URL模式
                                parsed_url = urlparse(url)
                                domain = parsed_url.netloc
                                
                                if domain not in attachment_analysis['url_patterns']:
                                    attachment_analysis['url_patterns'][domain] = 0
                                attachment_analysis['url_patterns'][domain] += 1
                                
                                # 分析文件扩展名
                                file_path = unquote(parsed_url.path)
                                if '.' in file_path:
                                    ext = file_path.split('.')[-1].lower()
                                    if ext not in attachment_analysis['file_extensions']:
                                        attachment_analysis['file_extensions'][ext] = 0
                                    attachment_analysis['file_extensions'][ext] += 1
                                
                                # 记录附件类型
                                if field_name not in attachment_analysis['attachment_types']:
                                    attachment_analysis['attachment_types'][field_name] = 0
                                attachment_analysis['attachment_types'][field_name] += 1
                                
                                product_attachments.append({
                                    'type': field_name,
                                    'field': field,
                                    'url': url,
                                    'domain': domain,
                                    'extension': ext if '.' in file_path else 'unknown'
                                })
                
                if has_attachments:
                    attachment_analysis['products_with_attachments'] += 1
                    attachment_analysis['samples'].append({
                        'product_id': product.get('id'),
                        'product_name': product.get('name'),
                        'attachments': product_attachments
                    })
            
            # 输出分析结果
            print(f"   产品总数: {attachment_analysis['total_products']}")
            print(f"   有附件的产品: {attachment_analysis['products_with_attachments']}")
            print(f"   附件覆盖率: {(attachment_analysis['products_with_attachments']/attachment_analysis['total_products']*100):.1f}%")
            
            print("\n   附件类型分布:")
            for att_type, count in attachment_analysis['attachment_types'].items():
                print(f"     {att_type}: {count} 个")
            
            print("\n   URL域名分布:")
            for domain, count in attachment_analysis['url_patterns'].items():
                print(f"     {domain}: {count} 个")
            
            print("\n   文件扩展名分布:")
            for ext, count in attachment_analysis['file_extensions'].items():
                print(f"     .{ext}: {count} 个")
            
            self.verification_results['api_attachment_analysis'] = attachment_analysis
            
        except Exception as e:
            print(f"❌ API附件数据分析失败: {e}")
            self.verification_results['api_attachment_analysis']['error'] = str(e)


def main():
    """主函数"""
    verifier = AttachmentProcessingVerifier()
    results = verifier.verify_attachment_processing()
    
    # 保存验证结果
    with open('attachment_verification_report.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2, default=str)
    
    print(f"\n📊 验证报告已保存到: attachment_verification_report.json")


if __name__ == "__main__":
    main()
