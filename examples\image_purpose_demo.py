#!/usr/bin/env python3
"""
云商系统图片用途功能演示脚本
展示如何使用图片用途分类系统处理API数据
"""

import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.data_processing import DataProcessor
from utils.image_processor import ImageProcessor, ImagePurposeManager
import json
from datetime import datetime


def demo_purpose_mapping():
    """演示图片用途映射功能"""
    print("🏷️ 图片用途映射演示")
    print("=" * 50)

    # 创建数据处理器
    processor = DataProcessor()

    # 模拟API数据
    api_data = [
        {
            "id": "prod_001",
            "name": "指纹识别考勤机",
            "smallImg": "https://example.com/products/fingerprint_small.jpg",
            "banner": "https://example.com/products/banner1.jpg,https://example.com/products/banner2.jpg",
            "sizeImg": "https://example.com/products/size_diagram.png",
            "category": "考勤设备",
        },
        {
            "id": "case_001",
            "name": "办公楼考勤系统案例",
            "img": "https://example.com/cases/office_main.jpg",
            "banner": "https://example.com/cases/office_banner.jpg",
            "smallImg": "https://example.com/cases/office_thumb.jpg",
        },
    ]

    print("📥 原始API数据:")
    print(json.dumps(api_data[0], indent=2, ensure_ascii=False))
    print()

    # 处理产品数据
    print("🔄 处理产品数据...")
    processed_products = processor.process_api_response([api_data[0]], "product")

    print("📤 处理后的产品数据（包含用途信息）:")
    product = processed_products[0]

    # 显示新增的用途字段
    purpose_fields = [k for k in product.keys() if k.endswith("_purpose")]
    for field in purpose_fields:
        print(f"  {field}: {product[field]}")

    print()

    # 显示图片信息字段
    info_fields = [k for k in product.keys() if k.endswith("_info")]
    for field in info_fields:
        if product[field]:
            print(f"  {field}:")
            print(f"    用途: {product[field].get('purpose', 'N/A')}")
            print(f"    分类: {product[field].get('category', 'N/A')}")
            print(f"    本地路径: {product[field].get('local_path', 'N/A')}")

    print()


def demo_purpose_categories():
    """演示图片用途分类系统"""
    print("📂 图片用途分类系统演示")
    print("=" * 50)

    # 显示所有用途分类
    print("🗂️ 用途分类体系:")
    for category, purposes in ImagePurposeManager.PURPOSE_CATEGORIES.items():
        print(f"  {category}:")
        for purpose in purposes:
            print(f"    - {purpose}")

    print()

    # 演示用途验证
    print("✅ 用途验证演示:")
    test_purposes = ["缩略图", "轮播图", "无效用途", "尺寸图"]

    for purpose in test_purposes:
        is_valid = ImagePurposeManager.validate_purpose(purpose)
        category = ImagePurposeManager.get_category_by_purpose(purpose)
        print(f"  '{purpose}' -> 有效: {is_valid}, 分类: {category}")

    print()


def demo_storage_config():
    """演示存储配置功能"""
    print("⚙️ 存储配置演示")
    print("=" * 50)

    # 显示不同用途的存储配置
    print("📋 各用途存储配置:")

    purposes_to_show = ["缩略图", "轮播图", "尺寸图", "视频封面"]

    for purpose in purposes_to_show:
        config = ImagePurposeManager.PURPOSE_STORAGE_CONFIG.get(purpose, {})
        if config:
            print(f"  {purpose}:")
            print(f"    质量: {config.get('quality', 'N/A')}")
            print(f"    最大尺寸: {config.get('max_size', 'N/A')}")
            print(f"    格式: {config.get('format', 'N/A')}")

    print()


def demo_directory_structure():
    """演示目录结构创建"""
    print("📁 目录结构演示")
    print("=" * 50)

    # 创建图片处理器（会自动创建目录结构）
    processor = ImageProcessor("demo_images")

    print("🏗️ 已创建的目录结构:")
    print("demo_images/")

    modules = ["products", "cases", "programmes", "information"]
    categories = ["展示", "技术", "媒体", "文档", "其他"]

    for module in modules:
        print(f"├── {module}/")
        print(f"│   ├── original/")
        for category in categories:
            print(f"│   │   ├── {category}/")
        print(f"│   └── thumbnails/")
        for category in categories:
            print(f"│       ├── {category}/")

    print()


def demo_field_mapping():
    """演示字段到用途的映射"""
    print("🔗 字段用途映射演示")
    print("=" * 50)

    # 创建数据处理器
    processor = DataProcessor()

    # 显示各模块的字段映射
    modules = ["product", "case", "programme", "information"]

    for module in modules:
        print(f"📦 {module.upper()} 模块:")
        field_mapping = processor._get_image_fields_by_type(module)

        if isinstance(field_mapping, dict):
            for field, purpose in field_mapping.items():
                category = ImagePurposeManager.get_category_by_purpose(purpose)
                print(f"  {field} -> {purpose} ({category})")
        else:
            print(f"  字段列表: {field_mapping}")

        print()


def demo_usage_example():
    """演示实际使用场景"""
    print("💡 实际使用场景演示")
    print("=" * 50)

    # 模拟从API获取的数据
    api_response = {
        "code": 200,
        "message": "success",
        "data": [
            {
                "id": "P001",
                "name": "智能门禁系统",
                "smallImg": "https://cdn.example.com/products/door_small.jpg",
                "banner": "https://cdn.example.com/products/door_banner1.jpg,https://cdn.example.com/products/door_banner2.jpg",
                "sizeImg": "https://cdn.example.com/products/door_size.png",
                "description": "高安全性智能门禁系统",
                "price": 2999.00,
            }
        ],
        "total": 1,
    }

    print("📡 模拟API响应:")
    print(json.dumps(api_response, indent=2, ensure_ascii=False))
    print()

    # 处理API响应
    processor = DataProcessor()
    processed_response = processor.process_api_response(api_response, "product")

    print("🔄 处理后的响应（包含图片用途信息）:")

    if "data" in processed_response and processed_response["data"]:
        product = processed_response["data"][0]

        # 显示原有字段
        print("  原有字段:")
        for key, value in product.items():
            if not key.endswith(("_local", "_purpose", "_info")):
                print(f"    {key}: {value}")

        print()

        # 显示新增的用途相关字段
        print("  新增的图片用途字段:")
        for key, value in product.items():
            if key.endswith("_purpose"):
                print(f"    {key}: {value}")

        print()

        # 显示本地路径字段
        print("  本地存储路径:")
        for key, value in product.items():
            if key.endswith("_local") and value:
                print(f"    {key}: {value}")

    print()


def main():
    """主函数"""
    print("🎯 云商系统图片用途功能演示")
    print("=" * 60)
    print(f"⏰ 演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # 运行各个演示
    demo_purpose_categories()
    demo_storage_config()
    demo_field_mapping()
    demo_directory_structure()
    demo_purpose_mapping()
    demo_usage_example()

    print("✅ 演示完成！")
    print()
    print("📝 说明:")
    print("  - 图片用途系统自动识别字段类型并分配合适的用途")
    print("  - 根据用途优化图片存储策略（质量、尺寸、格式）")
    print("  - 按用途分类创建目录结构，便于管理")
    print("  - 在数据中添加用途标识，便于前端展示")
    print("  - 支持自定义用途和存储配置")


if __name__ == "__main__":
    main()
