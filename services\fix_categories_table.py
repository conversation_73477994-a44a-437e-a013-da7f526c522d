#!/usr/bin/env python3
"""
修复categories表结构脚本
添加缺失的source和type字段
"""

import os
import sys
import logging
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.database import get_db_connection, return_db_connection

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class CategoriesTableFixer:
    """Categories表结构修复器"""

    def __init__(self):
        self.conn = None

    def _execute_with_connection(self, operation_func, *args, **kwargs):
        """
        使用连接池执行数据库操作的通用方法

        Args:
            operation_func: 要执行的操作函数
            *args: 传递给操作函数的参数
            **kwargs: 传递给操作函数的关键字参数

        Returns:
            操作函数的返回值
        """
        conn = None
        try:
            conn = get_db_connection()
            if not conn:
                raise Exception("无法从连接池获取数据库连接")

            logger.debug("从连接池获取数据库连接成功")
            result = operation_func(conn, *args, **kwargs)
            conn.commit()
            logger.debug("数据库操作成功提交")
            return result

        except Exception as e:
            if conn:
                conn.rollback()
                logger.error(f"数据库操作失败，已回滚: {e}")
            raise
        finally:
            if conn:
                return_db_connection(conn)
                logger.debug("数据库连接已归还到连接池")

    def check_column_exists(self, table_name: str, column_name: str) -> bool:
        """检查字段是否存在"""

        def _check_column_operation(conn, table_name, column_name):
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT EXISTS (
                        SELECT FROM information_schema.columns 
                        WHERE table_schema = 'public' 
                        AND table_name = %s 
                        AND column_name = %s
                    )
                """,
                    (table_name, column_name),
                )
                result = cursor.fetchone()
                return result[0] if result else False

        try:
            return self._execute_with_connection(
                _check_column_operation, table_name, column_name
            )
        except Exception as e:
            logger.error(f"检查字段失败: {e}")
            return False

    def add_missing_columns(self) -> bool:
        """添加缺失的字段"""

        def _add_columns_operation(conn):
            columns_to_add = [
                {
                    "name": "source",
                    "type": "VARCHAR(100)",
                    "default": "'api'",
                    "comment": "数据来源",
                },
                {
                    "name": "type",
                    "type": "VARCHAR(50)",
                    "default": "'normal'",
                    "comment": "分类类型",
                },
            ]

            with conn.cursor() as cursor:
                for column in columns_to_add:
                    # 检查字段是否已存在
                    if self.check_column_exists("categories", column["name"]):
                        logger.info(f"字段 {column['name']} 已存在，跳过")
                        continue

                    # 添加字段
                    sql = f"""
                    ALTER TABLE categories 
                    ADD COLUMN {column['name']} {column['type']} DEFAULT {column['default']}
                    """

                    cursor.execute(sql)
                    logger.info(f"成功添加字段: {column['name']} {column['type']}")

                    # 添加字段注释
                    comment_sql = f"""
                    COMMENT ON COLUMN categories.{column['name']} IS '{column['comment']}'
                    """
                    cursor.execute(comment_sql)
                    logger.info(f"添加字段注释: {column['name']} - {column['comment']}")

            logger.info("所有缺失字段添加完成")
            return True

        try:
            return self._execute_with_connection(_add_columns_operation)
        except Exception as e:
            logger.error(f"添加字段失败: {e}")
            return False

    def verify_table_structure(self) -> bool:
        """验证表结构"""

        def _verify_structure_operation(conn):
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT column_name, data_type, column_default, is_nullable
                    FROM information_schema.columns 
                    WHERE table_name = 'categories' 
                    ORDER BY ordinal_position
                """
                )

                columns = cursor.fetchall()
                logger.info("Categories表当前完整结构:")
                for col in columns:
                    nullable = "NULL" if col[3] == "YES" else "NOT NULL"
                    default = f"DEFAULT {col[2]}" if col[2] else ""
                    logger.info(f"  {col[0]}: {col[1]} {nullable} {default}")

                # 检查必需字段
                required_fields = ["id", "name", "source", "type", "level", "parent_id"]
                existing_fields = [col[0] for col in columns]

                missing_fields = set(required_fields) - set(existing_fields)
                if missing_fields:
                    logger.error(f"仍然缺少字段: {missing_fields}")
                    return False

                logger.info("表结构验证通过，所有必需字段都存在")
                return True

        try:
            return self._execute_with_connection(_verify_structure_operation)
        except Exception as e:
            logger.error(f"验证表结构失败: {e}")
            return False

    def test_insert_operation(self) -> bool:
        """测试插入操作"""

        def _test_insert_operation(conn):
            with conn.cursor() as cursor:
                # 测试INSERT语句（不实际插入数据）
                test_sql = """
                SELECT 1 FROM (
                    SELECT id, name, source, type, level, parent_id, 
                           create_time, update_time
                    FROM categories LIMIT 0
                ) AS test_query
                """
                cursor.execute(test_sql)
                logger.info("INSERT操作测试通过，字段映射正确")
                return True

        try:
            return self._execute_with_connection(_test_insert_operation)
        except Exception as e:
            logger.error(f"INSERT操作测试失败: {e}")
            return False


def main():
    """主函数"""
    logger.info("开始修复Categories表结构")

    fixer = CategoriesTableFixer()

    try:
        # 添加缺失字段
        if not fixer.add_missing_columns():
            logger.error("添加字段失败，退出")
            return False

        # 验证表结构
        if not fixer.verify_table_structure():
            logger.error("表结构验证失败，退出")
            return False

        # 测试插入操作
        if not fixer.test_insert_operation():
            logger.error("INSERT操作测试失败，退出")
            return False

        logger.info("Categories表结构修复完成！")
        return True

    except Exception as e:
        logger.error(f"修复过程中发生错误: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
