#!/usr/bin/env python3
"""
数据库基础操作类
提供数据库迁移和基础操作的基类
"""

import logging
import functools
import asyncio
import signal
import threading
from typing import Any, Dict, List, Optional, Callable, TypeVar, Union
from contextlib import contextmanager
import psycopg2
from psycopg2.extras import RealDictCursor
from utils.database import (
    get_db_connection_context,
    get_db_connection,
    return_db_connection,
)
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)

T = TypeVar("T")


class DatabaseConnectionError(Exception):
    """数据库连接错误"""

    pass


class DatabaseOperationError(Exception):
    """数据库操作错误"""

    pass


class DatabaseTimeoutError(Exception):
    """数据库操作超时错误"""

    pass


def timeout_handler(signum, frame):
    """超时信号处理器"""
    raise DatabaseTimeoutError("数据库操作超时")


def with_timeout(timeout_seconds: int = 30):
    """
    数据库操作超时装饰器

    Args:
        timeout_seconds: 超时时间（秒）
    """

    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        @functools.wraps(func)
        def wrapper(*args, **kwargs) -> T:
            import threading
            import time

            result = [None]
            exception = [None]
            completed = [False]

            def target():
                try:
                    result[0] = func(*args, **kwargs)
                    completed[0] = True
                except Exception as e:
                    exception[0] = e
                    completed[0] = True

            # 启动执行线程
            thread = threading.Thread(target=target)
            thread.daemon = True
            thread.start()

            # 等待完成或超时
            thread.join(timeout=timeout_seconds)

            if not completed[0]:
                logger.error(
                    f"数据库操作超时: {func.__name__} (超时时间: {timeout_seconds}秒)"
                )
                raise DatabaseTimeoutError(f"数据库操作超时: {timeout_seconds}秒")

            if exception[0]:
                raise exception[0]

            return result[0]

        return wrapper

    return decorator


def require_db_connection(func: Callable[..., T]) -> Callable[..., T]:
    """
    装饰器：确保方法执行时有数据库连接
    自动处理连接的获取和归还
    """

    @functools.wraps(func)
    def wrapper(self, *args, **kwargs) -> T:
        if not hasattr(self, "_ensure_connection"):
            raise AttributeError("类必须继承自 DatabaseOperationMixin")

        # 确保有连接
        if not self._ensure_connection():
            raise DatabaseConnectionError("无法获取数据库连接")

        try:
            return func(self, *args, **kwargs)
        except Exception as e:
            logger.error(f"数据库操作失败 {func.__name__}: {e}")
            raise DatabaseOperationError(f"数据库操作失败: {e}") from e
        finally:
            # 方法执行完毕后归还连接
            self._return_connection()

    return wrapper


class DatabaseOperationMixin:
    """
    数据库操作混入类

    提供统一的数据库连接管理和操作方法
    所有需要数据库操作的类都应该继承此混入类
    """

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._conn = None
        self._connection_count = 0  # 连接获取计数

    def _ensure_connection(self) -> bool:
        """
        确保有可用的数据库连接

        Returns:
            bool: 是否成功获取连接
        """
        if self._conn is not None:
            try:
                # 测试连接是否有效
                with self._conn.cursor() as cursor:
                    cursor.execute("SELECT 1")
                return True
            except Exception:
                # 连接无效，需要重新获取
                self._return_connection()

        try:
            self._conn = get_db_connection()
            self._connection_count += 1
            logger.debug(f"获取数据库连接成功 (第{self._connection_count}次)")
            return True
        except Exception as e:
            logger.error(f"获取数据库连接失败: {e}")
            return False

    def _return_connection(self):
        """归还数据库连接到连接池"""
        if self._conn is not None:
            try:
                return_db_connection(self._conn)
                logger.debug("数据库连接已归还到连接池")
            except Exception as e:
                logger.error(f"归还数据库连接失败: {e}")
            finally:
                self._conn = None

    @contextmanager
    def get_db_cursor(self, cursor_factory=RealDictCursor):
        """
        获取数据库游标的上下文管理器

        Args:
            cursor_factory: 游标工厂类

        Yields:
            数据库游标
        """
        # 使用新的连接上下文管理器，确保连接正确管理
        with get_db_connection_context() as conn:
            cursor = None
            try:
                cursor = conn.cursor(cursor_factory=cursor_factory)
                yield cursor
            except Exception as e:
                conn.rollback()
                logger.error(f"数据库操作失败: {e}")
                raise
            else:
                conn.commit()
            finally:
                if cursor:
                    cursor.close()

    @contextmanager
    def get_db_transaction(self):
        """
        获取数据库事务的上下文管理器

        Yields:
            数据库连接对象
        """
        # 使用新的连接上下文管理器
        with get_db_connection_context() as conn:
            try:
                # 开始事务
                yield conn
                # 提交事务
                conn.commit()
            except Exception as e:
                # 回滚事务
                conn.rollback()
                logger.error(f"数据库事务失败: {e}")
                raise

    def execute_query(
        self,
        query: str,
        params: Optional[tuple] = None,
        fetch_one: bool = False,
        fetch_all: bool = True,
        cursor_factory=RealDictCursor,
    ) -> Union[List[Dict], Dict, None]:
        """
        执行查询语句

        Args:
            query: SQL查询语句
            params: 查询参数
            fetch_one: 是否只获取一条记录
            fetch_all: 是否获取所有记录
            cursor_factory: 游标工厂

        Returns:
            查询结果
        """
        with self.get_db_cursor(cursor_factory=cursor_factory) as cursor:
            cursor.execute(query, params)

            if fetch_one:
                result = cursor.fetchone()
                return dict(result) if result else None
            elif fetch_all:
                results = cursor.fetchall()
                return [dict(row) for row in results]
            else:
                return None

    def execute_query_with_timeout(
        self,
        query: str,
        params: Optional[tuple] = None,
        timeout_seconds: int = 30,
        fetch_one: bool = False,
        fetch_all: bool = True,
        cursor_factory=RealDictCursor,
    ) -> Union[List[Dict], Dict, None]:
        """
        执行带超时的查询语句

        Args:
            query: SQL查询语句
            params: 查询参数
            timeout_seconds: 超时时间（秒）
            fetch_one: 是否只获取一条记录
            fetch_all: 是否获取所有记录
            cursor_factory: 游标工厂

        Returns:
            查询结果
        """

        @with_timeout(timeout_seconds)
        def _execute_query():
            return self.execute_query(
                query, params, fetch_one, fetch_all, cursor_factory
            )

        return _execute_query()

    async def execute_query_async(
        self,
        query: str,
        params: Optional[tuple] = None,
        fetch_one: bool = False,
        fetch_all: bool = True,
        cursor_factory=RealDictCursor,
        timeout_seconds: int = 30,
    ) -> Union[List[Dict], Dict, None]:
        """
        异步执行查询语句

        Args:
            query: SQL查询语句
            params: 查询参数
            fetch_one: 是否只获取一条记录
            fetch_all: 是否获取所有记录
            cursor_factory: 游标工厂
            timeout_seconds: 超时时间（秒）

        Returns:
            查询结果
        """
        loop = asyncio.get_event_loop()

        def _sync_execute():
            return self.execute_query_with_timeout(
                query, params, timeout_seconds, fetch_one, fetch_all, cursor_factory
            )

        try:
            # 在线程池中执行同步数据库操作
            result = await asyncio.wait_for(
                loop.run_in_executor(None, _sync_execute), timeout=timeout_seconds
            )
            return result
        except asyncio.TimeoutError:
            raise DatabaseTimeoutError(f"异步查询超时: {timeout_seconds}秒")

    def execute_query_with_retry(
        self,
        query: str,
        params: Optional[tuple] = None,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        timeout_seconds: int = 30,
        fetch_one: bool = False,
        fetch_all: bool = True,
        cursor_factory=RealDictCursor,
    ) -> Union[List[Dict], Dict, None]:
        """
        执行带重试机制的查询语句

        Args:
            query: SQL查询语句
            params: 查询参数
            max_retries: 最大重试次数
            retry_delay: 重试延迟（秒）
            timeout_seconds: 每次尝试的超时时间（秒）
            fetch_one: 是否只获取一条记录
            fetch_all: 是否获取所有记录
            cursor_factory: 游标工厂

        Returns:
            查询结果
        """
        last_exception = None

        for attempt in range(max_retries + 1):
            try:
                return self.execute_query_with_timeout(
                    query, params, timeout_seconds, fetch_one, fetch_all, cursor_factory
                )
            except (DatabaseTimeoutError, DatabaseConnectionError, psycopg2.Error) as e:
                last_exception = e
                logger.warning(f"查询失败 (尝试 {attempt + 1}/{max_retries + 1}): {e}")

                if attempt < max_retries:
                    # 等待后重试
                    import time

                    time.sleep(retry_delay)
                    # 重新获取连接
                    self._return_connection()
                    if not self._ensure_connection():
                        logger.error("重试时无法获取数据库连接")
                        break
                else:
                    logger.error(f"查询最终失败，已达到最大重试次数: {max_retries}")

        # 所有重试都失败了
        raise DatabaseOperationError(
            f"查询失败，已重试 {max_retries} 次: {last_exception}"
        )

    def execute_update(
        self, query: str, params: Optional[tuple] = None, return_id: bool = False
    ) -> Union[int, None]:
        """
        执行更新语句（INSERT, UPDATE, DELETE）

        Args:
            query: SQL更新语句
            params: 更新参数
            return_id: 是否返回插入的ID

        Returns:
            影响的行数或插入的ID
        """
        with self.get_db_cursor() as cursor:
            cursor.execute(query, params)

            if return_id and query.strip().upper().startswith("INSERT"):
                # 对于INSERT语句，尝试返回ID
                if "RETURNING" in query.upper():
                    result = cursor.fetchone()
                    return result[0] if result else None
                else:
                    return cursor.lastrowid
            else:
                return cursor.rowcount

    def execute_update_with_timeout(
        self,
        query: str,
        params: Optional[tuple] = None,
        timeout_seconds: int = 30,
        return_id: bool = False,
    ) -> Union[int, None]:
        """
        执行带超时的更新语句

        Args:
            query: SQL更新语句
            params: 更新参数
            timeout_seconds: 超时时间（秒）
            return_id: 是否返回插入的ID

        Returns:
            影响的行数或插入的ID
        """

        @with_timeout(timeout_seconds)
        def _execute_update():
            return self.execute_update(query, params, return_id)

        return _execute_update()

    async def execute_update_async(
        self,
        query: str,
        params: Optional[tuple] = None,
        timeout_seconds: int = 30,
        return_id: bool = False,
    ) -> Union[int, None]:
        """
        异步执行更新语句

        Args:
            query: SQL更新语句
            params: 更新参数
            timeout_seconds: 超时时间（秒）
            return_id: 是否返回插入的ID

        Returns:
            影响的行数或插入的ID
        """
        loop = asyncio.get_event_loop()

        def _sync_execute():
            return self.execute_update_with_timeout(
                query, params, timeout_seconds, return_id
            )

        try:
            result = await asyncio.wait_for(
                loop.run_in_executor(None, _sync_execute), timeout=timeout_seconds
            )
            return result
        except asyncio.TimeoutError:
            raise DatabaseTimeoutError(f"异步更新超时: {timeout_seconds}秒")

    def execute_batch(self, query: str, params_list: List[tuple]) -> int:
        """
        批量执行语句

        Args:
            query: SQL语句
            params_list: 参数列表

        Returns:
            影响的行数
        """
        with self.get_db_cursor() as cursor:
            cursor.executemany(query, params_list)
            return cursor.rowcount

    def execute_batch_with_timeout(
        self,
        query: str,
        params_list: List[tuple],
        timeout_seconds: int = 60,
        chunk_size: int = 1000,
    ) -> int:
        """
        执行带超时的批量操作

        Args:
            query: SQL语句
            params_list: 参数列表
            timeout_seconds: 超时时间（秒）
            chunk_size: 分块大小

        Returns:
            影响的行数
        """
        if not params_list:
            return 0

        total_affected = 0

        # 分块处理以避免长时间操作
        for i in range(0, len(params_list), chunk_size):
            chunk = params_list[i : i + chunk_size]

            @with_timeout(timeout_seconds)
            def _execute_chunk():
                return self.execute_batch(query, chunk)

            try:
                affected = _execute_chunk()
                total_affected += affected
                logger.debug(f"批量操作块 {i//chunk_size + 1} 完成，影响 {affected} 行")
            except DatabaseTimeoutError:
                logger.error(f"批量操作块 {i//chunk_size + 1} 超时")
                raise

        return total_affected

    def execute_batch_insert(
        self,
        table_name: str,
        columns: List[str],
        data_list: List[tuple],
        chunk_size: int = 1000,
        on_conflict: str = None,
        return_ids: bool = False,
    ) -> Dict[str, Any]:
        """
        高性能批量插入

        Args:
            table_name: 表名
            columns: 列名列表
            data_list: 数据列表
            chunk_size: 分块大小
            on_conflict: 冲突处理策略 (DO NOTHING, DO UPDATE SET ...)
            return_ids: 是否返回插入的ID列表

        Returns:
            插入结果统计
        """
        if not data_list:
            return {"total": 0, "success": 0, "failed": 0, "ids": []}

        # 构建SQL语句
        placeholders = ",".join(["%s"] * len(columns))
        column_names = ",".join(columns)

        base_query = (
            f"INSERT INTO {table_name} ({column_names}) VALUES ({placeholders})"
        )

        if on_conflict:
            base_query += f" ON CONFLICT {on_conflict}"

        if return_ids:
            base_query += " RETURNING id"

        result = {
            "total": len(data_list),
            "success": 0,
            "failed": 0,
            "errors": [],
            "ids": [],
        }

        # 分块处理
        for i in range(0, len(data_list), chunk_size):
            chunk = data_list[i : i + chunk_size]

            try:
                with self.get_db_transaction():
                    with self._conn.cursor() as cursor:
                        if return_ids:
                            # 逐条插入以获取ID
                            for data_row in chunk:
                                cursor.execute(base_query, data_row)
                                if cursor.rowcount > 0:
                                    result["success"] += 1
                                    if return_ids:
                                        returned_id = cursor.fetchone()
                                        if returned_id:
                                            result["ids"].append(returned_id[0])
                                else:
                                    result["failed"] += 1
                        else:
                            # 批量插入
                            cursor.executemany(base_query, chunk)
                            result["success"] += cursor.rowcount

            except Exception as e:
                result["failed"] += len(chunk)
                result["errors"].append(f"Chunk {i//chunk_size + 1}: {str(e)}")
                logger.error(f"批量插入失败 (chunk {i//chunk_size + 1}): {e}")

        logger.info(
            f"批量插入完成: 总计 {result['total']}, 成功 {result['success']}, 失败 {result['failed']}"
        )
        return result

    def execute_batch_update(
        self,
        table_name: str,
        update_data: List[Dict[str, Any]],
        where_columns: List[str],
        chunk_size: int = 1000,
    ) -> Dict[str, Any]:
        """
        批量更新

        Args:
            table_name: 表名
            update_data: 更新数据列表，每个字典包含所有需要更新的列和WHERE条件列
            where_columns: WHERE条件列名列表
            chunk_size: 分块大小

        Returns:
            更新结果统计
        """
        if not update_data:
            return {"total": 0, "success": 0, "failed": 0}

        result = {"total": len(update_data), "success": 0, "failed": 0, "errors": []}

        # 获取所有需要更新的列（排除WHERE条件列）
        all_columns = set()
        for row in update_data:
            all_columns.update(row.keys())

        update_columns = list(all_columns - set(where_columns))

        if not update_columns:
            logger.warning("没有需要更新的列")
            return result

        # 构建UPDATE语句
        set_clause = ", ".join([f"{col} = %s" for col in update_columns])
        where_clause = " AND ".join([f"{col} = %s" for col in where_columns])

        query = f"UPDATE {table_name} SET {set_clause} WHERE {where_clause}"

        # 分块处理
        for i in range(0, len(update_data), chunk_size):
            chunk = update_data[i : i + chunk_size]

            try:
                with self.get_db_transaction():
                    with self._conn.cursor() as cursor:
                        for row_data in chunk:
                            # 准备参数：先是更新列的值，然后是WHERE条件列的值
                            params = []
                            params.extend([row_data.get(col) for col in update_columns])
                            params.extend([row_data.get(col) for col in where_columns])

                            cursor.execute(query, params)
                            if cursor.rowcount > 0:
                                result["success"] += 1
                            else:
                                result["failed"] += 1

            except Exception as e:
                result["failed"] += len(chunk)
                result["errors"].append(f"Chunk {i//chunk_size + 1}: {str(e)}")
                logger.error(f"批量更新失败 (chunk {i//chunk_size + 1}): {e}")

        logger.info(
            f"批量更新完成: 总计 {result['total']}, 成功 {result['success']}, 失败 {result['failed']}"
        )
        return result

    def execute_batch_upsert(
        self,
        table_name: str,
        data_list: List[Dict[str, Any]],
        conflict_columns: List[str],
        update_columns: List[str] = None,
        chunk_size: int = 1000,
    ) -> Dict[str, Any]:
        """
        批量UPSERT操作（INSERT ... ON CONFLICT DO UPDATE）

        Args:
            table_name: 表名
            data_list: 数据列表
            conflict_columns: 冲突检测列
            update_columns: 冲突时需要更新的列，None表示更新所有列（除冲突列外）
            chunk_size: 分块大小

        Returns:
            操作结果统计
        """
        if not data_list:
            return {"total": 0, "inserted": 0, "updated": 0, "failed": 0}

        result = {
            "total": len(data_list),
            "inserted": 0,
            "updated": 0,
            "failed": 0,
            "errors": [],
        }

        # 获取所有列名
        all_columns = set()
        for row in data_list:
            all_columns.update(row.keys())
        all_columns = list(all_columns)

        # 确定更新列
        if update_columns is None:
            update_columns = [col for col in all_columns if col not in conflict_columns]

        # 构建SQL语句
        column_names = ",".join(all_columns)
        placeholders = ",".join(["%s"] * len(all_columns))
        conflict_clause = ",".join(conflict_columns)

        if update_columns:
            update_clause = ",".join(
                [f"{col} = EXCLUDED.{col}" for col in update_columns]
            )
            query = f"""
                INSERT INTO {table_name} ({column_names}) 
                VALUES ({placeholders})
                ON CONFLICT ({conflict_clause}) 
                DO UPDATE SET {update_clause}
            """
        else:
            query = f"""
                INSERT INTO {table_name} ({column_names}) 
                VALUES ({placeholders})
                ON CONFLICT ({conflict_clause}) 
                DO NOTHING
            """

        # 分块处理
        for i in range(0, len(data_list), chunk_size):
            chunk = data_list[i : i + chunk_size]

            try:
                with self.get_db_transaction():
                    with self._conn.cursor() as cursor:
                        for row_data in chunk:
                            # 准备参数
                            params = [row_data.get(col) for col in all_columns]

                            cursor.execute(query, params)
                            # 注意：PostgreSQL的ON CONFLICT不会直接告诉我们是插入还是更新
                            # 这里简单统计影响的行数
                            if cursor.rowcount > 0:
                                result["inserted"] += 1  # 简化处理
                            else:
                                result["failed"] += 1

            except Exception as e:
                result["failed"] += len(chunk)
                result["errors"].append(f"Chunk {i//chunk_size + 1}: {str(e)}")
                logger.error(f"批量UPSERT失败 (chunk {i//chunk_size + 1}): {e}")

        logger.info(
            f"批量UPSERT完成: 总计 {result['total']}, 处理 {result['inserted']}, 失败 {result['failed']}"
        )
        return result

    def execute_bulk_copy(
        self,
        table_name: str,
        columns: List[str],
        data_iterator,
        chunk_size: int = 10000,
        delimiter: str = "\t",
        null_value: str = "\\N",
    ) -> Dict[str, Any]:
        """
        使用COPY命令进行高性能批量导入

        Args:
            table_name: 表名
            columns: 列名列表
            data_iterator: 数据迭代器
            chunk_size: 分块大小
            delimiter: 分隔符
            null_value: NULL值表示

        Returns:
            导入结果统计
        """
        import io

        result = {"total": 0, "success": 0, "failed": 0, "errors": []}

        try:
            with self.get_db_transaction():
                with self._conn.cursor() as cursor:
                    # 创建内存缓冲区
                    buffer = io.StringIO()

                    count = 0
                    for row in data_iterator:
                        # 将数据写入缓冲区
                        row_str = delimiter.join(
                            [
                                str(value) if value is not None else null_value
                                for value in row
                            ]
                        )
                        buffer.write(row_str + "\n")
                        count += 1
                        result["total"] += 1

                        # 达到分块大小时执行COPY
                        if count >= chunk_size:
                            buffer.seek(0)
                            cursor.copy_from(
                                buffer,
                                table_name,
                                columns=columns,
                                sep=delimiter,
                                null=null_value,
                            )
                            result["success"] += count

                            # 重置缓冲区
                            buffer.close()
                            buffer = io.StringIO()
                            count = 0

                    # 处理剩余数据
                    if count > 0:
                        buffer.seek(0)
                        cursor.copy_from(
                            buffer,
                            table_name,
                            columns=columns,
                            sep=delimiter,
                            null=null_value,
                        )
                        result["success"] += count

                    buffer.close()

        except Exception as e:
            result["failed"] = result["total"] - result["success"]
            result["errors"].append(str(e))
            logger.error(f"COPY批量导入失败: {e}")

        logger.info(
            f"COPY批量导入完成: 总计 {result['total']}, 成功 {result['success']}, 失败 {result['failed']}"
        )
        return result

    def check_table_exists(self, table_name: str) -> bool:
        """
        检查表是否存在

        Args:
            table_name: 表名

        Returns:
            bool: 表是否存在
        """
        query = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = %s
            )
        """
        result = self.execute_query(query, (table_name,), fetch_one=True)
        return result.get("exists", False) if result else False

    def get_table_columns(self, table_name: str) -> List[str]:
        """
        获取表的列名

        Args:
            table_name: 表名

        Returns:
            列名列表
        """
        query = """
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_schema = 'public' 
            AND table_name = %s
            ORDER BY ordinal_position
        """
        results = self.execute_query(query, (table_name,))
        return [row["column_name"] for row in results] if results else []

    def get_connection_info(self) -> Dict[str, Any]:
        """
        获取当前连接信息

        Returns:
            连接信息字典
        """
        if not self._conn:
            return {"connected": False, "info": None}

        try:
            info = self._conn.get_dsn_parameters()
            return {
                "connected": True,
                "info": {
                    "host": info.get("host"),
                    "port": info.get("port"),
                    "database": info.get("dbname"),
                    "user": info.get("user"),
                    "connection_count": self._connection_count,
                    "server_version": (
                        self._conn.server_version
                        if hasattr(self._conn, "server_version")
                        else None
                    ),
                },
            }
        except Exception as e:
            logger.error(f"获取连接信息失败: {e}")
            return {"connected": False, "error": str(e)}

    def test_connection_performance(self, test_count: int = 10) -> Dict[str, Any]:
        """
        测试连接性能

        Args:
            test_count: 测试次数

        Returns:
            性能测试结果
        """
        import time

        results = {
            "test_count": test_count,
            "success_count": 0,
            "failed_count": 0,
            "total_time": 0,
            "average_time": 0,
            "min_time": float("inf"),
            "max_time": 0,
            "errors": [],
        }

        for i in range(test_count):
            start_time = time.time()
            try:
                # 执行简单查询测试
                self.execute_query("SELECT 1 as test", fetch_one=True)

                execution_time = time.time() - start_time
                results["success_count"] += 1
                results["total_time"] += execution_time
                results["min_time"] = min(results["min_time"], execution_time)
                results["max_time"] = max(results["max_time"], execution_time)

            except Exception as e:
                results["failed_count"] += 1
                results["errors"].append(f"Test {i+1}: {str(e)}")

        if results["success_count"] > 0:
            results["average_time"] = results["total_time"] / results["success_count"]

        if results["min_time"] == float("inf"):
            results["min_time"] = 0

        return results

    def __del__(self):
        """析构函数，确保连接被正确释放"""
        self._return_connection()


class DatabaseService(DatabaseOperationMixin):
    """
    标准数据库服务基类

    所有需要数据库操作的服务类都应该继承此基类
    提供统一的数据库操作接口和连接管理
    """

    def __init__(self, service_name: str = "DatabaseService"):
        super().__init__()
        self.service_name = service_name
        logger.info(f"{service_name} 初始化完成")

    @require_db_connection
    def get_status(self) -> Dict[str, Any]:
        """
        获取服务状态

        Returns:
            服务状态信息
        """
        try:
            # 测试数据库连接
            result = self.execute_query("SELECT NOW() as current_time", fetch_one=True)

            return {
                "service": self.service_name,
                "status": "active",
                "database_connected": True,
                "current_time": result["current_time"] if result else None,
                "connection_count": self._connection_count,
            }
        except Exception as e:
            return {
                "service": self.service_name,
                "status": "error",
                "database_connected": False,
                "error": str(e),
                "connection_count": self._connection_count,
            }


def deprecated_psycopg2_connect(*args, **kwargs):
    """
    禁用的 psycopg2.connect 方法

    抛出错误提醒开发者使用连接池
    """
    raise RuntimeError(
        "❌ 禁止直接使用 psycopg2.connect()！\n"
        "✅ 请使用连接池: \n"
        "   from utils.database import get_db_connection, return_db_connection\n"
        "   或继承 DatabaseOperationMixin / DatabaseService 基类"
    )


def validate_service_compliance(service_class) -> bool:
    """
    验证服务类是否符合数据库连接规范

    Args:
        service_class: 服务类

    Returns:
        bool: 是否符合规范
    """
    # 检查是否继承了DatabaseOperationMixin
    if not issubclass(service_class, DatabaseOperationMixin):
        logger.warning(f"服务类 {service_class.__name__} 未继承 DatabaseOperationMixin")
        return False

    # 检查是否有直接的psycopg2.connect调用
    import inspect

    source = inspect.getsource(service_class)
    if "psycopg2.connect" in source:
        logger.warning(
            f"服务类 {service_class.__name__} 包含直接的 psycopg2.connect 调用"
        )
        return False

    logger.info(f"服务类 {service_class.__name__} 符合数据库连接规范")
    return True


# 导出主要类和函数
__all__ = [
    "DatabaseOperationMixin",
    "DatabaseService",
    "require_db_connection",
    "DatabaseConnectionError",
    "DatabaseOperationError",
    "DatabaseTimeoutError",
    "with_timeout",
    "validate_service_compliance",
]


class DatabaseMigration(DatabaseService):
    """数据库迁移基类"""

    def __init__(self):
        super().__init__()
        self.migration_name = self.__class__.__name__

    @abstractmethod
    def up(self) -> bool:
        """执行迁移"""
        pass

    @abstractmethod
    def down(self) -> bool:
        """回滚迁移"""
        pass

    def execute_sql(self, sql: str, params: Optional[tuple] = None) -> bool:
        """执行SQL语句"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(sql, params)
                    conn.commit()
                    return True
        except Exception as e:
            self.logger.error(f"SQL执行失败: {e}")
            return False

    def table_exists(self, table_name: str) -> bool:
        """检查表是否存在"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(
                        """
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_name = %s
                        )
                    """,
                        (table_name,),
                    )
                    return cursor.fetchone()[0]
        except Exception as e:
            self.logger.error(f"检查表存在性失败: {e}")
            return False

    def column_exists(self, table_name: str, column_name: str) -> bool:
        """检查列是否存在"""
        try:
            with self.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(
                        """
                        SELECT EXISTS (
                            SELECT FROM information_schema.columns 
                            WHERE table_name = %s AND column_name = %s
                        )
                    """,
                        (table_name, column_name),
                    )
                    return cursor.fetchone()[0]
        except Exception as e:
            self.logger.error(f"检查列存在性失败: {e}")
            return False

    def add_column(
        self,
        table_name: str,
        column_name: str,
        column_type: str,
        default_value: Optional[str] = None,
    ) -> bool:
        """添加列"""
        if self.column_exists(table_name, column_name):
            self.logger.info(f"列 {table_name}.{column_name} 已存在")
            return True

        sql = f"ALTER TABLE {table_name} ADD COLUMN {column_name} {column_type}"
        if default_value:
            sql += f" DEFAULT {default_value}"

        if self.execute_sql(sql):
            self.logger.info(f"成功添加列 {table_name}.{column_name}")
            return True
        else:
            self.logger.error(f"添加列 {table_name}.{column_name} 失败")
            return False

    def create_index(
        self, table_name: str, column_name: str, index_name: Optional[str] = None
    ) -> bool:
        """创建索引"""
        if not index_name:
            index_name = f"idx_{table_name}_{column_name}"

        sql = f"CREATE INDEX IF NOT EXISTS {index_name} ON {table_name} ({column_name})"

        if self.execute_sql(sql):
            self.logger.info(f"成功创建索引 {index_name}")
            return True
        else:
            self.logger.error(f"创建索引 {index_name} 失败")
            return False


class TableMigration(DatabaseMigration):
    """表迁移类"""

    def __init__(self, table_name: str):
        super().__init__()
        self.table_name = table_name

    def create_table(self, create_sql: str) -> bool:
        """创建表"""
        if self.table_exists(self.table_name):
            self.logger.info(f"表 {self.table_name} 已存在")
            return True

        if self.execute_sql(create_sql):
            self.logger.info(f"成功创建表 {self.table_name}")
            return True
        else:
            self.logger.error(f"创建表 {self.table_name} 失败")
            return False

    def drop_table(self) -> bool:
        """删除表"""
        sql = f"DROP TABLE IF EXISTS {self.table_name}"

        if self.execute_sql(sql):
            self.logger.info(f"成功删除表 {self.table_name}")
            return True
        else:
            self.logger.error(f"删除表 {self.table_name} 失败")
            return False
