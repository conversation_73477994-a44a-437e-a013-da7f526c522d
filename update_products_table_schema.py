#!/usr/bin/env python3
"""
更新产品表结构以支持完整的知识内容字段
"""

import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.database import get_db_connection
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def update_products_table_schema():
    """更新产品表结构"""
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查表是否存在
        try:
            cursor.execute("SELECT 1 FROM products LIMIT 1")
            logger.info("Products表存在")
        except Exception as e:
            logger.error(f"Products表不存在或无法访问: {e}")
            return False

        # 需要添加的字段
        new_columns = [
            ("new_param", "TEXT", "JSON格式的参数信息"),
            ("param_info_list", "TEXT", "参数信息列表"),
            ("video_explanation", "TEXT", "产品说明视频"),
            ("video_installation", "TEXT", "安装视频"),
            ("video_troubleshooting", "TEXT", "故障排除视频"),
            ("accessory", "TEXT", "配件信息"),
            ("accessory_list", "TEXT", "配件列表"),
            ("size_img", "TEXT", "尺寸图片"),
            ("unit", "INTEGER", "单位ID"),
            ("unit_name", "VARCHAR(100)", "单位名称"),
            ("show_for_company", "TEXT", "指定公司可见"),
            ("show_for_company_name", "TEXT", "指定公司名称"),
            ("product_gen_map", "TEXT", "产品生成映射"),
            ("question_list", "TEXT", "问题列表"),
            ("hard_list", "TEXT", "硬件列表"),
            ("soft_list", "TEXT", "软件列表"),
            ("product_id_list", "TEXT", "产品ID列表"),
            ("specification_info", "TEXT", "规格信息"),
            ("spec_list", "TEXT", "规格列表"),
            ("spec_search_list", "TEXT", "规格搜索列表"),
        ]

        # 检查哪些字段已存在
        cursor.execute(
            """
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'products'
        """
        )

        existing_columns = {row[0] for row in cursor.fetchall()}
        logger.info(f"现有字段数量: {len(existing_columns)}")

        # 添加缺失的字段
        added_count = 0
        for column_name, column_type, description in new_columns:
            if column_name not in existing_columns:
                try:
                    alter_sql = (
                        f"ALTER TABLE products ADD COLUMN {column_name} {column_type}"
                    )
                    cursor.execute(alter_sql)
                    logger.info(f"添加字段: {column_name} ({description})")
                    added_count += 1
                except Exception as e:
                    logger.error(f"添加字段 {column_name} 失败: {e}")

        # 提交更改
        conn.commit()
        logger.info(f"成功添加 {added_count} 个新字段")

        # 验证更新
        cursor.execute(
            """
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'products'
            ORDER BY column_name
        """
        )

        final_columns = [row[0] for row in cursor.fetchall()]
        logger.info(f"更新后字段总数: {len(final_columns)}")

        cursor.close()
        conn.close()

        return True

    except Exception as e:
        logger.error(f"更新表结构失败: {e}")
        import traceback

        traceback.print_exc()
        return False


if __name__ == "__main__":
    print("开始更新产品表结构...")
    success = update_products_table_schema()
    if success:
        print("✅ 产品表结构更新成功")
    else:
        print("❌ 产品表结构更新失败")
