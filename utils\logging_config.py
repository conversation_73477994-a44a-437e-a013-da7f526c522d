import logging
import logging.handlers
import os
import threading
from datetime import datetime

# 全局标志和锁，确保线程安全的单例模式
_logging_initialized = False
_logging_lock = threading.Lock()


def setup_logging():
    """配置日志系统（线程安全的单例模式）"""
    global _logging_initialized

    # 双重检查锁定模式
    if _logging_initialized:
        return logging.getLogger("product")

    with _logging_lock:
        # 再次检查，防止竞态条件
        if _logging_initialized:
            return logging.getLogger("product")

        # 创建日志目录
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

        # 配置根日志器
        root_logger = logging.getLogger()

        # 检查是否已经有处理器，如果有则先清除
        if root_logger.handlers:
            for handler in root_logger.handlers[:]:
                root_logger.removeHandler(handler)
                handler.close()

        root_logger.setLevel(logging.INFO)

        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )
        console_handler.setFormatter(console_formatter)

        # 文件处理器（按日期轮转）
        try:
            file_handler = logging.handlers.TimedRotatingFileHandler(
                filename=os.path.join(log_dir, "app.log"),
                when="midnight",
                interval=1,
                backupCount=30,
                encoding="utf-8",
            )
            file_handler.setLevel(logging.INFO)
            file_formatter = logging.Formatter(
                "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"
            )
            file_handler.setFormatter(file_formatter)

            # 错误日志处理器
            error_handler = logging.handlers.TimedRotatingFileHandler(
                filename=os.path.join(log_dir, "error.log"),
                when="midnight",
                interval=1,
                backupCount=30,
                encoding="utf-8",
            )
            error_handler.setLevel(logging.ERROR)
            error_handler.setFormatter(file_formatter)

            # 添加处理器
            root_logger.addHandler(file_handler)
            root_logger.addHandler(error_handler)

        except PermissionError as e:
            # 如果文件被占用，只使用控制台日志
            print(f"警告：无法创建文件日志处理器: {e}")
            print("将只使用控制台日志输出")

        # 添加控制台处理器
        root_logger.addHandler(console_handler)

        # 设置初始化标志
        _logging_initialized = True

        # 创建应用专用的logger
        logger = logging.getLogger("product")
        logger.info("日志系统初始化完成")

        return logger


def get_logger(name: str = "product"):
    """获取日志记录器（线程安全）"""
    # 确保日志系统已初始化
    if not _logging_initialized:
        setup_logging()

    return logging.getLogger(name)


def reset_logging():
    """重置日志系统（用于测试）"""
    global _logging_initialized
    with _logging_lock:
        _logging_initialized = False
        root_logger = logging.getLogger()
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
            handler.close()
