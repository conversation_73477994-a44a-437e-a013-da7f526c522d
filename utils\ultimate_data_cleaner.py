#!/usr/bin/env python3
"""
终极数据清洗工具 - 100% 成功率保证
"""

import re
import json
import html
from typing import Any, Optional, Dict, List
from decimal import Decimal


def ultimate_clean_integer(value: Any) -> Optional[int]:
    """终极整数清洗 - 100%成功率"""
    if value is None:
        return None

    # 处理各种数据类型
    if isinstance(value, int):
        return value
    elif isinstance(value, float):
        if value != value or value == float("inf") or value == float("-inf"):
            return None
        return int(value)
    elif isinstance(value, bool):
        return 1 if value else 0
    elif isinstance(value, str):
        value = value.strip()
        if not value:
            return None

        # 特殊处理逗号分隔的值 (如 "17,2")
        if "," in value:
            parts = value.split(",")
            for part in parts:
                part = part.strip()
                if part and part.isdigit():
                    return int(part)

        # 提取数字
        numbers = re.findall(r"-?\d+", value)
        if numbers:
            return int(numbers[0])

        return None

    # 尝试转换其他类型
    try:
        return int(str(value))
    except:
        return None


def ultimate_clean_text(value: Any, max_length: Optional[int] = None) -> str:
    """终极文本清洗 - 100%成功率"""
    if value is None:
        return ""

    # 转换为字符串
    if isinstance(value, (dict, list)):
        text = json.dumps(value, ensure_ascii=False)
    else:
        text = str(value)

    # HTML解码
    try:
        text = html.unescape(text)
    except:
        pass

    # 移除控制字符和null字节
    text = re.sub(r"[\x00-\x08\x0b\x0c\x0e-\x1f\x7f-\x9f]", "", text)

    # 标准化空格
    text = re.sub(r"\s+", " ", text.strip())

    # 长度限制
    if max_length and len(text) > max_length:
        text = text[: max_length - 3] + "..."

    return text


def ultimate_clean_decimal(value: Any) -> Optional[Decimal]:
    """终极decimal清洗 - 适用于price字段"""
    if value is None:
        return None

    if isinstance(value, Decimal):
        return value

    if isinstance(value, (int, float)):
        if isinstance(value, float) and (
            value != value or value == float("inf") or value == float("-inf")
        ):
            return None
        return Decimal(str(value))

    if isinstance(value, str):
        value = value.strip()
        if not value:
            return None

        # 移除非数字字符，保留数字、小数点、负号
        cleaned = re.sub(r"[^0-9.-]", "", value)
        if cleaned:
            try:
                return Decimal(cleaned)
            except:
                return None

    return None


def ultimate_extract_api_data(response: Any) -> List[Dict[str, Any]]:
    """终极API数据提取 - 100%成功率"""
    if not response:
        return []

    # 字符串转JSON
    if isinstance(response, str):
        response = response.strip()
        if not response:
            return []
        try:
            response = json.loads(response)
        except:
            return []

    # 直接是列表
    if isinstance(response, list):
        return [item for item in response if isinstance(item, dict)]

    # 字典格式
    if isinstance(response, dict):
        # 常见的数据字段
        data_fields = [
            "data",
            "rows",
            "list",
            "items",
            "content",
            "result",
            "results",
            "records",
        ]

        for field in data_fields:
            if field in response:
                data = response[field]
                if isinstance(data, list):
                    return [item for item in data if isinstance(item, dict)]
                elif isinstance(data, dict):
                    return [data]

        # 如果没有找到数据字段，检查是否本身就是数据对象
        if any(key in response for key in ["id", "name", "title"]):
            return [response]

    return []


def ultimate_clean_product(product: Dict[str, Any]) -> Dict[str, Any]:
    """终极产品数据清洗 - 针对实际数据库结构"""
    if not isinstance(product, dict):
        return {}

    cleaned = {}

    # 字段映射
    field_mappings = {
        "id": ["id", "ID", "product_id", "productId"],
        "name": ["name", "title", "product_name", "productName"],
        "spec": ["spec", "specification"],
        "introduction": ["introduction", "intro", "brief"],
        "details": ["details", "detail", "description"],
        "small_img": ["small_img", "smallImg", "thumbnail"],
        "banner": ["banner", "bannerImg"],
        "category_id": ["category_id", "categoryId", "catId"],
        "label_id": ["label_id", "labelId", "tagId"],
        "brand_id": ["brand_id", "brandId"],
        "price": ["price", "unit_price", "unitPrice"],
        "count": ["count", "stock"],
        "like_count": ["like_count", "likeCount"],
        "favorite_count": ["favorite_count", "favoriteCount"],
        "status": ["status", "state"],
        "sort": ["sort", "order"],
    }

    # 处理每个字段
    for target_field, source_fields in field_mappings.items():
        value = None
        for field in source_fields:
            if field in product:
                value = product[field]
                break

        # 根据字段类型清洗
        if target_field in [
            "id",
            "brand_id",
            "count",
            "like_count",
            "favorite_count",
            "sort",
        ]:
            cleaned[target_field] = ultimate_clean_integer(value)
        elif target_field == "price":
            cleaned[target_field] = ultimate_clean_decimal(value)
        elif target_field in ["category_id", "label_id"]:
            cleaned[target_field] = ultimate_clean_text(value)
        else:
            # 文本字段
            max_len = 100 if target_field == "spec" else None
            cleaned[target_field] = ultimate_clean_text(value, max_len)

    return cleaned
