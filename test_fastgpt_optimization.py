#!/usr/bin/env python3
"""
FastGPT知识库优化功能测试脚本

测试FastGPT知识库的优化功能：
1. 产品知识块创建
2. 索引关键词生成
3. 标签和摘要生成
4. 文档索引优化

严格遵循开发规范，使用真实API接口，禁止模拟数据。
"""

import logging
import os
import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.fastgpt_knowledge_service import FastGPTKnowledgeService

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def test_fastgpt_optimization():
    """测试FastGPT知识库优化功能"""
    try:
        logger.info("开始测试FastGPT知识库优化功能")
        
        # 初始化服务
        fastgpt_service = FastGPTKnowledgeService()
        
        # 测试产品数据
        test_product_data = {
            "model": "ZK3969",
            "name": "人脸识别门禁考勤一体机",
            "categoryName": "门禁考勤设备",
            "brandName": "中控智慧",
            "description": "支持人脸识别、指纹识别、刷卡等多种验证方式的智能门禁考勤设备，适用于办公楼、工厂等场所",
            "price": "2580.00",
            "specification": "7寸触摸屏，支持10000张人脸，50000条记录",
            "features": ["人脸识别", "指纹识别", "刷卡验证", "考勤统计", "门禁控制"],
            "application": "办公楼、工厂、学校等场所的门禁考勤管理"
        }
        
        # 测试文档数据
        test_documents = [
            {
                "file_name": "ZK3969_用户手册.pdf",
                "file_path": "/docs/ZK3969_manual.pdf",
                "file_size": 2048576,
                "document_type": "用户手册",
                "relevance_score": 0.95
            },
            {
                "file_name": "ZK3969_技术规格.docx",
                "file_path": "/docs/ZK3969_specs.docx",
                "file_size": 512000,
                "document_type": "技术规格",
                "relevance_score": 0.90
            },
            {
                "file_name": "ZK3969_安装指南.pdf",
                "file_path": "/docs/ZK3969_install.pdf",
                "file_size": 1024000,
                "document_type": "安装指南",
                "relevance_score": 0.85
            }
        ]
        
        # 测试1: 标签生成
        logger.info("=" * 60)
        logger.info("测试1: 产品标签生成")
        logger.info("=" * 60)
        
        tags = fastgpt_service._generate_product_tags(test_product_data, test_documents)
        logger.info(f"生成的标签: {tags}")
        logger.info(f"标签数量: {len(tags)}")
        
        # 测试2: 摘要生成
        logger.info("=" * 60)
        logger.info("测试2: 产品摘要生成")
        logger.info("=" * 60)
        
        summary = fastgpt_service._generate_product_summary(test_product_data, test_documents)
        logger.info(f"生成的摘要: {summary}")
        logger.info(f"摘要长度: {len(summary)} 字符")
        
        # 测试3: 索引关键词生成
        logger.info("=" * 60)
        logger.info("测试3: 索引关键词生成")
        logger.info("=" * 60)
        
        keywords = fastgpt_service._generate_index_keywords(test_product_data, test_documents)
        logger.info(f"生成的关键词: {keywords}")
        logger.info(f"关键词数量: {len(keywords)}")
        
        # 测试4: 完整产品知识内容构建
        logger.info("=" * 60)
        logger.info("测试4: 完整产品知识内容构建")
        logger.info("=" * 60)
        
        knowledge_content = fastgpt_service._build_comprehensive_product_content(
            product_model="ZK3969",
            product_data=test_product_data,
            documents=test_documents,
            summary=summary
        )
        
        logger.info(f"知识内容长度: {len(knowledge_content)} 字符")
        logger.info("知识内容预览:")
        logger.info(knowledge_content[:500] + "..." if len(knowledge_content) > 500 else knowledge_content)
        
        # 测试5: 产品知识块创建（如果配置了API）
        logger.info("=" * 60)
        logger.info("测试5: 产品知识块创建")
        logger.info("=" * 60)
        
        if fastgpt_service.api_token:
            success, knowledge_block_id, result = fastgpt_service.create_product_knowledge_block(
                product_model="ZK3969",
                product_data=test_product_data,
                documents=test_documents
            )
            
            if success:
                logger.info(f"✅ 产品知识块创建成功")
                logger.info(f"知识块ID: {knowledge_block_id}")
                logger.info(f"数据集ID: {result.get('dataset_id', 'N/A')}")
                logger.info(f"标签: {result.get('tags', [])}")
                logger.info(f"摘要: {result.get('summary', 'N/A')}")
                logger.info(f"内容长度: {result.get('content_length', 0)} 字符")
                logger.info(f"文档数量: {result.get('document_count', 0)}")
            else:
                logger.error(f"❌ 产品知识块创建失败: {result}")
        else:
            logger.warning("FastGPT API Token未配置，跳过实际创建测试")
        
        # 测试6: 批量产品数据处理
        logger.info("=" * 60)
        logger.info("测试6: 批量产品数据处理")
        logger.info("=" * 60)
        
        batch_products = [
            {
                "model": "MB460",
                "name": "金属探测安检门",
                "categoryName": "安检设备",
                "brandName": "中控智慧",
                "description": "高精度金属探测安检门，适用于机场、车站等安检场所"
            },
            {
                "model": "ACCESS-001",
                "name": "智能门锁",
                "categoryName": "智能锁具",
                "brandName": "中控智慧",
                "description": "支持指纹、密码、刷卡等多种开锁方式的智能门锁"
            }
        ]
        
        for product in batch_products:
            tags = fastgpt_service._generate_product_tags(product)
            summary = fastgpt_service._generate_product_summary(product)
            keywords = fastgpt_service._generate_index_keywords(product)
            
            logger.info(f"产品 {product['model']}:")
            logger.info(f"  标签: {tags[:5]}...")  # 只显示前5个
            logger.info(f"  摘要: {summary}")
            logger.info(f"  关键词: {keywords[:5]}...")  # 只显示前5个
        
        # 测试7: 关键词提取功能
        logger.info("=" * 60)
        logger.info("测试7: 关键词提取功能")
        logger.info("=" * 60)
        
        test_texts = [
            "ZK3969人脸识别门禁考勤一体机",
            "MB460金属探测安检门设备",
            "ACCESS-001智能指纹密码门锁",
            "7寸触摸屏显示器"
        ]
        
        for text in test_texts:
            keywords = fastgpt_service._extract_keywords_from_text(text)
            logger.info(f"文本: '{text}' -> 关键词: {keywords}")
        
        logger.info("FastGPT知识库优化功能测试完成")
        return True
        
    except Exception as e:
        logger.error(f"FastGPT知识库优化功能测试失败: {e}")
        return False


def test_fastgpt_basic_functions():
    """测试FastGPT基础功能"""
    try:
        logger.info("=" * 60)
        logger.info("测试FastGPT基础功能")
        logger.info("=" * 60)
        
        fastgpt_service = FastGPTKnowledgeService()
        
        if not fastgpt_service.api_token:
            logger.warning("FastGPT API Token未配置，跳过基础功能测试")
            return True
        
        # 测试获取知识库列表
        logger.info("测试获取知识库列表")
        success, datasets = fastgpt_service.get_dataset_list()
        
        if success:
            logger.info(f"✅ 获取知识库列表成功: {len(datasets)} 个知识库")
            for dataset in datasets[:3]:  # 只显示前3个
                logger.info(f"  - {dataset.get('name', 'N/A')} (ID: {dataset.get('id', 'N/A')})")
        else:
            logger.error("❌ 获取知识库列表失败")
        
        # 测试搜索功能（如果有知识库）
        if success and datasets:
            dataset_id = datasets[0].get('id')
            if dataset_id:
                logger.info(f"测试搜索功能 (知识库ID: {dataset_id})")
                search_success, search_results = fastgpt_service.search_dataset_data(
                    dataset_id=dataset_id,
                    query="门禁",
                    limit=5
                )
                
                if search_success:
                    logger.info(f"✅ 搜索成功: {len(search_results)} 条结果")
                else:
                    logger.error("❌ 搜索失败")
        
        return True
        
    except Exception as e:
        logger.error(f"FastGPT基础功能测试失败: {e}")
        return False


if __name__ == "__main__":
    logger.info("=" * 80)
    logger.info("FastGPT知识库优化功能测试")
    logger.info("=" * 80)
    
    # 运行优化功能测试
    success1 = test_fastgpt_optimization()
    
    # 运行基础功能测试
    success2 = test_fastgpt_basic_functions()
    
    if success1 and success2:
        logger.info("✅ 所有测试通过")
    else:
        logger.error("❌ 部分测试失败")
        sys.exit(1)
