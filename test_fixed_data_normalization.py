#!/usr/bin/env python3
"""
测试修复后的数据标准化功能
"""

import os
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 加载环境变量
from config import load_env_file
load_env_file()

from utils.api_client import ZKMallClient
from utils.auth import AuthManager
from utils.logging_config import get_logger

logger = get_logger()

def test_fixed_data_normalization():
    """测试修复后的数据标准化功能"""
    try:
        # 先进行登录认证
        print("🔐 正在登录...")
        success, user_info = AuthManager.login("18929343717", "Zk@123456")
        
        if not success:
            print("❌ 登录失败")
            return
            
        print(f"✅ 登录成功")
        
        # 初始化API客户端
        client = ZKMallClient()
        
        # 获取少量产品数据进行分析
        print("🔍 获取产品数据...")
        products = client.get_products(pageSize=3, current=1)
        
        if not products:
            print("❌ 未获取到产品数据")
            return
            
        print(f"✅ 获取到 {len(products)} 个产品")
        
        # 分析每个产品的标准化后数据结构
        for i, product in enumerate(products, 1):
            print(f"\n📦 产品 {i}:")
            print(f"标准化后数据键: {list(product.keys())}")
            
            # 检查基本信息
            product_id = product.get('id')
            product_name = product.get('name')
            category_name = product.get('category_name')
            category_id = product.get('category_id')
            brand_name = product.get('brand_name')
            spec = product.get('spec')
            
            print(f"  - ID: {product_id}")
            print(f"  - 名称: {product_name}")
            print(f"  - 产品型号(spec): {spec}")
            print(f"  - 类别名称: {category_name}")
            print(f"  - 类别ID: {category_id}")
            print(f"  - 品牌: {brand_name}")
            
            # 检查参数信息列表
            param_info_list = product.get('param_info_list', [])
            print(f"  - 参数信息列表长度: {len(param_info_list)}")
            
            if param_info_list:
                print("  - 参数信息详情:")
                for param in param_info_list:
                    if isinstance(param, dict):
                        param_name = param.get('params', '')
                        param_content = param.get('content', '')
                        print(f"    * {param_name}: {param_content}")
                        
                        # 特别检查型号信息
                        if '型号' in param_name:
                            print(f"    🎯 找到产品型号参数: {param_content}")
            
            # 检查新参数字段
            new_param = product.get('new_param', '')
            if new_param:
                print(f"  - 新参数字段存在，类型: {type(new_param)}")
                if isinstance(new_param, str) and new_param.startswith('['):
                    try:
                        parsed_params = json.loads(new_param)
                        print(f"  - 解析后参数数量: {len(parsed_params)}")
                        for param in parsed_params[:3]:  # 只显示前3个
                            if isinstance(param, dict):
                                print(f"    * {param.get('params', '')}: {param.get('content', '')}")
                    except:
                        print(f"  - 参数解析失败")
            
            # 检查其他重要字段
            introduction = product.get('introduction', '')
            use_to = product.get('use_to', '')
            
            print(f"  - 产品介绍: {introduction[:50]}..." if introduction else "  - 产品介绍: 无")
            print(f"  - 使用场景: {use_to[:50]}..." if use_to else "  - 使用场景: 无")
                
        # 验证数据完整性
        print(f"\n🔍 数据完整性验证:")
        missing_fields = []
        
        for i, product in enumerate(products, 1):
            print(f"\n产品 {i} 关键字段检查:")
            
            # 检查必要字段
            required_fields = ['id', 'name', 'spec', 'category_name']
            for field in required_fields:
                value = product.get(field)
                if value is None or value == '':
                    missing_fields.append(f"产品{i}-{field}")
                    print(f"  ❌ {field}: 缺失")
                else:
                    print(f"  ✅ {field}: {value}")
        
        if missing_fields:
            print(f"\n⚠️ 发现缺失字段: {missing_fields}")
        else:
            print(f"\n✅ 所有关键字段都存在")
                    
    except Exception as e:
        logger.error(f"测试修复后数据标准化失败: {e}", exc_info=True)
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_fixed_data_normalization()
