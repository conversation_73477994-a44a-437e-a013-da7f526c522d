#!/usr/bin/env python3
"""
HTML内容处理器 - 支持HTML到Markdown转换
可选依赖：beautifulsoup4, markdownify, html2text
"""

import re
import logging
from typing import Any, Dict, List, Optional, Union

logger = logging.getLogger(__name__)

# 可选导入HTML处理库
try:
    from bs4 import BeautifulSoup

    HAS_BS4 = True
except ImportError:
    logger.warning("beautifulsoup4未安装，HTML处理功能受限")
    HAS_BS4 = False

try:
    from markdownify import markdownify as md

    HAS_MARKDOWNIFY = True
except ImportError:
    logger.warning("markdownify未安装，部分转换功能不可用")
    HAS_MARKDOWNIFY = False

try:
    import html2text

    HAS_HTML2TEXT = True
except ImportError:
    logger.warning("html2text未安装，文本转换功能受限")
    HAS_HTML2TEXT = False


class HTMLProcessor:
    """HTML内容处理器"""

    def __init__(self):
        if HAS_HTML2TEXT:
            self.html2text_handler = html2text.HTML2Text()
            self.html2text_handler.ignore_links = False
            self.html2text_handler.ignore_images = False
            self.html2text_handler.body_width = 0  # 不限制宽度
            self.html2text_handler.single_line_break = True
            self.html2text_handler.mark_code = True
        else:
            self.html2text_handler = None

    def process_html_content(self, content: str, conversion_type: str = "auto") -> str:
        """
        处理HTML内容并转换为Markdown

        Args:
            content: HTML内容
            conversion_type: 转换类型 ('auto', 'table', 'text', 'mixed')

        Returns:
            转换后的Markdown内容
        """
        if not content or not isinstance(content, str):
            return str(content) if content else ""

        # 检查是否包含HTML标签
        if not self._contains_html_tags(content):
            return content

        try:
            # 根据转换类型选择处理方式
            if conversion_type == "auto":
                return self._auto_convert_html(content)
            elif conversion_type == "table":
                return self._convert_html_tables(content)
            elif conversion_type == "text":
                return self._convert_html_text(content)
            elif conversion_type == "mixed":
                return self._convert_mixed_html(content)
            else:
                return self._auto_convert_html(content)

        except Exception as e:
            logger.error(f"HTML转换失败: {e}")
            # 失败时返回去除HTML标签的纯文本
            return self._strip_html_tags(content)

    def _contains_html_tags(self, content: str) -> bool:
        """检查内容是否包含HTML标签"""
        html_pattern = re.compile(r"<[^>]+>")
        return bool(html_pattern.search(content))

    def _auto_convert_html(self, content: str) -> str:
        """自动检测并转换HTML内容"""
        if not HAS_BS4:
            return self._strip_html_tags(content)

        # 检查是否主要是表格内容
        if self._is_table_heavy_content(content):
            return self._convert_html_tables(content)
        else:
            return self._convert_mixed_html(content)

    def _is_table_heavy_content(self, content: str) -> bool:
        """判断内容是否以表格为主"""
        if not HAS_BS4:
            return False

        try:
            soup = BeautifulSoup(content, "html.parser")
            tables = soup.find_all("table")

            if not tables:
                return False

            # 计算表格内容占比
            table_text_length = sum(len(table.get_text()) for table in tables)
            total_text_length = len(soup.get_text())

            return (
                table_text_length / total_text_length > 0.5
                if total_text_length > 0
                else False
            )
        except Exception:
            return False

    def _convert_html_tables(self, content: str) -> str:
        """专门处理HTML表格转换"""
        if not HAS_BS4:
            return self._strip_html_tags(content)

        try:
            soup = BeautifulSoup(content, "html.parser")

            # 处理每个表格
            for table in soup.find_all("table"):
                markdown_table = self._table_to_markdown(table)
                # 用转换后的markdown替换原表格
                table.replace_with(BeautifulSoup(markdown_table, "html.parser"))

            # 转换其余内容
            if self.html2text_handler:
                return self.html2text_handler.handle(str(soup))
            else:
                return self._strip_html_tags(str(soup))

        except Exception as e:
            logger.error(f"表格转换失败: {e}")
            return self._convert_mixed_html(content)

    def _table_to_markdown(self, table_element) -> str:
        """将HTML表格转换为Markdown表格"""
        if not HAS_BS4:
            return "[表格处理需要beautifulsoup4]"

        try:
            rows = []

            # 处理表头
            thead = table_element.find("thead")
            if thead:
                header_rows = thead.find_all("tr")
                for row in header_rows:
                    cells = row.find_all(["th", "td"])
                    row_data = [
                        self._clean_cell_text(cell.get_text()) for cell in cells
                    ]
                    rows.append(row_data)

                # 添加分隔线
                if rows:
                    separator = ["---"] * len(rows[0])
                    rows.append(separator)

            # 处理表体
            tbody = table_element.find("tbody") or table_element
            body_rows = tbody.find_all("tr")

            for row in body_rows:
                # 跳过表头行（如果没有thead）
                if not thead and row.find("th"):
                    cells = row.find_all(["th", "td"])
                    row_data = [
                        self._clean_cell_text(cell.get_text()) for cell in cells
                    ]
                    rows.append(row_data)
                    # 添加分隔线
                    separator = ["---"] * len(row_data)
                    rows.append(separator)
                else:
                    cells = row.find_all(["th", "td"])
                    row_data = [
                        self._clean_cell_text(cell.get_text()) for cell in cells
                    ]
                    if row_data:  # 避免空行
                        rows.append(row_data)

            # 构建Markdown表格
            if not rows:
                return ""

            markdown_lines = []
            for row in rows:
                if row and all(cell == "---" for cell in row):
                    # 分隔线
                    markdown_lines.append("| " + " | ".join(row) + " |")
                else:
                    # 数据行
                    markdown_lines.append("| " + " | ".join(row) + " |")

            return "\n" + "\n".join(markdown_lines) + "\n"

        except Exception as e:
            logger.error(f"表格转换失败: {e}")
            return f"\n[表格转换失败: {str(e)}]\n"

    def _clean_cell_text(self, text: str) -> str:
        """清理表格单元格文本"""
        if not text:
            return ""

        # 移除多余的空白字符
        cleaned = re.sub(r"\s+", " ", text.strip())

        # 转义Markdown特殊字符
        cleaned = cleaned.replace("|", "\\|")
        cleaned = cleaned.replace("\n", "<br>")

        return cleaned

    def _convert_html_text(self, content: str) -> str:
        """转换HTML文本内容"""
        if self.html2text_handler:
            try:
                return self.html2text_handler.handle(content)
            except Exception as e:
                logger.error(f"HTML文本转换失败: {e}")
                return self._strip_html_tags(content)
        else:
            return self._strip_html_tags(content)

    def _convert_mixed_html(self, content: str) -> str:
        """处理混合HTML内容"""
        if HAS_MARKDOWNIFY:
            try:
                # 使用markdownify进行转换
                markdown_content = md(
                    content,
                    heading_style="ATX",  # 使用 # 风格的标题
                    bullets="-",  # 使用 - 作为列表项
                    strip=["script", "style"],  # 移除脚本和样式
                )
                return markdown_content
            except Exception as e:
                logger.error(f"混合HTML转换失败: {e}")
                return self._strip_html_tags(content)
        else:
            return self._strip_html_tags(content)

    def _strip_html_tags(self, content: str) -> str:
        """移除HTML标签，保留纯文本"""
        if not content:
            return ""

        # 移除HTML标签
        clean_text = re.sub(r"<[^>]+>", "", content)

        # 移除多余的空白
        clean_text = re.sub(r"\s+", " ", clean_text).strip()

        return clean_text

    def process_data_field(self, field_value: Any, field_name: str = "") -> Any:
        """
        处理数据字段中的HTML内容

        Args:
            field_value: 字段值
            field_name: 字段名（用于判断处理方式）

        Returns:
            处理后的字段值
        """
        if not isinstance(field_value, str):
            return field_value

        # 如果不包含HTML标签，直接返回
        if not self._contains_html_tags(field_value):
            return field_value

        # 根据字段名判断转换方式
        if "description" in field_name.lower() or "content" in field_name.lower():
            return self.process_html_content(field_value, conversion_type="mixed")
        elif "table" in field_name.lower():
            return self.process_html_content(field_value, conversion_type="table")
        else:
            return self.process_html_content(field_value, conversion_type="text")


def process_api_response_html(
    data: Union[Dict, List], html_fields: Optional[List[str]] = None
) -> Union[Dict, List]:
    """
    处理API响应中的HTML字段

    Args:
        data: API响应数据
        html_fields: 需要处理的HTML字段列表

    Returns:
        处理后的数据
    """
    if not data:
        return data

    processor = HTMLProcessor()

    # 默认的HTML字段
    if html_fields is None:
        html_fields = [
            "description",
            "content",
            "summary",
            "details",
            "notes",
            "remark",
            "comment",
        ]

    def process_item(item):
        if not isinstance(item, dict):
            return item

        processed_item = {}
        for key, value in item.items():
            if key.lower() in [field.lower() for field in html_fields]:
                processed_item[key] = processor.process_data_field(value, key)
            else:
                processed_item[key] = value

        return processed_item

    if isinstance(data, list):
        return [process_item(item) for item in data]
    elif isinstance(data, dict):
        return process_item(data)
    else:
        return data


# 测试函数
if __name__ == "__main__":
    print("🧪 测试HTML处理器")

    processor = HTMLProcessor()

    # 测试基本HTML转换
    test_html = "<p>这是一个<strong>测试</strong>内容</p>"
    result = processor.process_html_content(test_html)
    print(f"HTML转换测试: {test_html} -> {result}")

    # 测试表格转换
    table_html = """
    <table>
        <tr><th>姓名</th><th>年龄</th></tr>
        <tr><td>张三</td><td>25</td></tr>
    </table>
    """
    table_result = processor.process_html_content(table_html, "table")
    print(f"表格转换测试: {table_result}")

    print("✅ HTML处理器测试完成")
