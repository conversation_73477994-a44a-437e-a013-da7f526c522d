"""
工具函数包
"""

# 核心数据库功能 - 始终可用
try:
    from utils.database import get_db_connection, return_db_connection
except ImportError:
    pass

try:
    from utils.auth import AuthManager, require_auth
except ImportError:
    pass

try:
    from utils.api_client import ZKMallClient
except ImportError:
    pass

try:
    from utils.logging_config import get_logger
except ImportError:
    pass


# 可选功能 - 按需导入，避免依赖问题
def get_session_manager():
    """按需导入SessionManager，避免streamlit依赖问题"""
    try:
        from utils.session import SessionManager

        return SessionManager
    except ImportError:
        return None


def get_cache():
    """按需导入缓存功能"""
    try:
        from utils.cache import get_cache as _get_cache

        return _get_cache()
    except ImportError:
        return None
