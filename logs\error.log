2025-06-21 01:51:16,460 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 01:51:18,503 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 4:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 01:51:18,591 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 01:51:20,358 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 01:51:20,509 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 4:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 01:51:20,606 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 01:51:23,899 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 01:51:24,044 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 4:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 01:51:24,134 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 01:51:25,039 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 01:51:25,189 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 4:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 01:51:25,286 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 01:51:26,804 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 01:51:28,271 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 01:51:29,121 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 01:51:30,636 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 01:51:31,386 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 01:51:31,533 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 4:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 01:51:31,625 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 01:51:34,549 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 01:51:34,690 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 4:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 01:51:34,780 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 01:51:41,724 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 01:51:41,962 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 4:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 01:51:42,166 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 01:51:48,361 - utils.api_client - ERROR - _handle_response:87 - HTTP错误: 401 Client Error:  for url: https://zkmall.zktecoiot.com/api/business/info/list?current=1&pageSize=20
2025-06-21 01:51:50,412 - utils.api_client - ERROR - _handle_response:87 - HTTP错误: 401 Client Error:  for url: https://zkmall.zktecoiot.com/api/business/info/list?current=1&pageSize=20
2025-06-21 01:51:52,465 - utils.api_client - ERROR - _handle_response:87 - HTTP错误: 401 Client Error:  for url: https://zkmall.zktecoiot.com/api/business/info/list?current=1&pageSize=20
2025-06-21 01:52:24,811 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 01:52:24,956 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 4:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 01:52:25,051 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 02:04:29,956 - yunshang - ERROR - load_brands:210 - 加载品牌失败: 'ZKMallClient' object has no attribute 'get_brands'
2025-06-21 02:09:59,648 - yunshang - ERROR - load_brands:210 - 加载品牌失败: 'ZKMallClient' object has no attribute 'get_brands'
2025-06-21 02:15:37,695 - yunshang - ERROR - load_brands:210 - 加载品牌失败: 'ZKMallClient' object has no attribute 'get_brands'
2025-06-21 03:26:08,694 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 03:26:09,165 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 4:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 03:26:09,256 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 03:26:24,319 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 03:26:24,482 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 4:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 03:26:24,576 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 03:26:45,936 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,2"
LINE 10:                             label_id = '17,2',
                                                ^

2025-06-21 03:26:45,936 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,2"
LINE 10:                             label_id = '17,2',
                                                ^

2025-06-21 03:26:48,580 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,2"
LINE 10:                             label_id = '17,2',
                                                ^

2025-06-21 03:26:48,580 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,2"
LINE 10:                             label_id = '17,2',
                                                ^

2025-06-21 03:26:53,927 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "21,2"
LINE 10:                             label_id = '21,2',
                                                ^

2025-06-21 03:27:02,821 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "21,2"
LINE 10:                             label_id = '21,2',
                                                ^

2025-06-21 03:27:04,893 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "21,41,2"
LINE 10:                             label_id = '21,41,2',
                                                ^

2025-06-21 03:27:04,894 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "21,41,2"
LINE 10:                             label_id = '21,41,2',
                                                ^

2025-06-21 03:27:11,595 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "20,2"
LINE 10:                             label_id = '20,2',
                                                ^

2025-06-21 03:27:11,595 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "20,2"
LINE 10:                             label_id = '20,2',
                                                ^

2025-06-21 03:27:14,742 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,2"
LINE 10:                             label_id = '17,2',
                                                ^

2025-06-21 03:27:14,743 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,2"
LINE 10:                             label_id = '17,2',
                                                ^

2025-06-21 03:27:20,643 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,2"
LINE 11:                             label_id = '18,2',
                                                ^

2025-06-21 03:27:20,644 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,2"
LINE 11:                             label_id = '18,2',
                                                ^

2025-06-21 03:27:21,421 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "20,2"
LINE 10:                             label_id = '20,2',
                                                ^

2025-06-21 03:27:21,421 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "20,2"
LINE 10:                             label_id = '20,2',
                                                ^

2025-06-21 03:27:25,874 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:27:25,875 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:27:25,981 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "26,18"
LINE 10:                             label_id = '26,18',
                                                ^

2025-06-21 03:27:25,981 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "26,18"
LINE 10:                             label_id = '26,18',
                                                ^

2025-06-21 03:27:26,093 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:27:26,094 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:27:26,316 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:27:26,316 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:27:26,432 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:27:26,432 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:27:26,539 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:27:26,540 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:27:27,536 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:27:27,537 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:27:27,764 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:27:27,765 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:27:27,979 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:27:27,979 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:27:28,089 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:27:28,089 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:27:28,202 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:27:28,202 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:27:28,427 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "26,18"
LINE 10:                             label_id = '26,18',
                                                ^

2025-06-21 03:27:28,427 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "26,18"
LINE 10:                             label_id = '26,18',
                                                ^

2025-06-21 03:27:28,533 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:27:28,534 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:27:28,642 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:27:28,642 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:27:28,863 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:27:28,863 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:27:29,997 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "2,17"
LINE 10:                             label_id = '2,17',
                                                ^

2025-06-21 03:27:29,997 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "2,17"
LINE 10:                             label_id = '2,17',
                                                ^

2025-06-21 03:27:30,330 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "24,30"
LINE 10:                             label_id = '24,30',
                                                ^

2025-06-21 03:27:30,331 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "24,30"
LINE 10:                             label_id = '24,30',
                                                ^

2025-06-21 03:27:30,437 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "24,30"
LINE 10:                             label_id = '24,30',
                                                ^

2025-06-21 03:27:30,439 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "24,30"
LINE 10:                             label_id = '24,30',
                                                ^

2025-06-21 03:27:30,766 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,2"
LINE 10:                             label_id = '17,2',
                                                ^

2025-06-21 03:27:30,766 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,2"
LINE 10:                             label_id = '17,2',
                                                ^

2025-06-21 03:27:31,536 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,30"
LINE 10:                             label_id = '17,30',
                                                ^

2025-06-21 03:27:31,537 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,30"
LINE 10:                             label_id = '17,30',
                                                ^

2025-06-21 03:27:49,057 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,2"
LINE 10:                             label_id = '18,2',
                                                ^

2025-06-21 03:27:49,058 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,2"
LINE 10:                             label_id = '18,2',
                                                ^

2025-06-21 03:27:49,496 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "20,2"
LINE 10:                             label_id = '20,2',
                                                ^

2025-06-21 03:27:49,497 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "20,2"
LINE 10:                             label_id = '20,2',
                                                ^

2025-06-21 03:27:54,513 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "34,2"
LINE 10:                             label_id = '34,2',
                                                ^

2025-06-21 03:27:54,513 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "34,2"
LINE 10:                             label_id = '34,2',
                                                ^

2025-06-21 03:28:05,859 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:28:05,860 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:28:19,388 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "40,41,2"
LINE 16:                             label_id = '40,41,2',
                                                ^

2025-06-21 03:28:19,388 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "40,41,2"
LINE 16:                             label_id = '40,41,2',
                                                ^

2025-06-21 03:28:27,265 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "40,17"
LINE 15:                             label_id = '40,17',
                                                ^

2025-06-21 03:28:27,265 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "40,17"
LINE 15:                             label_id = '40,17',
                                                ^

2025-06-21 03:28:32,663 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "2,22"
LINE 10:                             label_id = '2,22',
                                                ^

2025-06-21 03:28:32,663 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "2,22"
LINE 10:                             label_id = '2,22',
                                                ^

2025-06-21 03:28:32,770 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "2,22"
LINE 10:                             label_id = '2,22',
                                                ^

2025-06-21 03:28:32,770 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "2,22"
LINE 10:                             label_id = '2,22',
                                                ^

2025-06-21 03:28:42,011 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:28:42,011 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:28:42,232 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,22"
LINE 10:                             label_id = '1,22',
                                                ^

2025-06-21 03:28:42,233 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,22"
LINE 10:                             label_id = '1,22',
                                                ^

2025-06-21 03:28:42,344 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,22"
LINE 10:                             label_id = '18,22',
                                                ^

2025-06-21 03:28:42,344 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,22"
LINE 10:                             label_id = '18,22',
                                                ^

2025-06-21 03:28:43,452 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:28:43,453 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:28:43,672 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,43"
LINE 10:                             label_id = '17,43',
                                                ^

2025-06-21 03:28:43,673 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,43"
LINE 10:                             label_id = '17,43',
                                                ^

2025-06-21 03:28:44,017 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "26,18"
LINE 10:                             label_id = '26,18',
                                                ^

2025-06-21 03:28:44,018 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "26,18"
LINE 10:                             label_id = '26,18',
                                                ^

2025-06-21 03:28:44,128 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "26,18"
LINE 10:                             label_id = '26,18',
                                                ^

2025-06-21 03:28:44,128 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "26,18"
LINE 10:                             label_id = '26,18',
                                                ^

2025-06-21 03:28:46,261 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "42,17"
LINE 10:                             label_id = '42,17',
                                                ^

2025-06-21 03:28:46,261 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "42,17"
LINE 10:                             label_id = '42,17',
                                                ^

2025-06-21 03:28:46,371 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "42,17"
LINE 10:                             label_id = '42,17',
                                                ^

2025-06-21 03:28:46,372 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "42,17"
LINE 10:                             label_id = '42,17',
                                                ^

2025-06-21 03:28:47,047 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "28,26,18"
LINE 10:                             label_id = '28,26,18',
                                                ^

2025-06-21 03:28:47,048 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "28,26,18"
LINE 10:                             label_id = '28,26,18',
                                                ^

2025-06-21 03:28:47,166 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "26,18"
LINE 10:                             label_id = '26,18',
                                                ^

2025-06-21 03:28:47,167 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "26,18"
LINE 10:                             label_id = '26,18',
                                                ^

2025-06-21 03:28:48,873 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "26,18"
LINE 10:                             label_id = '26,18',
                                                ^

2025-06-21 03:28:48,873 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "26,18"
LINE 10:                             label_id = '26,18',
                                                ^

2025-06-21 03:28:48,982 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "26,32,18"
LINE 10:                             label_id = '26,32,18',
                                                ^

2025-06-21 03:28:48,983 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "26,32,18"
LINE 10:                             label_id = '26,32,18',
                                                ^

2025-06-21 03:28:49,092 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "32,26,18"
LINE 10:                             label_id = '32,26,18',
                                                ^

2025-06-21 03:28:49,093 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "32,26,18"
LINE 10:                             label_id = '32,26,18',
                                                ^

2025-06-21 03:28:49,205 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "26,18"
LINE 10:                             label_id = '26,18',
                                                ^

2025-06-21 03:28:49,206 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "26,18"
LINE 10:                             label_id = '26,18',
                                                ^

2025-06-21 03:28:49,317 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "26,18"
LINE 10:                             label_id = '26,18',
                                                ^

2025-06-21 03:28:49,318 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "26,18"
LINE 10:                             label_id = '26,18',
                                                ^

2025-06-21 03:28:49,426 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "27,26,18"
LINE 11:                             label_id = '27,26,18',
                                                ^

2025-06-21 03:28:49,426 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "27,26,18"
LINE 11:                             label_id = '27,26,18',
                                                ^

2025-06-21 03:28:49,655 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "26,18"
LINE 12:                             label_id = '26,18',
                                                ^

2025-06-21 03:28:49,655 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "26,18"
LINE 12:                             label_id = '26,18',
                                                ^

2025-06-21 03:28:49,789 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "26,18"
LINE 12:                             label_id = '26,18',
                                                ^

2025-06-21 03:28:49,789 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "26,18"
LINE 12:                             label_id = '26,18',
                                                ^

2025-06-21 03:28:50,149 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:28:50,149 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:28:51,500 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,21"
LINE 10:                             label_id = '1,21',
                                                ^

2025-06-21 03:28:51,500 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,21"
LINE 10:                             label_id = '1,21',
                                                ^

2025-06-21 03:28:51,607 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,21"
LINE 10:                             label_id = '1,21',
                                                ^

2025-06-21 03:28:51,608 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,21"
LINE 10:                             label_id = '1,21',
                                                ^

2025-06-21 03:28:52,054 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,20,41,2"
LINE 10:                             label_id = '1,20,41,2',
                                                ^

2025-06-21 03:28:52,055 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,20,41,2"
LINE 10:                             label_id = '1,20,41,2',
                                                ^

2025-06-21 03:28:54,327 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "19,1"
LINE 11:                             label_id = '19,1',
                                                ^

2025-06-21 03:28:54,327 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "19,1"
LINE 11:                             label_id = '19,1',
                                                ^

2025-06-21 03:28:56,211 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:28:56,211 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:28:57,798 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,17"
LINE 10:                             label_id = '1,17',
                                                ^

2025-06-21 03:28:57,799 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,17"
LINE 10:                             label_id = '1,17',
                                                ^

2025-06-21 03:28:57,918 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,17"
LINE 10:                             label_id = '1,17',
                                                ^

2025-06-21 03:28:57,919 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,17"
LINE 10:                             label_id = '1,17',
                                                ^

2025-06-21 03:28:58,027 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,17,41,2"
LINE 10:                             label_id = '1,17,41,2',
                                                ^

2025-06-21 03:28:58,027 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,17,41,2"
LINE 10:                             label_id = '1,17,41,2',
                                                ^

2025-06-21 03:28:58,138 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,17"
LINE 10:                             label_id = '1,17',
                                                ^

2025-06-21 03:28:58,138 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,17"
LINE 10:                             label_id = '1,17',
                                                ^

2025-06-21 03:28:58,679 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,1"
LINE 10:                             label_id = '18,1',
                                                ^

2025-06-21 03:28:58,680 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,1"
LINE 10:                             label_id = '18,1',
                                                ^

2025-06-21 03:28:58,905 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,1,41,2"
LINE 10:                             label_id = '17,1,41,2',
                                                ^

2025-06-21 03:28:58,906 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,1,41,2"
LINE 10:                             label_id = '17,1,41,2',
                                                ^

2025-06-21 03:28:59,013 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,1"
LINE 11:                             label_id = '17,1',
                                                ^

2025-06-21 03:28:59,013 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,1"
LINE 11:                             label_id = '17,1',
                                                ^

2025-06-21 03:28:59,766 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "20,2"
LINE 10:                             label_id = '20,2',
                                                ^

2025-06-21 03:28:59,766 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "20,2"
LINE 10:                             label_id = '20,2',
                                                ^

2025-06-21 03:28:59,872 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,18,41,2"
LINE 11:                             label_id = '1,18,41,2',
                                                ^

2025-06-21 03:28:59,873 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,18,41,2"
LINE 11:                             label_id = '1,18,41,2',
                                                ^

2025-06-21 03:28:59,982 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,1"
LINE 14:                             label_id = '17,1',
                                                ^

2025-06-21 03:28:59,983 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,1"
LINE 14:                             label_id = '17,1',
                                                ^

2025-06-21 03:29:00,099 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,19"
LINE 10:                             label_id = '1,19',
                                                ^

2025-06-21 03:29:00,099 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,19"
LINE 10:                             label_id = '1,19',
                                                ^

2025-06-21 03:29:00,206 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "20,2"
LINE 10:                             label_id = '20,2',
                                                ^

2025-06-21 03:29:00,206 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "20,2"
LINE 10:                             label_id = '20,2',
                                                ^

2025-06-21 03:29:06,288 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,32"
LINE 11:                             label_id = '18,32',
                                                ^

2025-06-21 03:29:06,289 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,32"
LINE 11:                             label_id = '18,32',
                                                ^

2025-06-21 03:29:06,623 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,32"
LINE 10:                             label_id = '18,32',
                                                ^

2025-06-21 03:29:06,624 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,32"
LINE 10:                             label_id = '18,32',
                                                ^

2025-06-21 03:29:06,731 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,32"
LINE 11:                             label_id = '18,32',
                                                ^

2025-06-21 03:29:06,732 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,32"
LINE 11:                             label_id = '18,32',
                                                ^

2025-06-21 03:29:08,451 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:29:08,451 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:29:08,558 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "34,1"
LINE 10:                             label_id = '34,1',
                                                ^

2025-06-21 03:29:08,558 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "34,1"
LINE 10:                             label_id = '34,1',
                                                ^

2025-06-21 03:29:08,675 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "2,18"
LINE 13:                             label_id = '2,18',
                                                ^

2025-06-21 03:29:08,676 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "2,18"
LINE 13:                             label_id = '2,18',
                                                ^

2025-06-21 03:29:09,773 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "40,41,2"
LINE 10:                             label_id = '40,41,2',
                                                ^

2025-06-21 03:29:09,774 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "40,41,2"
LINE 10:                             label_id = '40,41,2',
                                                ^

2025-06-21 03:29:09,919 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:29:09,919 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:29:10,371 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,17"
LINE 10:                             label_id = '1,17',
                                                ^

2025-06-21 03:29:10,371 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,17"
LINE 10:                             label_id = '1,17',
                                                ^

2025-06-21 03:29:10,478 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,17,41,2"
LINE 10:                             label_id = '1,17,41,2',
                                                ^

2025-06-21 03:29:10,478 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,17,41,2"
LINE 10:                             label_id = '1,17,41,2',
                                                ^

2025-06-21 03:29:10,588 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,17"
LINE 10:                             label_id = '1,17',
                                                ^

2025-06-21 03:29:10,589 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,17"
LINE 10:                             label_id = '1,17',
                                                ^

2025-06-21 03:29:10,699 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:29:10,699 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:29:10,809 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:29:10,809 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:29:10,923 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "20,1"
LINE 10:                             label_id = '20,1',
                                                ^

2025-06-21 03:29:10,924 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "20,1"
LINE 10:                             label_id = '20,1',
                                                ^

2025-06-21 03:29:11,033 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "20,1"
LINE 10:                             label_id = '20,1',
                                                ^

2025-06-21 03:29:11,034 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "20,1"
LINE 10:                             label_id = '20,1',
                                                ^

2025-06-21 03:29:11,156 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "20,1"
LINE 10:                             label_id = '20,1',
                                                ^

2025-06-21 03:29:11,157 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "20,1"
LINE 10:                             label_id = '20,1',
                                                ^

2025-06-21 03:29:11,265 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,1"
LINE 10:                             label_id = '18,1',
                                                ^

2025-06-21 03:29:11,265 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,1"
LINE 10:                             label_id = '18,1',
                                                ^

2025-06-21 03:29:11,373 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,1"
LINE 10:                             label_id = '18,1',
                                                ^

2025-06-21 03:29:11,374 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,1"
LINE 10:                             label_id = '18,1',
                                                ^

2025-06-21 03:29:11,481 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,1"
LINE 10:                             label_id = '18,1',
                                                ^

2025-06-21 03:29:11,481 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,1"
LINE 10:                             label_id = '18,1',
                                                ^

2025-06-21 03:29:11,700 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,1"
LINE 10:                             label_id = '18,1',
                                                ^

2025-06-21 03:29:11,700 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,1"
LINE 10:                             label_id = '18,1',
                                                ^

2025-06-21 03:29:12,259 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "20,1"
LINE 10:                             label_id = '20,1',
                                                ^

2025-06-21 03:29:12,259 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "20,1"
LINE 10:                             label_id = '20,1',
                                                ^

2025-06-21 03:29:12,366 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "20,1"
LINE 10:                             label_id = '20,1',
                                                ^

2025-06-21 03:29:12,366 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "20,1"
LINE 10:                             label_id = '20,1',
                                                ^

2025-06-21 03:29:13,038 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,2,20"
LINE 10:                             label_id = '1,2,20',
                                                ^

2025-06-21 03:29:13,039 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,2,20"
LINE 10:                             label_id = '1,2,20',
                                                ^

2025-06-21 03:29:13,149 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:29:13,149 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:29:13,263 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,2,20"
LINE 10:                             label_id = '1,2,20',
                                                ^

2025-06-21 03:29:13,263 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,2,20"
LINE 10:                             label_id = '1,2,20',
                                                ^

2025-06-21 03:29:13,370 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:29:13,370 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:29:13,827 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:29:13,827 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:29:13,941 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:29:13,941 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:29:14,055 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:29:14,056 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:29:14,163 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:29:14,164 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:29:14,269 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:29:14,270 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:29:14,385 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:29:14,386 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:29:14,512 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:29:14,512 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:29:14,618 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:29:14,619 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:29:14,733 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:29:14,733 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:29:16,890 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:29:16,890 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:29:17,004 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,1"
LINE 10:                             label_id = '18,1',
                                                ^

2025-06-21 03:29:17,004 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,1"
LINE 10:                             label_id = '18,1',
                                                ^

2025-06-21 03:29:17,126 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:29:17,126 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:29:17,234 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,18"
LINE 11:                             label_id = '1,18',
                                                ^

2025-06-21 03:29:17,235 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,18"
LINE 11:                             label_id = '1,18',
                                                ^

2025-06-21 03:29:17,340 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,18"
LINE 11:                             label_id = '1,18',
                                                ^

2025-06-21 03:29:17,341 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,18"
LINE 11:                             label_id = '1,18',
                                                ^

2025-06-21 03:29:17,453 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,18"
LINE 11:                             label_id = '1,18',
                                                ^

2025-06-21 03:29:17,453 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,18"
LINE 11:                             label_id = '1,18',
                                                ^

2025-06-21 03:29:17,562 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,2,40"
LINE 10:                             label_id = '1,2,40',
                                                ^

2025-06-21 03:29:17,562 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,2,40"
LINE 10:                             label_id = '1,2,40',
                                                ^

2025-06-21 03:29:17,672 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,17,40"
LINE 10:                             label_id = '1,17,40',
                                                ^

2025-06-21 03:29:17,672 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,17,40"
LINE 10:                             label_id = '1,17,40',
                                                ^

2025-06-21 03:29:17,789 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,17,40"
LINE 10:                             label_id = '1,17,40',
                                                ^

2025-06-21 03:29:17,789 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,17,40"
LINE 10:                             label_id = '1,17,40',
                                                ^

2025-06-21 03:29:18,895 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,2"
LINE 10:                             label_id = '18,2',
                                                ^

2025-06-21 03:29:18,895 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,2"
LINE 10:                             label_id = '18,2',
                                                ^

2025-06-21 03:29:19,224 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,18"
LINE 11:                             label_id = '1,18',
                                                ^

2025-06-21 03:29:19,224 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,18"
LINE 11:                             label_id = '1,18',
                                                ^

2025-06-21 03:29:19,336 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,21"
LINE 10:                             label_id = '1,21',
                                                ^

2025-06-21 03:29:19,336 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,21"
LINE 10:                             label_id = '1,21',
                                                ^

2025-06-21 03:29:19,448 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "21,1"
LINE 10:                             label_id = '21,1',
                                                ^

2025-06-21 03:29:19,449 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "21,1"
LINE 10:                             label_id = '21,1',
                                                ^

2025-06-21 03:29:19,673 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:29:19,674 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:29:19,796 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:29:19,796 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:29:19,902 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:29:19,902 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:29:20,012 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:29:20,013 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:29:23,534 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "34,1"
LINE 10:                             label_id = '34,1',
                                                ^

2025-06-21 03:29:23,534 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "34,1"
LINE 10:                             label_id = '34,1',
                                                ^

2025-06-21 03:29:23,757 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,22"
LINE 10:                             label_id = '1,22',
                                                ^

2025-06-21 03:29:23,757 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,22"
LINE 10:                             label_id = '1,22',
                                                ^

2025-06-21 03:29:23,868 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:29:23,868 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:29:25,195 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,21"
LINE 10:                             label_id = '1,21',
                                                ^

2025-06-21 03:29:25,195 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,21"
LINE 10:                             label_id = '1,21',
                                                ^

2025-06-21 03:29:25,302 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,17"
LINE 10:                             label_id = '1,17',
                                                ^

2025-06-21 03:29:25,303 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,17"
LINE 10:                             label_id = '1,17',
                                                ^

2025-06-21 03:29:25,413 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,17"
LINE 10:                             label_id = '1,17',
                                                ^

2025-06-21 03:29:25,413 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,17"
LINE 10:                             label_id = '1,17',
                                                ^

2025-06-21 03:29:25,527 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,19"
LINE 11:                             label_id = '1,19',
                                                ^

2025-06-21 03:29:25,527 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,19"
LINE 11:                             label_id = '1,19',
                                                ^

2025-06-21 03:29:25,634 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,19"
LINE 10:                             label_id = '1,19',
                                                ^

2025-06-21 03:29:25,635 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,19"
LINE 10:                             label_id = '1,19',
                                                ^

2025-06-21 03:29:25,751 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,19"
LINE 10:                             label_id = '1,19',
                                                ^

2025-06-21 03:29:25,752 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,19"
LINE 10:                             label_id = '1,19',
                                                ^

2025-06-21 03:29:26,419 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:29:26,419 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:29:26,527 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:29:26,527 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:29:26,634 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "19,1"
LINE 11:                             label_id = '19,1',
                                                ^

2025-06-21 03:29:26,635 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "19,1"
LINE 11:                             label_id = '19,1',
                                                ^

2025-06-21 03:29:27,070 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "19,1"
LINE 10:                             label_id = '19,1',
                                                ^

2025-06-21 03:29:27,071 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "19,1"
LINE 10:                             label_id = '19,1',
                                                ^

2025-06-21 03:29:28,467 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: server closed the connection unexpectedly
	This probably means the server terminated abnormally
	before or while processing the request.

2025-06-21 03:29:28,551 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: server closed the connection unexpectedly
	This probably means the server terminated abnormally
	before or while processing the request.

2025-06-21 03:29:28,750 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 03:36:52,863 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 03:36:53,275 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 4:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 03:36:53,382 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 03:37:08,353 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 03:37:08,512 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 4:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 03:37:08,608 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 03:37:28,012 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,2"
LINE 10:                             label_id = '17,2',
                                                ^

2025-06-21 03:37:28,012 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,2"
LINE 10:                             label_id = '17,2',
                                                ^

2025-06-21 03:37:30,592 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,2"
LINE 10:                             label_id = '17,2',
                                                ^

2025-06-21 03:37:30,592 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,2"
LINE 10:                             label_id = '17,2',
                                                ^

2025-06-21 03:37:35,952 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "21,2"
LINE 10:                             label_id = '21,2',
                                                ^

2025-06-21 03:37:36,654 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "21,2"
LINE 10:                             label_id = '21,2',
                                                ^

2025-06-21 03:37:39,093 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "21,41,2"
LINE 10:                             label_id = '21,41,2',
                                                ^

2025-06-21 03:37:39,093 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "21,41,2"
LINE 10:                             label_id = '21,41,2',
                                                ^

2025-06-21 03:37:46,667 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "20,2"
LINE 10:                             label_id = '20,2',
                                                ^

2025-06-21 03:37:46,667 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "20,2"
LINE 10:                             label_id = '20,2',
                                                ^

2025-06-21 03:37:50,368 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,2"
LINE 10:                             label_id = '17,2',
                                                ^

2025-06-21 03:37:50,368 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,2"
LINE 10:                             label_id = '17,2',
                                                ^

2025-06-21 03:37:56,220 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,2"
LINE 11:                             label_id = '18,2',
                                                ^

2025-06-21 03:37:56,221 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,2"
LINE 11:                             label_id = '18,2',
                                                ^

2025-06-21 03:37:57,054 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "20,2"
LINE 10:                             label_id = '20,2',
                                                ^

2025-06-21 03:37:57,055 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "20,2"
LINE 10:                             label_id = '20,2',
                                                ^

2025-06-21 03:38:01,692 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:38:01,693 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:38:01,800 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "26,18"
LINE 10:                             label_id = '26,18',
                                                ^

2025-06-21 03:38:01,801 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "26,18"
LINE 10:                             label_id = '26,18',
                                                ^

2025-06-21 03:38:01,915 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:38:01,915 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:38:02,133 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:38:02,133 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:38:02,246 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:38:02,247 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:38:02,358 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:38:02,358 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:38:03,373 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:38:03,374 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:38:03,604 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:38:03,604 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:38:03,844 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:38:03,844 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:38:03,963 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:38:03,963 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:38:04,076 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:38:04,076 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:38:04,307 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "26,18"
LINE 10:                             label_id = '26,18',
                                                ^

2025-06-21 03:38:04,308 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "26,18"
LINE 10:                             label_id = '26,18',
                                                ^

2025-06-21 03:38:04,430 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:38:04,431 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:38:04,548 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:38:04,549 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:38:04,770 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:38:04,770 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:38:05,897 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "2,17"
LINE 10:                             label_id = '2,17',
                                                ^

2025-06-21 03:38:05,897 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "2,17"
LINE 10:                             label_id = '2,17',
                                                ^

2025-06-21 03:38:06,229 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "24,30"
LINE 10:                             label_id = '24,30',
                                                ^

2025-06-21 03:38:06,230 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "24,30"
LINE 10:                             label_id = '24,30',
                                                ^

2025-06-21 03:38:06,342 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "24,30"
LINE 10:                             label_id = '24,30',
                                                ^

2025-06-21 03:38:06,343 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "24,30"
LINE 10:                             label_id = '24,30',
                                                ^

2025-06-21 03:38:06,687 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,2"
LINE 10:                             label_id = '17,2',
                                                ^

2025-06-21 03:38:06,688 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,2"
LINE 10:                             label_id = '17,2',
                                                ^

2025-06-21 03:38:07,466 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,30"
LINE 10:                             label_id = '17,30',
                                                ^

2025-06-21 03:38:07,466 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,30"
LINE 10:                             label_id = '17,30',
                                                ^

2025-06-21 03:38:23,874 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,2"
LINE 10:                             label_id = '18,2',
                                                ^

2025-06-21 03:38:23,874 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,2"
LINE 10:                             label_id = '18,2',
                                                ^

2025-06-21 03:38:24,408 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "20,2"
LINE 10:                             label_id = '20,2',
                                                ^

2025-06-21 03:38:24,408 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "20,2"
LINE 10:                             label_id = '20,2',
                                                ^

2025-06-21 03:38:28,800 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "34,2"
LINE 10:                             label_id = '34,2',
                                                ^

2025-06-21 03:38:28,801 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "34,2"
LINE 10:                             label_id = '34,2',
                                                ^

2025-06-21 03:38:46,618 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:38:46,619 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,26"
LINE 10:                             label_id = '18,26',
                                                ^

2025-06-21 03:38:59,779 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "40,41,2"
LINE 16:                             label_id = '40,41,2',
                                                ^

2025-06-21 03:38:59,780 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "40,41,2"
LINE 16:                             label_id = '40,41,2',
                                                ^

2025-06-21 03:39:07,508 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "40,17"
LINE 15:                             label_id = '40,17',
                                                ^

2025-06-21 03:39:07,510 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "40,17"
LINE 15:                             label_id = '40,17',
                                                ^

2025-06-21 03:39:13,361 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "2,22"
LINE 10:                             label_id = '2,22',
                                                ^

2025-06-21 03:39:13,361 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "2,22"
LINE 10:                             label_id = '2,22',
                                                ^

2025-06-21 03:39:13,471 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "2,22"
LINE 10:                             label_id = '2,22',
                                                ^

2025-06-21 03:39:13,471 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "2,22"
LINE 10:                             label_id = '2,22',
                                                ^

2025-06-21 03:39:21,982 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:39:21,983 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:39:22,200 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,22"
LINE 10:                             label_id = '1,22',
                                                ^

2025-06-21 03:39:22,201 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,22"
LINE 10:                             label_id = '1,22',
                                                ^

2025-06-21 03:39:22,312 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,22"
LINE 10:                             label_id = '18,22',
                                                ^

2025-06-21 03:39:22,313 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,22"
LINE 10:                             label_id = '18,22',
                                                ^

2025-06-21 03:39:23,423 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:39:23,424 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:39:23,646 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,43"
LINE 10:                             label_id = '17,43',
                                                ^

2025-06-21 03:39:23,647 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,43"
LINE 10:                             label_id = '17,43',
                                                ^

2025-06-21 03:39:23,983 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "26,18"
LINE 10:                             label_id = '26,18',
                                                ^

2025-06-21 03:39:23,983 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "26,18"
LINE 10:                             label_id = '26,18',
                                                ^

2025-06-21 03:39:24,097 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "26,18"
LINE 10:                             label_id = '26,18',
                                                ^

2025-06-21 03:39:24,098 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "26,18"
LINE 10:                             label_id = '26,18',
                                                ^

2025-06-21 03:39:25,998 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "42,17"
LINE 10:                             label_id = '42,17',
                                                ^

2025-06-21 03:39:25,998 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "42,17"
LINE 10:                             label_id = '42,17',
                                                ^

2025-06-21 03:39:26,107 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "42,17"
LINE 10:                             label_id = '42,17',
                                                ^

2025-06-21 03:39:26,108 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "42,17"
LINE 10:                             label_id = '42,17',
                                                ^

2025-06-21 03:39:26,792 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "28,26,18"
LINE 10:                             label_id = '28,26,18',
                                                ^

2025-06-21 03:39:26,792 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "28,26,18"
LINE 10:                             label_id = '28,26,18',
                                                ^

2025-06-21 03:39:26,900 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "26,18"
LINE 10:                             label_id = '26,18',
                                                ^

2025-06-21 03:39:26,901 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "26,18"
LINE 10:                             label_id = '26,18',
                                                ^

2025-06-21 03:39:28,607 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "26,18"
LINE 10:                             label_id = '26,18',
                                                ^

2025-06-21 03:39:28,607 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "26,18"
LINE 10:                             label_id = '26,18',
                                                ^

2025-06-21 03:39:28,733 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "26,32,18"
LINE 10:                             label_id = '26,32,18',
                                                ^

2025-06-21 03:39:28,733 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "26,32,18"
LINE 10:                             label_id = '26,32,18',
                                                ^

2025-06-21 03:39:28,917 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "32,26,18"
LINE 10:                             label_id = '32,26,18',
                                                ^

2025-06-21 03:39:28,918 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "32,26,18"
LINE 10:                             label_id = '32,26,18',
                                                ^

2025-06-21 03:39:29,026 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "26,18"
LINE 10:                             label_id = '26,18',
                                                ^

2025-06-21 03:39:29,027 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "26,18"
LINE 10:                             label_id = '26,18',
                                                ^

2025-06-21 03:39:29,140 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "26,18"
LINE 10:                             label_id = '26,18',
                                                ^

2025-06-21 03:39:29,140 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "26,18"
LINE 10:                             label_id = '26,18',
                                                ^

2025-06-21 03:39:29,259 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "27,26,18"
LINE 11:                             label_id = '27,26,18',
                                                ^

2025-06-21 03:39:29,260 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "27,26,18"
LINE 11:                             label_id = '27,26,18',
                                                ^

2025-06-21 03:39:29,504 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "26,18"
LINE 12:                             label_id = '26,18',
                                                ^

2025-06-21 03:39:29,505 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "26,18"
LINE 12:                             label_id = '26,18',
                                                ^

2025-06-21 03:39:29,616 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "26,18"
LINE 12:                             label_id = '26,18',
                                                ^

2025-06-21 03:39:29,616 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "26,18"
LINE 12:                             label_id = '26,18',
                                                ^

2025-06-21 03:39:30,139 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:39:30,140 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:39:31,510 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,21"
LINE 10:                             label_id = '1,21',
                                                ^

2025-06-21 03:39:31,511 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,21"
LINE 10:                             label_id = '1,21',
                                                ^

2025-06-21 03:39:31,622 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,21"
LINE 10:                             label_id = '1,21',
                                                ^

2025-06-21 03:39:31,622 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,21"
LINE 10:                             label_id = '1,21',
                                                ^

2025-06-21 03:39:32,089 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,20,41,2"
LINE 10:                             label_id = '1,20,41,2',
                                                ^

2025-06-21 03:39:32,090 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,20,41,2"
LINE 10:                             label_id = '1,20,41,2',
                                                ^

2025-06-21 03:39:34,360 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "19,1"
LINE 11:                             label_id = '19,1',
                                                ^

2025-06-21 03:39:34,360 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "19,1"
LINE 11:                             label_id = '19,1',
                                                ^

2025-06-21 03:39:36,290 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:39:36,290 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:39:38,159 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,17"
LINE 10:                             label_id = '1,17',
                                                ^

2025-06-21 03:39:38,160 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,17"
LINE 10:                             label_id = '1,17',
                                                ^

2025-06-21 03:39:38,349 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,17"
LINE 10:                             label_id = '1,17',
                                                ^

2025-06-21 03:39:38,350 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,17"
LINE 10:                             label_id = '1,17',
                                                ^

2025-06-21 03:39:38,489 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,17,41,2"
LINE 10:                             label_id = '1,17,41,2',
                                                ^

2025-06-21 03:39:38,490 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,17,41,2"
LINE 10:                             label_id = '1,17,41,2',
                                                ^

2025-06-21 03:39:38,597 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,17"
LINE 10:                             label_id = '1,17',
                                                ^

2025-06-21 03:39:38,597 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,17"
LINE 10:                             label_id = '1,17',
                                                ^

2025-06-21 03:39:39,279 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,1"
LINE 10:                             label_id = '18,1',
                                                ^

2025-06-21 03:39:39,281 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,1"
LINE 10:                             label_id = '18,1',
                                                ^

2025-06-21 03:39:39,532 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,1,41,2"
LINE 10:                             label_id = '17,1,41,2',
                                                ^

2025-06-21 03:39:39,532 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,1,41,2"
LINE 10:                             label_id = '17,1,41,2',
                                                ^

2025-06-21 03:39:39,691 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,1"
LINE 11:                             label_id = '17,1',
                                                ^

2025-06-21 03:39:39,692 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,1"
LINE 11:                             label_id = '17,1',
                                                ^

2025-06-21 03:39:40,449 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "20,2"
LINE 10:                             label_id = '20,2',
                                                ^

2025-06-21 03:39:40,449 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "20,2"
LINE 10:                             label_id = '20,2',
                                                ^

2025-06-21 03:39:40,609 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,18,41,2"
LINE 11:                             label_id = '1,18,41,2',
                                                ^

2025-06-21 03:39:40,610 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,18,41,2"
LINE 11:                             label_id = '1,18,41,2',
                                                ^

2025-06-21 03:39:40,750 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,1"
LINE 14:                             label_id = '17,1',
                                                ^

2025-06-21 03:39:40,750 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,1"
LINE 14:                             label_id = '17,1',
                                                ^

2025-06-21 03:39:40,969 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,19"
LINE 10:                             label_id = '1,19',
                                                ^

2025-06-21 03:39:40,970 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,19"
LINE 10:                             label_id = '1,19',
                                                ^

2025-06-21 03:39:41,078 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "20,2"
LINE 10:                             label_id = '20,2',
                                                ^

2025-06-21 03:39:41,078 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "20,2"
LINE 10:                             label_id = '20,2',
                                                ^

2025-06-21 03:39:47,625 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,32"
LINE 11:                             label_id = '18,32',
                                                ^

2025-06-21 03:39:47,626 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,32"
LINE 11:                             label_id = '18,32',
                                                ^

2025-06-21 03:39:47,962 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,32"
LINE 10:                             label_id = '18,32',
                                                ^

2025-06-21 03:39:47,962 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,32"
LINE 10:                             label_id = '18,32',
                                                ^

2025-06-21 03:39:48,075 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,32"
LINE 11:                             label_id = '18,32',
                                                ^

2025-06-21 03:39:48,075 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,32"
LINE 11:                             label_id = '18,32',
                                                ^

2025-06-21 03:39:49,830 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:39:49,830 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:39:49,943 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "34,1"
LINE 10:                             label_id = '34,1',
                                                ^

2025-06-21 03:39:49,944 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "34,1"
LINE 10:                             label_id = '34,1',
                                                ^

2025-06-21 03:39:50,057 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "2,18"
LINE 13:                             label_id = '2,18',
                                                ^

2025-06-21 03:39:50,057 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "2,18"
LINE 13:                             label_id = '2,18',
                                                ^

2025-06-21 03:39:51,086 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "40,41,2"
LINE 10:                             label_id = '40,41,2',
                                                ^

2025-06-21 03:39:51,086 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "40,41,2"
LINE 10:                             label_id = '40,41,2',
                                                ^

2025-06-21 03:39:51,198 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:39:51,199 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:39:51,656 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,17"
LINE 10:                             label_id = '1,17',
                                                ^

2025-06-21 03:39:51,656 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,17"
LINE 10:                             label_id = '1,17',
                                                ^

2025-06-21 03:39:51,766 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,17,41,2"
LINE 10:                             label_id = '1,17,41,2',
                                                ^

2025-06-21 03:39:51,766 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,17,41,2"
LINE 10:                             label_id = '1,17,41,2',
                                                ^

2025-06-21 03:39:51,874 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,17"
LINE 10:                             label_id = '1,17',
                                                ^

2025-06-21 03:39:51,874 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,17"
LINE 10:                             label_id = '1,17',
                                                ^

2025-06-21 03:39:51,983 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:39:51,984 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:39:52,093 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:39:52,094 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:39:52,205 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "20,1"
LINE 10:                             label_id = '20,1',
                                                ^

2025-06-21 03:39:52,205 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "20,1"
LINE 10:                             label_id = '20,1',
                                                ^

2025-06-21 03:39:52,315 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "20,1"
LINE 10:                             label_id = '20,1',
                                                ^

2025-06-21 03:39:52,316 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "20,1"
LINE 10:                             label_id = '20,1',
                                                ^

2025-06-21 03:39:52,428 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "20,1"
LINE 10:                             label_id = '20,1',
                                                ^

2025-06-21 03:39:52,428 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "20,1"
LINE 10:                             label_id = '20,1',
                                                ^

2025-06-21 03:39:52,540 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,1"
LINE 10:                             label_id = '18,1',
                                                ^

2025-06-21 03:39:52,540 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,1"
LINE 10:                             label_id = '18,1',
                                                ^

2025-06-21 03:39:52,648 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,1"
LINE 10:                             label_id = '18,1',
                                                ^

2025-06-21 03:39:52,649 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,1"
LINE 10:                             label_id = '18,1',
                                                ^

2025-06-21 03:39:52,756 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,1"
LINE 10:                             label_id = '18,1',
                                                ^

2025-06-21 03:39:52,757 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,1"
LINE 10:                             label_id = '18,1',
                                                ^

2025-06-21 03:39:52,980 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,1"
LINE 10:                             label_id = '18,1',
                                                ^

2025-06-21 03:39:52,980 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,1"
LINE 10:                             label_id = '18,1',
                                                ^

2025-06-21 03:39:53,564 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "20,1"
LINE 10:                             label_id = '20,1',
                                                ^

2025-06-21 03:39:53,565 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "20,1"
LINE 10:                             label_id = '20,1',
                                                ^

2025-06-21 03:39:53,673 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "20,1"
LINE 10:                             label_id = '20,1',
                                                ^

2025-06-21 03:39:53,674 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "20,1"
LINE 10:                             label_id = '20,1',
                                                ^

2025-06-21 03:39:54,404 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,2,20"
LINE 10:                             label_id = '1,2,20',
                                                ^

2025-06-21 03:39:54,405 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,2,20"
LINE 10:                             label_id = '1,2,20',
                                                ^

2025-06-21 03:39:54,531 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:39:54,531 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:39:54,644 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,2,20"
LINE 10:                             label_id = '1,2,20',
                                                ^

2025-06-21 03:39:54,644 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,2,20"
LINE 10:                             label_id = '1,2,20',
                                                ^

2025-06-21 03:39:54,754 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:39:54,755 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:39:55,208 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:39:55,209 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:39:55,316 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:39:55,316 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:39:55,428 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:39:55,428 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:39:55,552 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:39:55,552 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:39:55,662 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:39:55,663 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:39:55,781 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:39:55,781 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:39:55,894 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:39:55,894 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:39:56,010 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:39:56,010 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:39:56,120 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:39:56,121 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:39:58,314 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:39:58,315 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:39:58,428 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,1"
LINE 10:                             label_id = '18,1',
                                                ^

2025-06-21 03:39:58,428 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,1"
LINE 10:                             label_id = '18,1',
                                                ^

2025-06-21 03:39:58,538 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:39:58,539 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:39:58,647 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,18"
LINE 11:                             label_id = '1,18',
                                                ^

2025-06-21 03:39:58,648 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,18"
LINE 11:                             label_id = '1,18',
                                                ^

2025-06-21 03:39:58,758 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,18"
LINE 11:                             label_id = '1,18',
                                                ^

2025-06-21 03:39:58,758 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,18"
LINE 11:                             label_id = '1,18',
                                                ^

2025-06-21 03:39:58,869 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,18"
LINE 11:                             label_id = '1,18',
                                                ^

2025-06-21 03:39:58,870 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,18"
LINE 11:                             label_id = '1,18',
                                                ^

2025-06-21 03:39:58,983 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,2,40"
LINE 10:                             label_id = '1,2,40',
                                                ^

2025-06-21 03:39:58,984 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,2,40"
LINE 10:                             label_id = '1,2,40',
                                                ^

2025-06-21 03:39:59,099 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,17,40"
LINE 10:                             label_id = '1,17,40',
                                                ^

2025-06-21 03:39:59,099 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,17,40"
LINE 10:                             label_id = '1,17,40',
                                                ^

2025-06-21 03:39:59,221 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,17,40"
LINE 10:                             label_id = '1,17,40',
                                                ^

2025-06-21 03:39:59,221 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,17,40"
LINE 10:                             label_id = '1,17,40',
                                                ^

2025-06-21 03:40:00,354 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "18,2"
LINE 10:                             label_id = '18,2',
                                                ^

2025-06-21 03:40:00,354 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "18,2"
LINE 10:                             label_id = '18,2',
                                                ^

2025-06-21 03:40:00,696 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,18"
LINE 11:                             label_id = '1,18',
                                                ^

2025-06-21 03:40:00,697 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,18"
LINE 11:                             label_id = '1,18',
                                                ^

2025-06-21 03:40:00,809 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,21"
LINE 10:                             label_id = '1,21',
                                                ^

2025-06-21 03:40:00,809 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,21"
LINE 10:                             label_id = '1,21',
                                                ^

2025-06-21 03:40:00,933 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "21,1"
LINE 10:                             label_id = '21,1',
                                                ^

2025-06-21 03:40:00,934 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "21,1"
LINE 10:                             label_id = '21,1',
                                                ^

2025-06-21 03:40:01,149 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:40:01,149 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:40:01,268 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:40:01,269 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:40:01,377 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:40:01,378 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:40:01,493 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:40:01,493 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,20"
LINE 10:                             label_id = '1,20',
                                                ^

2025-06-21 03:40:04,242 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "34,1"
LINE 10:                             label_id = '34,1',
                                                ^

2025-06-21 03:40:04,243 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "34,1"
LINE 10:                             label_id = '34,1',
                                                ^

2025-06-21 03:40:04,471 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,22"
LINE 10:                             label_id = '1,22',
                                                ^

2025-06-21 03:40:04,471 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,22"
LINE 10:                             label_id = '1,22',
                                                ^

2025-06-21 03:40:04,580 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:40:04,580 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,18"
LINE 10:                             label_id = '1,18',
                                                ^

2025-06-21 03:40:06,006 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,21"
LINE 10:                             label_id = '1,21',
                                                ^

2025-06-21 03:40:06,006 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,21"
LINE 10:                             label_id = '1,21',
                                                ^

2025-06-21 03:40:06,114 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,17"
LINE 10:                             label_id = '1,17',
                                                ^

2025-06-21 03:40:06,114 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,17"
LINE 10:                             label_id = '1,17',
                                                ^

2025-06-21 03:40:06,227 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,17"
LINE 10:                             label_id = '1,17',
                                                ^

2025-06-21 03:40:06,227 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,17"
LINE 10:                             label_id = '1,17',
                                                ^

2025-06-21 03:40:06,340 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,19"
LINE 11:                             label_id = '1,19',
                                                ^

2025-06-21 03:40:06,341 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,19"
LINE 11:                             label_id = '1,19',
                                                ^

2025-06-21 03:40:06,448 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,19"
LINE 10:                             label_id = '1,19',
                                                ^

2025-06-21 03:40:06,449 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,19"
LINE 10:                             label_id = '1,19',
                                                ^

2025-06-21 03:40:06,565 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "1,19"
LINE 10:                             label_id = '1,19',
                                                ^

2025-06-21 03:40:06,566 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "1,19"
LINE 10:                             label_id = '1,19',
                                                ^

2025-06-21 03:40:07,249 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:40:07,250 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:40:07,368 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:40:07,369 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "17,1"
LINE 10:                             label_id = '17,1',
                                                ^

2025-06-21 03:40:07,479 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "19,1"
LINE 11:                             label_id = '19,1',
                                                ^

2025-06-21 03:40:07,480 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "19,1"
LINE 11:                             label_id = '19,1',
                                                ^

2025-06-21 03:40:07,929 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: invalid input syntax for type integer: "19,1"
LINE 10:                             label_id = '19,1',
                                                ^

2025-06-21 03:40:07,929 - services.sync_service - ERROR - sync_products:142 - 保存产品失败: invalid input syntax for type integer: "19,1"
LINE 10:                             label_id = '19,1',
                                                ^

2025-06-21 03:40:10,219 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 03:40:10,386 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 4:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 03:40:10,485 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 03:59:06,622 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 03:59:06,787 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 4:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 03:59:06,896 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 04:01:24,961 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 04:01:25,120 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 4:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 04:01:25,209 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 04:01:27,734 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 04:01:27,890 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 4:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 04:01:27,975 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 04:01:38,266 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 04:01:38,466 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 4:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 04:01:38,599 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 04:01:51,422 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 04:01:51,577 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 4:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 04:01:51,680 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 04:05:06,826 - utils.api_client - ERROR - _handle_response:87 - HTTP错误: 401 Client Error:  for url: https://zkmall.zktecoiot.com/api/business/info/list?current=1&pageSize=20
2025-06-21 04:05:08,891 - utils.api_client - ERROR - _handle_response:87 - HTTP错误: 401 Client Error:  for url: https://zkmall.zktecoiot.com/api/business/info/list?current=1&pageSize=20
2025-06-21 04:05:10,939 - utils.api_client - ERROR - _handle_response:87 - HTTP错误: 401 Client Error:  for url: https://zkmall.zktecoiot.com/api/business/info/list?current=1&pageSize=20
2025-06-21 04:05:20,832 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: column "source" of relation "categories" does not exist
LINE 3:                     id, name, source, type, level, parent_id...
                                      ^

2025-06-21 04:05:20,833 - services.sync_service - ERROR - sync_categories:196 - 保存分类失败: column "source" of relation "categories" does not exist
LINE 3:                     id, name, source, type, level, parent_id...
                                      ^

2025-06-21 04:05:20,919 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: column "source" of relation "categories" does not exist
LINE 3:                     id, name, source, type, level, parent_id...
                                      ^

2025-06-21 04:05:20,920 - services.sync_service - ERROR - sync_categories:196 - 保存分类失败: column "source" of relation "categories" does not exist
LINE 3:                     id, name, source, type, level, parent_id...
                                      ^

2025-06-21 04:05:21,014 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: column "source" of relation "categories" does not exist
LINE 3:                     id, name, source, type, level, parent_id...
                                      ^

2025-06-21 04:05:21,014 - services.sync_service - ERROR - sync_categories:196 - 保存分类失败: column "source" of relation "categories" does not exist
LINE 3:                     id, name, source, type, level, parent_id...
                                      ^

2025-06-21 04:05:21,101 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: column "source" of relation "categories" does not exist
LINE 3:                     id, name, source, type, level, parent_id...
                                      ^

2025-06-21 04:05:21,101 - services.sync_service - ERROR - sync_categories:196 - 保存分类失败: column "source" of relation "categories" does not exist
LINE 3:                     id, name, source, type, level, parent_id...
                                      ^

2025-06-21 04:05:22,767 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 04:05:22,928 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 4:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 04:05:23,041 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM products WHERE cr...
                                   ^
HINT:  Perhaps you meant to reference the column "products.create_by".

2025-06-21 04:19:32,680 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 04:19:33,115 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 04:19:33,215 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 04:19:35,585 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 04:19:35,744 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 04:19:35,844 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 04:20:01,919 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 04:20:02,080 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 04:20:02,166 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 04:23:09,780 - utils.api_client - ERROR - _handle_response:87 - HTTP错误: 401 Client Error:  for url: https://zkmall.zktecoiot.com/api/business/info/list?current=1&pageSize=20
2025-06-21 04:23:11,835 - utils.api_client - ERROR - _handle_response:87 - HTTP错误: 401 Client Error:  for url: https://zkmall.zktecoiot.com/api/business/info/list?current=1&pageSize=20
2025-06-21 04:23:13,891 - utils.api_client - ERROR - _handle_response:87 - HTTP错误: 401 Client Error:  for url: https://zkmall.zktecoiot.com/api/business/info/list?current=1&pageSize=20
2025-06-21 04:23:20,781 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: column "source" of relation "categories" does not exist
LINE 3:                     id, name, source, type, level, parent_id...
                                      ^

2025-06-21 04:23:20,782 - services.sync_service - ERROR - sync_categories:196 - 保存分类失败: column "source" of relation "categories" does not exist
LINE 3:                     id, name, source, type, level, parent_id...
                                      ^

2025-06-21 04:23:20,881 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: column "source" of relation "categories" does not exist
LINE 3:                     id, name, source, type, level, parent_id...
                                      ^

2025-06-21 04:23:20,881 - services.sync_service - ERROR - sync_categories:196 - 保存分类失败: column "source" of relation "categories" does not exist
LINE 3:                     id, name, source, type, level, parent_id...
                                      ^

2025-06-21 04:23:20,961 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: column "source" of relation "categories" does not exist
LINE 3:                     id, name, source, type, level, parent_id...
                                      ^

2025-06-21 04:23:20,962 - services.sync_service - ERROR - sync_categories:196 - 保存分类失败: column "source" of relation "categories" does not exist
LINE 3:                     id, name, source, type, level, parent_id...
                                      ^

2025-06-21 04:23:21,049 - services.sync_service - ERROR - _execute_with_connection:55 - 数据库操作失败: column "source" of relation "categories" does not exist
LINE 3:                     id, name, source, type, level, parent_id...
                                      ^

2025-06-21 04:23:21,050 - services.sync_service - ERROR - sync_categories:196 - 保存分类失败: column "source" of relation "categories" does not exist
LINE 3:                     id, name, source, type, level, parent_id...
                                      ^

2025-06-21 04:23:22,117 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 04:23:22,369 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 04:23:22,456 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:22:24,690 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:22:25,139 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:22:25,253 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:22:31,288 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:22:31,482 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:22:31,613 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:22:37,363 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:22:37,610 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:22:37,772 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:35:09,753 - utils.database - ERROR - _validate_connection:279 - 连接验证失败: server closed the connection unexpectedly
	This probably means the server terminated abnormally
	before or while processing the request.

2025-06-21 08:35:09,753 - utils.database - ERROR - get_connection:268 - 获取数据库连接失败: server closed the connection unexpectedly
	This probably means the server terminated abnormally
	before or while processing the request.

2025-06-21 08:35:09,758 - utils.database - ERROR - _validate_connection:279 - 连接验证失败: server closed the connection unexpectedly
	This probably means the server terminated abnormally
	before or while processing the request.

2025-06-21 08:35:09,759 - utils.database - ERROR - get_connection:268 - 获取数据库连接失败: server closed the connection unexpectedly
	This probably means the server terminated abnormally
	before or while processing the request.

2025-06-21 08:35:09,759 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: server closed the connection unexpectedly
	This probably means the server terminated abnormally
	before or while processing the request.

2025-06-21 08:35:10,051 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:35:10,166 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:36:26,350 - utils.auth - ERROR - login:88 - 登录失败，API返回: {'msg': '用户不存在/密码错误', 'code': 500}
2025-06-21 08:36:46,120 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:36:47,295 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:36:47,463 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:36:48,387 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:36:48,570 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:36:48,704 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:37:03,489 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:37:03,677 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:37:03,801 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:39:29,285 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:39:29,473 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:39:29,595 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:39:57,945 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:39:58,127 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:39:58,252 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:41:21,982 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:41:22,169 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:41:22,286 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:41:30,228 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:41:30,414 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:41:30,557 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:41:32,741 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:41:32,926 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:41:33,050 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:42:19,670 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:42:19,853 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:42:19,977 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:42:29,006 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:42:29,191 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:42:29,313 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:48:11,133 - __main__ - ERROR - generate_performance_data:664 - 获取性能监控数据失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:48:11,308 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: column "created_at" does not exist
LINE 6:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 08:48:11,421 - __main__ - ERROR - get_data_trend:855 - 获取数据趋势失败: column "created_at" does not exist
LINE 8:                     SELECT created_at FROM categories WHERE ...
                                   ^
HINT:  There is a column named "created_at" in table "*SELECT* 1", but it cannot be referenced from this part of the query.

2025-06-21 09:19:20,745 - __main__ - ERROR - get_sync_statistics:804 - 获取同步统计信息失败: 0
2025-06-21 09:24:05,179 - __main__ - ERROR - get_sync_statistics:803 - 获取同步统计信息失败: 0
2025-06-21 09:51:28,425 - __main__ - ERROR - get_sync_statistics:805 - 获取同步统计信息失败: 0
2025-06-21 09:51:31,414 - __main__ - ERROR - get_sync_statistics:805 - 获取同步统计信息失败: 0
2025-06-21 09:51:37,735 - __main__ - ERROR - get_sync_statistics:805 - 获取同步统计信息失败: 0
2025-06-21 09:51:40,670 - __main__ - ERROR - get_sync_statistics:805 - 获取同步统计信息失败: 0
2025-06-21 10:00:17,595 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 10:00:24,205 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:00:24,206 - services.sync_service - ERROR - _update_sync_status:1010 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:00:24,870 - services.sync_service - ERROR - sync_products:243 - 同步产品数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:00:24,870 - services.sync_service - ERROR - sync_products:243 - 同步产品数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:00:24,874 - services.sync_service - ERROR - sync_products:243 - 同步产品数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:00:24,875 - services.sync_service - ERROR - sync_products:243 - 同步产品数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:00:24,875 - services.sync_service - ERROR - sync_products:243 - 同步产品数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:00:24,875 - services.sync_service - ERROR - sync_products:243 - 同步产品数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:00:24,876 - services.sync_service - ERROR - sync_products:243 - 同步产品数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:00:24,876 - services.sync_service - ERROR - sync_products:243 - 同步产品数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:00:24,877 - services.sync_service - ERROR - sync_products:243 - 同步产品数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:00:24,877 - services.sync_service - ERROR - sync_products:243 - 同步产品数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:00:25,003 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 10:00:25,004 - services.sync_service - ERROR - _update_sync_status:1010 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 10:00:39,856 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:00:39,857 - services.sync_service - ERROR - _update_sync_status:1010 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:00:50,437 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:00:50,437 - services.sync_service - ERROR - _update_sync_status:1010 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:00:50,662 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:00:50,662 - services.sync_service - ERROR - _update_sync_status:1010 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:01:11,076 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:01:11,077 - services.sync_service - ERROR - _update_sync_status:1010 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:01:11,299 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:01:11,300 - services.sync_service - ERROR - _update_sync_status:1010 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:01:12,231 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:12,231 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:12,231 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:12,231 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:12,232 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:12,232 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:12,232 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:12,233 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:12,233 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:12,233 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:12,234 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:12,234 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:12,234 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:12,234 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:12,235 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:12,236 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:12,236 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:12,236 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:12,237 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:12,237 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:12,358 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 10:01:12,360 - services.sync_service - ERROR - _update_sync_status:1010 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 10:01:12,587 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:01:12,588 - services.sync_service - ERROR - _update_sync_status:1010 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:01:12,832 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:01:12,832 - services.sync_service - ERROR - _update_sync_status:1010 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:01:13,053 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:01:13,053 - services.sync_service - ERROR - _update_sync_status:1010 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:01:13,228 - services.sync_service - ERROR - sync_information:718 - 同步资讯数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:13,228 - services.sync_service - ERROR - sync_information:718 - 同步资讯数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:13,228 - services.sync_service - ERROR - sync_information:718 - 同步资讯数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:13,229 - services.sync_service - ERROR - sync_information:718 - 同步资讯数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:13,229 - services.sync_service - ERROR - sync_information:718 - 同步资讯数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:13,229 - services.sync_service - ERROR - sync_information:718 - 同步资讯数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:13,229 - services.sync_service - ERROR - sync_information:718 - 同步资讯数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:13,229 - services.sync_service - ERROR - sync_information:718 - 同步资讯数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:13,230 - services.sync_service - ERROR - sync_information:718 - 同步资讯数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:13,230 - services.sync_service - ERROR - sync_information:718 - 同步资讯数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:13,230 - services.sync_service - ERROR - sync_information:718 - 同步资讯数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:13,230 - services.sync_service - ERROR - sync_information:718 - 同步资讯数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:13,231 - services.sync_service - ERROR - sync_information:718 - 同步资讯数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:13,231 - services.sync_service - ERROR - sync_information:718 - 同步资讯数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:13,231 - services.sync_service - ERROR - sync_information:718 - 同步资讯数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:13,231 - services.sync_service - ERROR - sync_information:718 - 同步资讯数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:13,231 - services.sync_service - ERROR - sync_information:718 - 同步资讯数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:13,232 - services.sync_service - ERROR - sync_information:718 - 同步资讯数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:13,232 - services.sync_service - ERROR - sync_information:718 - 同步资讯数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:13,232 - services.sync_service - ERROR - sync_information:718 - 同步资讯数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:13,354 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 10:01:13,354 - services.sync_service - ERROR - _update_sync_status:1010 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 10:01:13,575 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:01:13,577 - services.sync_service - ERROR - _update_sync_status:1010 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:01:15,665 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 10:01:16,251 - services.sync_service - ERROR - sync_distribution_orders:835 - 同步配单数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:16,251 - services.sync_service - ERROR - sync_distribution_orders:835 - 同步配单数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:16,252 - services.sync_service - ERROR - sync_distribution_orders:835 - 同步配单数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:16,252 - services.sync_service - ERROR - sync_distribution_orders:835 - 同步配单数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:16,252 - services.sync_service - ERROR - sync_distribution_orders:835 - 同步配单数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:16,252 - services.sync_service - ERROR - sync_distribution_orders:835 - 同步配单数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:16,252 - services.sync_service - ERROR - sync_distribution_orders:835 - 同步配单数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:16,252 - services.sync_service - ERROR - sync_distribution_orders:835 - 同步配单数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:16,253 - services.sync_service - ERROR - sync_distribution_orders:835 - 同步配单数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:16,253 - services.sync_service - ERROR - sync_distribution_orders:835 - 同步配单数据失败: 'ApiResponseNormalizer' object has no attribute 'normalize'
2025-06-21 10:01:16,490 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 10:01:16,490 - services.sync_service - ERROR - _update_sync_status:1010 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 10:50:28,914 - utils.auth - ERROR - login:88 - 登录失败，API返回: {'msg': '用户不存在/密码错误', 'code': 500}
2025-06-21 10:50:34,058 - utils.auth - ERROR - login:88 - 登录失败，API返回: {'msg': '用户不存在/密码错误', 'code': 500}
2025-06-21 10:50:55,212 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 10:51:01,304 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 10:51:04,151 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 10:51:25,231 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 10:51:38,407 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 10:51:41,107 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 10:51:43,683 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 10:51:46,473 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 10:51:48,413 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 10:51:50,108 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:51:50,108 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:51:51,042 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 10,
                                ^

2025-06-21 10:51:51,043 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 10,
                                ^

2025-06-21 10:51:51,328 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 20,
                                ^

2025-06-21 10:51:51,328 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 20,
                                ^

2025-06-21 10:51:51,606 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 30,
                                ^

2025-06-21 10:51:51,606 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 30,
                                ^

2025-06-21 10:51:51,895 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 40,
                                ^

2025-06-21 10:51:51,896 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 40,
                                ^

2025-06-21 10:51:52,186 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 50,
                                ^

2025-06-21 10:51:52,186 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 50,
                                ^

2025-06-21 10:51:52,705 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 60,
                                ^

2025-06-21 10:51:52,706 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 60,
                                ^

2025-06-21 10:51:52,980 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 70,
                                ^

2025-06-21 10:51:52,980 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 70,
                                ^

2025-06-21 10:51:53,523 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 80,
                                ^

2025-06-21 10:51:53,523 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 80,
                                ^

2025-06-21 10:51:53,795 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 90,
                                ^

2025-06-21 10:51:53,796 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 90,
                                ^

2025-06-21 10:51:54,455 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 10:51:54,456 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 10:51:54,583 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 10:51:54,584 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 10:52:09,345 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:52:09,345 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:52:20,391 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 10:52:48,997 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 10:52:50,686 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:52:50,687 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:52:52,034 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 10,
                                ^

2025-06-21 10:52:52,034 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 10,
                                ^

2025-06-21 10:52:53,062 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 20,
                                ^

2025-06-21 10:52:53,063 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 20,
                                ^

2025-06-21 10:52:53,396 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 30,
                                ^

2025-06-21 10:52:53,396 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 30,
                                ^

2025-06-21 10:52:53,668 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 40,
                                ^

2025-06-21 10:52:53,669 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 40,
                                ^

2025-06-21 10:52:53,940 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 50,
                                ^

2025-06-21 10:52:53,940 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 50,
                                ^

2025-06-21 10:52:54,214 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 60,
                                ^

2025-06-21 10:52:54,215 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 60,
                                ^

2025-06-21 10:52:54,550 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 70,
                                ^

2025-06-21 10:52:54,550 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 70,
                                ^

2025-06-21 10:52:54,842 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 80,
                                ^

2025-06-21 10:52:54,844 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 80,
                                ^

2025-06-21 10:52:55,372 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 90,
                                ^

2025-06-21 10:52:55,373 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 90,
                                ^

2025-06-21 10:52:55,648 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 10:52:55,649 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 10:52:55,773 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 10:52:55,773 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 10:53:10,147 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:53:10,148 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:53:22,478 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:53:22,478 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:53:22,704 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:53:22,705 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:53:34,031 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:53:34,032 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:53:34,253 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:53:34,254 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:53:35,348 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:35,349 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:35,755 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:35,755 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:35,904 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:35,904 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:36,055 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:36,055 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:36,202 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:36,202 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:36,600 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:36,601 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:36,749 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:36,749 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:36,918 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:36,918 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:37,081 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:37,082 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:37,234 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:37,235 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:37,384 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:37,385 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:37,533 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:37,534 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:37,689 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:37,689 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:37,837 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:37,838 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:37,983 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:37,984 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:38,137 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:38,138 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:38,289 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:38,290 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:38,439 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:38,439 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:38,587 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:38,587 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:38,744 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:38,744 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 10:53:38,872 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 10:53:38,873 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 10:53:39,103 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:53:39,104 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:53:39,377 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:53:39,378 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:53:39,851 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:53:39,851 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:53:40,269 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:40,270 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:40,363 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:40,363 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:40,458 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:40,459 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:40,549 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:40,549 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:40,637 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:40,638 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:40,736 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:40,740 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:40,836 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:40,836 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:41,172 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:41,173 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:41,271 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:41,272 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:41,359 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:41,359 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:41,458 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:41,459 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:41,549 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:41,549 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:41,645 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:41,645 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:42,004 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:42,005 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:42,093 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:42,094 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:42,183 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:42,184 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:42,271 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:42,272 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:42,359 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:42,360 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:42,448 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:42,449 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:42,550 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:42,551 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 10:53:42,667 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 10:53:42,668 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 10:53:42,894 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:53:42,894 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 10:53:45,777 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 10:53:45,777 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 10:53:45,935 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 10:53:45,936 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 10:53:46,087 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 10:53:46,088 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 10:53:46,236 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 10:53:46,237 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 10:53:46,391 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 10:53:46,391 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 10:53:46,803 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 10:53:46,804 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 10:53:46,961 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 10:53:46,963 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 10:53:47,385 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 10:53:47,385 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 10:53:47,539 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 10:53:47,539 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 10:53:47,698 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 10:53:47,699 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 10:53:48,077 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 10:53:48,078 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 10:53:50,077 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 12:03:25,859 - utils.database - ERROR - _validate_connection:279 - 连接验证失败: server closed the connection unexpectedly
	This probably means the server terminated abnormally
	before or while processing the request.

2025-06-21 12:03:25,860 - utils.database - ERROR - get_connection:268 - 获取数据库连接失败: server closed the connection unexpectedly
	This probably means the server terminated abnormally
	before or while processing the request.

2025-06-21 12:03:25,862 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: server closed the connection unexpectedly
	This probably means the server terminated abnormally
	before or while processing the request.

2025-06-21 12:03:25,863 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: server closed the connection unexpectedly
	This probably means the server terminated abnormally
	before or while processing the request.

2025-06-21 12:03:27,262 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 10,
                                ^

2025-06-21 12:03:27,263 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 10,
                                ^

2025-06-21 12:03:27,603 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 20,
                                ^

2025-06-21 12:03:27,604 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 20,
                                ^

2025-06-21 12:03:27,886 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 30,
                                ^

2025-06-21 12:03:27,886 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 30,
                                ^

2025-06-21 12:03:28,156 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 40,
                                ^

2025-06-21 12:03:28,157 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 40,
                                ^

2025-06-21 12:03:28,680 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 50,
                                ^

2025-06-21 12:03:28,680 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 50,
                                ^

2025-06-21 12:03:28,951 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 60,
                                ^

2025-06-21 12:03:28,951 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 60,
                                ^

2025-06-21 12:03:29,233 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 70,
                                ^

2025-06-21 12:03:29,233 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 70,
                                ^

2025-06-21 12:03:30,010 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 80,
                                ^

2025-06-21 12:03:30,010 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 80,
                                ^

2025-06-21 12:03:30,576 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 90,
                                ^

2025-06-21 12:03:30,576 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 90,
                                ^

2025-06-21 12:03:30,854 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 12:03:30,854 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 12:03:30,974 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 12:03:30,975 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 12:03:49,176 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:03:49,177 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:04:03,945 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:04:03,946 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:04:04,163 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:04:04,164 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:04:22,512 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:04:22,512 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:04:22,729 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:04:22,729 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:04:24,149 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:24,149 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:24,317 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:24,318 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:24,469 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:24,469 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:25,228 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:25,229 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:25,377 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:25,378 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:25,787 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:25,788 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:25,943 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:25,944 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:26,094 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:26,096 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:26,238 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:26,239 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:26,384 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:26,384 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:26,821 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:26,823 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:26,974 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:26,975 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:27,702 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:27,703 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:27,857 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:27,859 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:28,010 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:28,011 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:28,401 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:28,401 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:28,548 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:28,549 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:28,696 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:28,697 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:29,093 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:29,093 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:29,495 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:29,496 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:04:30,363 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 12:04:30,363 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 12:04:30,583 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:04:30,583 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:04:31,091 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:04:31,092 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:04:31,312 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:04:31,312 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:04:31,586 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:31,587 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:31,712 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:31,713 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:31,801 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:31,801 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:31,887 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:31,887 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:31,988 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:31,989 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:32,076 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:32,076 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:32,165 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:32,165 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:32,252 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:32,253 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:32,587 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:32,588 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:32,688 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:32,688 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:32,775 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:32,775 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:32,870 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:32,870 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:32,958 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:32,958 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:33,050 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:33,050 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:33,741 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:33,741 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:33,830 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:33,830 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:33,919 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:33,920 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:34,008 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:34,008 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:34,110 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:34,110 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:34,453 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:34,453 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:04:34,839 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 12:04:34,840 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 12:04:35,333 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:04:35,335 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:04:38,565 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:04:38,566 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:04:38,762 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:04:38,762 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:04:38,910 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:04:38,910 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:04:39,354 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:04:39,354 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:04:39,509 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:04:39,509 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:04:39,934 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:04:39,934 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:04:40,090 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:04:40,091 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:04:40,253 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:04:40,254 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:04:40,662 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:04:40,662 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:04:40,808 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:04:40,809 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:04:40,927 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 12:04:40,927 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 12:04:42,827 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 12:07:25,071 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 12:08:05,908 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 12:45:57,946 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 12:46:02,546 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 12:46:04,192 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 12:46:08,836 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 12:46:21,532 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 12:46:24,350 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 12:46:27,065 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 12:46:30,508 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 12:46:30,615 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:46:30,616 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:46:32,075 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 10,
                                ^

2025-06-21 12:46:32,076 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 10,
                                ^

2025-06-21 12:46:32,604 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 20,
                                ^

2025-06-21 12:46:32,605 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 20,
                                ^

2025-06-21 12:46:32,874 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 30,
                                ^

2025-06-21 12:46:32,874 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 30,
                                ^

2025-06-21 12:46:33,951 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 40,
                                ^

2025-06-21 12:46:33,952 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 40,
                                ^

2025-06-21 12:46:34,543 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 50,
                                ^

2025-06-21 12:46:34,544 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 50,
                                ^

2025-06-21 12:46:35,314 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 60,
                                ^

2025-06-21 12:46:35,314 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 60,
                                ^

2025-06-21 12:46:35,834 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 70,
                                ^

2025-06-21 12:46:35,835 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 70,
                                ^

2025-06-21 12:46:36,158 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 80,
                                ^

2025-06-21 12:46:36,158 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 80,
                                ^

2025-06-21 12:46:36,422 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 90,
                                ^

2025-06-21 12:46:36,422 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 90,
                                ^

2025-06-21 12:46:36,688 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 12:46:36,690 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 12:46:36,802 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 12:46:36,803 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 12:46:51,994 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:46:51,995 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:47:03,121 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:47:03,121 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:47:03,335 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:47:03,336 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:47:14,164 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:47:14,164 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:47:14,948 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:47:14,949 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:47:16,126 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:16,127 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:16,300 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:16,301 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:16,451 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:16,452 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:16,848 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:16,848 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:17,261 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:17,262 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:17,670 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:17,672 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:17,815 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:17,815 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:18,214 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:24,816 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:25,214 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:25,215 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:26,117 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:26,118 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:26,260 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:26,260 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:26,671 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:26,672 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:27,162 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:27,162 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:27,350 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:27,351 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:27,545 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:27,545 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:27,741 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:27,741 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:27,916 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:27,917 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:28,360 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:28,360 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:28,565 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:28,567 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:28,771 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:28,773 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "details" of relation "cases" does not exist
LINE 3: ...                 id, name, introduction, content, details, i...
                                                             ^

2025-06-21 12:47:28,942 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 12:47:28,943 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 12:47:29,204 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:47:29,205 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:47:29,760 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:47:29,761 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:47:30,328 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:47:30,328 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:47:30,645 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:30,645 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:30,983 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:30,983 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:31,188 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:31,189 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:31,294 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:31,294 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:31,779 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:31,780 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:31,984 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:31,985 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:32,135 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:32,135 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:32,222 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:32,223 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:33,169 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:33,170 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:33,262 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:33,263 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:33,379 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:33,379 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:33,480 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:33,481 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:33,834 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:33,834 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:33,926 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:33,926 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:34,264 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:34,265 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:34,359 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:34,359 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:34,450 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:34,450 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:34,656 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:34,656 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:34,754 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:34,754 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:35,416 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:35,417 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:47:35,532 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 12:47:35,532 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 12:47:35,752 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:47:35,752 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:47:39,024 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:47:39,025 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:47:39,176 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:47:39,176 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:47:39,320 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:47:39,321 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:47:39,734 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:47:39,734 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:47:40,138 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:47:40,138 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:47:40,386 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:47:40,387 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:47:40,799 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:47:40,799 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:47:41,202 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:47:41,203 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:47:41,594 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:47:41,594 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:47:41,739 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:47:41,740 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:47:42,107 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 12:47:42,107 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 12:47:44,971 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 12:49:50,715 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 12:50:02,683 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 12:50:17,153 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 12:50:17,712 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:50:17,713 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:50:18,602 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 10,
                                ^

2025-06-21 12:50:18,602 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 10,
                                ^

2025-06-21 12:50:18,950 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 20,
                                ^

2025-06-21 12:50:18,952 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 20,
                                ^

2025-06-21 12:50:19,494 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 30,
                                ^

2025-06-21 12:50:19,494 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 30,
                                ^

2025-06-21 12:50:20,005 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 40,
                                ^

2025-06-21 12:50:20,006 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 40,
                                ^

2025-06-21 12:50:20,260 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 50,
                                ^

2025-06-21 12:50:20,260 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 50,
                                ^

2025-06-21 12:50:21,101 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 60,
                                ^

2025-06-21 12:50:21,101 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 60,
                                ^

2025-06-21 12:50:21,633 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 70,
                                ^

2025-06-21 12:50:21,633 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 70,
                                ^

2025-06-21 12:50:22,944 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 80,
                                ^

2025-06-21 12:50:22,945 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 80,
                                ^

2025-06-21 12:50:23,463 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 90,
                                ^

2025-06-21 12:50:23,464 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 90,
                                ^

2025-06-21 12:50:23,968 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 12:50:23,969 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 12:50:24,111 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 12:50:24,111 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 12:50:40,526 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:50:40,527 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:50:51,183 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:50:51,184 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:50:51,659 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:50:51,659 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:51:03,709 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:51:03,710 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:51:03,926 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:51:03,926 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:51:05,036 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:05,037 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:05,216 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:05,217 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:05,619 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:05,619 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:06,028 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:06,028 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:06,442 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:06,442 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:07,096 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:07,097 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:07,495 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:07,496 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:08,421 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:08,422 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:08,820 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:08,820 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:08,965 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:08,966 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:09,122 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:09,123 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:09,270 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:09,271 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:09,505 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:09,506 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:09,911 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:16,797 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:16,995 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:16,995 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:17,421 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:17,421 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:17,564 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:17,564 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:17,973 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:17,974 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:18,136 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:18,137 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:18,846 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:18,847 - services.sync_service - ERROR - sync_cases:476 - 同步案例数据失败: column "images" of relation "cases" does not exist
LINE 3: ...on, content, details, img, banner, small_img, video, images,
                                                                ^

2025-06-21 12:51:18,958 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 12:51:18,958 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 12:51:19,168 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:51:19,169 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:51:19,442 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:51:19,443 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:51:19,910 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:51:19,910 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:51:20,467 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:20,467 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:20,556 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:20,556 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:20,666 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:20,667 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:20,752 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:20,752 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:20,852 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:20,852 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:20,937 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:20,938 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:21,030 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:21,031 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:21,372 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:21,373 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:21,459 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:21,460 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:21,545 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:21,546 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:22,199 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:22,200 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:22,294 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:22,295 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:22,383 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:22,384 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:22,467 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:22,468 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:22,558 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:22,558 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:22,643 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:22,644 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:22,989 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:22,989 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:23,078 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:23,078 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:23,418 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:23,419 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:23,501 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:23,502 - services.sync_service - ERROR - sync_information:702 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 12:51:23,621 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 12:51:23,621 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 12:51:24,083 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:51:24,084 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 0,
                                ^

2025-06-21 12:51:27,570 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:51:27,571 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:51:28,023 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:51:28,024 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:51:28,204 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:51:28,205 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:51:28,384 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:51:28,385 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:51:28,827 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:51:28,827 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:51:29,645 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:51:29,646 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:51:30,344 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:51:30,345 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:51:30,488 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:51:30,489 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:51:30,681 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:51:30,681 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:51:31,226 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:51:31,226 - services.sync_service - ERROR - sync_distribution_orders:815 - 同步配单数据失败: column "type" of relation "distribution_orders" does not exist
LINE 3:                             id, status, name, type, customer...
                                                      ^

2025-06-21 12:51:31,350 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 12:51:31,350 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: column "progress" of relation "sync_status" does not exist
LINE 9:                         progress = 100,
                                ^

2025-06-21 12:51:34,183 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 13:01:50,351 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 13:09:41,326 - utils.database - ERROR - _validate_connection:279 - 连接验证失败: server closed the connection unexpectedly
	This probably means the server terminated abnormally
	before or while processing the request.

2025-06-21 13:09:41,326 - utils.database - ERROR - get_connection:268 - 获取数据库连接失败: server closed the connection unexpectedly
	This probably means the server terminated abnormally
	before or while processing the request.

2025-06-21 13:09:41,327 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: server closed the connection unexpectedly
	This probably means the server terminated abnormally
	before or while processing the request.

2025-06-21 13:09:41,327 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: server closed the connection unexpectedly
	This probably means the server terminated abnormally
	before or while processing the request.

2025-06-21 13:09:42,338 - utils.database - ERROR - _validate_connection:279 - 连接验证失败: server closed the connection unexpectedly
	This probably means the server terminated abnormally
	before or while processing the request.

2025-06-21 13:09:42,339 - utils.database - ERROR - get_connection:268 - 获取数据库连接失败: server closed the connection unexpectedly
	This probably means the server terminated abnormally
	before or while processing the request.

2025-06-21 13:09:42,340 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: server closed the connection unexpectedly
	This probably means the server terminated abnormally
	before or while processing the request.

2025-06-21 13:09:42,341 - services.sync_service - ERROR - sync_products:239 - 同步产品数据失败: server closed the connection unexpectedly
	This probably means the server terminated abnormally
	before or while processing the request.

2025-06-21 13:10:09,138 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: value too long for type character varying(20)

2025-06-21 13:10:09,138 - services.sync_service - ERROR - _update_sync_status:990 - 更新同步状态失败: value too long for type character varying(20)

2025-06-21 13:10:11,515 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 13:14:49,665 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 13:25:09,731 - utils.auth - ERROR - login:88 - 登录失败，API返回: {'msg': '用户不存在/密码错误', 'code': 500}
2025-06-21 13:25:50,636 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 13:26:00,557 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 13:33:50,897 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 13:33:58,943 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 13:34:10,801 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 13:35:10,493 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:10,494 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:10,771 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:10,772 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:11,199 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:15,902 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:16,051 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:16,052 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:16,444 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:16,444 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:16,589 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:16,589 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:16,725 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:16,726 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:17,439 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:17,440 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:17,583 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:17,584 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:17,733 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:17,733 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:17,871 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:17,871 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:18,018 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:18,019 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:18,159 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:18,159 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:18,305 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:18,306 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:18,948 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:18,948 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:19,339 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:19,340 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:19,485 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:19,485 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:19,866 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:19,867 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:20,017 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:20,017 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:20,155 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:20,155 - services.sync_service - ERROR - sync_cases:484 - 同步案例数据失败: column "product_id" of relation "cases" does not exist
LINE 4:                             category_id, product_id, product...
                                                 ^

2025-06-21 13:35:20,272 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: value too long for type character varying(20)

2025-06-21 13:35:20,272 - services.sync_service - ERROR - _update_sync_status:998 - 更新同步状态失败: value too long for type character varying(20)

2025-06-21 13:35:21,277 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:21,278 - services.sync_service - ERROR - sync_information:710 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:21,363 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:21,363 - services.sync_service - ERROR - sync_information:710 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:21,447 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:21,448 - services.sync_service - ERROR - sync_information:710 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:21,529 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:21,529 - services.sync_service - ERROR - sync_information:710 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:21,861 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:21,861 - services.sync_service - ERROR - sync_information:710 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:21,942 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:21,943 - services.sync_service - ERROR - sync_information:710 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:22,024 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:22,025 - services.sync_service - ERROR - sync_information:710 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:22,116 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:22,117 - services.sync_service - ERROR - sync_information:710 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:22,448 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:22,448 - services.sync_service - ERROR - sync_information:710 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:22,539 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:22,539 - services.sync_service - ERROR - sync_information:710 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:22,627 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:22,628 - services.sync_service - ERROR - sync_information:710 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:22,711 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:22,711 - services.sync_service - ERROR - sync_information:710 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:22,801 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:22,801 - services.sync_service - ERROR - sync_information:710 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:23,231 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:23,231 - services.sync_service - ERROR - sync_information:710 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:23,318 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:23,318 - services.sync_service - ERROR - sync_information:710 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:24,518 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:27,726 - services.sync_service - ERROR - sync_information:710 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:27,818 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:27,819 - services.sync_service - ERROR - sync_information:710 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:27,925 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:27,925 - services.sync_service - ERROR - sync_information:710 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:28,006 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:28,007 - services.sync_service - ERROR - sync_information:710 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:28,383 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:28,383 - services.sync_service - ERROR - sync_information:710 - 同步资讯数据失败: 'NoneType' object has no attribute 'get'
2025-06-21 13:35:28,548 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: value too long for type character varying(20)

2025-06-21 13:35:28,549 - services.sync_service - ERROR - _update_sync_status:998 - 更新同步状态失败: value too long for type character varying(20)

2025-06-21 13:35:31,900 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 13:35:31,902 - services.sync_service - ERROR - sync_distribution_orders:823 - 同步配单数据失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 13:35:32,050 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 13:35:32,051 - services.sync_service - ERROR - sync_distribution_orders:823 - 同步配单数据失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 13:35:32,210 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 13:35:32,211 - services.sync_service - ERROR - sync_distribution_orders:823 - 同步配单数据失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 13:35:32,365 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 13:35:32,365 - services.sync_service - ERROR - sync_distribution_orders:823 - 同步配单数据失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 13:35:33,297 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 13:35:33,299 - services.sync_service - ERROR - sync_distribution_orders:823 - 同步配单数据失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 13:35:33,963 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 13:35:33,967 - services.sync_service - ERROR - sync_distribution_orders:823 - 同步配单数据失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 13:35:34,113 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 13:35:34,114 - services.sync_service - ERROR - sync_distribution_orders:823 - 同步配单数据失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 13:35:34,274 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 13:35:34,274 - services.sync_service - ERROR - sync_distribution_orders:823 - 同步配单数据失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 13:35:34,412 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 13:35:34,414 - services.sync_service - ERROR - sync_distribution_orders:823 - 同步配单数据失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 13:35:34,560 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 13:35:34,561 - services.sync_service - ERROR - sync_distribution_orders:823 - 同步配单数据失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 13:35:34,679 - services.sync_service - ERROR - _execute_with_connection:62 - 数据库操作失败: value too long for type character varying(20)

2025-06-21 13:35:34,679 - services.sync_service - ERROR - _update_sync_status:998 - 更新同步状态失败: value too long for type character varying(20)

2025-06-21 13:35:36,712 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 14:07:20,316 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 14:14:54,348 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 14:15:18,631 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 14:15:24,915 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 14:15:35,642 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 14:15:44,560 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 14:17:19,738 - __main__ - ERROR - get_sync_statistics:797 - 获取同步统计信息失败: 0
2025-06-21 14:38:41,453 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 14:38:55,961 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 14:38:58,552 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 14:39:00,341 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 14:39:08,373 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 14:39:11,937 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 14:39:13,942 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 14:39:18,430 - utils.api_response_normalizer - ERROR - _extract_list_data:702 - API返回错误: code=500, message=Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "pcList"
2025-06-21 14:39:25,691 - utils.api_response_normalizer - ERROR - _extract_list_data:702 - API返回错误: code=500, message=Failed to convert value of type 'java.lang.String' to required type 'java.lang.Long'; nested exception is java.lang.NumberFormatException: For input string: "pcList"
2025-06-21 14:39:33,559 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 15:04:56,648 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 15:04:58,999 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 15:05:03,857 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 15:05:36,504 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 15:05:52,819 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 15:06:05,644 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 15:06:08,096 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 15:06:16,473 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 15:08:07,851 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 15:08:34,608 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 15:09:10,979 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 15:09:15,637 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 15:09:19,412 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 15:10:10,144 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:10,145 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:10,290 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:10,290 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:10,698 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:10,698 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:10,871 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:10,871 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:11,021 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:11,021 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:11,172 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:11,172 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:11,320 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:11,321 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:11,479 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:11,480 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:11,625 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:11,626 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:12,533 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:27,860 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:28,286 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:28,287 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:28,685 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:28,686 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:28,829 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:28,830 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:28,982 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:28,983 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:29,131 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:29,132 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:29,281 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:29,281 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:29,429 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:29,430 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:29,819 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:29,820 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:30,219 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:30,220 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:30,367 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:30,367 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:10:30,495 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: value too long for type character varying(20)

2025-06-21 15:10:30,496 - services.sync_service - ERROR - _update_sync_status:1093 - 更新同步状态失败: value too long for type character varying(20)

2025-06-21 15:10:32,467 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 15:10:59,492 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 15:11:05,696 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 15:11:08,984 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 15:11:30,566 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 15:12:21,336 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:21,337 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:21,483 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:21,483 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:21,625 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:21,626 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:22,013 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:22,014 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:22,155 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:22,156 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:22,299 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:22,300 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:22,451 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:22,453 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:22,592 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:22,592 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:22,738 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:22,739 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:23,135 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:23,136 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:23,296 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:23,297 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:23,687 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:23,688 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:23,835 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:23,836 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:23,980 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:23,980 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:24,374 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:24,375 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:24,533 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:24,534 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:24,919 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:24,920 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:25,068 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:25,069 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:25,213 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:25,213 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:25,353 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:25,354 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 15:12:25,473 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: value too long for type character varying(20)

2025-06-21 15:12:25,474 - services.sync_service - ERROR - _update_sync_status:1093 - 更新同步状态失败: value too long for type character varying(20)

2025-06-21 15:12:35,944 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 15:12:35,945 - services.sync_service - ERROR - sync_distribution_orders:918 - 同步配单数据失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 15:12:36,169 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 15:12:36,170 - services.sync_service - ERROR - sync_distribution_orders:918 - 同步配单数据失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 15:12:36,407 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 15:12:36,407 - services.sync_service - ERROR - sync_distribution_orders:918 - 同步配单数据失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 15:12:36,642 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 15:12:36,642 - services.sync_service - ERROR - sync_distribution_orders:918 - 同步配单数据失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 15:12:36,819 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 15:12:36,819 - services.sync_service - ERROR - sync_distribution_orders:918 - 同步配单数据失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 15:12:37,023 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 15:12:37,023 - services.sync_service - ERROR - sync_distribution_orders:918 - 同步配单数据失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 15:12:37,237 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 15:12:37,237 - services.sync_service - ERROR - sync_distribution_orders:918 - 同步配单数据失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 15:12:37,433 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 15:12:37,433 - services.sync_service - ERROR - sync_distribution_orders:918 - 同步配单数据失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 15:12:37,580 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 15:12:37,580 - services.sync_service - ERROR - sync_distribution_orders:918 - 同步配单数据失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 15:12:37,717 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 15:12:37,718 - services.sync_service - ERROR - sync_distribution_orders:918 - 同步配单数据失败: column "company_name" of relation "distribution_orders" does not exist
LINE 3: ...type, customer_name, phone, contacts, company_id, company_na...
                                                             ^

2025-06-21 15:12:37,835 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: value too long for type character varying(20)

2025-06-21 15:12:37,835 - services.sync_service - ERROR - _update_sync_status:1093 - 更新同步状态失败: value too long for type character varying(20)

2025-06-21 15:12:39,721 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 16:18:00,879 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 16:18:06,531 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 16:18:12,242 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 16:19:05,405 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:05,406 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:05,826 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:05,827 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:06,001 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:06,002 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:06,692 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:29,144 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:29,311 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:29,312 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:29,481 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:29,481 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:29,653 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:29,654 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:29,829 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:29,829 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:29,995 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:29,996 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:30,161 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:30,162 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:30,579 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:30,580 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:31,000 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:31,001 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:31,163 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:31,164 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:31,330 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:31,331 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:31,749 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:31,749 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:31,914 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:31,915 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:32,335 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:32,336 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:32,508 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:32,508 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:32,678 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:32,678 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:32,844 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:32,845 - services.sync_service - ERROR - sync_cases:579 - 同步案例数据失败: column "industry" of relation "cases" does not exist
LINE 5: ...                        company_id, company_name, industry, ...
                                                             ^

2025-06-21 16:19:32,977 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: value too long for type character varying(20)

2025-06-21 16:19:32,977 - services.sync_service - ERROR - _update_sync_status:1093 - 更新同步状态失败: value too long for type character varying(20)

2025-06-21 16:19:57,020 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:19:57,021 - services.sync_service - ERROR - sync_distribution_orders:918 - 同步配单数据失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:19:57,463 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:19:57,463 - services.sync_service - ERROR - sync_distribution_orders:918 - 同步配单数据失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:19:57,886 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:20:02,530 - services.sync_service - ERROR - sync_distribution_orders:918 - 同步配单数据失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:20:02,852 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:20:02,852 - services.sync_service - ERROR - sync_distribution_orders:918 - 同步配单数据失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:20:03,079 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:20:03,080 - services.sync_service - ERROR - sync_distribution_orders:918 - 同步配单数据失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:20:03,319 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:20:03,320 - services.sync_service - ERROR - sync_distribution_orders:918 - 同步配单数据失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:20:03,611 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:20:03,612 - services.sync_service - ERROR - sync_distribution_orders:918 - 同步配单数据失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:20:03,904 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:20:03,905 - services.sync_service - ERROR - sync_distribution_orders:918 - 同步配单数据失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:20:04,085 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:20:04,085 - services.sync_service - ERROR - sync_distribution_orders:918 - 同步配单数据失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:20:04,294 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:20:04,296 - services.sync_service - ERROR - sync_distribution_orders:918 - 同步配单数据失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:20:04,537 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: value too long for type character varying(20)

2025-06-21 16:20:04,538 - services.sync_service - ERROR - _update_sync_status:1093 - 更新同步状态失败: value too long for type character varying(20)

2025-06-21 16:20:07,330 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 16:48:45,347 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:48:45,347 - services.sync_service - ERROR - sync_distribution_orders:918 - 同步配单数据失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:48:45,599 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:48:45,600 - services.sync_service - ERROR - sync_distribution_orders:918 - 同步配单数据失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:48:46,038 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:48:46,039 - services.sync_service - ERROR - sync_distribution_orders:918 - 同步配单数据失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:48:46,289 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:48:46,289 - services.sync_service - ERROR - sync_distribution_orders:918 - 同步配单数据失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:48:46,715 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:48:46,715 - services.sync_service - ERROR - sync_distribution_orders:918 - 同步配单数据失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:48:46,971 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:48:46,971 - services.sync_service - ERROR - sync_distribution_orders:918 - 同步配单数据失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:48:47,218 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:48:47,218 - services.sync_service - ERROR - sync_distribution_orders:918 - 同步配单数据失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:48:47,385 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:48:47,385 - services.sync_service - ERROR - sync_distribution_orders:918 - 同步配单数据失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:48:47,814 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:48:47,814 - services.sync_service - ERROR - sync_distribution_orders:918 - 同步配单数据失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:48:47,971 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:48:47,971 - services.sync_service - ERROR - sync_distribution_orders:918 - 同步配单数据失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 16:48:48,105 - services.sync_service - ERROR - _execute_with_connection:115 - 数据库操作失败: value too long for type character varying(20)

2025-06-21 16:48:48,105 - services.sync_service - ERROR - _update_sync_status:1093 - 更新同步状态失败: value too long for type character varying(20)

2025-06-21 16:48:51,147 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 17:04:18,246 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 17:05:37,252 - services.sync_service - ERROR - _execute_with_connection:118 - 数据库操作失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 17:05:37,253 - services.sync_service - ERROR - sync_distribution_orders:921 - 同步配单数据失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 17:05:37,458 - services.sync_service - ERROR - _execute_with_connection:118 - 数据库操作失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 17:05:37,458 - services.sync_service - ERROR - sync_distribution_orders:921 - 同步配单数据失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 17:05:37,689 - services.sync_service - ERROR - _execute_with_connection:118 - 数据库操作失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 17:05:37,689 - services.sync_service - ERROR - sync_distribution_orders:921 - 同步配单数据失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 17:05:38,217 - services.sync_service - ERROR - _execute_with_connection:118 - 数据库操作失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 17:05:38,217 - services.sync_service - ERROR - sync_distribution_orders:921 - 同步配单数据失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 17:05:38,472 - services.sync_service - ERROR - _execute_with_connection:118 - 数据库操作失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 17:05:38,472 - services.sync_service - ERROR - sync_distribution_orders:921 - 同步配单数据失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 17:05:38,871 - services.sync_service - ERROR - _execute_with_connection:118 - 数据库操作失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 17:05:38,871 - services.sync_service - ERROR - sync_distribution_orders:921 - 同步配单数据失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 17:05:39,027 - services.sync_service - ERROR - _execute_with_connection:118 - 数据库操作失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 17:05:54,383 - services.sync_service - ERROR - sync_distribution_orders:921 - 同步配单数据失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 17:05:54,563 - services.sync_service - ERROR - _execute_with_connection:118 - 数据库操作失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 17:05:54,563 - services.sync_service - ERROR - sync_distribution_orders:921 - 同步配单数据失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 17:05:54,980 - services.sync_service - ERROR - _execute_with_connection:118 - 数据库操作失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 17:05:54,981 - services.sync_service - ERROR - sync_distribution_orders:921 - 同步配单数据失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 17:05:55,141 - services.sync_service - ERROR - _execute_with_connection:118 - 数据库操作失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 17:05:55,141 - services.sync_service - ERROR - sync_distribution_orders:921 - 同步配单数据失败: column "fir_category_name" of relation "distribution_orders" does not exist
LINE 4: ...                     hide_price, fir_category_id, fir_catego...
                                                             ^

2025-06-21 17:05:55,260 - services.sync_service - ERROR - _execute_with_connection:118 - 数据库操作失败: value too long for type character varying(20)

2025-06-21 17:05:55,261 - services.sync_service - ERROR - _update_sync_status:1096 - 更新同步状态失败: value too long for type character varying(20)

2025-06-21 17:05:57,789 - __main__ - ERROR - get_sync_statistics:802 - 获取同步统计信息失败: 0
2025-06-21 17:07:14,745 - product - ERROR - load_products_data:321 - 加载产品数据失败: type object 'ApiResponseNormalizer' has no attribute 'normalize_pagination_response'
2025-06-21 20:12:07,770 - __main__ - ERROR - show_product_knowledge_management:73 - 产品知识库管理页面错误: name 'FastGPTKnowledgeService' is not defined
