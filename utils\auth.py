import hashlib
import hmac
import base64
import time
import json
import requests
import logging
from typing import Dict, Any, Optional, Tuple
import streamlit as st
from functools import wraps
import os

from utils.session import SessionManager

logger = logging.getLogger(__name__)


class AuthManager:
    """认证管理器"""

    @staticmethod
    def login(username: str, password: str) -> Tuple[bool, Optional[Dict[str, Any]]]:
        """
        登录获取token

        Args:
            username: 用户名
            password: 密码

        Returns:
            (成功标志, 用户信息)
        """
        try:
            # 获取API基础URL
            api_base_url = os.getenv("ZKMALL_API_BASE", "https://zkmall.zktecoiot.com")

            # 构建请求
            url = f"{api_base_url}/api/loginPlatform"
            payload = {"username": username, "password": password}

            # 设置请求头
            headers = {
                "Content-Type": "application/json",
                "User-Agent": "YunShang-System/1.0",
            }

            # 发送请求，增加超时和重试
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    response = requests.post(
                        url, json=payload, headers=headers, timeout=30
                    )
                    response.raise_for_status()
                    data = response.json()

                    if data.get("code") == 200 and data.get("token"):
                        # 登录成功，保存token
                        token = data.get("token")

                        # 记录token获取时间
                        token_time = int(time.time())

                        # 尝试获取用户信息，但不作为登录成功的必要条件
                        user_info = AuthManager.get_user_info(token)

                        if user_info:
                            logger.info(f"用户 {username} 登录成功，获取到用户信息")
                        else:
                            logger.info(f"用户 {username} 登录成功，使用基础用户信息")
                            # 使用基础用户信息
                            user_info = {
                                "userName": username,
                                "userId": username,
                                "nickName": username,
                                "loginTime": token_time,
                            }

                        # 保存到session
                        SessionManager.set("auth_token", token)
                        SessionManager.set("token_time", token_time)
                        SessionManager.set("user_info", user_info)
                        SessionManager.set("user_authenticated", True)

                        logger.info(f"用户 {username} 认证状态设置成功")
                        return True, user_info
                    else:
                        logger.error(f"登录失败，API返回: {data}")

                    break  # 如果到这里，说明请求成功但登录失败，不需要重试

                except requests.exceptions.RequestException as e:
                    logger.warning(
                        f"登录请求失败 (尝试 {attempt + 1}/{max_retries}): {e}"
                    )
                    if attempt == max_retries - 1:
                        logger.error("登录请求达到最大重试次数")
                        return False, None
                    time.sleep(2**attempt)  # 指数退避

            return False, None

        except Exception as e:
            logger.error(f"登录过程异常: {e}")
            return False, None

    @staticmethod
    def get_user_info(token: str) -> Optional[Dict[str, Any]]:
        """
        获取用户信息 (简化版本，避免API调用错误)

        Args:
            token: 认证令牌

        Returns:
            用户信息
        """
        try:
            # 获取API基础URL
            api_base_url = os.getenv("ZKMALL_API_BASE", "https://zkmall.zktecoiot.com")

            # 只尝试最可能成功的端点，避免使用可能出错的端点
            safe_endpoints = [
                "/api/system/user/profile",  # 标准用户信息端点
                "/api/user/profile",  # 简化用户信息端点
            ]

            headers = {
                "Authorization": token,
                "Content-Type": "application/json",
                "User-Agent": "YunShang-System/1.0",
            }

            for endpoint in safe_endpoints:
                try:
                    url = f"{api_base_url}{endpoint}"
                    logger.debug(f"尝试获取用户信息: {endpoint}")

                    response = requests.get(url, headers=headers, timeout=10)

                    # 如果返回404，尝试下一个端点
                    if response.status_code == 404:
                        logger.debug(f"端点 {endpoint} 返回404，尝试下一个")
                        continue

                    # 如果返回500，可能是服务器错误，跳过
                    if response.status_code == 500:
                        logger.debug(f"端点 {endpoint} 返回500服务器错误，跳过")
                        continue

                    response.raise_for_status()
                    data = response.json()

                    if data.get("code") == 200:
                        logger.info(f"成功获取用户信息，使用端点: {endpoint}")
                        return data.get("user", data.get("data", {}))
                    else:
                        logger.debug(
                            f"端点 {endpoint} 返回业务错误: {data.get('msg', '未知错误')}"
                        )

                except requests.exceptions.RequestException as e:
                    logger.debug(f"端点 {endpoint} 请求异常: {e}")
                    continue
                except Exception as e:
                    logger.debug(f"端点 {endpoint} 处理异常: {e}")
                    continue

            # 所有端点都失败，记录信息但不记录错误
            logger.info("用户信息API不可用，将使用基础用户信息")
            return None

        except Exception as e:
            logger.debug(f"获取用户信息过程异常: {e}")
            return None

    @staticmethod
    def logout() -> bool:
        """
        退出登录

        Returns:
            成功标志
        """
        try:
            # 清理session
            SessionManager.delete("auth_token")
            SessionManager.delete("token_time")
            SessionManager.delete("user_info")
            SessionManager.set("user_authenticated", False)

            logger.info("用户退出登录")
            return True

        except Exception as e:
            logger.error(f"退出登录失败: {e}")
            return False

    @staticmethod
    def is_authenticated() -> bool:
        """
        检查是否已认证

        Returns:
            认证状态
        """
        return SessionManager.get("user_authenticated", False)

    @staticmethod
    def get_current_user() -> Optional[Dict[str, Any]]:
        """
        获取当前用户信息

        Returns:
            用户信息
        """
        return SessionManager.get("user_info")

    @staticmethod
    def get_token() -> Optional[str]:
        """
        获取当前token，如果token可能过期则尝试刷新

        Returns:
            认证令牌
        """
        token = SessionManager.get("auth_token")
        token_time = SessionManager.get("token_time")

        # 如果没有token，返回None
        if not token:
            logger.debug("Token不存在")
            return None

        # 检查token是否可能过期（默认2小时有效期）
        current_time = int(time.time())
        # token有效期设为1.5小时（5400秒），提前刷新
        if token_time and (current_time - token_time) > 5400:
            logger.info("Token可能即将过期，尝试刷新...")
            # 获取登录凭证
            username = os.getenv("ZKMALL_USERNAME")
            password = os.getenv("ZKMALL_PASSWORD")

            if username and password:
                # 重新登录获取新token
                success, _ = AuthManager.login(username, password)
                if success:
                    logger.info("Token刷新成功")
                    # 获取新的token
                    return SessionManager.get("auth_token")
                else:
                    logger.error("Token刷新失败")
                    # 即使刷新失败，也返回旧token，让API调用去处理
                    return token
            else:
                logger.warning("环境变量中缺少用户名或密码，无法刷新Token")

        return token

    @staticmethod
    def validate_token(token: str) -> bool:
        """
        验证token是否有效 (简化版本，避免依赖用户信息API)

        Args:
            token: 认证令牌

        Returns:
            token是否有效
        """
        try:
            # 简单验证：检查token格式和长度
            if not token or len(token) < 10:
                return False

            # 可以尝试调用一个简单的API来验证token
            # 这里我们假设如果token存在且格式正确，就认为有效
            # 实际的验证会在具体API调用时进行
            return True

        except Exception as e:
            logger.error(f"验证token失败: {e}")
            return False

    @staticmethod
    def ensure_authenticated() -> bool:
        """
        确保用户已认证，如果未认证则尝试自动登录

        Returns:
            是否已认证
        """
        # 如果已经认证，直接返回True
        if AuthManager.is_authenticated():
            return True

        # 尝试使用环境变量自动登录
        username = os.getenv("ZKMALL_USERNAME")
        password = os.getenv("ZKMALL_PASSWORD")

        if username and password:
            logger.info("尝试自动登录...")
            success, _ = AuthManager.login(username, password)
            if success:
                logger.info("自动登录成功")
                return True
            else:
                logger.error("自动登录失败")

        return False


def require_auth(func):
    """认证装饰器"""

    @wraps(func)
    def wrapper(*args, **kwargs):
        # 检查认证状态
        if not AuthManager.is_authenticated():
            st.error("🔒 请先登录以访问此页面")
            st.info("您将被重定向到登录页面...")

            # 显示登录表单
            with st.container():
                st.markdown("### 登录")
                with st.form("login_form"):
                    username = st.text_input("用户名")
                    password = st.text_input("密码", type="password")

                    if st.form_submit_button("登录", type="primary"):
                        if username and password:
                            success, user_info = AuthManager.login(username, password)
                            if success:
                                st.success("登录成功！")
                                st.rerun()
                            else:
                                st.error("登录失败，请检查用户名和密码")
                        else:
                            st.error("请输入用户名和密码")

            st.stop()  # 停止执行后续代码

        return func(*args, **kwargs)

    return wrapper
