"""
完整的附件管理服务
整合附件下载、数据库操作和业务集成功能
"""

import os
import logging
import asyncio
from typing import Dict, List, Any, Optional, Tuple, Union
from concurrent.futures import ThreadPoolExecutor, as_completed
from utils.attachment_service import AttachmentService, AttachmentInfo
from utils.attachment_db import AttachmentDB
from utils.zkmall_client import ZKMallClient
from config import Config

logger = logging.getLogger(__name__)


class AttachmentManager:
    """完整的附件管理服务"""

    def __init__(
        self, attachment_dir: str = None, database_url: str = None, max_workers: int = 4
    ):
        """
        初始化附件管理器

        Args:
            attachment_dir: 附件存储目录
            database_url: 数据库连接URL
            max_workers: 并发下载线程数
        """
        self.attachment_dir = attachment_dir or os.path.join(os.getcwd(), "attachments")
        self.attachment_service = AttachmentService(self.attachment_dir)
        self.db = AttachmentDB(database_url)
        self.api_client = ZKMallClient()
        self.max_workers = max_workers

        # 业务模块附件字段映射
        self.attachment_fields = {
            "products": ["other", "qualifications", "smallImg", "banner"],
            "cases": ["img", "banner", "smallImg", "video"],
            "programmes": ["other", "video", "smallImg", "banner"],
            "information": ["otherUrl", "videoUrl", "picVideo", "smallImg", "images"],
            "distribution_orders": ["other"],
        }

    def initialize(self) -> bool:
        """
        初始化附件管理器

        Returns:
            初始化是否成功
        """
        try:
            # 创建附件目录
            os.makedirs(self.attachment_dir, exist_ok=True)

            # 初始化数据库连接
            if not self.db.connect():
                return False

            # 初始化API客户端
            if not self.api_client.login():
                logger.warning("API客户端登录失败，某些功能可能不可用")

            logger.info("附件管理器初始化成功")
            return True

        except Exception as e:
            logger.error(f"附件管理器初始化失败: {e}")
            return False

    def close(self):
        """关闭附件管理器"""
        try:
            self.db.disconnect()
            logger.info("附件管理器已关闭")
        except Exception as e:
            logger.error(f"关闭附件管理器失败: {e}")

    def extract_attachment_urls(
        self, data: Dict[str, Any], business_module: str
    ) -> Dict[str, str]:
        """
        从业务数据中提取附件URL

        Args:
            data: 业务数据
            business_module: 业务模块

        Returns:
            字段名到URL的映射
        """
        attachment_urls = {}

        if business_module not in self.attachment_fields:
            return attachment_urls

        for field_name in self.attachment_fields[business_module]:
            field_value = data.get(field_name)

            if field_value:
                if isinstance(field_value, str) and field_value.strip():
                    # 单个URL
                    attachment_urls[field_name] = field_value.strip()
                elif isinstance(field_value, list) and field_value:
                    # URL列表，取第一个
                    for url in field_value:
                        if isinstance(url, str) and url.strip():
                            attachment_urls[field_name] = url.strip()
                            break

        return attachment_urls

    def download_business_attachments(
        self, business_module: str, business_id: str, business_data: Dict[str, Any]
    ) -> Dict[str, AttachmentInfo]:
        """
        下载单个业务对象的附件

        Args:
            business_module: 业务模块
            business_id: 业务ID
            business_data: 业务数据

        Returns:
            字段名到附件信息的映射
        """
        attachment_infos = {}

        # 提取附件URL
        attachment_urls = self.extract_attachment_urls(business_data, business_module)

        if not attachment_urls:
            logger.info(f"业务对象 {business_module}:{business_id} 没有附件")
            return attachment_infos

        # 下载附件
        for field_name, url in attachment_urls.items():
            try:
                # 先标记为待下载
                self.db.mark_attachment_pending(
                    business_module, business_id, field_name, url
                )

                # 下载附件
                attachment_info = self.attachment_service.download_file(
                    url, business_module, business_id
                )

                if attachment_info:
                    attachment_infos[field_name] = attachment_info

                    # 保存到数据库
                    attachment_id = self.db.save_attachment(
                        attachment_info, business_module, business_id, field_name
                    )

                    if attachment_id:
                        logger.info(
                            f"附件下载并保存成功: {field_name} -> {attachment_info.filename}"
                        )
                    else:
                        logger.error(f"附件保存到数据库失败: {field_name}")
                else:
                    # 更新状态为失败
                    logger.warning(f"附件下载失败: {field_name} -> {url}")

            except Exception as e:
                logger.error(f"处理附件失败 {field_name}: {e}")

        return attachment_infos

    def batch_download_attachments(
        self,
        business_module: str,
        business_data_list: List[Dict[str, Any]],
        batch_size: int = 10,
    ) -> Dict[str, Dict[str, AttachmentInfo]]:
        """
        批量下载业务对象的附件

        Args:
            business_module: 业务模块
            business_data_list: 业务数据列表
            batch_size: 批处理大小

        Returns:
            业务ID到附件信息映射的字典
        """
        all_attachments = {}

        # 分批处理
        for i in range(0, len(business_data_list), batch_size):
            batch = business_data_list[i : i + batch_size]

            logger.info(f"处理批次 {i//batch_size + 1}: {len(batch)} 个对象")

            # 使用线程池并发下载
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                future_to_data = {}

                for business_data in batch:
                    business_id = str(business_data.get("id", ""))
                    if business_id:
                        future = executor.submit(
                            self.download_business_attachments,
                            business_module,
                            business_id,
                            business_data,
                        )
                        future_to_data[future] = business_id

                # 收集结果
                for future in as_completed(future_to_data):
                    business_id = future_to_data[future]
                    try:
                        attachments = future.result()
                        if attachments:
                            all_attachments[business_id] = attachments
                    except Exception as e:
                        logger.error(f"批量下载附件失败 {business_id}: {e}")

        logger.info(f"批量下载完成: {len(all_attachments)} 个对象有附件")
        return all_attachments

    def sync_module_attachments(
        self, business_module: str, force_refresh: bool = False
    ) -> Dict[str, Any]:
        """
        同步模块的附件

        Args:
            business_module: 业务模块
            force_refresh: 是否强制刷新

        Returns:
            同步结果统计
        """
        logger.info(f"开始同步 {business_module} 模块附件")

        try:
            # 获取业务数据
            if business_module == "products":
                business_data_list = self.api_client.get_all_products()
            elif business_module == "cases":
                business_data_list = self.api_client.get_all_cases()
            elif business_module == "programmes":
                business_data_list = self.api_client.get_all_programmes()
            elif business_module == "information":
                business_data_list = self.api_client.get_all_information()
            elif business_module == "distribution_orders":
                business_data_list = self.api_client.get_all_distribution_orders()
            else:
                raise ValueError(f"不支持的业务模块: {business_module}")

            if not business_data_list:
                logger.warning(f"模块 {business_module} 没有数据")
                return {"success": False, "message": "没有数据"}

            # 批量下载附件
            all_attachments = self.batch_download_attachments(
                business_module, business_data_list
            )

            # 统计结果
            total_items = len(business_data_list)
            items_with_attachments = len(all_attachments)
            total_attachments = sum(
                len(attachments) for attachments in all_attachments.values()
            )

            result = {
                "success": True,
                "business_module": business_module,
                "total_items": total_items,
                "items_with_attachments": items_with_attachments,
                "total_attachments": total_attachments,
                "attachment_details": all_attachments,
            }

            logger.info(
                f"模块 {business_module} 附件同步完成: {total_attachments} 个附件"
            )
            return result

        except Exception as e:
            logger.error(f"同步模块附件失败 {business_module}: {e}")
            return {"success": False, "message": str(e)}

    def sync_all_attachments(self, modules: List[str] = None) -> Dict[str, Any]:
        """
        同步所有模块的附件

        Args:
            modules: 要同步的模块列表，None表示所有模块

        Returns:
            同步结果统计
        """
        if modules is None:
            modules = list(self.attachment_fields.keys())

        logger.info(f"开始同步所有模块附件: {modules}")

        all_results = {}
        total_success = 0
        total_attachments = 0

        for module in modules:
            try:
                result = self.sync_module_attachments(module)
                all_results[module] = result

                if result["success"]:
                    total_success += 1
                    total_attachments += result.get("total_attachments", 0)

            except Exception as e:
                logger.error(f"同步模块 {module} 失败: {e}")
                all_results[module] = {"success": False, "message": str(e)}

        summary = {
            "success": total_success == len(modules),
            "total_modules": len(modules),
            "successful_modules": total_success,
            "total_attachments": total_attachments,
            "module_results": all_results,
        }

        logger.info(
            f"全部同步完成: {total_success}/{len(modules)} 模块成功，共 {total_attachments} 个附件"
        )
        return summary

    def get_business_attachments(
        self, business_module: str, business_id: str
    ) -> List[Dict[str, Any]]:
        """
        获取业务对象的附件列表

        Args:
            business_module: 业务模块
            business_id: 业务ID

        Returns:
            附件信息列表
        """
        return self.db.get_attachments_by_business(business_module, business_id)

    def get_attachment_statistics(self) -> Dict[str, Any]:
        """
        获取附件统计信息

        Returns:
            统计信息
        """
        try:
            # 获取模块统计
            module_stats = self.db.get_module_statistics()

            # 获取文件类型统计
            type_stats = self.db.get_type_statistics()

            # 获取存储统计
            storage_stats = self.attachment_service.get_storage_statistics()

            return {
                "module_statistics": module_stats,
                "type_statistics": type_stats,
                "storage_statistics": storage_stats,
            }

        except Exception as e:
            logger.error(f"获取附件统计失败: {e}")
            return {}

    def cleanup_attachments(self, orphaned_only: bool = True) -> Dict[str, int]:
        """
        清理附件

        Args:
            orphaned_only: 是否只清理孤立文件

        Returns:
            清理统计
        """
        try:
            result = {"cleaned_files": 0, "cleaned_records": 0}

            if orphaned_only:
                # 清理孤立文件
                cleaned_files = self.attachment_service.cleanup_orphaned_files()
                result["cleaned_files"] = cleaned_files

            # 清理失败的下载记录
            cleaned_records = self.db.cleanup_failed_downloads()
            result["cleaned_records"] = cleaned_records

            logger.info(f"附件清理完成: {result}")
            return result

        except Exception as e:
            logger.error(f"清理附件失败: {e}")
            return {"cleaned_files": 0, "cleaned_records": 0}

    def verify_attachments_integrity(
        self, business_module: str = None
    ) -> Dict[str, Any]:
        """
        验证附件完整性

        Args:
            business_module: 业务模块，None表示验证所有

        Returns:
            验证结果
        """
        try:
            # 获取需要验证的附件
            if business_module:
                module_stats = self.db.get_module_statistics(business_module)
                total_files = module_stats.get("total_files", 0)
            else:
                all_stats = self.db.get_module_statistics()
                total_files = sum(stat.get("total_files", 0) for stat in all_stats)

            # TODO: 实现具体的完整性验证逻辑
            # 这里可以遍历所有附件，验证文件是否存在、MD5是否匹配等

            return {
                "total_files": total_files,
                "verified_files": 0,  # 实际验证的文件数
                "corrupted_files": 0,  # 损坏的文件数
                "missing_files": 0,  # 丢失的文件数
            }

        except Exception as e:
            logger.error(f"验证附件完整性失败: {e}")
            return {}

    def __enter__(self):
        """上下文管理器入口"""
        if self.initialize():
            return self
        else:
            raise RuntimeError("附件管理器初始化失败")

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close()


# 便捷函数
def sync_single_module(module_name: str, attachment_dir: str = None) -> Dict[str, Any]:
    """
    同步单个模块的附件

    Args:
        module_name: 模块名
        attachment_dir: 附件目录

    Returns:
        同步结果
    """
    with AttachmentManager(attachment_dir) as manager:
        return manager.sync_module_attachments(module_name)


def sync_all_modules(attachment_dir: str = None) -> Dict[str, Any]:
    """
    同步所有模块的附件

    Args:
        attachment_dir: 附件目录

    Returns:
        同步结果
    """
    with AttachmentManager(attachment_dir) as manager:
        return manager.sync_all_attachments()


def get_statistics(attachment_dir: str = None) -> Dict[str, Any]:
    """
    获取附件统计信息

    Args:
        attachment_dir: 附件目录

    Returns:
        统计信息
    """
    with AttachmentManager(attachment_dir) as manager:
        return manager.get_attachment_statistics()


# 使用示例
def main():
    """使用示例"""

    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )

    try:
        # 使用上下文管理器
        with AttachmentManager() as manager:
            # 同步所有模块附件
            print("开始同步所有模块附件...")
            result = manager.sync_all_attachments()
            print(f"同步结果: {result}")

            # 获取统计信息
            print("\n获取附件统计...")
            stats = manager.get_attachment_statistics()
            print(f"统计信息: {stats}")

            # 清理附件
            print("\n清理孤立附件...")
            cleanup_result = manager.cleanup_attachments()
            print(f"清理结果: {cleanup_result}")

    except Exception as e:
        logger.error(f"示例执行失败: {e}")


if __name__ == "__main__":
    main()
