---
description: 
globs: 
alwaysApply: true
---
# 日常MCP工作流程规范

## 📋 概述

本文档定义了开发人员在日常工作中必须遵循的MCP工具使用流程，确保每个开发环节都充分利用MCP工具提升效率和质量。

## 🌅 每日开发启动流程

### 1. 项目状态检查 (必须执行)
```javascript
// 获取最新项目信息
mcp_codelf_get-project-info({
    "rootPath": "/d:/syncdb-nnnnn/yunshang"
})

// 检查系统状态
mcp_mcp-feedback-enhanced_get_system_info()

// 记录当日工作计划
mcp_think-tool_think({
    "thought": "今日开发计划：1. 完成产品列表优化 2. 修复登录bug 3. 更新文档"
})
```

### 2. GitHub任务同步 (必须执行)
```javascript
// 检查分配给自己的Issue
mcp_github_search_issues({
    "q": "is:issue is:open assignee:@me repo:my-org/yunshang"
})

// 检查需要审查的PR
mcp_github_search_issues({
    "q": "is:pr is:open review-requested:@me repo:my-org/yunshang"
})
```

## 🔧 开发过程MCP使用流程

### 阶段1: 需求分析与设计

#### 必须使用的工具
1. **思考工具** - 分析需求
2. **推理工具** - 制定技术方案
3. **文档检索工具** - 获取技术资料

```javascript
// 1. 需求分析
mcp_think-tool_think({
    "thought": "分析用户需求：需要实现产品搜索功能，包含关键词搜索、分类筛选、价格区间筛选"
})

// 2. 技术方案设计
mcp_deepseek-claude_reason({
    "query": {
        "context": "Streamlit应用，需要实现高性能的产品搜索功能",
        "question": "如何设计搜索架构，包括前端组件、后端API、数据库查询优化？"
    }
})

// 3. 获取技术文档
mcp_context7_get-library-docs({
    "context7CompatibleLibraryID": "/streamlit/streamlit",
    "topic": "search and filtering"
})
```

### 阶段2: 编码实现

#### 必须使用的工具
1. **UI组件生成工具** - 生成界面组件
2. **项目管理工具** - 更新项目状态
3. **GitHub工具** - 代码管理

```javascript
// 1. 生成UI组件
mcp_21st-devmagic_21st_magic_component_builder({
    "message": "创建产品搜索组件，包含搜索框、筛选器、结果列表",
    "searchQuery": "search filter",
    "absolutePathToCurrentFile": "/d:/syncdb-nnnnn/yunshang/components/product_search.py",
    "absolutePathToProjectDirectory": "/d:/syncdb-nnnnn/yunshang",
    "standaloneRequestQuery": "产品搜索组件，支持关键词搜索、分类筛选、价格筛选、结果展示"
})

// 2. 更新项目信息
mcp_codelf_update-project-info({
    "rootPath": "/d:/syncdb-nnnnn/yunshang"
})

// 3. 创建功能分支
mcp_github_create_branch({
    "owner": "my-org",
    "repo": "yunshang",
    "branch": "feature/product-search",
    "from_branch": "main"
})
```

### 阶段3: 测试验证

#### 必须使用的工具
1. **浏览器自动化工具** - 功能测试
2. **思考工具** - 记录测试结果

```javascript
// 1. 启动应用测试
mcp_playwright_playwright_navigate({
    "url": "http://localhost:8501",
    "browserType": "chromium",
    "headless": false
})

// 2. 测试搜索功能
mcp_playwright_playwright_fill({
    "selector": "input[placeholder='搜索产品']",
    "value": "智能门锁"
})

mcp_playwright_playwright_press_key({
    "key": "Enter",
    "selector": "input[placeholder='搜索产品']"
})

// 3. 截图记录
mcp_playwright_playwright_screenshot({
    "name": "search_test_result",
    "savePng": true,
    "fullPage": true
})

// 4. 记录测试结果
mcp_think-tool_think({
    "thought": "搜索功能测试结果：1. 关键词搜索正常 2. 筛选器工作正常 3. 结果展示完整 4. 性能良好"
})
```

### 阶段4: 代码提交

#### 必须使用的工具
1. **GitHub工具** - 创建PR和Issue
2. **反馈收集工具** - 收集审查意见

```javascript
// 1. 推送代码
mcp_github_push_files({
    "owner": "my-org",
    "repo": "yunshang",
    "branch": "feature/product-search",
    "files": [
        {
            "path": "components/product_search.py",
            "content": "# 搜索组件代码..."
        }
    ],
    "message": "feat: 实现产品搜索功能"
})

// 2. 创建Pull Request
mcp_github_create_pull_request({
    "owner": "my-org",
    "repo": "yunshang",
    "title": "feat: 产品搜索功能",
    "head": "feature/product-search",
    "base": "main",
    "body": "## 功能描述\n- 关键词搜索\n- 分类筛选\n- 价格区间筛选\n\n## 测试说明\n- 功能测试通过\n- 性能测试通过"
})

// 3. 收集代码审查反馈
mcp_mcp-feedback-enhanced_interactive_feedback({
    "project_directory": "/d:/syncdb-nnnnn/yunshang",
    "summary": "产品搜索功能开发完成，已创建PR，请进行代码审查",
    "timeout": 600
})
```

## 🐛 问题解决MCP流程

### 遇到Bug时的标准流程

```javascript
// 1. 记录问题分析
mcp_think-tool_think({
    "thought": "Bug分析：用户反馈登录失败，错误信息显示'认证失败'，可能原因：1. 密码验证逻辑错误 2. 数据库连接问题 3. 会话管理异常"
})

// 2. 使用推理工具深度分析
mcp_deepseek-claude_reason({
    "query": {
        "context": "用户登录功能出现认证失败错误",
        "question": "如何系统性地排查和修复登录认证问题？"
    }
})

// 3. 搜索相关代码
mcp_github_search_code({
    "q": "login authentication repo:my-org/yunshang"
})

// 4. 复现问题
mcp_playwright_playwright_navigate({
    "url": "http://localhost:8501/login"
})

mcp_playwright_playwright_fill({
    "selector": "input[name='username']",
    "value": "testuser"
})

mcp_playwright_playwright_fill({
    "selector": "input[name='password']",
    "value": "testpass"
})

mcp_playwright_playwright_click({
    "selector": "#login-button"
})

// 5. 获取错误日志
mcp_playwright_playwright_console_logs({
    "type": "error",
    "limit": 20
})

// 6. 创建Bug Issue
mcp_github_create_issue({
    "owner": "my-org",
    "repo": "yunshang",
    "title": "修复登录认证失败问题",
    "body": "## 问题描述\n用户登录时出现认证失败错误\n\n## 复现步骤\n1. 访问登录页面\n2. 输入用户名密码\n3. 点击登录按钮\n\n## 预期结果\n成功登录\n\n## 实际结果\n显示认证失败",
    "labels": ["bug", "urgent"]
})
```

## 📊 性能优化MCP流程

### 发现性能问题时的处理流程

```javascript
// 1. 性能问题分析
mcp_think-tool_think({
    "thought": "性能问题分析：页面加载时间超过3秒，用户体验差。可能原因：1. 数据库查询慢 2. 缓存未命中 3. 前端渲染阻塞 4. 网络请求过多"
})

// 2. 深度性能分析
mcp_deepseek-claude_reason({
    "query": {
        "context": "Streamlit应用页面加载缓慢，影响用户体验",
        "question": "制定全面的性能优化策略，包括前端、后端、数据库层面的优化方案"
    }
})

// 3. 性能测试
mcp_playwright_playwright_evaluate({
    "script": `
        const performance = window.performance;
        const timing = performance.timing;
        const loadTime = timing.loadEventEnd - timing.navigationStart;
        const domReady = timing.domContentLoadedEventEnd - timing.navigationStart;
        return { loadTime, domReady, url: window.location.href };
    `
})

// 4. 组件性能优化
mcp_21st-devmagic_21st_magic_component_refiner({
    "userMessage": "优化页面加载性能，实现懒加载和缓存优化",
    "absolutePathToRefiningFile": "/d:/syncdb-nnnnn/yunshang/pages/01_product_management.py",
    "context": "当前页面加载时间过长，需要优化数据加载策略，实现懒加载、缓存优化、虚拟滚动等性能提升措施"
})

// 5. 更新项目文档
mcp_codelf_update-project-info({
    "rootPath": "/d:/syncdb-nnnnn/yunshang"
})
```

## 🔄 每日收尾流程

### 工作结束前必须执行

```javascript
// 1. 总结当日工作
mcp_think-tool_think({
    "thought": "今日工作总结：1. 完成产品搜索功能开发 2. 修复登录bug 3. 优化页面性能 4. 更新项目文档。明日计划：1. 代码审查 2. 部署测试 3. 用户反馈收集"
})

// 2. 更新项目状态
mcp_codelf_update-project-info({
    "rootPath": "/d:/syncdb-nnnnn/yunshang"
})

// 3. 收集当日反馈
mcp_mcp-feedback-enhanced_interactive_feedback({
    "project_directory": "/d:/syncdb-nnnnn/yunshang",
    "summary": "今日开发工作已完成，包括产品搜索功能、bug修复、性能优化。请查看进度并提供反馈。",
    "timeout": 300
})

// 4. 检查未完成的Issue
mcp_github_search_issues({
    "q": "is:issue is:open assignee:@me repo:my-org/yunshang"
})

// 5. 获取思考统计
mcp_think-tool_get_thought_stats()
```

## 📋 每日MCP使用检查清单

### 开发启动检查
- [ ] 获取项目最新状态
- [ ] 检查GitHub任务分配
- [ ] 制定当日工作计划
- [ ] 记录工作思考

### 开发过程检查
- [ ] 需求分析使用思考和推理工具
- [ ] 技术设计获取相关文档
- [ ] 编码实现使用UI生成工具
- [ ] 功能测试使用自动化工具

### 代码提交检查
- [ ] 创建功能分支
- [ ] 推送代码变更
- [ ] 创建Pull Request
- [ ] 收集审查反馈

### 问题处理检查
- [ ] 记录问题分析过程
- [ ] 使用工具复现问题
- [ ] 创建Bug Issue跟踪
- [ ] 验证修复效果

### 每日收尾检查
- [ ] 总结当日工作成果
- [ ] 更新项目文档状态
- [ ] 收集用户反馈意见
- [ ] 规划明日工作计划

## 🚨 强制执行要求

1. **每个开发日都必须执行启动和收尾流程**
2. **任何代码变更都必须使用对应的MCP工具**
3. **遇到问题必须使用思考工具记录分析过程**
4. **功能完成必须使用自动化工具进行测试**
5. **违反流程的代码提交将被拒绝**

## 📚 相关参考

- [全面MCP工具使用指南](mdc:.cursor/rules/comprehensive-mcp-usage-guide.mdc) - 完整工具说明
- [Streamlit开发规范](mdc:.cursor/rules/streamlit-dev-standards.mdc) - 前端开发规范
- [代码开发规范](mdc:.cursor/rules/code-standards.mdc) - 代码质量标准

---

**重要提醒**: 本流程是强制性的日常工作规范，所有开发人员必须严格遵循。充分使用MCP工具将确保代码质量、提升开发效率、促进团队协作。

