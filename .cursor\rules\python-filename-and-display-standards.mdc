---
description: 
globs: 
alwaysApply: true
---
# Python文件命名与页面展示规范

## 📋 核心原则

本项目严格执行以下核心原则：
1. **Python文件名必须使用英文**，禁止使用中文或特殊字符
2. **页面展示内容必须使用中文**，确保用户界面完全中文化
3. **代码内部标识符使用英文**，注释和文档字符串使用中文

## 🚫 文件命名禁止规则

### 1. 严禁中文文件名
```bash
# ❌ 禁止的命名方式
pages/01_产品管理.py
pages/02_产品详情.py  
components/数据表格.py
utils/数据库工具.py
主页.py

# ✅ 正确的命名方式
pages/01_product_management.py
pages/02_product_detail.py
components/data_table.py
utils/database_utils.py
main.py
```

### 2. 严禁特殊字符和表情符号
```bash
# ❌ 禁止使用
01_📦_产品管理.py
02_📋_产品详情.py
03_🎯_方案管理.py

# ✅ 使用英文描述
01_product_management.py
02_product_detail.py
03_solution_management.py
```

## ✅ 文件命名规范

### 1. 命名格式
- 使用小写英文字母
- 单词间用下划线连接（snake_case）
- 数字前缀用于表示顺序（可选）
- 文件名要简洁且具有描述性

### 2. 标准模板
```bash
# 页面文件
pages/01_product_management.py      # 产品管理
pages/02_product_detail.py          # 产品详情
pages/03_case_management.py         # 案例管理
pages/04_solution_management.py     # 方案管理
pages/05_news_management.py         # 资讯管理
pages/06_order_management.py        # 配单管理
pages/07_data_sync.py               # 数据同步
pages/08_data_query.py              # 数据查询

# 组件文件
components/data_table.py            # 数据表格组件
components/search_filter.py         # 搜索筛选组件
components/product_card.py          # 产品卡片组件

# 工具文件
utils/api_client.py                 # API客户端
utils/database_utils.py             # 数据库工具
utils/auth_utils.py                 # 认证工具

# 服务文件
services/product_service.py         # 产品服务
services/user_service.py            # 用户服务
services/data_sync_service.py       # 数据同步服务
```

## 🎨 页面展示中文化要求

### 1. 用户界面必须完全中文化
```python
# ✅ 正确的页面内容
st.title("产品管理")
st.subheader("搜索和筛选")
st.text_input("产品名称", placeholder="请输入产品名称")
st.button("搜索")
st.success("操作成功！")
st.error("操作失败，请重试")
```

### 2. 页面配置中文化
```python
# ✅ 页面配置示例
st.set_page_config(
    page_title="产品管理系统",
    page_icon="🏢",
    layout="wide"
)
```

### 3. 数据显示中文化
```python
# ✅ 表格列名中文化
columns_mapping = {
    'id': 'ID',
    'name': '产品名称',
    'category': '分类',
    'brand': '品牌',
    'price': '价格',
    'status': '状态',
    'created_at': '创建时间'
}

# ✅ 状态值中文化
status_mapping = {
    0: '正常',
    1: '停用',
    'active': '激活',
    'inactive': '未激活'
}
```

## 💾 代码内容规范

### 1. 变量和函数名使用英文
```python
# ✅ 正确的命名
def get_product_list(search_term: str, category_id: int) -> List[Dict]:
    """获取产品列表"""
    pass

def render_product_card(product_data: Dict) -> None:
    """渲染产品卡片"""
    pass

# ❌ 禁止中文变量名
def 获取产品列表(搜索词: str, 分类ID: int) -> List[Dict]:
    pass
```

### 2. 注释和文档字符串使用中文
```python
def load_products_data(page: int = 1, size: int = 20) -> Dict:
    """
    加载产品数据
    
    Args:
        page: 页码，从1开始
        size: 每页数量，默认20条
        
    Returns:
        包含产品数据和分页信息的字典
        
    Raises:
        APIException: 当API调用失败时抛出
    """
    # 构建请求参数
    params = {
        'page': page,
        'size': size
    }
    
    try:
        # 调用API获取数据
        response = api_client.get('/products', params=params)
        return response.json()
    except Exception as e:
        logger.error(f"加载产品数据失败: {e}")
        raise APIException("产品数据加载失败")
```

## 🔄 页面路径引用规范

### 1. 文件路径必须使用英文文件名
```python
# ✅ 正确的页面跳转
if st.button("查看详情"):
    st.switch_page("pages/02_product_detail.py")

if st.button("返回首页"):
    st.switch_page("main.py")

# ❌ 禁止中文路径
if st.button("查看详情"):
    st.switch_page("pages/02_产品详情.py")  # 禁止
```

### 2. 导入语句使用英文模块名
```python
# ✅ 正确的导入
from utils.api_client import ZKMallClient
from components.data_table import render_data_table
from services.product_service import ProductService

# ❌ 禁止中文模块名
from utils.数据库工具 import 数据库客户端  # 禁止
```

## 📂 目录结构规范

```
yunshang/
├── main.py                         # 主页面（英文文件名）
├── pages/                          # 页面目录
│   ├── 01_product_management.py    # 产品管理（英文文件名）
│   ├── 02_product_detail.py        # 产品详情（英文文件名）
│   ├── 03_case_management.py       # 案例管理（英文文件名）
│   └── ...
├── components/                     # 组件目录
│   ├── data_table.py              # 数据表格（英文文件名）
│   ├── search_filter.py           # 搜索筛选（英文文件名）
│   └── ...
├── utils/                         # 工具目录
│   ├── api_client.py              # API客户端（英文文件名）
│   ├── database_utils.py          # 数据库工具（英文文件名）
│   └── ...
└── services/                      # 服务目录
    ├── product_service.py         # 产品服务（英文文件名）
    └── ...
```

## 🛠️ 实施指南

### 1. 新建文件时
- 始终使用英文文件名
- 文件内容（用户界面）使用中文
- 添加完整的中文文档字符串

### 2. 重构现有文件时
- 将中文文件名改为英文
- 更新所有文件引用
- 确保页面展示内容保持中文

### 3. 代码审查清单
- [ ] 文件名是否使用英文？
- [ ] 变量和函数名是否使用英文？
- [ ] 用户界面是否完全中文化？
- [ ] 注释和文档是否使用中文？
- [ ] 文件引用路径是否正确？

## ⚡ 自动化检查

### 1. 文件名检查脚本
```python
import os
import re

def check_chinese_filenames(directory: str) -> List[str]:
    """检查目录中是否有中文文件名"""
    chinese_files = []
    chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
    
    for root, dirs, files in os.walk(directory):
        for file in files:
            if file.endswith('.py') and chinese_pattern.search(file):
                chinese_files.append(os.path.join(root, file))
    
    return chinese_files
```

### 2. 页面内容检查
```python
def check_ui_chinese_content(file_path: str) -> bool:
    """检查Python文件中的UI内容是否中文化"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查st.title, st.header等是否包含中文
    ui_patterns = [
        r'st\.title\([\'"](mdc:[^/'"]*)[\'"]',
        r'st\.header\([\'"](mdc:[^/'"]*)[\'"]',
        r'st\.button\([\'"](mdc:[^/'"]*)[\'"]'
    ]
    
    for pattern in ui_patterns:
        matches = re.findall(pattern, content)
        for match in matches:
            if not re.search(r'[\u4e00-\u9fff]', match):
                return False  # 发现英文UI文本
    
    return True
```

## 🚨 违规处理

### 1. 发现中文文件名
- 立即重命名为英文
- 更新所有引用
- 测试功能完整性

### 2. 发现英文用户界面
- 立即翻译为中文
- 确保用户体验一致性
- 测试界面显示效果

## 📋 检查清单

### 开发阶段
- [ ] 新建文件使用英文命名
- [ ] 页面标题使用中文
- [ ] 按钮文本使用中文
- [ ] 表单标签使用中文
- [ ] 错误信息使用中文

### 代码审查阶段
- [ ] 文件名符合英文命名规范
- [ ] 用户界面完全中文化
- [ ] 变量函数名使用英文
- [ ] 注释文档使用中文
- [ ] 文件引用路径正确

### 测试阶段
- [ ] 页面跳转正常工作
- [ ] 用户界面显示中文
- [ ] 功能操作无异常
- [ ] 错误提示友好

---

**重要提醒**: 这是强制性规范，所有开发人员必须严格遵守。违反此规范的代码将不被接受，需要重新修改后才能合并。

**文档版本**: v1.0  
**最后更新**: 2024-12-19  
**维护者**: 开发团队
