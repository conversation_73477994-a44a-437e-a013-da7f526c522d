"""
API响应格式标准化工具

该模块提供统一的API响应格式处理和字段映射功能，确保所有API响应处理的一致性。
"""

import logging
from typing import Any, Dict, List, Optional, Union
from datetime import datetime

logger = logging.getLogger(__name__)


class ApiResponseNormalizer:
    """API响应格式标准化器"""

    # 预定义的字段映射表
    FIELD_MAPPINGS = {
        "product": {
            # 基础产品信息
            "productName": "name",
            "category": "categoryId",
            "categoryName": "categoryName",
            "label": "labelId",
            "labelList": "labels",
            "labelName": "labelName",
            "smallImg": "thumbnail",
            "banner": "bannerImage",
            "spec": "specification",
            "introduction": "introduction",
            "details": "details",
            # 参数和规格信息
            "paramInfo": "parameterInfo",
            "paramInfoList": "parameterList",
            "params": "parameters",
            "newParam": "newParameters",
            "spec": "specification",
            "specList": "specificationList",
            "specSearchList": "specSearchList",
            "parameterInfo": "parameterInfo",
            "specificationInfo": "specificationInfo",
            # 使用和应用信息
            "useTo": "usage",
            "showFor": "visibility",
            "showForCompany": "visibilityCompany",
            "showForCompanyName": "visibilityCompanyName",
            "showForCompanyList": "visibilityCompanyList",
            "commonProblem": "commonIssues",
            "commonProblemList": "commonIssuesList",
            "instructions": "instructionIds",
            "instructionsList": "instructionsList",
            "other": "attachments",
            "guide": "operationGuide",
            # 资质和认证
            "qualifications": "qualificationUrl",
            # 视频相关
            "videoExplanation": "explanationVideo",
            "videoInstallation": "installationVideo",
            "videoTroubleshooting": "troubleshootingVideo",
            "video": "videoUrl",
            # 配件和附件
            "accessory": "accessories",
            "accessoryList": "accessoriesList",
            # 统计数据
            "count": "viewCount",
            "likeCount": "likes",
            "favoriteCount": "favorites",
            "collectSize": "collectCount",
            # 状态和属性
            "isNew": "isNewProduct",
            "isHot": "isHotProduct",
            "isSuggest": "isRecommended",
            "isPush": "isPushed",
            "isShare": "isShared",
            "status": "status",
            "attribute": "attribute",
            "showTime": "displayTime",
            "upTime": "updateTime",
            "sort": "sortOrder",
            # 价格信息
            "price": "price",
            "hidePrice": "isPriceHidden",
            # 产品生成相关
            "productGenMap": "productGenerationMap",
            "productGenListHandle": "productGenListHandle",
            "productGenListInstall": "productGenListInstall",
            "productGenListFault": "productGenListFault",
            "productGenListLearning": "productGenListLearning",
            "allProductGenListHandle": "allProductGenListHandle",
            "allProductGenListInstall": "allProductGenListInstall",
            "allProductGenListFault": "allProductGenListFault",
            "allProductGenListLearning": "allProductGenListLearning",
            # 关联信息
            "productIdList": "relatedProductIds",
            "programmeIdList": "relatedProgrammeIds",
            "programmeList": "relatedProgrammes",
            "idList": "relatedIds",
            "questionList": "questionList",
            "hardList": "hardwareList",
            "softList": "softwareList",
            "list": "itemList",
            # 系统信息
            "siteId": "siteId",
            "companyId": "companyId",
            "userId": "userId",
            "brandId": "brandId",
            "brandName": "brandName",
            "unit": "unit",
            "unitName": "unitName",
            "size": "size",
            "type": "type",
            "remark": "remark",
            "params": "params",
            # 地理位置
            "province": "province",
            "city": "city",
            "county": "county",
            "conty": "district",
            # 时间字段
            "createTime": "createdAt",
            "updateTime": "updatedAt",
            "createBy": "createdBy",
            "updateBy": "updatedBy",
            "showTime": "showTime",
        },
        "case": {
            # 基础案例信息
            "caseName": "title",
            "name": "title",
            "category": "categoryId",
            "categoryName": "categoryName",
            "keywords": "tags",
            "introduction": "summary",
            "content": "description",
            "details": "details",
            # 图片和媒体
            "img": "image",
            "banner": "bannerImage",
            "smallImg": "thumbnail",
            "video": "videoUrl",
            "images": "imageList",
            "picVideo": "coverMedia",
            # 发布者信息
            "publishName": "publisher",
            "realName": "realPublisher",
            "publishId": "publisherId",
            "companyId": "companyId",
            "companyName": "companyName",
            "industry": "industry",
            # 地理位置
            "province": "province",
            "city": "city",
            "county": "district",
            "location": "location",
            # 产品关联
            "productId": "productId",
            "productName": "productName",
            "productList": "relatedProducts",
            # 统计数据
            "count": "viewCount",
            "likeCount": "likes",
            "favoriteCount": "favorites",
            "watch": "watchCount",
            # 状态信息
            "approveStatus": "approvalStatus",
            "isSuggest": "isRecommended",
            "isAuth": "isAuthenticated",
            "isPush": "isPushed",
            "isShare": "isShared",
            "isHot": "isHot",
            "parentId": "parentId",
            "top": "isTop",
            "status": "status",
            # 系统信息
            "siteId": "siteId",
            "belongId": "belongId",
            "type": "type",
            "sort": "sortOrder",
            # 时间字段
            "createTime": "createdAt",
            "updateTime": "updatedAt",
            "createBy": "createdBy",
            "updateBy": "updatedBy",
        },
        "programme": {
            # 基础方案信息
            "id": "id",
            "name": "title",
            "programmeName": "title",
            "categoryId": "categoryId",
            "categoryName": "categoryName",
            "categorySceneId": "categorySceneId",
            "categorySceneName": "categorySceneName",
            "introduction": "description",
            "content": "content",
            "details": "details",
            "overview": "overview",
            "features": "features",
            "advantages": "advantages",
            "applications": "applications",
            # 媒体文件
            "other": "attachments",
            "video": "videoUrl",
            "smallImg": "thumbnailImage",
            "banner": "bannerImage",
            "images": "imageList",
            # 产品关联
            "list": "productList",
            "productList": "relatedProducts",
            "productId": "productId",
            "productName": "productName",
            "programmeDetails": "details",
            "hardList": "hardwareList",
            "softList": "softwareList",
            "customList": "customList",
            # 公司信息
            "companyId": "companyId",
            "companyName": "companyName",
            # 地理位置
            "province": "province",
            "city": "city",
            "county": "county",
            # 统计数据
            "count": "viewCount",
            "likeCount": "likes",
            "favoriteCount": "favorites",
            "collectSize": "collectCount",
            # 状态信息
            "isSuggest": "isRecommended",
            "isHot": "isHot",
            "isNew": "isNew",
            "isPush": "isPushed",
            "top": "isTop",
            "status": "status",
            # 系统信息
            "siteId": "siteId",
            "type": "type",
            "sort": "sortOrder",
            # 时间字段
            "createTime": "createdAt",
            "updateTime": "updatedAt",
            "createBy": "createdBy",
            "updateBy": "updatedBy",
        },
        "information": {
            # 基础资讯信息
            "id": "id",
            "title": "title",
            "details": "content",
            "content": "content",
            "summary": "summary",
            "author": "author",
            # 媒体文件
            "picVideo": "coverImage",
            "smallImg": "thumbnailImage",
            "images": "images",
            "otherUrl": "attachmentUrl",
            "videoUrl": "videoUrl",
            "coverImage": "coverImage",
            # 分类信息
            "categoryId": "categoryId",
            "categoryName": "categoryName",
            # 关联信息
            "productId": "productId",
            "productName": "productName",
            "belongId": "belongId",
            # 状态信息
            "isHot": "isHot",
            "isSuggest": "isRecommended",
            "showType": "displayType",
            "top": "isPinned",
            "status": "status",
            # 统计数据
            "watch": "viewCount",
            "readCount": "readCount",
            "likeCount": "likes",
            "favoriteCount": "favorites",
            # 发布信息
            "publishTime": "publishTime",
            "publishName": "publisher",
            # 系统信息
            "siteId": "siteId",
            "type": "type",
            # 时间字段
            "createTime": "createdAt",
            "updateTime": "updatedAt",
            "createBy": "createdBy",
            "updateBy": "updatedBy",
        },
        "distribution_order": {
            # 基础配单信息
            "name": "title",
            "orderNo": "orderNumber",
            "type": "type",
            "status": "status",
            # 客户信息
            "customerName": "customerName",
            "phone": "phoneNumber",
            "contacts": "contactPerson",
            # 公司信息
            "companyId": "companyId",
            "companyName": "companyName",
            "userId": "userId",
            # 产品列表
            "list": "productList",
            "delList": "deletedList",
            "hardList": "hardwareList",
            "softList": "softwareList",
            "customList": "customList",
            "productList": "allProductsList",
            "userList": "userList",
            # 来源信息
            "fromType": "sourceType",
            "sourceType": "orderSourceType",
            "fromUserId": "sourceUserId",
            "fromUserName": "sourceUserName",
            "platformOrderId": "platformOrderId",
            # 价格信息
            "price": "totalPrice",
            "hidePrice": "isPriceHidden",
            # 分类信息
            "firCategoryId": "primaryCategoryId",
            "firCategoryName": "primaryCategoryName",
            "secCategoryId": "secondaryCategoryId",
            "secCategoryName": "secondaryCategoryName",
            "categoryScene": "categoryScene",
            "categorySceneName": "categorySceneName",
            # 统计数据
            "likeCount": "likes",
            "favoriteCount": "favorites",
            # 附件和详情
            "other": "attachments",
            "details": "description",
            # 状态信息
            "hasExpire": "hasExpiredProducts",
            # 系统信息
            "siteId": "siteId",
            # 时间字段
            "createTime": "createdAt",
            "updateTime": "updatedAt",
            "createBy": "createdBy",
            "updateBy": "updatedBy",
        },
        "category": {
            "id": "id",
            "name": "name",
            "parentId": "parentId",
            "level": "level",
            "sort": "sortOrder",
            "status": "status",
            "siteId": "siteId",
            "type": "type",
            "source": "source",
            "createTime": "createdAt",
            "updateTime": "updatedAt",
            "createBy": "createdBy",
            "updateBy": "updatedBy",
        },
        "label": {
            "id": "id",
            "name": "name",
            "showDay": "displayDays",
            "sort": "sortOrder",
            "status": "status",
            "siteId": "siteId",
            "createTime": "createdAt",
            "updateTime": "updatedAt",
            "createBy": "createdBy",
            "updateBy": "updatedBy",
        },
        "brand": {
            "id": "id",
            "name": "name",
            "logo": "logoUrl",
            "description": "description",
            "status": "status",
            "siteId": "siteId",
            "createTime": "createdAt",
            "updateTime": "updatedAt",
            "createBy": "createdBy",
            "updateBy": "updatedBy",
        },
    }

    def normalize(self, response: Any, data_type: str = "generic") -> Any:
        """
        通用的标准化方法，自动判断响应类型并应用对应的标准化处理

        Args:
            response: API响应数据
            data_type: 数据类型，用于选择字段映射

        Returns:
            标准化后的数据
        """
        try:
            field_mapping = self.FIELD_MAPPINGS.get(data_type, {})

            # 判断响应类型
            if isinstance(response, list):
                return self.normalize_list_response(response, field_mapping, data_type)
            elif isinstance(response, dict):
                if "rows" in response or "data" in response or "list" in response:
                    return self.normalize_list_response(
                        response, field_mapping, data_type
                    )
                else:
                    result = self.normalize_single_response(
                        response, field_mapping, data_type
                    )
                    return result if result is not None else {}
            else:
                logger.warning(f"无法处理的响应类型: {type(response)}")
                return response

        except Exception as e:
            logger.error(f"数据标准化失败: {e}")
            return response

    @staticmethod
    def normalize_list_response(
        response: Any,
        field_mapping: Optional[Dict[str, str]] = None,
        data_type: str = "generic",
    ) -> List[Dict[str, Any]]:
        """
        标准化列表响应

        Args:
            response: 原始API响应
            field_mapping: 自定义字段映射
            data_type: 数据类型 (product, case, programme, etc.)

        Returns:
            标准化后的数据列表
        """
        try:
            # 1. 提取数据列表
            data_list = ApiResponseNormalizer._extract_list_data(response)

            # 2. 应用字段映射
            if field_mapping or data_type in ApiResponseNormalizer.FIELD_MAPPINGS:
                mapping = field_mapping or ApiResponseNormalizer.FIELD_MAPPINGS.get(
                    data_type, {}
                )
                data_list = [
                    ApiResponseNormalizer.apply_field_mapping(item, mapping)
                    for item in data_list
                ]

            # 3. 数据清洗和验证
            data_list = [
                ApiResponseNormalizer._clean_data_item(item)
                for item in data_list
                if item
            ]

            logger.info(
                f"成功标准化 {data_type} 类型列表响应，数据量: {len(data_list)}"
            )
            return data_list

        except Exception as e:
            logger.error(f"列表响应标准化失败: {e}")
            return []

    @staticmethod
    def normalize_single_response(
        response: Any,
        field_mapping: Optional[Dict[str, str]] = None,
        data_type: str = "generic",
    ) -> Optional[Dict[str, Any]]:
        """
        标准化单个对象响应

        Args:
            response: 原始API响应
            field_mapping: 自定义字段映射
            data_type: 数据类型

        Returns:
            标准化后的数据对象
        """
        try:
            # 1. 提取单个数据对象
            data_item = ApiResponseNormalizer._extract_single_data(response)

            if not data_item:
                return None

            # 2. 应用字段映射
            if field_mapping or data_type in ApiResponseNormalizer.FIELD_MAPPINGS:
                mapping = field_mapping or ApiResponseNormalizer.FIELD_MAPPINGS.get(
                    data_type, {}
                )
                data_item = ApiResponseNormalizer.apply_field_mapping(
                    data_item, mapping
                )

            # 3. 数据清洗
            data_item = ApiResponseNormalizer._clean_data_item(data_item)

            logger.info(f"成功标准化 {data_type} 类型单个响应")
            return data_item

        except Exception as e:
            logger.error(f"单个响应标准化失败: {e}")
            return None

    @staticmethod
    def normalize_pagination_response(response: Any) -> Dict[str, Any]:
        """
        标准化分页响应

        Args:
            response: 原始API响应

        Returns:
            标准化的分页信息
        """
        pagination = {
            "total": 0,
            "current": 1,
            "size": 20,
            "pages": 0,
            "hasNext": False,
            "hasPrevious": False,
        }

        try:
            # 从标准格式提取
            if (
                isinstance(response, dict)
                and response.get("code") == 200
                and "data" in response
            ):
                data = response["data"]
                if isinstance(data, dict):
                    pagination.update(
                        {
                            "total": data.get("total", 0),
                            "current": data.get("current", 1),
                            "size": data.get("size", data.get("pageSize", 20)),
                            "pages": data.get("pages", 0),
                        }
                    )

            # 从直接格式提取
            elif isinstance(response, dict):
                pagination.update(
                    {
                        "total": response.get("total", 0),
                        "current": response.get("current", 1),
                        "size": response.get("size", response.get("pageSize", 20)),
                        "pages": response.get("pages", 0),
                    }
                )

            # 计算总页数和导航信息
            if pagination["total"] > 0 and pagination["size"] > 0:
                pagination["pages"] = (
                    pagination["total"] + pagination["size"] - 1
                ) // pagination["size"]
                pagination["hasNext"] = pagination["current"] < pagination["pages"]
                pagination["hasPrevious"] = pagination["current"] > 1

            logger.debug(f"分页信息标准化完成: {pagination}")
            return pagination

        except Exception as e:
            logger.error(f"分页信息标准化失败: {e}")
            return pagination

    @staticmethod
    def apply_field_mapping(
        data: Dict[str, Any], field_mapping: Dict[str, str]
    ) -> Dict[str, Any]:
        """
        应用字段映射

        Args:
            data: 原始数据
            field_mapping: 字段映射表 {源字段名: 目标字段名}

        Returns:
            映射后的数据
        """
        if not field_mapping or not isinstance(data, dict):
            return data

        mapped_data = data.copy()

        for source_field, target_field in field_mapping.items():
            if source_field in mapped_data:
                # 如果目标字段不同于源字段，进行映射
                if source_field != target_field:
                    mapped_data[target_field] = mapped_data.pop(source_field)

        return mapped_data

    @staticmethod
    def _extract_list_data(response: Any) -> List[Dict[str, Any]]:
        """
        从响应中提取列表数据

        Args:
            response: 原始响应

        Returns:
            数据列表
        """
        # 1. 标准格式: {"code": 200, "data": {"rows": [...]}}
        if isinstance(response, dict) and response.get("code") == 200:
            data = response.get("data", {})
            if isinstance(data, dict) and "rows" in data:
                rows = data["rows"]
                if isinstance(rows, list):
                    logger.debug(f"从标准分页格式提取到{len(rows)}条记录")
                    return rows
                else:
                    logger.warning(f"rows字段不是列表格式: {type(rows)}")
                    return []
            elif isinstance(data, list):
                logger.debug(f"从标准列表格式提取到{len(data)}条记录")
                return data

        # 2. 直接格式: {"rows": [...]} 或 {"products": [...]}
        elif isinstance(response, dict):
            # 尝试多种可能的列表字段名
            list_fields = [
                "rows",
                "products",
                "records",
                "list",
                "data",
                "items",
                "content",
                "result",
                "results",
            ]

            for field in list_fields:
                if field in response and isinstance(response[field], list):
                    field_data = response[field]
                    logger.info(f"发现潜在列表字段 {field}: {len(field_data)} 项")

                    # 验证列表内容是否为数据记录
                    if len(field_data) > 0:
                        if isinstance(field_data[0], dict):
                            logger.info(f"使用字段 {field} 作为数据源")
                            return field_data
                        else:
                            logger.debug(
                                f"字段 {field} 的内容不是字典类型: {type(field_data[0])}"
                            )
                    else:
                        logger.debug(f"字段 {field} 为空列表")

            # 深度搜索其他可能的列表字段
            logger.debug(f"在响应中深度搜索列表字段，响应字段: {list(response.keys())}")
            for key, value in response.items():
                if isinstance(value, list) and len(value) > 0:
                    if isinstance(value[0], dict):
                        logger.info(f"深度搜索发现列表字段 {key}: {len(value)} 项")
                        logger.info(f"使用字段 {key} 作为数据源")
                        return value
                elif isinstance(value, dict):
                    # 检查嵌套的字典中是否有列表
                    for nested_key, nested_value in value.items():
                        if isinstance(nested_value, list) and len(nested_value) > 0:
                            if isinstance(nested_value[0], dict):
                                logger.info(
                                    f"在嵌套字典中发现列表字段 {key}.{nested_key}: {len(nested_value)} 项"
                                )
                                logger.info(
                                    f"使用嵌套字段 {key}.{nested_key} 作为数据源"
                                )
                                return nested_value

        # 3. 列表格式: [...]
        elif isinstance(response, list):
            logger.debug(f"直接列表格式，包含{len(response)}条记录")
            return response

        # 4. 其他格式 - 提供详细的调试信息
        logger.warning(f"无法识别的列表响应格式: {type(response)}")

        if isinstance(response, dict):
            # 提供更详细的调试信息
            logger.debug(f"响应字段详情:")
            for key, value in response.items():
                value_type = type(value).__name__
                if isinstance(value, (dict, list)):
                    length_info = f" (长度: {len(value)})"
                else:
                    length_info = ""
                logger.debug(f"  {key}: {value_type}{length_info}")

            # 特殊情况处理
            if response.get("code") != 200:
                error_msg = response.get("message", response.get("msg", "未知错误"))
                logger.error(
                    f"API返回错误: code={response.get('code')}, message={error_msg}"
                )
                return []

        elif response is None:
            logger.warning("收到空响应")
        else:
            logger.warning(f"收到非字典非列表响应: {str(response)[:100]}...")

        return []

    @staticmethod
    def _extract_single_data(response: Any) -> Optional[Dict[str, Any]]:
        """
        从响应中提取单个数据对象

        Args:
            response: 原始响应

        Returns:
            数据对象
        """
        # 1. 标准格式: {"code": 200, "data": {...}}
        if isinstance(response, dict) and response.get("code") == 200:
            data = response.get("data")
            if isinstance(data, dict):
                return data
            elif isinstance(data, list) and len(data) > 0:
                return data[0]

        # 2. 直接格式: {...}
        elif isinstance(response, dict) and "code" not in response:
            return response

        # 3. 列表格式: [...]
        elif isinstance(response, list) and len(response) > 0:
            return response[0]

        return None

    @staticmethod
    def _clean_data_item(data: Dict[str, Any]) -> Dict[str, Any]:
        """
        清洗数据项

        Args:
            data: 原始数据项

        Returns:
            清洗后的数据项
        """
        if not isinstance(data, dict):
            return data

        cleaned_data = {}

        for key, value in data.items():
            # 清理键名
            clean_key = key.strip() if isinstance(key, str) else key

            # 清理值
            if isinstance(value, str):
                cleaned_value = value.strip()
                # 处理空字符串
                cleaned_value = cleaned_value if cleaned_value else None
            elif isinstance(value, (int, float)):
                cleaned_value = value
            elif isinstance(value, list):
                # 清理列表中的空值
                cleaned_value = [item for item in value if item is not None]
            else:
                cleaned_value = value

            cleaned_data[clean_key] = cleaned_value

        return cleaned_data


class FieldMappingValidator:
    """字段映射一致性验证器"""

    @staticmethod
    def validate_response_format(
        response: Any, expected_fields: List[str]
    ) -> Dict[str, Any]:
        """
        验证响应格式

        Args:
            response: API响应
            expected_fields: 期望的字段列表

        Returns:
            验证结果
        """
        validation_result = {
            "valid": True,
            "missing_fields": [],
            "extra_fields": [],
            "format_type": "unknown",
            "data_count": 0,
        }

        try:
            # 确定响应格式类型
            if isinstance(response, dict) and response.get("code") == 200:
                validation_result["format_type"] = "standard"
                data = response.get("data", {})
                if isinstance(data, dict) and "rows" in data:
                    validation_result["format_type"] = "standard_paginated"
                    sample_data = data["rows"][0] if data["rows"] else {}
                    validation_result["data_count"] = len(data["rows"])
                elif isinstance(data, list):
                    validation_result["format_type"] = "standard_list"
                    sample_data = data[0] if data else {}
                    validation_result["data_count"] = len(data)
                else:
                    sample_data = data
                    validation_result["data_count"] = 1
            elif isinstance(response, dict) and "rows" in response:
                validation_result["format_type"] = "direct_paginated"
                sample_data = response["rows"][0] if response["rows"] else {}
                validation_result["data_count"] = len(response["rows"])
            elif isinstance(response, list):
                validation_result["format_type"] = "direct_list"
                sample_data = response[0] if response else {}
                validation_result["data_count"] = len(response)
            else:
                validation_result["valid"] = False
                validation_result["format_type"] = "unknown"
                return validation_result

            # 检查字段
            if sample_data and isinstance(sample_data, dict):
                actual_fields = set(sample_data.keys())
                expected_fields_set = set(expected_fields)

                validation_result["missing_fields"] = list(
                    expected_fields_set - actual_fields
                )
                validation_result["extra_fields"] = list(
                    actual_fields - expected_fields_set
                )

                if validation_result["missing_fields"]:
                    validation_result["valid"] = False

        except Exception as e:
            logger.error(f"响应格式验证失败: {e}")
            validation_result["valid"] = False
            validation_result["error"] = str(e)

        return validation_result

    @staticmethod
    def check_field_consistency(data_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        检查字段一致性

        Args:
            data_list: 数据列表

        Returns:
            一致性检查结果
        """
        consistency_result = {
            "consistent": True,
            "total_records": len(data_list),
            "field_analysis": {},
            "issues": [],
        }

        if not data_list:
            return consistency_result

        # 收集所有字段
        all_fields = set()
        for item in data_list:
            if isinstance(item, dict):
                all_fields.update(item.keys())

        # 分析每个字段
        for field in all_fields:
            field_stats = {
                "present_count": 0,
                "null_count": 0,
                "empty_string_count": 0,
                "data_types": set(),
            }

            for item in data_list:
                if field in item:
                    field_stats["present_count"] += 1
                    value = item[field]

                    if value is None:
                        field_stats["null_count"] += 1
                    elif isinstance(value, str) and value.strip() == "":
                        field_stats["empty_string_count"] += 1

                    field_stats["data_types"].add(type(value).__name__)

            # 转换set为list以便JSON序列化
            field_stats["data_types"] = list(field_stats["data_types"])
            consistency_result["field_analysis"][field] = field_stats

            # 检查一致性问题
            if field_stats["present_count"] < len(data_list):
                consistency_result["issues"].append(
                    f"字段 '{field}' 在部分记录中缺失 ({field_stats['present_count']}/{len(data_list)})"
                )
                consistency_result["consistent"] = False

            if len(field_stats["data_types"]) > 1:
                consistency_result["issues"].append(
                    f"字段 '{field}' 存在多种数据类型: {field_stats['data_types']}"
                )
                consistency_result["consistent"] = False

        return consistency_result

    @staticmethod
    def generate_mapping_report(api_responses: Dict[str, Any]) -> str:
        """
        生成映射报告

        Args:
            api_responses: API响应集合 {api_name: response}

        Returns:
            映射报告文本
        """
        report_lines = [
            "# API响应字段映射报告",
            f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "## 概述",
            f"分析了 {len(api_responses)} 个API端点的响应格式",
            "",
        ]

        for api_name, response in api_responses.items():
            report_lines.extend([f"## {api_name} API", ""])

            # 验证响应格式
            validation = FieldMappingValidator.validate_response_format(response, [])
            report_lines.extend(
                [
                    f"- **响应格式**: {validation['format_type']}",
                    f"- **数据数量**: {validation['data_count']}",
                    f"- **格式有效**: {'是' if validation['valid'] else '否'}",
                    "",
                ]
            )

            # 提取字段信息
            data_list = ApiResponseNormalizer._extract_list_data(response)
            if data_list:
                consistency = FieldMappingValidator.check_field_consistency(data_list)

                report_lines.extend(
                    [
                        "### 字段分析",
                        "| 字段名 | 出现频率 | 数据类型 | 问题 |",
                        "|--------|----------|----------|------|",
                    ]
                )

                for field, stats in consistency["field_analysis"].items():
                    presence_rate = (
                        f"{stats['present_count']}/{consistency['total_records']}"
                    )
                    data_types = ", ".join(stats["data_types"])
                    issues = (
                        "无"
                        if stats["present_count"] == consistency["total_records"]
                        and len(stats["data_types"]) == 1
                        else "有问题"
                    )

                    report_lines.append(
                        f"| {field} | {presence_rate} | {data_types} | {issues} |"
                    )

                report_lines.extend(["", "### 一致性问题"])
                if consistency["issues"]:
                    for issue in consistency["issues"]:
                        report_lines.append(f"- {issue}")
                else:
                    report_lines.append("- 无一致性问题")

                report_lines.append("")

        return "\n".join(report_lines)


# 便捷函数
def normalize_api_response(
    response: Any, data_type: str = "generic"
) -> List[Dict[str, Any]]:
    """
    便捷的API响应标准化函数

    Args:
        response: API响应
        data_type: 数据类型

    Returns:
        标准化后的数据列表
    """
    return ApiResponseNormalizer.normalize_list_response(response, data_type=data_type)


def normalize_single_api_response(
    response: Any, data_type: str = "generic"
) -> Optional[Dict[str, Any]]:
    """
    便捷的单个API响应标准化函数

    Args:
        response: API响应
        data_type: 数据类型

    Returns:
        标准化后的数据对象
    """
    return ApiResponseNormalizer.normalize_single_response(
        response, data_type=data_type
    )


def get_pagination_info(response: Any) -> Dict[str, Any]:
    """
    便捷的分页信息获取函数

    Args:
        response: API响应

    Returns:
        分页信息
    """
    return ApiResponseNormalizer.normalize_pagination_response(response)


def validate_api_consistency(api_responses: Dict[str, Any]) -> Dict[str, Any]:
    """
    便捷的API一致性验证函数

    Args:
        api_responses: API响应集合 {响应名称: 响应数据}

    Returns:
        验证结果字典
    """
    validation_results = {}

    for name, response in api_responses.items():
        # 基础格式验证
        format_validation = FieldMappingValidator.validate_response_format(
            response, []  # 暂时不检查具体字段
        )

        validation_results[name] = {
            "format_type": format_validation.get("format_type", "unknown"),
            "data_count": format_validation.get("data_count", 0),
            "valid": format_validation.get("valid", False),
            "errors": format_validation.get("missing_fields", []),
        }

    return validation_results
