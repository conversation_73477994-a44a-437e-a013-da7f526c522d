#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Markdown文档处理脚本
功能：
1. 处理标题级别：
   - 将'# 2.1文字'格式的一级标题改为二级标题
   - 将'# 2.1.1文字'格式的一级标题改为三级标题
   - 保留'# 2 文字'格式的一级标题
   - 去掉其他不符合规则的一级标题的标题级别
2. 优化图片表达式：
   - 将![](images/xxx.jpg)\n图 x-x xxx格式优化为![图 x-x xxx](images/xxx.jpg)
"""

import os
import re
import glob
from typing import List, Tu<PERSON>


def process_heading_levels(content: str) -> str:
    """
    处理标题级别
    """
    lines = content.split('\n')
    processed_lines = []
    
    for line in lines:
        # 检查是否为一级标题
        if line.startswith('# '):
            heading_text = line[2:].strip()
            
            # 计算数字点的层级数量
            dot_match = re.match(r'^(\d+(?:\.\d+)*)', heading_text)
            if dot_match:
                dot_sequence = dot_match.group(1)
                dot_count = dot_sequence.count('.')
                
                if dot_count >= 3:
                    # 四级及以上：# *******文字 -> #### *******文字 (四级标题)
                    processed_lines.append(f"#### {heading_text}")
                elif dot_count == 2:
                    # 三级：# 2.1.1文字 -> ### 2.1.1文字 (三级标题)
                    processed_lines.append(f"### {heading_text}")
                elif dot_count == 1:
                    # 二级：# 2.1文字 -> ## 2.1文字 (二级标题)
                    processed_lines.append(f"## {heading_text}")
                else:
                    # 一级：# 2 文字 -> 保留一级标题（只有数字，后面跟空格）
                    if re.match(r'^\d+\s', heading_text):
                        processed_lines.append(line)
                    else:
                        # 单独的数字但后面没有空格，去掉标题级别
                        processed_lines.append(line)
            else:
                # 其他格式的一级标题去掉标题级别
                processed_lines.append(heading_text)
        else:
            processed_lines.append(line)
    
    return '\n'.join(processed_lines)


def process_image_expressions(content: str) -> str:
    """
    优化图片表达式
    从 ![](images/xxx.jpg)\n图 x-x xxx 格式
    改为 ![图 x-x xxx](images/xxx.jpg)
    """
    # 匹配模式：![](images/文件名.jpg) 后跟可能的空行和图片描述
    # 支持有空格和无空格的情况，如：
    # ![](images/xxx.jpg)  \n图6-17 配置定位校时
    # ![](images/xxx.jpg)  \n图 6-16 手动校时
    # 同时支持 png 和其他图片格式
    
    # 更宽松的匹配模式，处理各种空白字符组合
    pattern = r'!\[\]\((images/[^)]+\.(?:jpg|jpeg|png|gif|bmp|webp))\)[ \t]*\r?\n(图\s*\d+(?:[-–]\d+)?\s+[^\r\n]*)'
    
    def replace_image(match):
        image_path = match.group(1)
        caption = match.group(2).strip()
        return f"![{caption}]({image_path})"
    
    # 执行替换
    processed_content = re.sub(pattern, replace_image, content, flags=re.MULTILINE)
    
    return processed_content


def process_markdown_file(file_path: str) -> bool:
    """
    处理单个Markdown文件
    返回是否成功处理
    """
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 处理标题级别
        content = process_heading_levels(content)
        
        # 处理图片表达式
        content = process_image_expressions(content)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ 已处理: {file_path}")
            return True
        else:
            print(f"○ 无需更改: {file_path}")
            return False
            
    except Exception as e:
        print(f"✗ 处理失败 {file_path}: {str(e)}")
        return False


def find_markdown_files(root_dir: str) -> List[str]:
    """
    递归查找所有Markdown文件
    """
    markdown_files = []
    
    # 使用glob递归查找所有.md文件
    pattern = os.path.join(root_dir, '**', '*.md')
    markdown_files = glob.glob(pattern, recursive=True)
    
    return markdown_files


def preview_changes(file_path: str) -> Tuple[bool, str, str]:
    """
    预览文件将要进行的更改
    返回 (是否有更改, 原始内容前50行, 处理后内容前50行)
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 处理标题级别
        content = process_heading_levels(content)
        
        # 处理图片表达式
        content = process_image_expressions(content)
        
        has_changes = content != original_content
        
        # 获取前50行预览
        original_lines = original_content.split('\n')[:50]
        processed_lines = content.split('\n')[:50]
        
        return has_changes, '\n'.join(original_lines), '\n'.join(processed_lines)
        
    except Exception as e:
        return False, f"错误: {str(e)}", ""


def main():
    """
    主函数
    """
    # 设置目标目录
    target_dir = 'output_new'
    
    if not os.path.exists(target_dir):
        print(f"错误：目录 {target_dir} 不存在！")
        return
    
    print("Markdown文档处理脚本")
    print("="*50)
    print("功能说明：")
    print("1. 标题级别处理：")
    print("   - # 2.1文字 -> ## 2.1文字 (二级标题)")
    print("   - # 2.1.1文字 -> ### 2.1.1文字 (三级标题)")
    print("   - # *******文字 -> #### *******文字 (四级标题)")
    print("   - # 2 文字 -> 保留一级标题")
    print("   - 其他一级标题 -> 去掉标题级别")
    print("2. 图片表达式优化：")
    print("   - ![](images/xxx.jpg)\\n图 x-x xxx -> ![图 x-x xxx](images/xxx.jpg)")
    print("="*50)
    print(f"目标目录: {target_dir}")
    
    # 查找所有Markdown文件
    markdown_files = find_markdown_files(target_dir)
    
    if not markdown_files:
        print("未找到任何Markdown文件！")
        return
    
    print(f"找到 {len(markdown_files)} 个Markdown文件")
    
    # 询问是否预览更改
    choice = input("\n请选择操作：\n1. 直接处理所有文件\n2. 预览第一个需要更改的文件\n3. 退出\n请输入选择 (1/2/3): ").strip()
    
    if choice == '3':
        print("已退出。")
        return
    elif choice == '2':
        # 预览模式
        print("\n正在查找需要更改的文件...")
        for file_path in markdown_files[:10]:  # 只检查前10个文件
            has_changes, original, processed = preview_changes(file_path)
            if has_changes:
                print(f"\n预览文件: {file_path}")
                print("-" * 30 + " 原始内容 " + "-" * 30)
                print(original[:1000] + "..." if len(original) > 1000 else original)
                print("-" * 30 + " 处理后内容 " + "-" * 30)
                print(processed[:1000] + "..." if len(processed) > 1000 else processed)
                
                confirm = input(f"\n是否处理所有文件？(y/n): ").strip().lower()
                if confirm == 'y':
                    choice = '1'
                    break
                else:
                    print("已取消处理。")
                    return
        else:
            print("前10个文件都无需更改。")
            return
    
    if choice == '1':
        print("\n开始处理文件...")
        print("-" * 50)
        
        # 处理统计
        processed_count = 0
        success_count = 0
        
        # 处理每个文件
        for file_path in markdown_files:
            processed_count += 1
            if process_markdown_file(file_path):
                success_count += 1
        
        print("-" * 50)
        print(f"处理完成！")
        print(f"总文件数: {processed_count}")
        print(f"成功处理: {success_count}")
        print(f"无需更改: {processed_count - success_count}")
    else:
        print("无效选择，已退出。")


if __name__ == "__main__":
    main() 