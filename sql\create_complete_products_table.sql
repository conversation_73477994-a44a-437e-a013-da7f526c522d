-- 创建完整的产品表结构
-- 包含所有API字段的完整映射

DROP TABLE IF EXISTS products_complete CASCADE;

CREATE TABLE products_complete (
    -- 主键和基本标识
    id SERIAL PRIMARY KEY,
    product_id INTEGER UNIQUE NOT NULL,
    
    -- 基本产品信息
    name VARCHAR(500) NOT NULL,
    spec VARCHAR(200),                    -- 产品型号/规格
    introduction TEXT,                    -- 产品介绍
    details TEXT,                        -- 详细描述
    
    -- 图片和媒体
    small_img TEXT,                      -- 缩略图
    banner TEXT,                         -- 轮播图
    size_img TEXT,                       -- 尺寸图
    
    -- 分类和标签
    category_id INTEGER,                 -- 分类ID
    category_name VARCHAR(255),          -- 分类名称
    label_id INTEGER,                    -- 标签ID
    label_name VARCHAR(255),             -- 标签名称
    label_list JSONB,                    -- 标签列表
    
    -- 品牌信息
    brand_id INTEGER,                    -- 品牌ID
    brand_name VARCHAR(255),             -- 品牌名称
    
    -- 产品属性
    attribute VARCHAR(20) DEFAULT '0',   -- 产品属性
    status VARCHAR(20) DEFAULT '0',      -- 状态
    price DECIMAL(12,2) DEFAULT 0.00,   -- 价格
    unit INTEGER DEFAULT 2,             -- 单位
    unit_name VARCHAR(100),              -- 单位名称
    
    -- 参数信息
    param_info TEXT,                     -- 参数信息HTML
    param_info_list JSONB,               -- 参数信息列表
    new_param TEXT,                      -- 新参数JSON字符串
    spec_list JSONB,                     -- 规格列表
    spec_search_list JSONB,              -- 规格搜索列表
    
    -- 使用场景和展示
    use_to TEXT,                         -- 适用场景
    show_for TEXT,                       -- 展示对象
    show_for_company TEXT,               -- 展示公司
    show_for_company_name VARCHAR(255),  -- 展示公司名称
    show_for_company_list JSONB,         -- 展示公司列表
    
    -- 文档和资源
    qualifications TEXT,                 -- 资质文件
    instructions TEXT,                   -- 说明书ID
    instructions_list JSONB,             -- 说明书列表
    other TEXT,                          -- 其他附件
    guide TEXT,                          -- 操作指南
    
    -- 问题和支持
    common_problem TEXT,                 -- 常见问题ID
    common_problem_list JSONB,           -- 常见问题列表
    question_list JSONB,                 -- 问题列表
    
    -- 视频资源
    video_explanation TEXT,              -- 说明视频
    video_installation TEXT,             -- 安装视频
    video_troubleshooting TEXT,          -- 故障排除视频
    
    -- 配件信息
    accessory TEXT,                      -- 配件
    accessory_list JSONB,                -- 配件列表
    
    -- 推荐和热门标识
    is_suggest VARCHAR(10) DEFAULT '0',  -- 是否推荐
    is_hot VARCHAR(10) DEFAULT '0',      -- 是否热门
    is_new INTEGER DEFAULT 1,            -- 是否新品
    
    -- 统计信息
    count INTEGER DEFAULT 0,             -- 浏览量
    like_count INTEGER DEFAULT 0,        -- 点赞数
    favorite_count INTEGER DEFAULT 0,    -- 收藏数
    
    -- 产品生成内容
    product_gen_map JSONB,               -- 产品生成映射
    product_gen_list_handle JSONB,       -- 处理生成列表
    product_gen_list_install JSONB,      -- 安装生成列表
    product_gen_list_fault JSONB,        -- 故障生成列表
    product_gen_list_learning JSONB,     -- 学习生成列表
    
    -- 全量生成内容
    all_product_gen_list_handle JSONB,   -- 全部处理生成列表
    all_product_gen_list_install JSONB,  -- 全部安装生成列表
    all_product_gen_list_fault JSONB,    -- 全部故障生成列表
    all_product_gen_list_learning JSONB, -- 全部学习生成列表
    
    -- 过滤和搜索
    show_for_company_filter BOOLEAN DEFAULT FALSE,  -- 公司过滤
    third_type_search BOOLEAN DEFAULT FALSE,        -- 第三方搜索
    is_filter_show_for BOOLEAN,                     -- 是否过滤展示
    
    -- 公司和站点信息
    company_id INTEGER,                  -- 公司ID
    site_id INTEGER DEFAULT 999,        -- 站点ID
    
    -- 时间字段
    show_time TIMESTAMP,                 -- 展示时间
    up_time TIMESTAMP,                   -- 上架时间
    sort INTEGER DEFAULT 0,             -- 排序
    
    -- 系统字段
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(100),
    update_by VARCHAR(100),
    
    -- 扩展字段
    remark TEXT,                         -- 备注
    params JSONB,                        -- 扩展参数
    
    -- 原始数据存储
    raw_data JSONB,                      -- 原始API数据
    
    -- 数据处理标识
    is_processed BOOLEAN DEFAULT FALSE,   -- 是否已处理
    processed_at TIMESTAMP,              -- 处理时间
    sync_status VARCHAR(20) DEFAULT 'pending'  -- 同步状态
);

-- 创建索引
CREATE INDEX idx_products_complete_product_id ON products_complete(product_id);
CREATE INDEX idx_products_complete_name ON products_complete(name);
CREATE INDEX idx_products_complete_spec ON products_complete(spec);
CREATE INDEX idx_products_complete_category_id ON products_complete(category_id);
CREATE INDEX idx_products_complete_category_name ON products_complete(category_name);
CREATE INDEX idx_products_complete_brand_id ON products_complete(brand_id);
CREATE INDEX idx_products_complete_status ON products_complete(status);
CREATE INDEX idx_products_complete_is_suggest ON products_complete(is_suggest);
CREATE INDEX idx_products_complete_is_hot ON products_complete(is_hot);
CREATE INDEX idx_products_complete_is_new ON products_complete(is_new);
CREATE INDEX idx_products_complete_sync_status ON products_complete(sync_status);
CREATE INDEX idx_products_complete_processed ON products_complete(is_processed);

-- 添加表注释
COMMENT ON TABLE products_complete IS '完整产品信息表 - 包含所有API字段的完整映射';
COMMENT ON COLUMN products_complete.product_id IS '产品ID - 来自API的唯一标识';
COMMENT ON COLUMN products_complete.spec IS '产品型号/规格 - 如ZK3969';
COMMENT ON COLUMN products_complete.is_suggest IS '是否推荐 - 0:否, 1:是';
COMMENT ON COLUMN products_complete.is_hot IS '是否热门 - 0:否, 1:是';
COMMENT ON COLUMN products_complete.is_new IS '是否新品 - 0:否, 1:是';
COMMENT ON COLUMN products_complete.raw_data IS '原始API响应数据 - 用于调试和数据恢复';
COMMENT ON COLUMN products_complete.sync_status IS '同步状态 - pending:待处理, processing:处理中, completed:已完成, failed:失败';
