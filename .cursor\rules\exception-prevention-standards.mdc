---
description: 
globs: 
alwaysApply: true
---
# 异常预防开发规范

## 📋 概述

本文档基于云商系统项目的历史异常分析，制定全面的异常预防开发规范。通过系统性的预防措施，确保项目稳定性和代码质量。

## 🔍 历史异常分析总结

### 认证和API异常类型
- **配单数据API认证失败**：大量401错误，提示"请求访问：/api/distributionOrderPlatform/list，认证失败，无法访问系统资源"
- **FastGPT应用列表接口问题**：从静态配置改为真实API集成时出现的兼容性问题
- **API字段映射错误**：productId vs id, productName vs name等字段不一致导致的数据处理错误
- **用户信息API端点错误**：使用错误的端点/api/system/user/getInfo导致类型转换异常

### 环境兼容性异常类型
- **Windows批处理脚本编码问题**：中文字符和表情符号导致命令截断('oduct', 'rname'等)
- **React 19兼容性问题**：Dialog组件ref访问方式变更，element.ref被移除
- **数据库类型误用**：SQLite误用问题，应使用PostgreSQL
- **跨平台路径问题**：Windows和Linux路径分隔符不一致

### 数据一致性异常类型
- **数据库字段命名不一致**：emailVerified vs email_verified, camelCase vs snake_case混用
- **模拟数据混入生产代码**：placeholder.svg、硬编码回复等模拟数据未清理
- **TypeScript导入错误**：类型不匹配、导入路径错误
- **数据类型处理错误**：字符串与整数运算、List vs Dict类型混淆

### 性能和资源异常类型
- **函数永远挂起**：getCPUUsage函数Promise未调用resolve
- **页面无限等待**：管理员登录页面卡在"验证管理员权限中..."
- **数据库连接池问题**：连接泄漏、超时处理不当
- **内存泄漏问题**：缓存未清理、资源未释放

### 代码质量异常类型
- **功能未实现**：多个TODO标记(案例、方案、资讯保存逻辑未实现)
- **异常处理不当**：大量使用通用"except Exception"而非具体异常类型
- **错误处理策略缺失**：缺乏统一的错误处理机制
- **文件组织混乱**：备份文件与原文件混在一起、多个虚拟环境目录

## 🛡️ 异常预防核心原则

### 1. 预防优于修复原则
- **设计阶段预防**：在系统设计阶段就考虑可能的异常情况
- **编码阶段预防**：编写代码时主动添加异常处理和验证
- **测试阶段预防**：通过全面测试发现潜在问题
- **部署阶段预防**：部署前进行环境兼容性检查

### 2. 全链路真实性原则
- **禁止模拟数据**：所有功能必须使用真实数据和API
- **真实环境测试**：在生产环境相同的条件下进行测试
- **端到端验证**：从用户输入到数据存储的完整链路验证
- **持续监控**：生产环境持续监控异常情况

### 3. 标准化和规范化原则
- **统一编码规范**：所有代码遵循相同的编码标准
- **统一异常处理**：建立标准的异常处理模式
- **统一命名规范**：数据库字段、变量名、函数名保持一致
- **统一环境配置**：开发、测试、生产环境配置标准化

## 🔧 具体预防措施

### 1. API和认证异常预防

#### 标准认证实现
```python
class AuthenticationManager:
    def __init__(self):
        self.max_retries = 3
        self.retry_delay = 2
        self.timeout = 30
    
    def authenticate_with_retry(self, credentials: Dict[str, str]) -> Dict[str, Any]:
        """带重试机制的认证"""
        for attempt in range(self.max_retries):
            try:
                # 验证必需字段
                if not credentials.get('username') or not credentials.get('password'):
                    raise ValueError("用户名和密码不能为空")
                
                # 执行认证
                result = self._perform_authentication(credentials)
                
                # 验证认证结果
                if not result.get('success'):
                    raise AuthenticationError(result.get('message', '认证失败'))
                
                return result
                
            except (ConnectionError, TimeoutError) as e:
                if attempt == self.max_retries - 1:
                    raise
                logger.warning(f"认证尝试 {attempt + 1} 失败，重试中: {e}")
                time.sleep(self.retry_delay * (2 ** attempt))
            
            except AuthenticationError as e:
                # 认证错误不重试
                logger.error(f"认证失败: {e}")
                raise
```

#### API调用规范
```python
class APIClient:
    def __init__(self):
        self.field_mapping = {
            # 统一字段映射管理
            'api_to_internal': {
                'productId': 'id',
                'productName': 'name',
                'paramInfoList': 'specifications'
            },
            'internal_to_api': {
                'id': 'productId',
                'name': 'productName',
                'specifications': 'paramInfoList'
            }
        }
    
    def call_api_with_validation(self, endpoint: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """带验证的API调用"""
        try:
            # 参数验证
            self._validate_api_params(endpoint, params)
            
            # API调用
            response = self._make_api_call(endpoint, params)
            
            # 响应验证
            validated_response = self._validate_api_response(response)
            
            # 字段映射
            mapped_response = self._map_response_fields(validated_response)
            
            return mapped_response
            
        except APIValidationError as e:
            logger.error(f"API参数验证失败: {e}")
            raise
        except APIResponseError as e:
            logger.error(f"API响应验证失败: {e}")
            raise
        except Exception as e:
            logger.error(f"API调用异常: {e}")
            raise APICallError(f"API调用失败: {e}")
```

### 2. 环境兼容性异常预防

#### Windows环境规范
```python
import os
import platform
from pathlib import Path

class EnvironmentManager:
    @staticmethod
    def get_platform_specific_config():
        """获取平台特定配置"""
        if platform.system() == 'Windows':
            return {
                'path_separator': '\\',
                'line_ending': '\r\n',
                'encoding': 'utf-8-sig',
                'shell': 'cmd'
            }
        else:
            return {
                'path_separator': '/',
                'line_ending': '\n',
                'encoding': 'utf-8',
                'shell': 'bash'
            }
    
    @staticmethod
    def create_cross_platform_path(path_parts: List[str]) -> str:
        """创建跨平台路径"""
        return str(Path(*path_parts))
    
    @staticmethod
    def validate_environment():
        """验证环境配置"""
        required_vars = ['DATABASE_URL', 'API_BASE_URL']
        missing_vars = []
        
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            raise EnvironmentError(f"缺少环境变量: {missing_vars}")
```

#### 批处理脚本规范
```batch
@echo off
REM Windows batch script - avoid Chinese characters
REM Use only ASCII characters to prevent encoding issues

echo Setting environment variables...

REM Validate required variables
if "%DATABASE_URL%"=="" (
    echo ERROR: DATABASE_URL not set
    exit /b 1
)

if "%API_BASE_URL%"=="" (
    echo ERROR: API_BASE_URL not set
    exit /b 1
)

echo Environment validation passed
echo Database URL: %DATABASE_URL%
echo API Base URL: %API_BASE_URL%
```

### 3. 数据一致性异常预防

#### 统一字段映射管理
```python
class FieldMappingManager:
    """统一字段映射管理器"""
    
    # 数据库字段映射(统一使用snake_case)
    DB_FIELD_MAPPING = {
        'user_id': 'user_id',
        'email_verified': 'email_verified',
        'created_at': 'created_at',
        'updated_at': 'updated_at'
    }
    
    # API字段映射(可能是camelCase)
    API_FIELD_MAPPING = {
        'userId': 'user_id',
        'emailVerified': 'email_verified',
        'createdAt': 'created_at',
        'updatedAt': 'updated_at'
    }
    
    @classmethod
    def map_api_to_db(cls, api_data: Dict[str, Any]) -> Dict[str, Any]:
        """将API数据映射为数据库格式"""
        mapped_data = {}
        for api_field, db_field in cls.API_FIELD_MAPPING.items():
            if api_field in api_data:
                mapped_data[db_field] = api_data[api_field]
        return mapped_data
    
    @classmethod
    def map_db_to_api(cls, db_data: Dict[str, Any]) -> Dict[str, Any]:
        """将数据库数据映射为API格式"""
        mapped_data = {}
        reverse_mapping = {v: k for k, v in cls.API_FIELD_MAPPING.items()}
        for db_field, api_field in reverse_mapping.items():
            if db_field in db_data:
                mapped_data[api_field] = db_data[db_field]
        return mapped_data
```

#### 数据库字段规范
```sql
-- 统一使用snake_case命名
-- 统一时间字段命名
-- 统一状态字段类型

CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    email_verified BOOLEAN DEFAULT FALSE,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 触发器确保updated_at自动更新
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON users 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();
```

### 4. 性能异常预防

#### 超时和资源管理
```python
import asyncio
import contextlib
from typing import Optional, Any

class ResourceManager:
    def __init__(self, timeout: int = 30):
        self.timeout = timeout
    
    @contextlib.asynccontextmanager
    async def with_timeout(self, operation_name: str):
        """带超时的操作上下文管理器"""
        try:
            async with asyncio.timeout(self.timeout):
                logger.debug(f"开始执行操作: {operation_name}")
                yield
                logger.debug(f"操作完成: {operation_name}")
        except asyncio.TimeoutError:
            logger.error(f"操作超时: {operation_name} (超时时间: {self.timeout}秒)")
            raise TimeoutError(f"操作超时: {operation_name}")
        except Exception as e:
            logger.error(f"操作异常: {operation_name} - {e}")
            raise

class DatabaseConnectionPool:
    def __init__(self, max_connections: int = 10, timeout: int = 30):
        self.max_connections = max_connections
        self.timeout = timeout
        self.pool = None
    
    async def get_connection(self):
        """获取数据库连接"""
        if not self.pool:
            await self._create_pool()
        
        try:
            async with asyncio.timeout(self.timeout):
                connection = await self.pool.acquire()
                return connection
        except asyncio.TimeoutError:
            raise DatabaseConnectionError("获取数据库连接超时")
    
    async def return_connection(self, connection):
        """归还数据库连接"""
        if self.pool and connection:
            await self.pool.release(connection)
```

#### 数据库连接池管理
```python
class DatabaseService:
    def __init__(self):
        self.connection_pool = None
        self.max_connections = 20
        self.min_connections = 5
        self.connection_timeout = 30
    
    async def initialize_pool(self):
        """初始化连接池"""
        try:
            self.connection_pool = await asyncpg.create_pool(
                dsn=DATABASE_URL,
                min_size=self.min_connections,
                max_size=self.max_connections,
                command_timeout=self.connection_timeout,
                server_settings={
                    'application_name': 'yunshang_system',
                    'timezone': 'UTC'
                }
            )
            logger.info(f"数据库连接池初始化成功: {self.min_connections}-{self.max_connections}")
        except Exception as e:
            logger.error(f"数据库连接池初始化失败: {e}")
            raise DatabaseConnectionError(f"连接池初始化失败: {e}")
    
    @contextlib.asynccontextmanager
    async def get_connection(self):
        """获取数据库连接的上下文管理器"""
        if not self.connection_pool:
            await self.initialize_pool()
        
        connection = None
        try:
            connection = await self.connection_pool.acquire()
            yield connection
        except Exception as e:
            logger.error(f"数据库操作异常: {e}")
            if connection:
                # 回滚事务
                try:
                    await connection.execute("ROLLBACK")
                except:
                    pass
            raise
        finally:
            if connection:
                await self.connection_pool.release(connection)
```

## 📋 异常预防检查清单

### 开发阶段检查清单
- [ ] 代码是否遵循统一的命名规范？
- [ ] 是否使用了具体的异常类型而非通用Exception？
- [ ] 是否添加了适当的超时处理？
- [ ] 是否进行了参数验证？
- [ ] 是否添加了详细的日志记录？
- [ ] 是否清理了所有模拟数据和TODO标记？
- [ ] 是否测试了跨平台兼容性？

### 测试阶段检查清单
- [ ] 是否进行了功能完整性测试？
- [ ] 是否进行了异常情况测试？
- [ ] 是否进行了性能压力测试？
- [ ] 是否进行了环境兼容性测试？
- [ ] 是否验证了所有API接口？
- [ ] 是否测试了数据库连接池？
- [ ] 是否验证了字段映射正确性？

### 部署阶段检查清单
- [ ] 是否验证了所有环境变量？
- [ ] 是否测试了数据库连接？
- [ ] 是否验证了API服务可用性？
- [ ] 是否设置了监控和告警？
- [ ] 是否准备了回滚方案？
- [ ] 是否更新了相关文档？

## 🛠️ 预防工具

### 自动化检查工具
```python
class CodeQualityChecker:
    """代码质量检查器"""
    
    def check_exception_handling(self, file_path: str) -> List[str]:
        """检查异常处理质量"""
        issues = []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查通用异常捕获
        if 'except Exception:' in content:
            issues.append("发现通用异常捕获，建议使用具体异常类型")
        
        # 检查TODO标记
        if 'TODO:' in content or '# TODO' in content:
            issues.append("发现未完成的TODO标记")
        
        # 检查模拟数据
        mock_patterns = ['mock', 'fake', 'placeholder', 'test_data']
        for pattern in mock_patterns:
            if pattern in content.lower():
                issues.append(f"可能包含模拟数据: {pattern}")
        
        return issues
    
    def check_field_consistency(self, db_schema: str, api_docs: str) -> List[str]:
        """检查字段一致性"""
        # 实现字段一致性检查逻辑
        pass
```

### 环境验证工具
```python
class EnvironmentValidator:
    """环境验证工具"""
    
    def validate_startup_environment(self) -> Dict[str, bool]:
        """启动前环境检查"""
        results = {}
        
        # 检查环境变量
        results['env_vars'] = self._check_environment_variables()
        
        # 检查数据库连接
        results['database'] = self._check_database_connection()
        
        # 检查API服务
        results['api_service'] = self._check_api_service()
        
        # 检查文件权限
        results['file_permissions'] = self._check_file_permissions()
        
        return results
    
    def _check_environment_variables(self) -> bool:
        """检查必需的环境变量"""
        required_vars = [
            'DATABASE_URL',
            'API_BASE_URL',
            'API_USERNAME',
            'API_PASSWORD'
        ]
        
        for var in required_vars:
            if not os.getenv(var):
                logger.error(f"缺少环境变量: {var}")
                return False
        
        return True
```

### 运行时监控工具
```python
class RuntimeMonitor:
    """运行时监控工具"""
    
    def __init__(self):
        self.error_threshold = 10  # 错误阈值
        self.error_count = 0
        self.start_time = time.time()
    
    def log_exception(self, exception: Exception, context: str = ""):
        """记录异常并检查是否需要告警"""
        self.error_count += 1
        
        # 记录异常详情
        logger.error(f"异常发生: {context} - {type(exception).__name__}: {exception}")
        
        # 检查是否达到告警阈值
        if self.error_count >= self.error_threshold:
            self._send_alert(f"异常数量达到阈值: {self.error_count}")
    
    def _send_alert(self, message: str):
        """发送告警"""
        # 实现告警逻辑（邮件、短信、钉钉等）
        logger.critical(f"系统告警: {message}")
```

## 🔄 持续改进机制

### 异常回顾机制
- **每周回顾**：回顾本周发生的异常，分析原因和改进措施
- **月度总结**：汇总月度异常统计，更新预防措施
- **季度评估**：评估预防机制有效性，调整策略
- **年度规划**：制定下一年的异常预防规划

### 知识积累机制
- **异常案例库**：建立异常案例数据库，记录问题和解决方案
- **最佳实践库**：积累异常预防的最佳实践
- **培训体系**：定期进行异常预防培训
- **代码审查**：通过代码审查传播最佳实践

### 工具和流程优化
- **自动化检查**：持续改进自动化检查工具
- **流程优化**：根据实际情况优化开发流程
- **工具集成**：集成更多有用的预防工具
- **标准更新**：定期更新开发标准和规范

---

**文档版本**: v2.0  
**最后更新**: 2024-12-19  
**维护者**: 开发团队

**重要提醒**: 本规范是强制性的，所有开发人员必须严格遵循。异常预防是确保系统稳定性的关键，必须在开发的每个阶段都认真执行。



