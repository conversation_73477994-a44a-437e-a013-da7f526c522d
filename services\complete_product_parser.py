#!/usr/bin/env python3
"""
完整的产品字段解析器

基于实际API返回的数据结构，解析所有产品字段
"""

import json
import logging
import re
from datetime import datetime
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


class CompleteProductParser:
    """完整的产品字段解析器"""

    def __init__(self):
        """初始化解析器"""
        self.parsed_fields = set()
        self.missing_fields = set()

    def parse_product(self, raw_product: Dict[str, Any]) -> Dict[str, Any]:
        """
        解析单个产品的所有字段

        Args:
            raw_product: 原始产品数据

        Returns:
            解析后的完整产品数据
        """
        logger.debug(f"开始解析产品ID: {raw_product.get('productId', 'Unknown')}")

        parsed_product = {}
        self.parsed_fields.clear()
        self.missing_fields.clear()

        # 解析所有字段
        parsed_product.update(self._parse_all_fields(raw_product))

        # 记录解析统计
        self._log_parsing_stats(raw_product, parsed_product)

        return parsed_product

    def _parse_all_fields(self, raw_product: Dict[str, Any]) -> Dict[str, Any]:
        """解析所有字段"""
        fields = {}

        # 定义完整的字段映射
        field_mappings = {
            # 基础标识信息
            "productId": ("product_id", "int"),
            "id": ("id", "int"),
            "name": ("name", "str"),
            "productName": ("product_name", "str"),
            "spec": ("spec", "str"),
            "introduction": ("introduction", "str"),
            "details": ("details", "str"),
            # 分类信息
            "categoryId": ("category_id", "int"),
            "categoryName": ("category_name", "str"),
            "category": ("category", "str"),
            "firCategoryId": ("fir_category_id", "int"),
            "firCategoryName": ("fir_category_name", "str"),
            "secCategoryId": ("sec_category_id", "int"),
            "secCategoryName": ("sec_category_name", "str"),
            "thiCategoryId": ("thi_category_id", "int"),
            "thiCategoryName": ("thi_category_name", "str"),
            # 品牌信息
            "brandId": ("brand_id", "int"),
            "brandName": ("brand_name", "str"),
            # 标签信息 - 特殊处理逗号分隔的标签ID
            "label": ("label", "str"),  # 保持原始逗号分隔格式
            "labelId": ("label_id", "int"),  # 只取第一个标签ID作为主标签
            "labelName": ("label_name", "str"),
            "labelList": ("label_list", "json"),
            "labelListForSelect": ("label_list_for_select", "json"),
            "listTag": ("list_tag", "str"),
            # 图片和媒体字段 - 确保包含所有图片相关字段
            "smallImg": ("small_img", "str"),
            "banner": ("banner", "str"),
            "sizeImg": ("size_img", "str"),
            "bigImg": ("big_img", "str"),
            "mainImg": ("main_img", "str"),
            "thumbImg": ("thumb_img", "str"),
            "previewImg": ("preview_img", "str"),
            "coverImg": ("cover_img", "str"),
            "logoImg": ("logo_img", "str"),
            "productImg": ("product_img", "str"),
            "detailImg": ("detail_img", "str"),
            # 视频字段
            "video": ("video", "str"),
            "videoUrl": ("video_url", "str"),
            "videoExplanation": ("video_explanation", "str"),
            "videoInstallation": ("video_installation", "str"),
            "videoTroubleshooting": ("video_troubleshooting", "str"),
            "videoLearning": ("video_learning", "str"),
            "videoHandle": ("video_handle", "str"),
            "videoInstall": ("video_install", "str"),
            "videoFault": ("video_fault", "str"),
            # 价格和数量
            "price": ("price", "float"),
            "quantity": ("quantity", "int"),
            "count": ("count", "int"),
            "likeCount": ("like_count", "int"),
            "favoriteCount": ("favorite_count", "int"),
            "collectSize": ("collect_size", "int"),
            # 状态和标识
            "status": ("status", "str"),
            "isSuggest": ("is_suggest", "str"),
            "isHot": ("is_hot", "str"),
            "isNew": ("is_new", "int"),
            "isPush": ("is_push", "str"),
            "top": ("top", "str"),
            "showForCompanyFilter": ("show_for_company_filter", "bool"),
            "thirdTypeSearch": ("third_type_search", "bool"),
            "isFilterShowFor": ("is_filter_show_for", "str"),
            "isApp": ("is_app", "str"),
            # 时间字段
            "showTime": ("show_time", "datetime"),
            "upTime": ("up_time", "datetime"),
            "createTime": ("create_time", "datetime"),
            "updateTime": ("update_time", "datetime"),
            "createTimeStamp": ("create_time_stamp", "int"),
            "updateTimeStamp": ("update_time_stamp", "int"),
            # 系统字段
            "siteId": ("site_id", "int"),
            "sort": ("sort", "int"),
            "createBy": ("create_by", "str"),
            "updateBy": ("update_by", "str"),
            "companyId": ("company_id", "int"),
            "distributionOrderPlatformId": ("distribution_order_platform_id", "int"),
            "remark": ("remark", "str"),
            "params": ("params", "json"),
            "type": ("type", "int"),
            # 地理位置信息
            "city": ("city", "str"),
            "province": ("province", "str"),
            "county": ("county", "str"),
            "conty": ("conty", "str"),
            # 产品属性
            "attribute": ("attribute", "str"),
            "showFor": ("show_for", "str"),
            "showForCompany": ("show_for_company", "str"),
            "showForCompanyName": ("show_for_company_name", "str"),
            "showForCompanyList": ("show_for_company_list", "json"),
            "unit": ("unit", "int"),
            "unitName": ("unit_name", "str"),
            "size": ("size", "str"),
            # 详细信息
            "useTo": ("use_to", "str"),
            "qualifications": ("qualifications", "str"),
            "instructions": ("instructions", "str"),
            "other": ("other", "str"),
            "guide": ("guide", "str"),
            "commonProblem": ("common_problem", "str"),
            "commonProblemList": ("common_problem_list", "json"),
            "instructionsList": ("instructions_list", "json"),
            "accessory": ("accessory", "str"),
            "accessoryList": ("accessory_list", "json"),
            "paramInfo": ("param_info", "str"),
            # 参数和规格
            "paramInfoList": ("param_info_list", "json"),
            "specList": ("spec_list", "json"),
            "specSearchList": ("spec_search_list", "json"),
            # 产品生成相关
            "productGenMap": ("product_gen_map", "json"),
            "productGenListHandle": ("product_gen_list_handle", "json"),
            "productGenListInstall": ("product_gen_list_install", "json"),
            "productGenListFault": ("product_gen_list_fault", "json"),
            "productGenListLearning": ("product_gen_list_learning", "json"),
            "allProductGenListHandle": ("all_product_gen_list_handle", "json"),
            "allProductGenListInstall": ("all_product_gen_list_install", "json"),
            "allProductGenListFault": ("all_product_gen_list_fault", "json"),
            "allProductGenListLearning": ("all_product_gen_list_learning", "json"),
            # 其他列表字段
            "productIdList": ("product_id_list", "json"),
            "programmeIdList": ("programme_id_list", "json"),
            "programmeList": ("programme_list", "json"),
            "idList": ("id_list", "json"),
            "questionList": ("question_list", "json"),
            "hardList": ("hard_list", "json"),
            "softList": ("soft_list", "json"),
            "productZoneForSelect": ("product_zone_for_select", "json"),
            "rmIds": ("rm_ids", "str"),
            "list": ("list", "json"),
            # 方案相关字段
            "programmeId": ("programme_id", "int"),
        }

        # 处理已知字段
        for raw_key, (parsed_key, data_type) in field_mappings.items():
            if raw_key in raw_product:
                value = raw_product[raw_key]
                fields[parsed_key] = self._convert_value(value, data_type)
                self.parsed_fields.add(raw_key)

        # 处理未知字段
        unknown_fields = {}
        for key, value in raw_product.items():
            if key not in self.parsed_fields:
                unknown_fields[key] = value
                self.missing_fields.add(key)

        if unknown_fields:
            fields["unknown_fields"] = json.dumps(unknown_fields, ensure_ascii=False)
            logger.info(f"发现未知字段: {list(unknown_fields.keys())}")

        # 智能提取产品型号
        fields["extracted_model"] = self._extract_model_info(raw_product)

        # 添加解析时间戳
        fields["parsed_at"] = datetime.now()

        # 保存完整的原始数据
        fields["all_fields"] = json.dumps(raw_product, ensure_ascii=False)

        return fields

    def _convert_value(self, value: Any, data_type: str) -> Any:
        """根据数据类型转换值"""
        if value is None:
            return None

        if data_type == "str":
            return self._safe_string(value)
        elif data_type == "int":
            return self._safe_int(value)
        elif data_type == "float":
            return self._safe_float(value)
        elif data_type == "bool":
            return self._safe_bool(value)
        elif data_type == "datetime":
            return self._parse_datetime(value)
        elif data_type == "json":
            return json.dumps(value, ensure_ascii=False) if value else None
        else:
            return value

    def _extract_model_info(self, raw_product: Dict[str, Any]) -> str:
        """智能提取产品型号信息"""
        # 1. 优先从spec字段获取
        spec = raw_product.get("spec", "").strip()
        if spec:
            return spec

        # 2. 从paramInfoList中提取型号
        param_info_list = raw_product.get("paramInfoList")
        if param_info_list and isinstance(param_info_list, list):
            for param in param_info_list:
                if param and isinstance(param, dict):
                    param_name = param.get("params", "")
                    if param_name in ["型号", "规格", "model", "spec", "产品型号"]:
                        content = param.get("content", "").strip()
                        if content:
                            return content

        # 3. 从name字段中提取型号模式
        name = raw_product.get("name", "") or raw_product.get("productName", "")
        if name:
            # 匹配名称中的型号模式
            model_patterns = [
                r"\b([A-Z]{1,4}\d{2,4}[A-Z]*)\b",  # GM900D, ZK1000
                r"\b([A-Z]+-[A-Z]*\d+)\b",  # Pro-123, ZK-1000
                r"\b([A-Z]+\s*\d+[A-Z]*)\b",  # Pro 123, ZK 1000
            ]

            for pattern in model_patterns:
                match = re.search(pattern, name)
                if match:
                    return match.group(1)

        # 4. 从introduction中提取
        introduction = raw_product.get("introduction", "")
        if introduction:
            model_patterns = [
                r"\b([A-Z]{2,4}\d{3,4}[A-Z]*)\b",  # GM900D, ZK1000
                r"\b([A-Z]+-[A-Z]*\d+)\b",  # Pro-123, ZK-1000
                r"\b([A-Z]+\s*\d+[A-Z]*)\b",  # Pro 123, ZK 1000
            ]

            for pattern in model_patterns:
                match = re.search(pattern, introduction)
                if match:
                    return match.group(1)

        # 5. 使用产品ID作为备选
        product_id = raw_product.get("productId") or raw_product.get("id")
        if product_id:
            return f"ID-{product_id}"

        return ""

    def _safe_string(self, value: Any) -> str:
        """安全转换为字符串"""
        if value is None:
            return ""
        if isinstance(value, (list, dict)):
            return json.dumps(value, ensure_ascii=False)
        return str(value).strip()

    def _safe_int(self, value: Any) -> Optional[int]:
        """安全转换为整数，支持逗号分隔的多值字符串（取第一个值）"""
        if value is None or value == "":
            return None
        try:
            # 如果是字符串且包含逗号，取第一个值
            if isinstance(value, str) and "," in value:
                first_value = value.split(",")[0].strip()
                return int(first_value) if first_value else None
            return int(value)
        except (ValueError, TypeError):
            logger.warning(f"无法转换为整数: {value}")
            return None

    def _safe_float(self, value: Any) -> float:
        """安全转换为浮点数"""
        if value is None or value == "":
            return 0.0
        try:
            # 如果是字符串且包含逗号，取第一个值
            if isinstance(value, str) and "," in value:
                first_value = value.split(",")[0].strip()
                return float(first_value) if first_value else 0.0
            return float(value)
        except (ValueError, TypeError):
            logger.warning(f"无法转换为浮点数: {value}")
            return 0.0

    def _safe_bool(self, value: Any) -> bool:
        """安全转换为布尔值"""
        if value is None or value == "":
            return False
        if isinstance(value, bool):
            return value
        if isinstance(value, str):
            return value.lower() in ("true", "1", "yes", "on", "是")
        if isinstance(value, (int, float)):
            return bool(value)
        return False

    def _parse_datetime(self, value: Any) -> Optional[datetime]:
        """解析日期时间"""
        if not value:
            return None

        try:
            if isinstance(value, str):
                # 尝试多种日期格式
                formats = [
                    "%Y-%m-%d %H:%M:%S",
                    "%Y-%m-%dT%H:%M:%S",
                    "%Y-%m-%d",
                    "%Y/%m/%d %H:%M:%S",
                    "%Y/%m/%d",
                ]

                for fmt in formats:
                    try:
                        return datetime.strptime(value, fmt)
                    except ValueError:
                        continue

            elif isinstance(value, (int, float)):
                # 处理时间戳
                return datetime.fromtimestamp(value / 1000 if value > 1e10 else value)

        except Exception as e:
            logger.debug(f"日期解析失败: {value}, 错误: {e}")

        return None

    def _log_parsing_stats(
        self, raw_product: Dict[str, Any], parsed_product: Dict[str, Any]
    ):
        """记录解析统计信息"""
        total_raw_fields = len(raw_product)
        parsed_field_count = len(self.parsed_fields)
        missing_field_count = len(self.missing_fields)

        logger.info(
            f"产品解析完成 - 原始字段: {total_raw_fields}, "
            f"已解析: {parsed_field_count}, 未解析: {missing_field_count}"
        )

        if self.missing_fields:
            logger.info(f"未解析字段: {sorted(self.missing_fields)}")

    def get_missing_fields(self) -> List[str]:
        """获取未解析的字段名称"""
        return sorted(self.missing_fields)

    def get_parsed_fields(self) -> List[str]:
        """获取已解析的字段名称"""
        return sorted(self.parsed_fields)
