-- 附件信息表设计
-- 用于存储从云商API下载的附件文件信息

-- 创建附件表
CREATE TABLE IF NOT EXISTS attachments (
    id SERIAL PRIMARY KEY,
    
    -- 业务关联信息
    business_module VARCHAR(50) NOT NULL,  -- 业务模块 (products, cases, programmes, information, distribution_orders)
    business_id VARCHAR(100) NOT NULL,     -- 业务数据ID
    field_name VARCHAR(100) NOT NULL,      -- 附件字段名
    
    -- 原始信息
    original_url TEXT NOT NULL,            -- 原始附件URL
    original_filename VARCHAR(500),        -- 原始文件名
    
    -- 本地存储信息
    local_path TEXT NOT NULL,              -- 本地存储路径
    local_filename VARCHAR(500) NOT NULL,  -- 本地文件名
    
    -- 文件元信息
    file_size BIGINT DEFAULT 0,            -- 文件大小（字节）
    file_type VARCHAR(50),                 -- 文件类型分类 (images, videos, documents, archives, others)
    mime_type VARCHAR(200),                -- MIME类型
    file_extension VARCHAR(20),            -- 文件扩展名
    
    -- 完整性验证
    md5_hash VARCHAR(32),                  -- MD5哈希值
    sha256_hash VARCHAR(64),               -- SHA256哈希值（可选）
    
    -- 下载信息
    download_status VARCHAR(20) DEFAULT 'pending',  -- 下载状态 (pending, downloading, completed, failed, verified)
    download_attempts INTEGER DEFAULT 0,   -- 下载尝试次数
    download_time TIMESTAMP,               -- 下载完成时间
    last_verified TIMESTAMP,               -- 最后验证时间
    
    -- 错误信息
    error_message TEXT,                    -- 错误信息
    
    -- 元数据
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- 索引约束
    UNIQUE(business_module, business_id, field_name, original_url)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_attachments_business ON attachments(business_module, business_id);
CREATE INDEX IF NOT EXISTS idx_attachments_field ON attachments(field_name);
CREATE INDEX IF NOT EXISTS idx_attachments_status ON attachments(download_status);
CREATE INDEX IF NOT EXISTS idx_attachments_type ON attachments(file_type);
CREATE INDEX IF NOT EXISTS idx_attachments_md5 ON attachments(md5_hash);
CREATE INDEX IF NOT EXISTS idx_attachments_created ON attachments(created_at);

-- 创建业务模块统计视图
CREATE OR REPLACE VIEW attachment_stats AS
SELECT 
    business_module,
    COUNT(*) as total_files,
    SUM(file_size) as total_size,
    COUNT(CASE WHEN download_status = 'completed' THEN 1 END) as completed_files,
    COUNT(CASE WHEN download_status = 'failed' THEN 1 END) as failed_files,
    COUNT(CASE WHEN download_status = 'pending' THEN 1 END) as pending_files,
    AVG(file_size) as avg_file_size,
    MAX(download_time) as last_download_time
FROM attachments 
GROUP BY business_module;

-- 创建文件类型统计视图
CREATE OR REPLACE VIEW attachment_type_stats AS
SELECT 
    file_type,
    COUNT(*) as file_count,
    SUM(file_size) as total_size,
    AVG(file_size) as avg_size,
    MIN(file_size) as min_size,
    MAX(file_size) as max_size
FROM attachments 
WHERE download_status = 'completed'
GROUP BY file_type
ORDER BY file_count DESC;

-- 创建附件历史表（用于记录附件变更历史）
CREATE TABLE IF NOT EXISTS attachment_history (
    id SERIAL PRIMARY KEY,
    attachment_id INTEGER REFERENCES attachments(id),
    action VARCHAR(50) NOT NULL,           -- 操作类型 (download, verify, update, delete)
    old_status VARCHAR(20),                -- 原状态
    new_status VARCHAR(20),                -- 新状态
    message TEXT,                          -- 操作信息
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建附件历史索引
CREATE INDEX IF NOT EXISTS idx_attachment_history_attachment ON attachment_history(attachment_id);
CREATE INDEX IF NOT EXISTS idx_attachment_history_action ON attachment_history(action);
CREATE INDEX IF NOT EXISTS idx_attachment_history_created ON attachment_history(created_at);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_attachment_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_attachment_updated_at
    BEFORE UPDATE ON attachments
    FOR EACH ROW
    EXECUTE FUNCTION update_attachment_updated_at();

-- 创建历史记录触发器
CREATE OR REPLACE FUNCTION log_attachment_changes()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'UPDATE' THEN
        IF OLD.download_status != NEW.download_status THEN
            INSERT INTO attachment_history (attachment_id, action, old_status, new_status, message)
            VALUES (NEW.id, 'status_change', OLD.download_status, NEW.download_status, 
                   'Status changed from ' || OLD.download_status || ' to ' || NEW.download_status);
        END IF;
        RETURN NEW;
    ELSIF TG_OP = 'INSERT' THEN
        INSERT INTO attachment_history (attachment_id, action, new_status, message)
        VALUES (NEW.id, 'created', NEW.download_status, 'Attachment record created');
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO attachment_history (attachment_id, action, old_status, message)
        VALUES (OLD.id, 'deleted', OLD.download_status, 'Attachment record deleted');
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_log_attachment_changes
    AFTER INSERT OR UPDATE OR DELETE ON attachments
    FOR EACH ROW
    EXECUTE FUNCTION log_attachment_changes();

-- 插入示例数据（可选）
-- INSERT INTO attachments (business_module, business_id, field_name, original_url, local_path, local_filename, file_size, file_type, mime_type, md5_hash, download_status)
-- VALUES 
-- ('products', '12345', 'banner', 'https://example.com/banner.jpg', 'products/12345/images/banner_image.jpg', 'banner_image.jpg', 102400, 'images', 'image/jpeg', 'abc123def456', 'completed'),
-- ('cases', '67890', 'video', 'https://example.com/demo.mp4', 'cases/67890/videos/video_demo.mp4', 'video_demo.mp4', 5242880, 'videos', 'video/mp4', 'def789abc123', 'completed');

-- 查询语句示例
-- 
-- -- 查看所有附件统计
-- SELECT * FROM attachment_stats;
-- 
-- -- 查看文件类型统计
-- SELECT * FROM attachment_type_stats;
-- 
-- -- 查看特定业务模块的附件
-- SELECT * FROM attachments WHERE business_module = 'products' AND download_status = 'completed';
-- 
-- -- 查看下载失败的附件
-- SELECT business_module, business_id, field_name, original_url, error_message 
-- FROM attachments 
-- WHERE download_status = 'failed';
-- 
-- -- 查看附件变更历史
-- SELECT ah.*, a.business_module, a.business_id, a.field_name 
-- FROM attachment_history ah 
-- JOIN attachments a ON ah.attachment_id = a.id 
-- ORDER BY ah.created_at DESC;

COMMENT ON TABLE attachments IS '附件信息表，存储从云商API下载的附件文件元数据';
COMMENT ON TABLE attachment_history IS '附件变更历史表，记录附件状态变更和操作日志';
COMMENT ON VIEW attachment_stats IS '按业务模块统计附件信息的视图';
COMMENT ON VIEW attachment_type_stats IS '按文件类型统计附件信息的视图'; 