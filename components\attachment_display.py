"""
附件展示组件
用于在前端界面显示本地附件信息
"""

import streamlit as st
import pandas as pd
from typing import List, Dict, Any, Optional
import os
from datetime import datetime
import logging

logger = logging.getLogger(__name__)


def format_file_size(size_bytes: int) -> str:
    """
    格式化文件大小显示

    Args:
        size_bytes: 文件字节大小

    Returns:
        格式化的文件大小字符串
    """
    if size_bytes == 0:
        return "0 B"

    size_names = ["B", "KB", "MB", "GB", "TB"]
    size_index = 0
    size = float(size_bytes)

    while size >= 1024.0 and size_index < len(size_names) - 1:
        size /= 1024.0
        size_index += 1

    return f"{size:.1f} {size_names[size_index]}"


def get_file_type_icon(file_type: str) -> str:
    """
    根据文件类型返回图标

    Args:
        file_type: 文件类型

    Returns:
        文件类型图标
    """
    icons = {
        "image": "🖼️",
        "video": "🎥",
        "document": "📄",
        "archive": "📦",
        "audio": "🎵",
        "other": "📎",
    }
    return icons.get(file_type, "📎")


def display_attachment_card(
    attachment: Dict[str, Any], show_details: bool = False
) -> None:
    """
    显示单个附件卡片

    Args:
        attachment: 附件信息字典
        show_details: 是否显示详细信息
    """
    file_icon = get_file_type_icon(attachment.get("file_type", "other"))
    file_size = format_file_size(attachment.get("file_size", 0))

    with st.container():
        col1, col2, col3, col4 = st.columns([1, 3, 2, 2])

        with col1:
            st.markdown(
                f"<div style='font-size: 2rem; text-align: center;'>{file_icon}</div>",
                unsafe_allow_html=True,
            )

        with col2:
            st.markdown(f"**{attachment.get('local_filename', '未知文件')}**")
            st.caption(f"字段: {attachment.get('field_name', '未知')}")

        with col3:
            st.text(file_size)
            status = attachment.get("download_status", "unknown")
            if status == "completed":
                st.success("✅ 已下载")
            elif status == "failed":
                st.error("❌ 下载失败")
            elif status == "downloading":
                st.info("⏳ 下载中")
            else:
                st.warning("❓ 未知状态")

        with col4:
            # 检查本地文件是否存在
            local_path = attachment.get("local_path", "")
            if local_path and os.path.exists(local_path):
                if st.button("📁 打开文件", key=f"open_{attachment.get('id')}"):
                    try:
                        import subprocess
                        import platform

                        if platform.system() == "Windows":
                            os.startfile(local_path)
                        elif platform.system() == "Darwin":  # macOS
                            subprocess.call(("open", local_path))
                        else:  # Linux
                            subprocess.call(("xdg-open", local_path))

                        st.success("文件已打开")
                    except Exception as e:
                        st.error(f"无法打开文件: {e}")
            else:
                st.button("❌ 文件缺失", disabled=True)

        if show_details:
            with st.expander("详细信息"):
                st.json(
                    {
                        "原始URL": attachment.get("original_url", ""),
                        "本地路径": attachment.get("local_path", ""),
                        "文件类型": attachment.get("file_type", ""),
                        "MIME类型": attachment.get("mime_type", ""),
                        "MD5哈希": attachment.get("md5_hash", ""),
                        "下载时间": attachment.get("download_time", ""),
                        "创建时间": attachment.get("created_at", ""),
                        "更新时间": attachment.get("updated_at", ""),
                    }
                )


def display_attachments_list(
    attachments: List[Dict[str, Any]],
    title: str = "附件列表",
    show_stats: bool = True,
    show_details: bool = False,
    enable_filtering: bool = True,
) -> None:
    """
    显示附件列表

    Args:
        attachments: 附件信息列表
        title: 列表标题
        show_stats: 是否显示统计信息
        show_details: 是否显示详细信息
        enable_filtering: 是否启用筛选功能
    """
    st.subheader(title)

    if not attachments:
        st.info("📭 暂无附件数据")
        return

    # 显示统计信息
    if show_stats:
        total_count = len(attachments)
        total_size = sum(att.get("file_size", 0) for att in attachments)
        completed_count = sum(
            1 for att in attachments if att.get("download_status") == "completed"
        )
        failed_count = sum(
            1 for att in attachments if att.get("download_status") == "failed"
        )

        col1, col2, col3, col4 = st.columns(4)
        col1.metric("总数量", total_count)
        col2.metric("总大小", format_file_size(total_size))
        col3.metric("已下载", completed_count)
        col4.metric("失败", failed_count)

        st.markdown("---")

    # 筛选功能
    filtered_attachments = attachments

    if enable_filtering and attachments:
        st.subheader("🔍 筛选选项")

        col1, col2, col3 = st.columns(3)

        with col1:
            # 文件类型筛选
            file_types = list(set(att.get("file_type", "other") for att in attachments))
            selected_types = st.multiselect(
                "文件类型", file_types, default=file_types, key="attachment_type_filter"
            )

        with col2:
            # 下载状态筛选
            statuses = list(
                set(att.get("download_status", "unknown") for att in attachments)
            )
            selected_statuses = st.multiselect(
                "下载状态", statuses, default=statuses, key="attachment_status_filter"
            )

        with col3:
            # 字段名筛选
            field_names = list(set(att.get("field_name", "") for att in attachments))
            selected_fields = st.multiselect(
                "字段名",
                field_names,
                default=field_names,
                key="attachment_field_filter",
            )

        # 应用筛选
        filtered_attachments = [
            att
            for att in attachments
            if (
                att.get("file_type", "other") in selected_types
                and att.get("download_status", "unknown") in selected_statuses
                and att.get("field_name", "") in selected_fields
            )
        ]

        st.info(f"显示 {len(filtered_attachments)} / {len(attachments)} 个附件")
        st.markdown("---")

    # 显示附件列表
    if filtered_attachments:
        for i, attachment in enumerate(filtered_attachments):
            with st.container():
                display_attachment_card(attachment, show_details)
                if i < len(filtered_attachments) - 1:
                    st.markdown("---")
    else:
        st.warning("🔍 没有符合筛选条件的附件")


def display_attachments_table(attachments: List[Dict[str, Any]]) -> None:
    """
    以表格形式显示附件列表

    Args:
        attachments: 附件信息列表
    """
    if not attachments:
        st.info("📭 暂无附件数据")
        return

    # 准备表格数据
    table_data = []
    for attachment in attachments:
        file_icon = get_file_type_icon(attachment.get("file_type", "other"))
        file_size = format_file_size(attachment.get("file_size", 0))

        # 状态显示
        status = attachment.get("download_status", "unknown")
        status_display = {
            "completed": "✅ 已下载",
            "failed": "❌ 下载失败",
            "downloading": "⏳ 下载中",
            "unknown": "❓ 未知状态",
        }.get(status, "❓ 未知状态")

        # 检查本地文件是否存在
        local_path = attachment.get("local_path", "")
        file_exists = (
            "✅ 存在" if (local_path and os.path.exists(local_path)) else "❌ 缺失"
        )

        table_data.append(
            {
                "类型": file_icon,
                "文件名": attachment.get("local_filename", "未知文件"),
                "大小": file_size,
                "状态": status_display,
                "本地文件": file_exists,
                "字段": attachment.get("field_name", "未知"),
                "业务模块": attachment.get("business_module", ""),
                "业务ID": attachment.get("business_id", ""),
                "下载时间": (
                    attachment.get("download_time", "")[:19]
                    if attachment.get("download_time")
                    else ""
                ),
            }
        )

    # 显示表格
    df = pd.DataFrame(table_data)

    # 使用可交互的数据表格
    st.dataframe(
        df,
        use_container_width=True,
        hide_index=True,
        column_config={
            "类型": st.column_config.TextColumn("类型", width="small"),
            "文件名": st.column_config.TextColumn("文件名", width="medium"),
            "大小": st.column_config.TextColumn("大小", width="small"),
            "状态": st.column_config.TextColumn("状态", width="small"),
            "本地文件": st.column_config.TextColumn("本地文件", width="small"),
            "字段": st.column_config.TextColumn("字段", width="small"),
            "业务模块": st.column_config.TextColumn("业务模块", width="small"),
            "业务ID": st.column_config.TextColumn("业务ID", width="small"),
            "下载时间": st.column_config.TextColumn("下载时间", width="medium"),
        },
    )
