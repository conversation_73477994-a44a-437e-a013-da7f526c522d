# Cursor Rules - 云商系统开发规范

## 📋 总览

本文档是云商系统的完整开发规范，基于历史异常分析和全局代码优化需求制定。所有开发人员必须严格遵循这些规范，以确保代码质量、系统稳定性和团队协作效率。

## 🚨 核心开发原则

### 1. 异常预防优先原则
- **预防优于修复**: 通过规范预防问题，而非事后修复
- **全链路真实性**: 禁止模拟数据混入生产代码
- **标准化规范**: 统一命名、格式和架构标准

### 2. 质量第一原则
- **零TODO原则**: 禁止提交包含TODO标记的代码到主分支
- **具体异常处理**: 禁止使用通用`except Exception`
- **完整实现**: 所有功能必须完整实现，不允许空函数体

## 📚 规范文档体系

### 核心规范文档
- [异常预防标准](mdc:exception-prevention-standards.mdc) - 异常预防的核心策略和实施方法
- [代码质量标准](mdc:code-quality-standards.mdc) - 代码质量要求和检查标准
- [项目结构标准](mdc:project-structure-standards.mdc) - 项目组织和文件管理规范

### 技术专项规范
- [API集成标准](mdc:api-integration-standards.mdc) - API调用、认证和字段映射规范
- [数据一致性标准](mdc:data-consistency-standards.mdc) - 数据处理和同步一致性要求
- [性能优化标准](mdc:performance-optimization-standards.mdc) - 性能监控和优化策略
- [环境兼容性标准](mdc:environment-compatibility-standards.mdc) - 跨平台兼容性处理规范

## 🐍 Python开发规范

### 1. 异常处理规范
```python
# ❌ 禁止的异常处理
try:
    some_operation()
except Exception as e:  # 过于宽泛
    pass

# ✅ 正确的异常处理
try:
    result = api_client.get_user_data(user_id)
except requests.exceptions.RequestException as e:
    logger.error(f"API请求失败: {e}")
    raise UserDataFetchError(f"无法获取用户数据: {e}")
except ValueError as e:
    logger.error(f"数据格式错误: {e}")
    raise DataValidationError(f"用户数据格式无效: {e}")
```

### 2. 函数和类规范
```python
# ✅ 必须的函数规范
def process_user_data(
    user_data: Dict[str, Any],
    validation_rules: Optional[Dict[str, Any]] = None
) -> UserProcessResult:
    """
    处理用户数据并进行验证
    
    Args:
        user_data: 用户原始数据字典
        validation_rules: 可选的验证规则
        
    Returns:
        UserProcessResult: 处理结果对象
        
    Raises:
        ValidationError: 当数据验证失败时
        ProcessingError: 当处理过程中发生错误时
    """
    if not user_data:
        raise ValidationError("用户数据不能为空")
    
    # 具体实现...
    return UserProcessResult(success=True, data=processed_data)
```

### 3. 导入和依赖规范
```python
# ✅ 标准导入顺序
# 1. 标准库导入
import os
import sys
from typing import Dict, List, Optional, Any
from datetime import datetime

# 2. 第三方库导入
import requests
import streamlit as st
from sqlalchemy import create_engine

# 3. 本地模块导入
from services.user_service import UserService
from utils.validation import validate_user_data
from config import settings
```

## 🌐 Streamlit开发规范

### 1. 页面结构规范
```python
# ✅ 标准页面结构
def render_user_management_page():
    """用户管理页面"""
    
    # 页面配置
    st.set_page_config(
        page_title="用户管理",
        page_icon="👥",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # 页面标题和描述
    st.title("👥 用户管理")
    st.markdown("管理系统用户账户和权限")
    
    # 主要功能区域
    with st.container():
        col1, col2 = st.columns([2, 1])
        
        with col1:
            render_user_list()
        
        with col2:
            render_user_actions()
    
    # 错误处理和状态显示
    if 'error_message' in st.session_state:
        st.error(st.session_state.error_message)
        del st.session_state.error_message
```

### 2. 缓存使用规范
```python
# ✅ 正确的缓存使用
@st.cache_data(ttl=300)  # 5分钟TTL
def load_user_data(user_id: int) -> Dict[str, Any]:
    """加载用户数据（带缓存）"""
    try:
        user_service = UserService()
        return user_service.get_user_by_id(user_id)
    except Exception as e:
        st.error(f"加载用户数据失败: {e}")
        return {}

@st.cache_resource
def get_database_connection():
    """获取数据库连接（资源缓存）"""
    return create_engine(settings.DATABASE_URL)
```

## 🗄️ 数据库操作规范

### 1. 连接管理规范
```python
# ✅ 正确的数据库连接管理
class DatabaseManager:
    def __init__(self):
        self.engine = create_engine(
            settings.DATABASE_URL,
            pool_size=10,
            max_overflow=20,
            pool_timeout=30,
            pool_recycle=3600
        )
    
    @contextmanager
    def get_session(self):
        """获取数据库会话"""
        session = Session(self.engine)
        try:
            yield session
            session.commit()
        except Exception:
            session.rollback()
            raise
        finally:
            session.close()
```

### 2. 查询规范
```python
# ✅ 安全的查询实现
def get_users_by_criteria(
    db: Session,
    status: Optional[str] = None,
    limit: int = 100,
    offset: int = 0
) -> List[User]:
    """根据条件查询用户"""
    query = db.query(User)
    
    if status:
        query = query.filter(User.status == status)
    
    return query.offset(offset).limit(limit).all()

# ❌ 禁止SQL注入风险
def get_user_by_email_unsafe(email: str):
    # 禁止字符串拼接SQL
    query = f"SELECT * FROM users WHERE email = '{email}'"
    return db.execute(query)
```

## 🚀 性能优化规则

### 1. 异步处理规范
```python
# ✅ 正确的异步实现
import asyncio
from typing import List

async def fetch_multiple_apis(urls: List[str]) -> List[Dict[str, Any]]:
    """并发获取多个API数据"""
    async with aiohttp.ClientSession() as session:
        tasks = [fetch_single_api(session, url) for url in urls]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        valid_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"API请求失败 {urls[i]}: {result}")
            else:
                valid_results.append(result)
        
        return valid_results

async def fetch_single_api(session: aiohttp.ClientSession, url: str) -> Dict[str, Any]:
    """获取单个API数据"""
    try:
        async with session.get(url, timeout=30) as response:
            response.raise_for_status()
            return await response.json()
    except asyncio.TimeoutError:
        raise APITimeoutError(f"API请求超时: {url}")
    except aiohttp.ClientError as e:
        raise APIRequestError(f"API请求失败: {e}")
```

### 2. 内存优化规范
```python
# ✅ 内存优化的数据处理
def process_large_dataset(data_source: str) -> Iterator[Dict[str, Any]]:
    """使用生成器处理大数据集"""
    with open(data_source, 'r') as file:
        for line in file:
            try:
                data = json.loads(line)
                processed_data = transform_data(data)
                yield processed_data
            except json.JSONDecodeError:
                logger.warning(f"跳过无效JSON行: {line[:100]}")
                continue

# ❌ 禁止一次性加载大数据
def load_all_data_bad(data_source: str) -> List[Dict[str, Any]]:
    with open(data_source, 'r') as file:
        return [json.loads(line) for line in file]  # 内存风险
```

## 🔒 安全编码规范

### 1. 输入验证规范
```python
# ✅ 严格的输入验证
from pydantic import BaseModel, validator, EmailStr

class UserCreateRequest(BaseModel):
    email: EmailStr
    name: str
    age: int
    
    @validator('name')
    def validate_name(cls, v):
        if not v or len(v.strip()) < 2:
            raise ValueError('姓名长度必须至少2个字符')
        if len(v) > 50:
            raise ValueError('姓名长度不能超过50个字符')
        return v.strip()
    
    @validator('age')
    def validate_age(cls, v):
        if v < 0 or v > 150:
            raise ValueError('年龄必须在0-150之间')
        return v

def create_user(request: UserCreateRequest) -> User:
    """创建用户（带验证）"""
    # Pydantic自动验证输入
    user_data = request.dict()
    return user_service.create_user(user_data)
```

### 2. 敏感信息处理
```python
# ✅ 安全的敏感信息处理
import hashlib
import secrets
from cryptography.fernet import Fernet

class SecurityManager:
    def __init__(self):
        self.encryption_key = os.getenv('ENCRYPTION_KEY')
        if not self.encryption_key:
            raise ValueError("ENCRYPTION_KEY环境变量未设置")
        self.cipher = Fernet(self.encryption_key.encode())
    
    def hash_password(self, password: str) -> str:
        """安全的密码哈希"""
        salt = secrets.token_hex(32)
        password_hash = hashlib.pbkdf2_hmac(
            'sha256',
            password.encode('utf-8'),
            salt.encode('utf-8'),
            100000  # 迭代次数
        )
        return f"{salt}:{password_hash.hex()}"
    
    def encrypt_sensitive_data(self, data: str) -> str:
        """加密敏感数据"""
        return self.cipher.encrypt(data.encode()).decode()
    
    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """解密敏感数据"""
        return self.cipher.decrypt(encrypted_data.encode()).decode()
```

## 🧪 测试规范

### 1. 单元测试规范
```python
# ✅ 完整的单元测试
import pytest
from unittest.mock import Mock, patch

class TestUserService:
    def setup_method(self):
        """测试前准备"""
        self.user_service = UserService()
        self.mock_db = Mock()
    
    def test_create_user_success(self):
        """测试成功创建用户"""
        # 准备测试数据
        user_data = {
            'email': '<EMAIL>',
            'name': 'Test User',
            'age': 25
        }
        
        # 模拟数据库操作
        with patch.object(self.user_service, 'db', self.mock_db):
            self.mock_db.add.return_value = None
            self.mock_db.commit.return_value = None
            
            # 执行测试
            result = self.user_service.create_user(user_data)
            
            # 验证结果
            assert result.email == user_data['email']
            assert result.name == user_data['name']
            self.mock_db.add.assert_called_once()
            self.mock_db.commit.assert_called_once()
    
    def test_create_user_invalid_email(self):
        """测试无效邮箱创建用户"""
        user_data = {
            'email': 'invalid-email',
            'name': 'Test User',
            'age': 25
        }
        
        with pytest.raises(ValidationError) as exc_info:
            self.user_service.create_user(user_data)
        
        assert "邮箱格式无效" in str(exc_info.value)
```

### 2. 集成测试规范
```python
# ✅ 集成测试示例
@pytest.mark.integration
class TestUserAPI:
    def setup_class(self):
        """测试类准备"""
        self.client = TestClient(app)
        self.test_db = create_test_database()
    
    def teardown_class(self):
        """测试类清理"""
        drop_test_database(self.test_db)
    
    def test_user_registration_flow(self):
        """测试用户注册流程"""
        # 1. 注册用户
        registration_data = {
            'email': '<EMAIL>',
            'name': 'Integration Test',
            'password': 'SecurePassword123!'
        }
        
        response = self.client.post('/api/users/register', json=registration_data)
        assert response.status_code == 201
        
        user_data = response.json()
        user_id = user_data['id']
        
        # 2. 验证用户创建
        response = self.client.get(f'/api/users/{user_id}')
        assert response.status_code == 200
        assert response.json()['email'] == registration_data['email']
        
        # 3. 测试登录
        login_data = {
            'email': registration_data['email'],
            'password': registration_data['password']
        }
        
        response = self.client.post('/api/auth/login', json=login_data)
        assert response.status_code == 200
        assert 'access_token' in response.json()
```

## 📦 部署规范

### 1. 环境配置规范
```python
# ✅ 环境配置管理
from pydantic import BaseSettings

class Settings(BaseSettings):
    """应用配置"""
    
    # 数据库配置
    DATABASE_URL: str
    DATABASE_POOL_SIZE: int = 10
    
    # API配置
    API_BASE_URL: str
    API_TIMEOUT: int = 30
    
    # 安全配置
    SECRET_KEY: str
    ENCRYPTION_KEY: str
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # 性能配置
    CACHE_TTL: int = 300
    MAX_WORKERS: int = 4
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# 全局配置实例
settings = Settings()
```

### 2. 健康检查规范
```python
# ✅ 健康检查实现
from fastapi import FastAPI, HTTPException
from typing import Dict, Any

app = FastAPI()

@app.get("/health")
async def health_check() -> Dict[str, Any]:
    """系统健康检查"""
    health_status = {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
        "checks": {}
    }
    
    # 数据库连接检查
    try:
        db_manager = DatabaseManager()
        with db_manager.get_session() as session:
            session.execute("SELECT 1")
        health_status["checks"]["database"] = "healthy"
    except Exception as e:
        health_status["checks"]["database"] = f"unhealthy: {e}"
        health_status["status"] = "unhealthy"
    
    # API依赖检查
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{settings.API_BASE_URL}/health", timeout=5) as response:
                if response.status == 200:
                    health_status["checks"]["external_api"] = "healthy"
                else:
                    health_status["checks"]["external_api"] = f"unhealthy: HTTP {response.status}"
                    health_status["status"] = "degraded"
    except Exception as e:
        health_status["checks"]["external_api"] = f"unhealthy: {e}"
        health_status["status"] = "degraded"
    
    # 根据状态返回相应的HTTP状态码
    if health_status["status"] == "unhealthy":
        raise HTTPException(status_code=503, detail=health_status)
    
    return health_status
```

## 🚫 严格禁止的模式

### 1. 代码质量禁止项
```python
# ❌ 绝对禁止的代码模式

# 1. TODO标记
def process_data():
    # TODO: 实现数据处理逻辑
    pass

# 2. 通用异常捕获
try:
    risky_operation()
except Exception:
    pass

# 3. 硬编码敏感信息
DATABASE_URL = "postgresql://user:password@localhost/db"
API_KEY = "sk-1234567890abcdef"

# 4. 模拟数据混入
def get_user_data(user_id):
    if user_id == "test":
        return {"id": "test", "name": "Test User"}  # 模拟数据
    return real_api_call(user_id)

# 5. 不安全的SQL
def get_user_by_email(email):
    query = f"SELECT * FROM users WHERE email = '{email}'"
    return execute_query(query)
```

### 2. 架构禁止项
```python
# ❌ 禁止的架构模式

# 1. 循环导入
# file1.py
from file2 import function_b

# file2.py
from file1 import function_a

# 2. 全局状态滥用
GLOBAL_USER_DATA = {}  # 禁止全局状态

# 3. 紧耦合设计
class UserService:
    def __init__(self):
        self.db = PostgreSQLDatabase()  # 硬编码依赖
        self.email_service = SMTPEmailService()  # 硬编码依赖
```

## 📋 开发检查清单

### 代码提交前检查
- [ ] 是否移除了所有TODO标记？
- [ ] 是否使用了具体的异常类型？
- [ ] 是否添加了完整的文档字符串？
- [ ] 是否通过了所有单元测试？
- [ ] 是否遵循了命名规范？
- [ ] 是否进行了代码格式化？
- [ ] 是否移除了调试代码和打印语句？

### 功能开发检查
- [ ] 是否实现了完整的功能逻辑？
- [ ] 是否添加了适当的错误处理？
- [ ] 是否考虑了边界情况？
- [ ] 是否进行了性能优化？
- [ ] 是否添加了必要的日志？
- [ ] 是否考虑了安全性？

### 部署前检查
- [ ] 是否配置了正确的环境变量？
- [ ] 是否通过了集成测试？
- [ ] 是否进行了性能测试？
- [ ] 是否准备了回滚方案？
- [ ] 是否更新了相关文档？

---

**重要说明**: 
1. 本规范是强制性的，所有代码必须严格遵循
2. 违反规范的代码将不被接受，需要重新修改
3. 定期审查和更新规范，确保与最佳实践保持一致
4. 所有团队成员都有责任维护和改进这些规范

**文档版本**: v2.0  
**最后更新**: 2024-12-19  
**维护者**: 开发团队 