#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片汇总脚本
功能：将指定文件夹下所有images子文件夹中的图片文件汇总到一个统一的文件夹
"""

import os
import glob
import shutil
from pathlib import Path
from typing import List, Dict


def find_images_folders(root_dir: str) -> List[str]:
    """
    递归查找所有名为 'images' 的文件夹
    """
    images_folders = []
    for root, dirs, files in os.walk(root_dir):
        if 'images' in dirs:
            images_folder = os.path.join(root, 'images')
            images_folders.append(images_folder)
    return images_folders


def get_image_files(folder_path: str) -> List[str]:
    """
    获取文件夹中所有图片文件
    支持常见的图片格式：jpg, jpeg, png, gif, bmp, webp, svg
    """
    image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.bmp', '*.webp', '*.svg']
    image_files = []
    
    for ext in image_extensions:
        pattern = os.path.join(folder_path, '**', ext)
        files = glob.glob(pattern, recursive=True)
        image_files.extend(files)
        
        # 同时搜索大写扩展名
        pattern = os.path.join(folder_path, '**', ext.upper())
        files = glob.glob(pattern, recursive=True)
        image_files.extend(files)
    
    return image_files


def generate_unique_filename(target_dir: str, original_filename: str, source_path: str) -> str:
    """
    生成唯一的文件名，避免重名冲突
    """
    filename = os.path.basename(original_filename)
    name, ext = os.path.splitext(filename)
    
    # 如果文件不存在，直接使用原名
    target_path = os.path.join(target_dir, filename)
    if not os.path.exists(target_path):
        return filename
    
    # 如果文件存在，添加来源路径信息
    # 获取来源文件夹的相对路径信息
    rel_path = os.path.relpath(os.path.dirname(source_path), start=os.path.dirname(os.path.dirname(source_path)))
    safe_rel_path = rel_path.replace('\\', '_').replace('/', '_').replace(':', '').replace(' ', '_')
    
    counter = 1
    while True:
        new_filename = f"{name}_{safe_rel_path}_{counter}{ext}"
        target_path = os.path.join(target_dir, new_filename)
        if not os.path.exists(target_path):
            return new_filename
        counter += 1


def copy_images_to_target(source_folders: List[str], target_dir: str) -> Dict[str, int]:
    """
    将所有图片复制到目标文件夹
    返回统计信息
    """
    # 确保目标文件夹存在
    os.makedirs(target_dir, exist_ok=True)
    
    stats = {
        'total_copied': 0,
        'folders_processed': 0,
        'errors': 0,
        'skipped_duplicates': 0
    }
    
    print(f"开始复制图片到目标文件夹: {target_dir}")
    print("-" * 60)
    
    for folder_path in source_folders:
        print(f"\n处理文件夹: {folder_path}")
        stats['folders_processed'] += 1
        
        # 获取该文件夹中的所有图片
        image_files = get_image_files(folder_path)
        
        if not image_files:
            print(f"  → 未找到图片文件")
            continue
        
        print(f"  → 找到 {len(image_files)} 个图片文件")
        
        folder_copied = 0
        for image_file in image_files:
            try:
                # 生成目标文件名
                target_filename = generate_unique_filename(target_dir, image_file, image_file)
                target_path = os.path.join(target_dir, target_filename)
                
                # 复制文件
                shutil.copy2(image_file, target_path)
                folder_copied += 1
                stats['total_copied'] += 1
                
                print(f"    ✓ {os.path.basename(image_file)} → {target_filename}")
                
            except Exception as e:
                print(f"    ✗ 复制失败 {os.path.basename(image_file)}: {str(e)}")
                stats['errors'] += 1
        
        print(f"  → 成功复制 {folder_copied} 个文件")
    
    return stats


def main():
    """
    主函数
    """
    # 设置源目录和目标目录
    source_dir = 'output_new - 副本 123脚本备份'
    target_dir = 'collected_images'
    
    print("图片汇总脚本")
    print("="*60)
    print("功能说明：")
    print("1. 查找源目录下所有名为'images'的文件夹")
    print("2. 收集其中所有图片文件")
    print("3. 复制到统一的目标文件夹")
    print("4. 自动处理重名文件冲突")
    print("="*60)
    print(f"源目录: {source_dir}")
    print(f"目标目录: {target_dir}")
    
    # 检查源目录是否存在
    if not os.path.exists(source_dir):
        print(f"\n错误：源目录 '{source_dir}' 不存在！")
        return
    
    # 查找所有images文件夹
    print(f"\n正在查找 '{source_dir}' 下的所有images文件夹...")
    images_folders = find_images_folders(source_dir)
    
    if not images_folders:
        print("未找到任何images文件夹！")
        return
    
    print(f"找到 {len(images_folders)} 个images文件夹：")
    for i, folder in enumerate(images_folders, 1):
        rel_path = os.path.relpath(folder, source_dir)
        print(f"  {i}. {rel_path}")
    
    # 预览图片文件总数
    total_images = 0
    for folder in images_folders:
        images_in_folder = len(get_image_files(folder))
        total_images += images_in_folder
        rel_path = os.path.relpath(folder, source_dir)
        print(f"    → {rel_path}: {images_in_folder} 个图片文件")
    
    print(f"\n总计发现 {total_images} 个图片文件")
    
    if total_images == 0:
        print("没有找到任何图片文件，程序结束。")
        return
    
    # 开始复制
    print(f"\n开始汇总图片...")
    stats = copy_images_to_target(images_folders, target_dir)
    
    # 输出最终统计
    print("\n" + "="*60)
    print("汇总完成！")
    print(f"处理文件夹数: {stats['folders_processed']}")
    print(f"成功复制图片: {stats['total_copied']}")
    print(f"复制失败: {stats['errors']}")
    print(f"目标文件夹: {os.path.abspath(target_dir)}")
    
    if stats['total_copied'] > 0:
        print(f"\n所有图片已汇总到: {os.path.abspath(target_dir)}")


if __name__ == "__main__":
    main() 