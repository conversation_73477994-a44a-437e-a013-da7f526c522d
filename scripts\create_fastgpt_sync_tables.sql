-- FastGPT同步模块数据表创建脚本
-- 创建时间: 2024-12-19
-- 描述: 用于Excel导入产品型号并同步到FastGPT知识库的相关表结构

-- 1. 待同步产品信息表
CREATE TABLE IF NOT EXISTS sync_products_queue (
    id SERIAL PRIMARY KEY,
    product_id INTEGER NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    product_model VARCHAR(100) NOT NULL,
    brand_name VARCHAR(100),
    category_name VARCHAR(100),
    introduction TEXT,
    details TEXT,
    param_info TEXT,
    use_to TEXT,
    price DECIMAL(10,2),
    small_img VARCHAR(500),
    banner VARCHAR(500),
    other TEXT,
    -- 同步状态字段
    sync_status VARCHAR(20) DEFAULT 'pending', -- pending, processing, completed, failed
    fastgpt_dataset_id VARCHAR(100), -- FastGPT知识库ID
    fastgpt_collection_id VARCHAR(100), -- FastGPT集合ID
    fastgpt_data_id VARCHAR(100), -- FastGPT数据ID
    sync_error_message TEXT,
    sync_attempts INTEGER DEFAULT 0,
    -- 导入来源字段
    import_batch_id VARCHAR(100) NOT NULL, -- 导入批次ID
    import_source VARCHAR(100) DEFAULT 'excel', -- excel, manual, api
    excel_filename VARCHAR(255), -- Excel文件名
    excel_row_number INTEGER, -- Excel行号
    -- 匹配信息字段
    match_type VARCHAR(20) DEFAULT 'exact', -- exact, fuzzy, manual
    match_confidence DECIMAL(3,2), -- 匹配置信度 0.00-1.00
    matched_by VARCHAR(100), -- 匹配字段 (model, name, spec等)
    -- 审计字段
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(100),
    update_by VARCHAR(100),
    -- 索引
    UNIQUE(product_id, import_batch_id)
);

-- 2. 待同步方案信息表
CREATE TABLE IF NOT EXISTS sync_solutions_queue (
    id SERIAL PRIMARY KEY,
    solution_id INTEGER NOT NULL,
    solution_name VARCHAR(255) NOT NULL,
    solution_category VARCHAR(100),
    solution_content TEXT,
    solution_description TEXT,
    solution_images TEXT, -- JSON格式存储图片列表
    hardware_list TEXT, -- 硬件清单
    software_list TEXT, -- 软件清单
    target_industry VARCHAR(100), -- 目标行业
    application_scenario TEXT, -- 应用场景
    -- 同步状态字段
    sync_status VARCHAR(20) DEFAULT 'pending',
    fastgpt_dataset_id VARCHAR(100),
    fastgpt_collection_id VARCHAR(100),
    fastgpt_data_id VARCHAR(100),
    sync_error_message TEXT,
    sync_attempts INTEGER DEFAULT 0,
    -- 导入来源字段
    import_batch_id VARCHAR(100) NOT NULL,
    import_source VARCHAR(100) DEFAULT 'manual',
    -- 审计字段
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(100),
    update_by VARCHAR(100),
    -- 索引
    UNIQUE(solution_id, import_batch_id)
);

-- 3. 待同步案例信息表
CREATE TABLE IF NOT EXISTS sync_cases_queue (
    id SERIAL PRIMARY KEY,
    case_id INTEGER NOT NULL,
    case_name VARCHAR(255) NOT NULL,
    case_category VARCHAR(100),
    case_content TEXT,
    case_description TEXT,
    case_images TEXT, -- JSON格式存储图片列表
    company_name VARCHAR(255), -- 客户公司名称
    project_location VARCHAR(255), -- 项目地点
    project_scale VARCHAR(100), -- 项目规模
    industry_type VARCHAR(100), -- 行业类型
    solution_brief TEXT, -- 解决方案简介
    key_benefits TEXT, -- 关键效益
    -- 同步状态字段
    sync_status VARCHAR(20) DEFAULT 'pending',
    fastgpt_dataset_id VARCHAR(100),
    fastgpt_collection_id VARCHAR(100),
    fastgpt_data_id VARCHAR(100),
    sync_error_message TEXT,
    sync_attempts INTEGER DEFAULT 0,
    -- 导入来源字段
    import_batch_id VARCHAR(100) NOT NULL,
    import_source VARCHAR(100) DEFAULT 'manual',
    -- 审计字段
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(100),
    update_by VARCHAR(100),
    -- 索引
    UNIQUE(case_id, import_batch_id)
);

-- 4. FastGPT同步日志表
CREATE TABLE IF NOT EXISTS fastgpt_sync_logs (
    id SERIAL PRIMARY KEY,
    batch_id VARCHAR(100) NOT NULL, -- 批次ID
    sync_type VARCHAR(20) NOT NULL, -- products, solutions, cases
    entity_id INTEGER NOT NULL, -- 实体ID
    entity_name VARCHAR(255), -- 实体名称
    operation_type VARCHAR(20) NOT NULL, -- create, update, delete
    sync_status VARCHAR(20) NOT NULL, -- success, failed, processing
    fastgpt_dataset_id VARCHAR(100),
    fastgpt_collection_id VARCHAR(100),
    fastgpt_data_id VARCHAR(100),
    request_data TEXT, -- JSON格式的请求数据
    response_data TEXT, -- JSON格式的响应数据
    error_message TEXT,
    sync_duration INTEGER, -- 同步耗时（毫秒）
    retry_count INTEGER DEFAULT 0,
    -- 审计字段
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(100)
);

-- 5. Excel导入记录表
CREATE TABLE IF NOT EXISTS excel_import_records (
    id SERIAL PRIMARY KEY,
    batch_id VARCHAR(100) UNIQUE NOT NULL,
    filename VARCHAR(255) NOT NULL,
    file_size INTEGER, -- 文件大小（字节）
    file_md5 VARCHAR(32), -- 文件MD5校验码
    sheet_name VARCHAR(100), -- 工作表名称
    total_rows INTEGER, -- 总行数
    header_row INTEGER DEFAULT 1, -- 标题行号
    model_column VARCHAR(50), -- 型号列名或列号
    processed_rows INTEGER DEFAULT 0, -- 已处理行数
    matched_rows INTEGER DEFAULT 0, -- 匹配成功行数
    failed_rows INTEGER DEFAULT 0, -- 匹配失败行数
    import_status VARCHAR(20) DEFAULT 'processing', -- processing, completed, failed
    error_message TEXT,
    processing_config TEXT, -- JSON格式的处理配置
    -- 审计字段
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(100),
    update_by VARCHAR(100)
);

-- 6. 产品型号匹配规则表
CREATE TABLE IF NOT EXISTS product_model_match_rules (
    id SERIAL PRIMARY KEY,
    rule_name VARCHAR(100) NOT NULL,
    rule_type VARCHAR(20) NOT NULL, -- exact, fuzzy, regex, custom
    pattern TEXT NOT NULL, -- 匹配模式
    priority INTEGER DEFAULT 0, -- 优先级，数字越大优先级越高
    is_active BOOLEAN DEFAULT true,
    match_field VARCHAR(50) DEFAULT 'model', -- 匹配字段：model, spec, name等
    description TEXT, -- 规则描述
    -- 审计字段
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(100),
    update_by VARCHAR(100)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_sync_products_queue_status ON sync_products_queue(sync_status);
CREATE INDEX IF NOT EXISTS idx_sync_products_queue_batch ON sync_products_queue(import_batch_id);
CREATE INDEX IF NOT EXISTS idx_sync_products_queue_product ON sync_products_queue(product_id);

CREATE INDEX IF NOT EXISTS idx_sync_solutions_queue_status ON sync_solutions_queue(sync_status);
CREATE INDEX IF NOT EXISTS idx_sync_solutions_queue_batch ON sync_solutions_queue(import_batch_id);

CREATE INDEX IF NOT EXISTS idx_sync_cases_queue_status ON sync_cases_queue(sync_status);
CREATE INDEX IF NOT EXISTS idx_sync_cases_queue_batch ON sync_cases_queue(import_batch_id);

CREATE INDEX IF NOT EXISTS idx_fastgpt_sync_logs_batch ON fastgpt_sync_logs(batch_id);
CREATE INDEX IF NOT EXISTS idx_fastgpt_sync_logs_type ON fastgpt_sync_logs(sync_type);
CREATE INDEX IF NOT EXISTS idx_fastgpt_sync_logs_status ON fastgpt_sync_logs(sync_status);
CREATE INDEX IF NOT EXISTS idx_fastgpt_sync_logs_time ON fastgpt_sync_logs(create_time);

CREATE INDEX IF NOT EXISTS idx_excel_import_records_batch ON excel_import_records(batch_id);
CREATE INDEX IF NOT EXISTS idx_excel_import_records_status ON excel_import_records(import_status);

-- 添加注释
COMMENT ON TABLE sync_products_queue IS '待同步产品信息队列表，用于存储从Excel导入或手动添加的待同步产品信息';
COMMENT ON TABLE sync_solutions_queue IS '待同步方案信息队列表，用于存储待同步的解决方案信息';
COMMENT ON TABLE sync_cases_queue IS '待同步案例信息队列表，用于存储待同步的项目案例信息';
COMMENT ON TABLE fastgpt_sync_logs IS 'FastGPT同步操作日志表，记录所有同步操作的详细信息';
COMMENT ON TABLE excel_import_records IS 'Excel文件导入记录表，跟踪文件导入的详细状态';
COMMENT ON TABLE product_model_match_rules IS '产品型号匹配规则表，定义型号匹配的各种规则';

-- 插入默认的匹配规则
INSERT INTO product_model_match_rules (rule_name, rule_type, pattern, priority, match_field, description) VALUES
('精确匹配-型号', 'exact', '{model}', 100, 'model', '直接匹配paramInfoList中的型号字段'),
('精确匹配-规格', 'exact', '{spec}', 90, 'spec', '直接匹配产品规格字段'),
('模糊匹配-型号', 'fuzzy', '{model}', 80, 'model', '模糊匹配型号字段，允许部分差异'),
('正则匹配-型号格式', 'regex', '^[A-Z]{2,4}[0-9]{4,6}$', 70, 'model', '匹配常见的型号格式（2-4个字母+4-6个数字）'); 