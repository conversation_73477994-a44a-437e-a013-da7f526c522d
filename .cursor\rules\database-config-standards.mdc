---
description:
globs:
alwaysApply: false
---
# 数据库配置标准

## 📋 强制性数据库配置要求

**重要提醒：项目中所有数据库配置必须严格遵循以下标准，违规将导致系统故障！**

### 🔧 标准数据库连接配置

所有数据库配置必须使用以下格式：

```python
DATABASE_URL = "***********************************************/product"
```

### 📂 必须更新的配置文件

以下文件中的数据库配置必须使用标准格式：

1. **主配置文件** - [config/config.py](mdc:config/config.py)
2. **环境变量文件** - [env.example](mdc:env.example)
3. **脚本文件** - [set_env.bat](mdc:set_env.bat), [set_env.ps1](mdc:set_env.ps1)
4. **数据库修复脚本** - 所有 `fix_*.py` 文件
5. **测试文件** - 所有 `test_*.py` 文件

### 🚫 禁止的配置模式

**严禁使用以下配置模式：**

```python
# ❌ 错误配置 - 禁止使用 localhost
"host": "localhost"
"host": "127.0.0.1"

# ❌ 错误配置 - 禁止使用错误的数据库名
"database": "yunshang"
"database": "dataann_email"
"database": "postgres"

# ❌ 错误配置 - 禁止使用错误的用户名
"user": "postgres"

# ❌ 错误配置 - 禁止使用错误的连接字符串
"postgresql://postgres:123456@localhost:5432/yunshang"
"postgresql://postgres:password@localhost:5432/dataann_email"
```

### ✅ 正确的配置模式

**必须使用以下标准配置：**

```python
# ✅ 正确的环境变量默认值
DATABASE_URL = os.getenv("DATABASE_URL", "***********************************************/product")

# ✅ 正确的分离配置
db_config = {
    "host": os.getenv("DB_HOST", "***********"),
    "port": os.getenv("DB_PORT", 5432),
    "database": os.getenv("DB_NAME", "product"),
    "user": os.getenv("DB_USER", "username"),
    "password": os.getenv("DB_PASSWORD", "password"),
}

# ✅ 正确的连接池配置
database_url = os.getenv('DATABASE_URL', '***********************************************/product')
```

### 📝 配置检查清单

在提交代码前，必须检查：

- [ ] 所有数据库主机地址为 `***********`
- [ ] 所有数据库端口为 `5432`
- [ ] 所有数据库名称为 `product`
- [ ] 所有用户名为 `username`
- [ ] 所有密码为 `password`
- [ ] 连接字符串格式为 `***********************************************/product`

### 🔍 代码审查要点

代码审查时必须验证：

1. **无硬编码本地配置**：不能包含 `localhost` 或 `127.0.0.1`
2. **统一数据库名称**：必须使用 `product`，不能使用 `yunshang`、`dataann_email` 等
3. **统一用户凭据**：用户名必须为 `username`
4. **环境变量优先**：优先使用环境变量，提供正确的默认值
5. **连接池配置**：使用标准连接字符串格式

### 🛠️ 修复指南

如发现不符合标准的配置，立即执行以下操作：

1. **定位所有违规文件**：搜索 `localhost`、`yunshang`、`postgres` 等关键词
2. **批量替换配置**：将所有违规配置替换为标准格式
3. **测试连接**：确认修改后数据库连接正常
4. **更新文档**：确保文档反映最新配置要求

### ⚠️ 紧急修复命令

使用以下命令快速检查和修复违规配置：

```bash
# 检查违规配置
grep -r "localhost" . --include="*.py" --exclude-dir=venv*
grep -r "yunshang" . --include="*.py" --exclude-dir=venv*
grep -r "dataann_email" . --include="*.py" --exclude-dir=venv*

# 批量替换（谨慎使用）
find . -name "*.py" -not -path "./venv*" -exec sed -i 's/localhost/***********/g' {} \;
find . -name "*.py" -not -path "./venv*" -exec sed -i 's/yunshang/product/g' {} \;
```

### 📚 相关文件引用

- 主配置: [config/config.py](mdc:config/config.py)
- 环境示例: [env.example](mdc:env.example)
- 数据库工具: [utils/database.py](mdc:utils/database.py)
- 启动脚本: [start_app.bat](mdc:start_app.bat)

---

**⚠️ 重要警告**: 任何偏离此标准的数据库配置都将导致系统无法正常运行。所有开发人员必须严格遵守此规范！
