#!/usr/bin/env python3
"""
数据库修复运行器
执行数据库架构修复，解决同步系统中的数据库问题
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

try:
    from scripts.fix_database_schema import DatabaseSchemaFixer

    def main():
        """主函数"""
        print("开始执行数据库架构修复...")

        fixer = DatabaseSchemaFixer()

        try:
            if not fixer.get_connection():
                print("❌ 数据库连接失败")
                return 1

            success = fixer.run_full_fix()

            if success:
                print("✅ 数据库架构修复成功")
                return 0
            else:
                print("❌ 数据库架构修复失败")
                return 1

        except Exception as e:
            print(f"❌ 执行过程中出现错误: {e}")
            return 1
        finally:
            fixer.close()

    if __name__ == "__main__":
        sys.exit(main())

except ImportError as e:
    print(f"❌ 导入模块失败: {e}")
    print("请确保当前目录为项目根目录")
    sys.exit(1)
