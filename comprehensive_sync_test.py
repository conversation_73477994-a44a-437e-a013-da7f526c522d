#!/usr/bin/env python3
"""
全面测试数据同步修复效果
"""

import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.database import get_db_connection
import json


def test_product_knowledge_content():
    """测试产品知识内容完整性"""
    try:
        print("=== 测试产品知识内容完整性 ===")

        conn = get_db_connection()
        cursor = conn.cursor()

        # 获取所有产品的知识内容字段
        cursor.execute(
            """
            SELECT 
                COUNT(*) as total_products,
                COUNT(CASE WHEN introduction IS NOT NULL AND introduction != '' THEN 1 END) as has_introduction,
                COUNT(CASE WHEN details IS NOT NULL AND details != '' THEN 1 END) as has_details,
                COUNT(CASE WHEN param_info IS NOT NULL AND param_info != '' THEN 1 END) as has_param_info,
                COUNT(CASE WHEN use_to IS NOT NULL AND use_to != '' THEN 1 END) as has_use_to,
                COUNT(CASE WHEN qualifications IS NOT NULL AND qualifications != '' THEN 1 END) as has_qualifications,
                COUNT(CASE WHEN instructions IS NOT NULL AND instructions != '' THEN 1 END) as has_instructions,
                COUNT(CASE WHEN guide IS NOT NULL AND guide != '' THEN 1 END) as has_guide,
                COUNT(CASE WHEN common_problem IS NOT NULL AND common_problem != '' THEN 1 END) as has_common_problem,
                COUNT(CASE WHEN new_param IS NOT NULL AND new_param != '' THEN 1 END) as has_new_param,
                COUNT(CASE WHEN param_info_list IS NOT NULL AND param_info_list != '' THEN 1 END) as has_param_info_list,
                COUNT(CASE WHEN video_explanation IS NOT NULL AND video_explanation != '' THEN 1 END) as has_video_explanation,
                COUNT(CASE WHEN video_installation IS NOT NULL AND video_installation != '' THEN 1 END) as has_video_installation,
                COUNT(CASE WHEN video_troubleshooting IS NOT NULL AND video_troubleshooting != '' THEN 1 END) as has_video_troubleshooting,
                COUNT(CASE WHEN accessory IS NOT NULL AND accessory != '' THEN 1 END) as has_accessory
            FROM products
        """
        )

        result = cursor.fetchone()
        if not result:
            print("❌ 无法获取统计数据")
            return False

        # 直接使用查询结果，按顺序对应
        total = result[0]
        has_introduction = result[1]
        has_details = result[2]
        has_param_info = result[3]
        has_use_to = result[4]
        has_qualifications = result[5]
        has_instructions = result[6]
        has_guide = result[7]
        has_common_problem = result[8]
        has_new_param = result[9]
        has_param_info_list = result[10]
        has_video_explanation = result[11]
        has_video_installation = result[12]
        has_video_troubleshooting = result[13]
        has_accessory = result[14]
        print(f"总产品数: {total}")

        if total == 0:
            print("❌ 数据库中没有产品数据")
            return False

        # 计算各字段覆盖率
        knowledge_fields = [
            ("introduction", "产品介绍"),
            ("details", "产品详情"),
            ("param_info", "参数信息"),
            ("use_to", "使用场景"),
            ("qualifications", "资质认证"),
            ("instructions", "说明书"),
            ("guide", "操作指南"),
            ("common_problem", "常见问题"),
            ("new_param", "新参数"),
            ("param_info_list", "参数列表"),
            ("video_explanation", "说明视频"),
            ("video_installation", "安装视频"),
            ("video_troubleshooting", "故障排除视频"),
            ("accessory", "配件信息"),
        ]

        coverage_stats = []
        for field, desc in knowledge_fields:
            count = stats.get(f"has_{field}", 0)
            percentage = (count / total * 100) if total > 0 else 0
            coverage_stats.append((field, desc, count, percentage))
            print(f"{desc}: {count}/{total} ({percentage:.1f}%)")

        # 计算总体覆盖率
        total_coverage = sum(stat[2] for stat in coverage_stats)
        max_coverage = total * len(knowledge_fields)
        overall_percentage = (
            (total_coverage / max_coverage * 100) if max_coverage > 0 else 0
        )

        print(
            f"\n总体知识内容覆盖率: {total_coverage}/{max_coverage} ({overall_percentage:.1f}%)"
        )

        cursor.close()
        conn.close()

        # 判断测试是否通过
        success_threshold = 50.0  # 50%以上覆盖率认为成功
        if overall_percentage >= success_threshold:
            print(
                f"✅ 知识内容测试通过 (覆盖率 {overall_percentage:.1f}% >= {success_threshold}%)"
            )
            return True
        else:
            print(
                f"❌ 知识内容测试失败 (覆盖率 {overall_percentage:.1f}% < {success_threshold}%)"
            )
            return False

    except Exception as e:
        print(f"知识内容测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_attachment_processing():
    """测试附件处理功能"""
    try:
        print("\n=== 测试附件处理功能 ===")

        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查附件相关字段
        cursor.execute(
            """
            SELECT 
                COUNT(*) as total_products,
                COUNT(CASE WHEN small_img IS NOT NULL AND small_img != '' THEN 1 END) as has_small_img,
                COUNT(CASE WHEN banner IS NOT NULL AND banner != '' THEN 1 END) as has_banner,
                COUNT(CASE WHEN size_img IS NOT NULL AND size_img != '' THEN 1 END) as has_size_img,
                COUNT(CASE WHEN other IS NOT NULL AND other != '' THEN 1 END) as has_other_attachments
            FROM products
        """
        )

        result = cursor.fetchone()
        columns = [desc[0] for desc in cursor.description]
        stats = dict(zip(columns, result))

        total = stats["total_products"]
        print(f"总产品数: {total}")

        if total == 0:
            print("❌ 数据库中没有产品数据")
            return False

        # 检查附件字段
        attachment_fields = [
            ("small_img", "缩略图"),
            ("banner", "横幅图"),
            ("size_img", "尺寸图"),
            ("other_attachments", "其他附件"),
        ]

        attachment_coverage = 0
        for field, desc in attachment_fields:
            count = stats.get(f"has_{field}", 0)
            percentage = (count / total * 100) if total > 0 else 0
            attachment_coverage += count
            print(f"{desc}: {count}/{total} ({percentage:.1f}%)")

        # 计算附件总体覆盖率
        max_attachment_coverage = total * len(attachment_fields)
        attachment_percentage = (
            (attachment_coverage / max_attachment_coverage * 100)
            if max_attachment_coverage > 0
            else 0
        )

        print(
            f"\n附件总体覆盖率: {attachment_coverage}/{max_attachment_coverage} ({attachment_percentage:.1f}%)"
        )

        cursor.close()
        conn.close()

        # 判断测试是否通过
        success_threshold = 30.0  # 30%以上覆盖率认为成功
        if attachment_percentage >= success_threshold:
            print(
                f"✅ 附件处理测试通过 (覆盖率 {attachment_percentage:.1f}% >= {success_threshold}%)"
            )
            return True
        else:
            print(
                f"❌ 附件处理测试失败 (覆盖率 {attachment_percentage:.1f}% < {success_threshold}%)"
            )
            return False

    except Exception as e:
        print(f"附件处理测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_data_integrity():
    """测试数据完整性"""
    try:
        print("\n=== 测试数据完整性 ===")

        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查基本数据完整性
        cursor.execute(
            """
            SELECT 
                COUNT(*) as total_products,
                COUNT(CASE WHEN name IS NOT NULL AND name != '' THEN 1 END) as has_name,
                COUNT(CASE WHEN id IS NOT NULL THEN 1 END) as has_id,
                COUNT(CASE WHEN last_sync_time IS NOT NULL THEN 1 END) as has_sync_time
            FROM products
        """
        )

        result = cursor.fetchone()
        columns = [desc[0] for desc in cursor.description]
        stats = dict(zip(columns, result))

        total = stats["total_products"]
        print(f"总产品数: {total}")

        if total == 0:
            print("❌ 数据库中没有产品数据")
            return False

        # 检查必要字段完整性
        required_fields = [
            ("id", "ID"),
            ("name", "产品名称"),
            ("sync_time", "同步时间"),
        ]

        integrity_issues = 0
        for field, desc in required_fields:
            count = stats.get(f"has_{field}", 0)
            percentage = (count / total * 100) if total > 0 else 0
            if percentage < 100:
                integrity_issues += 1
                print(f"❌ {desc}: {count}/{total} ({percentage:.1f}%) - 不完整")
            else:
                print(f"✅ {desc}: {count}/{total} ({percentage:.1f}%) - 完整")

        cursor.close()
        conn.close()

        if integrity_issues == 0:
            print("✅ 数据完整性测试通过")
            return True
        else:
            print(f"❌ 数据完整性测试失败 ({integrity_issues} 个字段不完整)")
            return False

    except Exception as e:
        print(f"数据完整性测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("=== 全面测试数据同步修复效果 ===\n")

    # 执行各项测试
    knowledge_test = test_product_knowledge_content()
    attachment_test = test_attachment_processing()
    integrity_test = test_data_integrity()

    # 总结测试结果
    print("\n=== 测试结果总结 ===")
    print(f"产品知识内容测试: {'✅ 通过' if knowledge_test else '❌ 失败'}")
    print(f"附件处理测试: {'✅ 通过' if attachment_test else '❌ 失败'}")
    print(f"数据完整性测试: {'✅ 通过' if integrity_test else '❌ 失败'}")

    # 计算总体成功率
    total_tests = 3
    passed_tests = sum([knowledge_test, attachment_test, integrity_test])
    success_rate = passed_tests / total_tests * 100

    print(f"\n总体测试成功率: {passed_tests}/{total_tests} ({success_rate:.1f}%)")

    if success_rate >= 80:
        print("🎉 数据同步修复效果验证成功！")
        return True
    else:
        print("⚠️ 数据同步修复效果需要进一步改进")
        return False


if __name__ == "__main__":
    main()
