"""
数据库连接规范检查和修复工具

该模块用于检查项目中所有数据库连接的使用情况，确保统一使用连接池，
识别并修复不规范的数据库连接使用。
"""

import os
import re
import ast
import logging
from typing import List, Dict, Any, Tuple, Set
from pathlib import Path

logger = logging.getLogger(__name__)


class DatabaseComplianceChecker:
    """数据库连接规范检查器"""

    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.violations = []
        self.compliant_files = []

    def check_project_compliance(self) -> Dict[str, Any]:
        """
        检查整个项目的数据库连接规范

        Returns:
            检查结果报告
        """
        logger.info("开始检查项目数据库连接规范...")

        # 重置结果
        self.violations = []
        self.compliant_files = []

        # 获取所有Python文件
        python_files = self._get_python_files()

        # 检查每个文件
        for file_path in python_files:
            try:
                violations = self._check_file_compliance(file_path)
                if violations:
                    self.violations.extend(violations)
                else:
                    self.compliant_files.append(str(file_path))
            except Exception as e:
                logger.error(f"检查文件失败 {file_path}: {e}")

        # 生成报告
        report = self._generate_report()
        logger.info(f"数据库连接规范检查完成，发现 {len(self.violations)} 个违规项")

        return report

    def _get_python_files(self) -> List[Path]:
        """获取项目中所有Python文件"""
        python_files = []
        
        # 排除的目录
        exclude_dirs = {'.git', '__pycache__', '.pytest_cache', 'venv', 'env', 'node_modules', 'venv_yunshang'}
        
        for root, dirs, files in os.walk(self.project_root):
            # 过滤排除的目录
            dirs[:] = [d for d in dirs if d not in exclude_dirs]
            
            for file in files:
                if file.endswith('.py'):
                    python_files.append(Path(root) / file)
        
        return python_files

    def _check_file_compliance(self, file_path: Path) -> List[Dict[str, Any]]:
        """
        检查单个文件的数据库连接规范

        Args:
            file_path: 文件路径

        Returns:
            违规项列表
        """
        violations = []

        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            # 检查直接的psycopg2.connect调用
            psycopg2_violations = self._check_psycopg2_connect(file_path, content)
            violations.extend(psycopg2_violations)

            # 检查是否使用了连接池
            pool_usage = self._check_connection_pool_usage(file_path, content)
            violations.extend(pool_usage)

            # 检查类继承情况
            inheritance_violations = self._check_class_inheritance(file_path, content)
            violations.extend(inheritance_violations)

        except Exception as e:
            logger.error(f"读取文件失败 {file_path}: {e}")

        return violations

    def _check_psycopg2_connect(
        self, file_path: Path, content: str
    ) -> List[Dict[str, Any]]:
        """检查直接的psycopg2.connect调用"""
        violations = []

        # 查找psycopg2.connect调用
        patterns = [
            r"psycopg2\.connect\s*\(",
            r"import\s+psycopg2.*\n.*\.connect\s*\(",
            r"from\s+psycopg2\s+import.*connect",
        ]

        lines = content.split("\n")
        for i, line in enumerate(lines, 1):
            for pattern in patterns:
                if re.search(pattern, line):
                    violations.append(
                        {
                            "type": "direct_psycopg2_connect",
                            "file": str(file_path),
                            "line": i,
                            "code": line.strip(),
                            "severity": "high",
                            "message": "禁止直接使用psycopg2.connect()，应使用连接池",
                        }
                    )

        return violations

    def _check_connection_pool_usage(
        self, file_path: Path, content: str
    ) -> List[Dict[str, Any]]:
        """检查连接池使用情况"""
        violations = []

        # 如果文件包含数据库操作但没有使用连接池
        has_db_operations = any(
            [
                "cursor" in content.lower(),
                "execute" in content.lower()
                and ("sql" in content.lower() or "query" in content.lower()),
                "commit" in content.lower(),
                "rollback" in content.lower(),
            ]
        )

        has_pool_usage = any(
            [
                "get_db_connection" in content,
                "return_db_connection" in content,
                "DatabaseOperationMixin" in content,
                "DatabaseService" in content,
            ]
        )

        if has_db_operations and not has_pool_usage:
            violations.append(
                {
                    "type": "missing_connection_pool",
                    "file": str(file_path),
                    "line": 0,
                    "code": "",
                    "severity": "medium",
                    "message": "文件包含数据库操作但未使用连接池",
                }
            )

        return violations

    def _check_class_inheritance(
        self, file_path: Path, content: str
    ) -> List[Dict[str, Any]]:
        """检查类继承情况"""
        violations = []

        try:
            tree = ast.parse(content)

            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    # 检查是否是服务类但没有继承DatabaseService
                    class_name = node.name
                    if "service" in class_name.lower() or "Service" in class_name:

                        # 检查基类
                        base_names = [
                            base.id for base in node.bases if hasattr(base, "id")
                        ]

                        if not any(
                            base in ["DatabaseService", "DatabaseOperationMixin"]
                            for base in base_names
                        ):
                            violations.append(
                                {
                                    "type": "missing_base_class",
                                    "file": str(file_path),
                                    "line": node.lineno,
                                    "code": f"class {class_name}",
                                    "severity": "medium",
                                    "message": f"服务类 {class_name} 应继承 DatabaseService 或 DatabaseOperationMixin",
                                }
                            )

        except SyntaxError:
            # 忽略语法错误的文件
            pass

        return violations

    def _generate_report(self) -> Dict[str, Any]:
        """生成检查报告"""
        # 按严重程度分组
        high_severity = [v for v in self.violations if v["severity"] == "high"]
        medium_severity = [v for v in self.violations if v["severity"] == "medium"]
        low_severity = [v for v in self.violations if v["severity"] == "low"]

        # 按文件分组
        files_with_violations = {}
        for violation in self.violations:
            file_path = violation["file"]
            if file_path not in files_with_violations:
                files_with_violations[file_path] = []
            files_with_violations[file_path].append(violation)

        return {
            "summary": {
                "total_files_checked": len(self.compliant_files)
                + len(files_with_violations),
                "compliant_files": len(self.compliant_files),
                "files_with_violations": len(files_with_violations),
                "total_violations": len(self.violations),
                "high_severity": len(high_severity),
                "medium_severity": len(medium_severity),
                "low_severity": len(low_severity),
            },
            "violations_by_severity": {
                "high": high_severity,
                "medium": medium_severity,
                "low": low_severity,
            },
            "violations_by_file": files_with_violations,
            "compliant_files": self.compliant_files,
        }

    def print_report(self, report: Dict[str, Any]):
        """打印检查报告"""
        print("=" * 60)
        print("数据库连接规范检查报告")
        print("=" * 60)

        summary = report["summary"]
        print(f"📊 检查统计:")
        print(f"   总文件数: {summary['total_files_checked']}")
        print(f"   符合规范: {summary['compliant_files']}")
        print(f"   存在违规: {summary['files_with_violations']}")
        print(f"   违规项总数: {summary['total_violations']}")
        print(f"   高危险级: {summary['high_severity']}")
        print(f"   中危险级: {summary['medium_severity']}")
        print(f"   低危险级: {summary['low_severity']}")

        print("\n🚨 违规详情:")
        for file_path, violations in report["violations_by_file"].items():
            print(f"\n📄 {file_path}:")
            for violation in violations:
                severity_icon = (
                    "🔴"
                    if violation["severity"] == "high"
                    else "🟡" if violation["severity"] == "medium" else "🟢"
                )
                print(
                    f"   {severity_icon} 第{violation['line']}行: {violation['message']}"
                )
                if violation["code"]:
                    print(f"      代码: {violation['code']}")

        print("\n✅ 符合规范的文件:")
        for file_path in report["compliant_files"][:10]:  # 只显示前10个
            print(f"   {file_path}")
        if len(report["compliant_files"]) > 10:
            print(f"   ... 还有 {len(report['compliant_files']) - 10} 个文件")


def main():
    """主函数"""
    checker = DatabaseComplianceChecker()
    report = checker.check_project_compliance()
    checker.print_report(report)

    if report["summary"]["total_violations"] > 0:
        print("\n🔧 发现违规项，请按以下建议修复:")
        print("1. 将所有服务类改为继承 DatabaseService")
        print("2. 使用 get_db_connection() 替代 psycopg2.connect()")
        print("3. 确保连接使用完毕后调用 return_db_connection()")
        print("4. 导入: from utils.db_base import DatabaseService")


if __name__ == "__main__":
    main()
