#!/usr/bin/env python3
"""
HTML转换功能使用示例
演示如何在云商接口数据处理中使用HTML到Markdown转换
"""

import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.html_processor import HTMLProcessor, process_api_response_html
from utils.data_processing import process_api_data


def demo_table_conversion():
    """演示表格转换功能"""
    print("=" * 60)
    print("📊 HTML表格转换示例")
    print("=" * 60)

    # 模拟云商接口返回的包含表格的产品参数信息
    html_table_content = """
    <h3>产品技术参数</h3>
    <table border="1">
        <thead>
            <tr>
                <th>参数名称</th>
                <th>技术指标</th>
                <th>备注</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>工作电压</td>
                <td>DC 12V±10%</td>
                <td>稳定电源供应</td>
            </tr>
            <tr>
                <td>工作电流</td>
                <td>≤2A</td>
                <td>待机状态≤200mA</td>
            </tr>
            <tr>
                <td>工作温度</td>
                <td>-20℃～+60℃</td>
                <td>宽温度范围</td>
            </tr>
            <tr>
                <td>存储卡容量</td>
                <td>10万张</td>
                <td>支持扩展</td>
            </tr>
        </tbody>
    </table>
    <p>注意：以上参数仅供参考，具体以实际产品为准。</p>
    """

    processor = HTMLProcessor()

    print("🔸 原始HTML内容:")
    print(html_table_content)
    print("\n" + "-" * 60 + "\n")

    # 转换为Markdown
    markdown_result = processor.process_html_content(html_table_content, "table")

    print("🔸 转换后的Markdown:")
    print(markdown_result)
    print("\n")


def demo_mixed_content_conversion():
    """演示混合内容转换功能"""
    print("=" * 60)
    print("📝 混合HTML内容转换示例")
    print("=" * 60)

    # 模拟产品详情页的混合HTML内容
    mixed_html_content = """
    <h2>产品特点</h2>
    <ul>
        <li><strong>高精度识别</strong>：采用先进的生物识别算法</li>
        <li><strong>快速响应</strong>：识别速度&lt;0.5秒</li>
        <li><strong>大容量存储</strong>：支持10万用户数据</li>
    </ul>
    
    <h3>应用场景</h3>
    <p>适用于以下场所：</p>
    <ol>
        <li>企业办公楼宇</li>
        <li>学校门禁系统</li>
        <li>住宅小区管理</li>
    </ol>
    
    <h3>技术规格对比</h3>
    <table>
        <tr>
            <th>型号</th>
            <th>ZK-AC100</th>
            <th>ZK-AC200</th>
        </tr>
        <tr>
            <td>用户容量</td>
            <td>5万</td>
            <td>10万</td>
        </tr>
        <tr>
            <td>通讯方式</td>
            <td>TCP/IP</td>
            <td>TCP/IP + WiFi</td>
        </tr>
    </table>
    
    <blockquote>
        <p>重要提醒：安装前请仔细阅读产品说明书。</p>
    </blockquote>
    """

    processor = HTMLProcessor()

    print("🔸 原始HTML内容:")
    print(mixed_html_content)
    print("\n" + "-" * 60 + "\n")

    # 转换为Markdown
    markdown_result = processor.process_html_content(mixed_html_content, "mixed")

    print("🔸 转换后的Markdown:")
    print(markdown_result)
    print("\n")


def demo_api_response_processing():
    """演示API响应数据处理"""
    print("=" * 60)
    print("🔄 API响应数据处理示例")
    print("=" * 60)

    # 模拟云商API返回的产品数据
    api_response = {
        "code": 200,
        "message": "success",
        "data": {
            "rows": [
                {
                    "id": 12345,
                    "name": "智能门禁控制器",
                    "introduction": "高性能门禁控制设备",
                    "details": """
                    <h3>产品概述</h3>
                    <p>本产品是一款<strong>高性能</strong>的门禁控制器，采用先进的处理器和算法。</p>
                    <h3>主要功能</h3>
                    <ul>
                        <li>人脸识别</li>
                        <li>指纹识别</li>
                        <li>刷卡识别</li>
                    </ul>
                    """,
                    "param_info": """
                    <table>
                        <tr><th>参数</th><th>值</th></tr>
                        <tr><td>CPU</td><td>ARM Cortex-A9</td></tr>
                        <tr><td>内存</td><td>1GB RAM</td></tr>
                        <tr><td>存储</td><td>8GB Flash</td></tr>
                    </table>
                    """,
                    "category_id": 101,
                    "brand_id": 5,
                    "create_time": "2024-01-15 10:30:00",
                }
            ],
            "total": 1,
        },
    }

    print("🔸 原始API响应:")
    print("产品详情 (details):")
    print(repr(api_response["data"]["rows"][0]["details"]))
    print("\n产品参数 (param_info):")
    print(repr(api_response["data"]["rows"][0]["param_info"]))
    print("\n" + "-" * 60 + "\n")

    # 使用数据处理器处理API响应
    processed_data = process_api_data(api_response, "product")

    print("🔸 处理后的数据:")
    product = processed_data["data"]["rows"][0]

    print("产品详情 (details):")
    print(product["details"])
    print("\n产品参数 (param_info):")
    print(product["param_info"])
    print("\n")


def demo_specific_field_processing():
    """演示特定字段处理"""
    print("=" * 60)
    print("🎯 特定字段处理示例")
    print("=" * 60)

    processor = HTMLProcessor()

    # 不同类型字段的处理示例
    test_fields = {
        "param_info": """
        <table>
            <tr><th>接口类型</th><th>数量</th><th>说明</th></tr>
            <tr><td>Wiegand</td><td>4</td><td>标准韦根协议</td></tr>
            <tr><td>RS485</td><td>2</td><td>通讯接口</td></tr>
        </table>
        """,
        "details": """
        <h3>安装说明</h3>
        <p>请按照以下步骤进行安装：</p>
        <ol>
            <li>确认安装位置</li>
            <li>固定设备支架</li>
            <li>连接电源和网络</li>
        </ol>
        """,
        "description": "这是一个<strong>简单</strong>的描述，包含<em>强调</em>内容。",
        "name": "普通产品名称",  # 不包含HTML
    }

    for field_name, field_value in test_fields.items():
        print(f"🔸 字段: {field_name}")
        print(f"原始内容: {repr(field_value)}")

        processed_value = processor.process_data_field(field_value, field_name)

        print(f"处理结果: {repr(processed_value)}")
        print("-" * 40)


def main():
    """主函数"""
    print("🚀 HTML到Markdown转换功能演示")
    print("本示例展示如何在云商系统中处理API返回的HTML内容\n")

    try:
        # 1. 表格转换示例
        demo_table_conversion()

        # 2. 混合内容转换示例
        demo_mixed_content_conversion()

        # 3. API响应处理示例
        demo_api_response_processing()

        # 4. 特定字段处理示例
        demo_specific_field_processing()

        print("=" * 60)
        print("✅ 所有示例运行完成！")
        print("=" * 60)

        print("\n📝 使用建议:")
        print("1. 在服务层的 _validate_data 方法中集成HTML处理")
        print("2. 根据数据类型选择合适的转换策略")
        print("3. 对于表格密集的字段（如param_info），使用'table'模式")
        print("4. 对于富文本内容（如details），使用'mixed'模式")
        print("5. 在Streamlit中可以直接使用st.markdown()渲染转换后的内容")

    except Exception as e:
        print(f"❌ 示例运行出错: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()
