#!/usr/bin/env python3
"""
产品知识库管理系统

1. 以型号作标识，梳理产品介绍说明及特性，整理成FastGPT知识块
2. 检测产品相关文档，PDF转markdown，Word转PDF再转markdown
3. 使用mineru进行文档转换，优化markdown格式
4. 集成AI多模态标注功能
"""

import sys
import os
import json
import logging
import requests
from pathlib import Path
from typing import Dict, Any, List, Optional
import hashlib
from datetime import datetime

# 添加项目路径
sys.path.append(".")

# 设置环境变量
os.environ["DATABASE_HOST"] = "***********"
os.environ["DATABASE_PORT"] = "5432"
os.environ["DATABASE_NAME"] = "product"
os.environ["DATABASE_USER"] = "username"
os.environ["DATABASE_PASSWORD"] = "password"
os.environ["ZKMALL_API_BASE"] = "https://zkmall.zktecoiot.com"
os.environ["ZKMALL_USERNAME"] = "18929343717"
os.environ["ZKMALL_PASSWORD"] = "Zk@123456"

from utils.auth import AuthManager
from utils.api_client import ZKMallClient
from utils.database import DatabaseManager

# 设置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class ProductKnowledgeManager:
    """产品知识库管理器"""

    def __init__(self):
        """初始化管理器"""
        self.client = None
        self.db_manager = None
        self.knowledge_base_dir = Path("knowledge_base")
        self.documents_dir = Path("documents")
        self.processed_dir = Path("processed_documents")

        # 创建必要目录
        for dir_path in [
            self.knowledge_base_dir,
            self.documents_dir,
            self.processed_dir,
        ]:
            dir_path.mkdir(exist_ok=True)

        self.processing_results = {
            "products_processed": 0,
            "knowledge_blocks_created": 0,
            "documents_converted": 0,
            "fastgpt_uploads": 0,
            "errors": [],
        }

    def run_knowledge_management(self) -> Dict[str, Any]:
        """运行知识库管理"""
        print("📚 开始产品知识库管理...")
        print("=" * 80)

        try:
            # 1. 环境检查
            if not self._check_environment():
                return self.processing_results

            # 2. 获取产品数据
            products = self._get_all_products()

            # 3. 按型号整理产品知识
            self._organize_products_by_model(products)

            # 4. 处理产品文档
            self._process_product_documents(products)

            # 5. 创建FastGPT知识块
            self._create_fastgpt_knowledge_blocks()

            # 6. 生成处理报告
            self._generate_processing_report()

            return self.processing_results

        except Exception as e:
            logger.error(f"知识库管理过程出错: {e}")
            import traceback

            traceback.print_exc()
            return self.processing_results

    def _check_environment(self) -> bool:
        """检查环境"""
        print("\n1. 🔧 环境检查")
        print("-" * 40)

        try:
            # 检查API认证
            if not AuthManager.ensure_authenticated():
                print("❌ API认证失败")
                return False

            print("✅ API认证成功")
            self.client = ZKMallClient()

            # 检查数据库连接
            try:
                self.db_manager = DatabaseManager()
                with self.db_manager.get_connection() as conn:
                    with conn.cursor() as cursor:
                        cursor.execute("SELECT 1")
                print("✅ 数据库连接成功")
            except Exception as e:
                print(f"❌ 数据库连接失败: {e}")
                return False

            # 检查mineru是否可用
            try:
                import subprocess

                result = subprocess.run(
                    ["python", "-c", "import magic_pdf"], capture_output=True, text=True
                )
                if result.returncode == 0:
                    print("✅ mineru可用")
                else:
                    print("⚠️ mineru不可用，将使用备用转换方法")
            except Exception as e:
                print(f"⚠️ mineru检查失败: {e}")

            return True

        except Exception as e:
            print(f"❌ 环境检查失败: {e}")
            return False

    def _get_all_products(self) -> List[Dict]:
        """获取所有产品数据"""
        print("\n2. 📊 获取产品数据")
        print("-" * 40)

        try:
            all_products = []
            page = 1
            page_size = 50

            while True:
                products = self.client.get_products(pageSize=page_size, current=page)
                if not products:
                    break

                all_products.extend(products)
                print(f"   获取第 {page} 页: {len(products)} 个产品")

                if len(products) < page_size:
                    break

                page += 1

            print(f"✅ 总共获取 {len(all_products)} 个产品")
            return all_products

        except Exception as e:
            print(f"❌ 获取产品数据失败: {e}")
            self.processing_results["errors"].append(f"获取产品数据失败: {e}")
            return []

    def _organize_products_by_model(self, products: List[Dict]):
        """按型号整理产品知识"""
        print("\n3. 🏷️ 按型号整理产品知识")
        print("-" * 40)

        try:
            # 按型号分组
            products_by_model = {}

            for product in products:
                spec = product.get("spec", "").strip()
                if not spec:
                    spec = f"未知型号_{product.get('id', 'unknown')}"

                if spec not in products_by_model:
                    products_by_model[spec] = []

                products_by_model[spec].append(product)

            print(f"   发现 {len(products_by_model)} 个不同型号")

            # 为每个型号创建知识块
            for spec, model_products in products_by_model.items():
                self._create_model_knowledge_block(spec, model_products)

            self.processing_results["products_processed"] = len(products)

        except Exception as e:
            print(f"❌ 产品知识整理失败: {e}")
            self.processing_results["errors"].append(f"产品知识整理失败: {e}")

    def _create_model_knowledge_block(self, spec: str, products: List[Dict]):
        """为型号创建知识块"""
        try:
            # 合并同型号产品的知识内容
            knowledge_content = {
                "model": spec,
                "products": [],
                "introduction": "",
                "specifications": {},
                "features": [],
                "applications": [],
                "documents": [],
                "attachments": [],
            }

            for product in products:
                product_info = {
                    "id": product.get("id"),
                    "name": product.get("name", ""),
                    "category": product.get("categoryName", ""),
                    "introduction": product.get("introduction", ""),
                    "details": product.get("details", ""),
                    "use_to": product.get("useTo", ""),
                    "param_info": product.get("paramInfo", ""),
                    "param_info_list": product.get("paramInfoList", []),
                    "new_param": product.get("newParam", ""),
                }

                knowledge_content["products"].append(product_info)

                # 合并介绍信息
                if product.get("introduction"):
                    if knowledge_content["introduction"]:
                        knowledge_content["introduction"] += "\n\n"
                    knowledge_content["introduction"] += product["introduction"]

                # 合并应用场景
                if product.get("useTo"):
                    applications = [
                        app.strip()
                        for app in product["useTo"].split("、")
                        if app.strip()
                    ]
                    knowledge_content["applications"].extend(applications)

                # 处理参数信息
                if product.get("paramInfoList"):
                    for param in product["paramInfoList"]:
                        param_name = param.get("params", "")
                        param_content = param.get("content", "")
                        if param_name and param_content:
                            knowledge_content["specifications"][
                                param_name
                            ] = param_content

                # 收集附件
                for field in ["other", "banner", "smallImg", "qualifications"]:
                    if product.get(field):
                        urls = str(product[field]).split(",")
                        for url in urls:
                            url = url.strip()
                            if url:
                                knowledge_content["attachments"].append(
                                    {
                                        "type": field,
                                        "url": url,
                                        "product_id": product.get("id"),
                                    }
                                )

            # 去重应用场景
            knowledge_content["applications"] = list(
                set(knowledge_content["applications"])
            )

            # 保存知识块
            knowledge_file = self.knowledge_base_dir / f"{spec}_knowledge.json"
            with open(knowledge_file, "w", encoding="utf-8") as f:
                json.dump(
                    knowledge_content, f, ensure_ascii=False, indent=2, default=str
                )

            print(f"   ✅ 创建知识块: {spec} ({len(products)} 个产品)")
            self.processing_results["knowledge_blocks_created"] += 1

        except Exception as e:
            print(f"   ❌ 创建知识块失败 {spec}: {e}")
            self.processing_results["errors"].append(f"创建知识块失败 {spec}: {e}")

    def _process_product_documents(self, products: List[Dict]):
        """处理产品文档"""
        print("\n4. 📄 处理产品文档")
        print("-" * 40)

        try:
            document_urls = set()

            # 收集所有文档URL
            for product in products:
                for field in ["other", "qualifications", "instructions"]:
                    if product.get(field):
                        urls = str(product[field]).split(",")
                        for url in urls:
                            url = url.strip()
                            if url and (
                                url.endswith(".pdf")
                                or url.endswith(".doc")
                                or url.endswith(".docx")
                            ):
                                document_urls.add(url)

            print(f"   发现 {len(document_urls)} 个文档URL")

            # 处理每个文档
            for url in document_urls:
                self._process_single_document(url)

        except Exception as e:
            print(f"❌ 文档处理失败: {e}")
            self.processing_results["errors"].append(f"文档处理失败: {e}")

    def _process_single_document(self, url: str):
        """处理单个文档"""
        try:
            # 生成文件名
            url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
            file_extension = url.split(".")[-1].lower()
            original_filename = f"doc_{url_hash}.{file_extension}"

            # 下载文档
            doc_path = self.documents_dir / original_filename
            if not doc_path.exists():
                response = requests.get(url, timeout=30)
                response.raise_for_status()

                with open(doc_path, "wb") as f:
                    f.write(response.content)

                print(f"   ✅ 下载文档: {original_filename}")

            # 转换文档
            if file_extension == "pdf":
                self._convert_pdf_to_markdown(doc_path)
            elif file_extension in ["doc", "docx"]:
                # 先转换为PDF，再转换为markdown
                pdf_path = self._convert_word_to_pdf(doc_path)
                if pdf_path:
                    self._convert_pdf_to_markdown(pdf_path)

            self.processing_results["documents_converted"] += 1

        except Exception as e:
            print(f"   ❌ 处理文档失败 {url}: {e}")
            self.processing_results["errors"].append(f"处理文档失败 {url}: {e}")

    def _convert_pdf_to_markdown(self, pdf_path: Path) -> Optional[Path]:
        """使用mineru将PDF转换为markdown"""
        try:
            # 尝试使用mineru
            try:
                import subprocess

                output_dir = self.processed_dir / pdf_path.stem
                output_dir.mkdir(exist_ok=True)

                # 使用mineru转换
                cmd = [
                    "python",
                    "-m",
                    "magic_pdf.pipe.UNIPipe",
                    "--pdf",
                    str(pdf_path),
                    "--output-dir",
                    str(output_dir),
                ]

                result = subprocess.run(
                    cmd, capture_output=True, text=True, timeout=300
                )

                if result.returncode == 0:
                    # 查找生成的markdown文件
                    md_files = list(output_dir.glob("*.md"))
                    if md_files:
                        md_path = md_files[0]
                        self._optimize_markdown_format(md_path)
                        print(f"   ✅ PDF转换成功: {pdf_path.name} -> {md_path.name}")
                        return md_path

            except Exception as mineru_error:
                print(f"   ⚠️ mineru转换失败，使用备用方法: {mineru_error}")
                return self._convert_pdf_fallback(pdf_path)

        except Exception as e:
            print(f"   ❌ PDF转换失败: {e}")
            return None

    def _convert_pdf_fallback(self, pdf_path: Path) -> Optional[Path]:
        """备用PDF转换方法"""
        try:
            # 使用PyPDF2或其他库的简单转换
            import PyPDF2

            md_path = self.processed_dir / f"{pdf_path.stem}.md"

            with open(pdf_path, "rb") as pdf_file:
                pdf_reader = PyPDF2.PdfReader(pdf_file)

                markdown_content = f"# {pdf_path.stem}\n\n"

                for page_num, page in enumerate(pdf_reader.pages, 1):
                    text = page.extract_text()
                    if text.strip():
                        markdown_content += f"## 第{page_num}页\n\n{text}\n\n"

            with open(md_path, "w", encoding="utf-8") as f:
                f.write(markdown_content)

            print(f"   ✅ 备用转换成功: {pdf_path.name}")
            return md_path

        except Exception as e:
            print(f"   ❌ 备用转换失败: {e}")
            return None

    def _convert_word_to_pdf(self, word_path: Path) -> Optional[Path]:
        """将Word文档转换为PDF"""
        try:
            # 尝试使用python-docx2pdf
            try:
                from docx2pdf import convert

                pdf_path = self.documents_dir / f"{word_path.stem}.pdf"
                convert(str(word_path), str(pdf_path))

                print(f"   ✅ Word转PDF成功: {word_path.name}")
                return pdf_path

            except ImportError:
                print("   ⚠️ docx2pdf不可用，跳过Word文档转换")
                return None

        except Exception as e:
            print(f"   ❌ Word转PDF失败: {e}")
            return None

    def _optimize_markdown_format(self, md_path: Path):
        """优化markdown格式"""
        try:
            with open(md_path, "r", encoding="utf-8") as f:
                content = f.read()

            # 优化标题层级
            lines = content.split("\n")
            optimized_lines = []

            for line in lines:
                # 确保标题层级正确
                if line.startswith("#"):
                    # 限制最大标题层级为6
                    if line.count("#") > 6:
                        line = "######" + line[6:]
                    optimized_lines.append(line)
                else:
                    optimized_lines.append(line)

            # 基于二级标题分段
            sections = []
            current_section = []
            current_h1 = ""
            current_h2 = ""

            for line in optimized_lines:
                if line.startswith("# "):
                    current_h1 = line[2:].strip()
                elif line.startswith("## "):
                    # 保存当前段落
                    if current_section:
                        section_title = (
                            f"{md_path.stem}_{current_h1}_{current_h2}".replace(
                                " ", "_"
                            )
                        )
                        sections.append(
                            {
                                "title": section_title,
                                "content": "\n".join(current_section),
                            }
                        )
                        current_section = []

                    current_h2 = line[3:].strip()
                    current_section.append(
                        f"# {md_path.stem}_{current_h1}_{current_h2}"
                    )

                current_section.append(line)

            # 保存最后一个段落
            if current_section:
                section_title = f"{md_path.stem}_{current_h1}_{current_h2}".replace(
                    " ", "_"
                )
                sections.append(
                    {"title": section_title, "content": "\n".join(current_section)}
                )

            # 保存分段文件
            for i, section in enumerate(sections):
                section_path = md_path.parent / f"{md_path.stem}_section_{i+1}.md"
                with open(section_path, "w", encoding="utf-8") as f:
                    f.write(section["content"])

            print(f"   ✅ 格式优化完成，生成 {len(sections)} 个段落")

        except Exception as e:
            print(f"   ❌ 格式优化失败: {e}")


def main():
    """主函数"""
    manager = ProductKnowledgeManager()
    results = manager.run_knowledge_management()

    # 保存处理结果
    with open("knowledge_management_results.json", "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2, default=str)

    print(f"\n📊 知识库管理结果已保存到: knowledge_management_results.json")


if __name__ == "__main__":
    main()
