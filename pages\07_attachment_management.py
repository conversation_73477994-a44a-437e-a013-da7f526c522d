"""
附件管理页面
专门用于管理和查看云商系统中的附件文件
"""

import streamlit as st
import logging
from typing import Dict, List, Any, Optional
import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.session import SessionManager
from components.attachment_display import (
    display_attachments_list,
    display_attachments_table,
    format_file_size,
    get_file_type_icon,
)

logger = logging.getLogger(__name__)


def main():
    """附件管理页面主函数"""
    st.set_page_config(
        page_title="附件管理",
        page_icon="📎",
        layout="wide",
        initial_sidebar_state="expanded",
    )

    # 初始化session
    initialize_session()

    # 渲染页面
    render_header()
    render_sidebar()
    render_content()
    render_footer()


def initialize_session():
    """初始化页面session状态"""
    defaults = {
        "selected_module": "全部",
        "selected_business_id": "",
        "view_mode": "卡片视图",
        "show_details": False,
        "last_sync_time": None,
        "attachment_stats": None,
    }

    for key, value in defaults.items():
        if key not in st.session_state:
            SessionManager.set(key, value)


def render_header():
    """渲染页面头部"""
    st.title("📎 附件管理系统")
    st.markdown(
        "管理和查看云商系统中的附件文件，支持附件下载、统计分析、完整性验证等功能"
    )

    # 状态指示器
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        # 检查数据库连接状态
        try:
            from utils.attachment_db import AttachmentDB

            attachment_db = AttachmentDB()
            if attachment_db.ensure_connection():
                st.success("🔗 数据库连接正常")
                attachment_db.disconnect()
            else:
                st.error("❌ 数据库连接失败")
        except Exception as e:
            st.error(f"❌ 数据库错误: {str(e)[:20]}...")

    with col2:
        # 检查存储目录
        storage_dir = "data/attachments"
        if os.path.exists(storage_dir):
            st.success("📁 存储目录正常")
        else:
            st.warning("⚠️ 存储目录不存在")

    with col3:
        # 显示上次同步时间
        last_sync = SessionManager.get("last_sync_time")
        if last_sync:
            st.info(f"🔄 上次同步: {last_sync}")
        else:
            st.info("🔄 尚未同步")

    with col4:
        # 系统状态
        st.success("✅ 系统正常")


def render_sidebar():
    """渲染侧边栏"""
    with st.sidebar:
        st.header("📋 控制面板")

        # 快速操作区域
        st.subheader("🚀 快速操作")

        if st.button("🔄 同步所有附件", use_container_width=True):
            sync_all_attachments()

        if st.button("📊 刷新统计", use_container_width=True):
            refresh_statistics()

        if st.button("🔍 验证完整性", use_container_width=True):
            verify_attachment_integrity()

        if st.button("🧹 清理失败记录", use_container_width=True):
            cleanup_failed_downloads()

        st.markdown("---")

        # 查询设置
        st.subheader("🔍 查询设置")

        # 业务模块选择
        module_options = [
            "全部",
            "products",
            "cases",
            "programmes",
            "information",
            "distribution_orders",
        ]
        selected_module = st.selectbox(
            "业务模块",
            module_options,
            index=module_options.index(SessionManager.get("selected_module")),
            key="module_selector",
        )
        SessionManager.set("selected_module", selected_module)

        # 业务ID输入
        business_id = st.text_input(
            "业务ID（可选）",
            value=SessionManager.get("selected_business_id"),
            help="留空显示所有记录",
            key="business_id_input",
        )
        SessionManager.set("selected_business_id", business_id)

        # 视图模式选择
        view_modes = ["卡片视图", "表格视图"]
        view_mode = st.radio(
            "显示模式",
            view_modes,
            index=view_modes.index(SessionManager.get("view_mode")),
            key="view_mode_selector",
        )
        SessionManager.set("view_mode", view_mode)

        # 详细信息开关
        show_details = st.checkbox(
            "显示详细信息",
            value=SessionManager.get("show_details"),
            key="show_details_toggle",
        )
        SessionManager.set("show_details", show_details)

        st.markdown("---")

        # 统计信息显示
        st.subheader("📊 统计概览")
        stats = SessionManager.get("attachment_stats")
        if stats:
            st.metric("总附件数", stats.get("total_count", 0))
            st.metric("总大小", format_file_size(stats.get("total_size", 0)))
            st.metric("成功率", f"{stats.get('success_rate', 0):.1f}%")
        else:
            st.info("点击刷新统计获取数据")


def render_content():
    """渲染主要内容"""
    # 创建选项卡
    tab1, tab2, tab3, tab4 = st.tabs(
        ["📋 附件列表", "📊 统计分析", "🔧 管理操作", "⚙️ 系统设置"]
    )

    with tab1:
        render_attachment_list_tab()

    with tab2:
        render_statistics_tab()

    with tab3:
        render_management_tab()

    with tab4:
        render_settings_tab()


def render_attachment_list_tab():
    """渲染附件列表选项卡"""
    st.subheader("📋 附件列表")

    # 查询按钮
    col1, col2, col3 = st.columns([2, 1, 1])

    with col1:
        if st.button("🔍 查询附件", type="primary", use_container_width=True):
            query_attachments()

    with col2:
        if st.button("📥 导出数据", use_container_width=True):
            export_attachment_data()

    with col3:
        if st.button("🔄 重新加载", use_container_width=True):
            st.rerun()

    # 显示附件数据
    if "attachment_list" in st.session_state:
        attachments = st.session_state["attachment_list"]
        view_mode = SessionManager.get("view_mode")
        show_details = SessionManager.get("show_details")

        if view_mode == "卡片视图":
            display_attachments_list(
                attachments,
                title="查询结果",
                show_details=show_details,
                enable_filtering=True,
            )
        else:
            display_attachments_table(attachments)
    else:
        st.info("👆 点击查询按钮获取附件数据")


def render_statistics_tab():
    """渲染统计分析选项卡"""
    st.subheader("📊 统计分析")

    # 刷新统计按钮
    if st.button("🔄 刷新统计数据", type="primary"):
        refresh_statistics()

    # 显示统计图表
    if "attachment_stats" in st.session_state and st.session_state["attachment_stats"]:
        stats = st.session_state["attachment_stats"]

        # 总体统计
        st.subheader("📈 总体统计")
        col1, col2, col3, col4 = st.columns(4)

        with col1:
            st.metric("总附件数", stats.get("total_count", 0))
        with col2:
            st.metric("总文件大小", format_file_size(stats.get("total_size", 0)))
        with col3:
            st.metric("下载成功", stats.get("completed_count", 0))
        with col4:
            st.metric("下载失败", stats.get("failed_count", 0))

        # 按模块统计
        if "module_stats" in stats:
            st.subheader("📋 按模块统计")
            module_data = []

            for module, module_stat in stats["module_stats"].items():
                module_data.append(
                    {
                        "模块": module,
                        "数量": module_stat.get("count", 0),
                        "大小": module_stat.get("size", 0),
                        "成功": module_stat.get("completed", 0),
                        "失败": module_stat.get("failed", 0),
                    }
                )

            if module_data:
                import pandas as pd

                module_df = pd.DataFrame(module_data)

                # 创建图表
                col1, col2 = st.columns(2)

                with col1:
                    st.bar_chart(module_df.set_index("模块")["数量"])

                with col2:
                    st.bar_chart(module_df.set_index("模块")["大小"])

                # 显示表格
                st.dataframe(module_df, use_container_width=True, hide_index=True)

        # 按文件类型统计
        if "type_stats" in stats:
            st.subheader("📁 按文件类型统计")
            type_data = []

            for file_type, type_stat in stats["type_stats"].items():
                type_data.append(
                    {
                        "类型": file_type,
                        "数量": type_stat.get("count", 0),
                        "大小": type_stat.get("size", 0),
                        "平均大小": type_stat.get("avg_size", 0),
                    }
                )

            if type_data:
                import pandas as pd

                type_df = pd.DataFrame(type_data)

                # 饼图显示文件类型分布
                st.subheader("文件类型分布")
                fig = st.plotly_chart(
                    {
                        "data": [
                            {
                                "type": "pie",
                                "labels": [
                                    f"{get_file_type_icon(t)} {t}"
                                    for t in type_df["类型"]
                                ],
                                "values": type_df["数量"],
                                "textinfo": "label+percent",
                            }
                        ],
                        "layout": {"title": "按文件类型分布"},
                    },
                    use_container_width=True,
                )

                # 显示表格
                st.dataframe(type_df, use_container_width=True, hide_index=True)

    else:
        st.info("点击刷新按钮获取统计数据")


def render_management_tab():
    """渲染管理操作选项卡"""
    st.subheader("🔧 管理操作")

    # 批量操作区域
    st.subheader("📦 批量操作")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("#### 🔄 同步操作")

        if st.button("同步所有模块附件", key="batch_sync_all"):
            sync_all_attachments()

        if st.button("同步产品附件", key="sync_products"):
            sync_module_attachments("products")

        if st.button("同步案例附件", key="sync_cases"):
            sync_module_attachments("cases")

        if st.button("同步方案附件", key="sync_programmes"):
            sync_module_attachments("programmes")

    with col2:
        st.markdown("#### 🧹 清理操作")

        if st.button("清理失败下载记录", key="cleanup_failed_mgmt"):
            cleanup_failed_downloads()

        if st.button("验证文件完整性", key="verify_integrity_mgmt"):
            verify_attachment_integrity()

        if st.button("清理孤立文件", key="cleanup_orphaned"):
            cleanup_orphaned_files()

        if st.button("重建索引", key="rebuild_index"):
            rebuild_attachment_index()

    # 重新下载区域
    st.subheader("🔄 重新下载")

    col1, col2 = st.columns(2)

    with col1:
        redownload_module = st.selectbox(
            "选择模块",
            ["products", "cases", "programmes", "information", "distribution_orders"],
            key="redownload_module_select",
        )

    with col2:
        redownload_business_id = st.text_input(
            "业务ID", help="重新下载指定业务记录的附件", key="redownload_business_id"
        )

    if st.button("🔄 重新下载附件", type="secondary"):
        if redownload_business_id:
            redownload_business_attachments(redownload_module, redownload_business_id)
        else:
            st.warning("请输入业务ID")


def render_settings_tab():
    """渲染系统设置选项卡"""
    st.subheader("⚙️ 系统设置")

    # 存储设置
    st.subheader("📁 存储设置")

    storage_dir = "data/attachments"
    st.text_input("附件存储目录", value=storage_dir, disabled=True)

    if os.path.exists(storage_dir):
        # 计算存储统计
        total_size = 0
        file_count = 0

        try:
            for root, dirs, files in os.walk(storage_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    if os.path.exists(file_path):
                        total_size += os.path.getsize(file_path)
                        file_count += 1

            col1, col2 = st.columns(2)
            col1.metric("存储文件数", file_count)
            col2.metric("存储总大小", format_file_size(total_size))

        except Exception as e:
            st.error(f"计算存储统计失败: {e}")

    else:
        st.warning("存储目录不存在")
        if st.button("创建存储目录"):
            try:
                os.makedirs(storage_dir, exist_ok=True)
                st.success("存储目录创建成功")
                st.rerun()
            except Exception as e:
                st.error(f"创建目录失败: {e}")

    # 数据库设置
    st.subheader("🗄️ 数据库设置")

    try:
        from utils.attachment_db import AttachmentDB

        attachment_db = AttachmentDB()

        if attachment_db.ensure_connection():
            st.success("✅ 数据库连接正常")

            # 显示数据库统计
            try:
                cursor = attachment_db.connection.cursor()

                # 附件表统计
                cursor.execute("SELECT COUNT(*) FROM attachments")
                attachment_count = cursor.fetchone()[0]

                # 附件历史表统计
                cursor.execute("SELECT COUNT(*) FROM attachment_history")
                history_count = cursor.fetchone()[0]

                cursor.close()
                attachment_db.disconnect()

                col1, col2 = st.columns(2)
                col1.metric("附件记录数", attachment_count)
                col2.metric("历史记录数", history_count)

            except Exception as e:
                st.error(f"获取数据库统计失败: {e}")
        else:
            st.error("❌ 数据库连接失败")

    except Exception as e:
        st.error(f"数据库检查失败: {e}")

    # 系统配置
    st.subheader("🔧 系统配置")

    col1, col2 = st.columns(2)

    with col1:
        max_concurrent_downloads = st.number_input(
            "最大并发下载数",
            min_value=1,
            max_value=10,
            value=3,
            help="同时进行的下载任务数量",
        )

    with col2:
        download_timeout = st.number_input(
            "下载超时时间(秒)",
            min_value=10,
            max_value=300,
            value=60,
            help="单个文件下载的超时时间",
        )

    if st.button("💾 保存设置"):
        # 这里可以保存设置到配置文件或数据库
        st.success("设置已保存")


def render_footer():
    """渲染页面脚部"""
    st.markdown("---")
    st.markdown("📎 **附件管理系统** | 云商平台 | © 2024")


# 业务逻辑函数


def query_attachments():
    """查询附件数据"""
    selected_module = SessionManager.get("selected_module")
    business_id = SessionManager.get("selected_business_id")

    with st.spinner("正在查询附件数据..."):
        try:
            from utils.attachment_db import AttachmentDB

            attachment_db = AttachmentDB()

            if not attachment_db.ensure_connection():
                st.error("数据库连接失败")
                return

            attachments = []

            if selected_module == "全部":
                # 查询所有模块
                modules = [
                    "products",
                    "cases",
                    "programmes",
                    "information",
                    "distribution_orders",
                ]
                for module in modules:
                    if business_id:
                        module_attachments = attachment_db.get_attachments_by_business(
                            module, business_id
                        )
                    else:
                        # 获取模块的所有附件（这里需要实现相应的方法）
                        module_attachments = []
                    attachments.extend(module_attachments)
            else:
                if business_id:
                    attachments = attachment_db.get_attachments_by_business(
                        selected_module, business_id
                    )
                else:
                    # 获取模块的所有附件
                    attachments = []

            attachment_db.disconnect()

            # 保存查询结果到session
            st.session_state["attachment_list"] = attachments

            st.success(f"查询完成！找到 {len(attachments)} 个附件")

        except Exception as e:
            st.error(f"查询失败: {e}")
            logger.error(f"查询附件失败: {e}")


def sync_all_attachments():
    """同步所有附件"""
    with st.spinner("正在同步所有模块的附件..."):
        try:
            from utils.enhanced_api_client import EnhancedZKMallClient

            enhanced_client = EnhancedZKMallClient()

            results = enhanced_client.sync_all_attachments()

            st.success("同步完成！")

            # 显示结果
            for module, count in results.items():
                if count >= 0:
                    st.info(f"✅ {module}: 处理了 {count} 个项目")
                else:
                    st.error(f"❌ {module}: 同步失败")

            # 更新同步时间
            from datetime import datetime

            SessionManager.set(
                "last_sync_time", datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            )

            # 刷新统计
            refresh_statistics()

        except Exception as e:
            st.error(f"同步失败: {e}")
            logger.error(f"同步所有附件失败: {e}")


def sync_module_attachments(module: str):
    """同步指定模块的附件"""
    with st.spinner(f"正在同步 {module} 模块的附件..."):
        try:
            from utils.enhanced_api_client import EnhancedZKMallClient

            enhanced_client = EnhancedZKMallClient()

            # 这里需要实现单模块同步方法
            count = enhanced_client.sync_module_attachments(module)

            if count >= 0:
                st.success(f"{module} 模块同步完成！处理了 {count} 个项目")
            else:
                st.error(f"{module} 模块同步失败")

        except Exception as e:
            st.error(f"同步 {module} 模块失败: {e}")
            logger.error(f"同步{module}模块附件失败: {e}")


def refresh_statistics():
    """刷新统计信息"""
    with st.spinner("正在获取统计信息..."):
        try:
            from utils.attachment_db import AttachmentDB

            attachment_db = AttachmentDB()

            if not attachment_db.ensure_connection():
                st.error("数据库连接失败")
                return

            # 获取统计信息
            stats = attachment_db.get_module_statistics()

            attachment_db.disconnect()

            # 保存到session
            SessionManager.set("attachment_stats", stats)

            st.success("统计信息已刷新")

        except Exception as e:
            st.error(f"获取统计信息失败: {e}")
            logger.error(f"刷新统计失败: {e}")


def verify_attachment_integrity():
    """验证附件完整性"""
    with st.spinner("正在验证附件完整性..."):
        try:
            from utils.enhanced_api_client import EnhancedZKMallClient

            enhanced_client = EnhancedZKMallClient()

            results = enhanced_client.verify_attachment_integrity()

            st.success("验证完成！")
            st.info(f"✅ 验证通过: {results.get('verified', 0)} 个")

            if results.get("corrupted", 0) > 0:
                st.warning(f"⚠️ 发现损坏文件: {results.get('corrupted', 0)} 个")

            st.info(f"📊 总计: {results.get('total', 0)} 个")

        except Exception as e:
            st.error(f"验证失败: {e}")
            logger.error(f"验证附件完整性失败: {e}")


def cleanup_failed_downloads():
    """清理失败的下载记录"""
    with st.spinner("正在清理失败的下载记录..."):
        try:
            from utils.attachment_db import AttachmentDB

            attachment_db = AttachmentDB()

            if not attachment_db.ensure_connection():
                st.error("数据库连接失败")
                return

            cleaned_count = attachment_db.cleanup_failed_downloads()
            attachment_db.disconnect()

            st.success(f"清理完成！删除了 {cleaned_count} 条失败记录")

        except Exception as e:
            st.error(f"清理失败: {e}")
            logger.error(f"清理失败下载失败: {e}")


def cleanup_orphaned_files():
    """清理孤立文件"""
    with st.spinner("正在清理孤立文件..."):
        try:
            # 这里实现清理孤立文件的逻辑
            cleaned_count = 0  # 临时值

            st.success(f"清理完成！删除了 {cleaned_count} 个孤立文件")

        except Exception as e:
            st.error(f"清理孤立文件失败: {e}")
            logger.error(f"清理孤立文件失败: {e}")


def rebuild_attachment_index():
    """重建附件索引"""
    with st.spinner("正在重建附件索引..."):
        try:
            # 这里实现重建索引的逻辑
            st.success("索引重建完成！")

        except Exception as e:
            st.error(f"重建索引失败: {e}")
            logger.error(f"重建附件索引失败: {e}")


def redownload_business_attachments(module: str, business_id: str):
    """重新下载业务记录的附件"""
    with st.spinner(f"正在重新下载 {module}/{business_id} 的附件..."):
        try:
            from utils.enhanced_api_client import EnhancedZKMallClient

            enhanced_client = EnhancedZKMallClient()

            # 这里需要实现重新下载指定业务记录附件的方法
            count = enhanced_client.redownload_business_attachments(module, business_id)

            st.success(f"重新下载完成！处理了 {count} 个附件")

        except Exception as e:
            st.error(f"重新下载失败: {e}")
            logger.error(f"重新下载{module}/{business_id}附件失败: {e}")


def export_attachment_data():
    """导出附件数据"""
    with st.spinner("正在导出附件数据..."):
        try:
            if "attachment_list" not in st.session_state:
                st.warning("请先查询附件数据")
                return

            attachments = st.session_state["attachment_list"]

            if not attachments:
                st.warning("没有可导出的数据")
                return

            # 准备导出数据
            import pandas as pd

            export_data = []
            for att in attachments:
                export_data.append(
                    {
                        "文件名": att.get("local_filename", ""),
                        "业务模块": att.get("business_module", ""),
                        "业务ID": att.get("business_id", ""),
                        "字段名": att.get("field_name", ""),
                        "文件类型": att.get("file_type", ""),
                        "文件大小": att.get("file_size", 0),
                        "下载状态": att.get("download_status", ""),
                        "原始URL": att.get("original_url", ""),
                        "本地路径": att.get("local_path", ""),
                        "下载时间": att.get("download_time", ""),
                        "创建时间": att.get("created_at", ""),
                        "更新时间": att.get("updated_at", ""),
                    }
                )

            df = pd.DataFrame(export_data)

            # 转换为CSV
            csv = df.to_csv(index=False, encoding="utf-8-sig")

            # 提供下载
            st.download_button(
                label="📥 下载附件数据 (CSV)",
                data=csv,
                file_name=f"attachments_export_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv",
            )

            st.success(f"准备导出 {len(export_data)} 条记录")

        except Exception as e:
            st.error(f"导出失败: {e}")
            logger.error(f"导出附件数据失败: {e}")


if __name__ == "__main__":
    main()
