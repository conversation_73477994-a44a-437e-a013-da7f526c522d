import logging
from typing import Dict, List, Any, Optional
from .api_client import ZKMallClient
from .attachment_service import AttachmentService
from .attachment_db import AttachmentDB
import concurrent.futures
import time
import os

logger = logging.getLogger(__name__)


class EnhancedZKMallClient(ZKMallClient):
    """
    增强的云商API客户端，集成附件下载功能
    """

    def __init__(self, base_url: Optional[str] = None, enable_attachments: bool = True):
        """
        初始化增强API客户端

        Args:
            base_url: API基础URL
            enable_attachments: 是否启用附件下载功能
        """
        super().__init__(base_url)

        self.enable_attachments = enable_attachments
        if enable_attachments:
            self.attachment_service = AttachmentService()
            self.attachment_db = AttachmentDB()
            logger.info("附件下载功能已启用")

    def _extract_attachment_fields(
        self, item_data: Dict[str, Any], module: str
    ) -> Dict[str, str]:
        """
        提取项目数据中的附件字段

        Args:
            item_data: 项目数据
            module: 业务模块名

        Returns:
            附件字段字典 {字段名: URL}
        """
        attachment_fields = {}

        # 定义各模块的附件字段
        field_mappings = {
            "products": ["other", "qualifications", "smallImg", "banner"],
            "cases": ["img", "banner", "smallImg", "video"],
            "programmes": ["other", "video", "smallImg", "banner"],
            "information": ["otherUrl", "videoUrl", "picVideo", "smallImg", "images"],
            "distribution_orders": ["other"],
        }

        fields = field_mappings.get(module, [])

        for field in fields:
            if field in item_data and item_data[field]:
                url = item_data[field]
                if isinstance(url, str) and url.startswith(("http://", "https://")):
                    attachment_fields[field] = url
                elif isinstance(url, list):
                    # 处理多个URL的情况（如images字段）
                    for i, single_url in enumerate(url):
                        if isinstance(single_url, str) and single_url.startswith(
                            ("http://", "https://")
                        ):
                            attachment_fields[f"{field}_{i}"] = single_url

        return attachment_fields

    def _download_attachments_for_items(
        self, items: List[Dict[str, Any]], module: str
    ) -> None:
        """
        为项目列表批量下载附件

        Args:
            items: 项目数据列表
            module: 业务模块名
        """
        if not self.enable_attachments or not items:
            return

        logger.info(f"开始为{len(items)}个{module}项目下载附件")

        # 使用线程池并发下载
        with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
            futures = []

            for item in items:
                item_id = str(item.get("id", ""))
                if not item_id:
                    continue

                attachment_fields = self._extract_attachment_fields(item, module)

                for field_name, url in attachment_fields.items():
                    # 检查数据库中是否已存在该附件
                    existing = self.attachment_db.get_attachment(
                        module, item_id, field_name, url
                    )
                    if existing and existing.get("download_status") == "completed":
                        logger.debug(f"附件已存在，跳过下载: {url}")
                        continue

                    # 提交下载任务
                    future = executor.submit(
                        self._download_single_attachment,
                        url,
                        module,
                        item_id,
                        field_name,
                    )
                    futures.append(future)

            # 等待所有下载任务完成
            completed = 0
            failed = 0
            for future in concurrent.futures.as_completed(futures):
                try:
                    result = future.result()
                    if result:
                        completed += 1
                    else:
                        failed += 1
                except Exception as e:
                    logger.error(f"附件下载任务失败: {e}")
                    failed += 1

        logger.info(f"附件下载完成 - 成功: {completed}, 失败: {failed}")

    def _download_single_attachment(
        self, url: str, module: str, item_id: str, field_name: str
    ) -> bool:
        """
        下载单个附件

        Args:
            url: 附件URL
            module: 业务模块名
            item_id: 项目ID
            field_name: 字段名

        Returns:
            下载是否成功
        """
        try:
            # 记录下载开始
            self.attachment_db.record_attachment(
                module, item_id, field_name, url, download_status="downloading"
            )

            # 执行下载
            attachment_info = self.attachment_service.download_file(
                url, module, item_id, field_name
            )

            if attachment_info:
                # 更新数据库记录
                self.attachment_db.update_attachment_status(
                    module,
                    item_id,
                    field_name,
                    url,
                    status="completed",
                    local_path=attachment_info.local_path,
                    local_filename=attachment_info.filename,
                    file_size=attachment_info.file_size,
                    file_type=attachment_info.file_type,
                    mime_type=attachment_info.mime_type,
                    md5_hash=attachment_info.md5_hash,
                    download_time=attachment_info.download_time,
                )
                logger.debug(f"附件下载成功: {url} -> {attachment_info.local_path}")
                return True
            else:
                # 下载失败
                self.attachment_db.update_attachment_status(
                    module,
                    item_id,
                    field_name,
                    url,
                    status="failed",
                    error_message="下载失败",
                )
                return False

        except Exception as e:
            logger.error(f"下载附件时发生错误 {url}: {e}")
            self.attachment_db.update_attachment_status(
                module, item_id, field_name, url, status="failed", error_message=str(e)
            )
            return False

    # 重写原有方法，增加附件下载功能

    def get_products_with_attachments(self, **kwargs) -> List[Dict[str, Any]]:
        """
        获取产品列表并下载附件

        Args:
            **kwargs: 查询参数

        Returns:
            产品列表
        """
        products = self.get_products(**kwargs)

        if products and self.enable_attachments:
            # 异步下载附件
            self._download_attachments_for_items(products, "products")

            # 为每个产品添加本地附件信息
            for product in products:
                product["local_attachments"] = self.get_local_attachments(
                    "products", str(product.get("id", ""))
                )

        return products

    def get_cases_with_attachments(self, **kwargs) -> List[Dict[str, Any]]:
        """
        获取案例列表并下载附件

        Args:
            **kwargs: 查询参数

        Returns:
            案例列表
        """
        cases = self.get_cases(**kwargs)

        if cases and self.enable_attachments:
            self._download_attachments_for_items(cases, "cases")

            for case in cases:
                case["local_attachments"] = self.get_local_attachments(
                    "cases", str(case.get("id", ""))
                )

        return cases

    def get_programmes_with_attachments(self, **kwargs) -> List[Dict[str, Any]]:
        """
        获取方案列表并下载附件

        Args:
            **kwargs: 查询参数

        Returns:
            方案列表
        """
        programmes = self.get_programmes(**kwargs)

        if programmes and self.enable_attachments:
            self._download_attachments_for_items(programmes, "programmes")

            for programme in programmes:
                programme["local_attachments"] = self.get_local_attachments(
                    "programmes", str(programme.get("id", ""))
                )

        return programmes

    def get_information_with_attachments(self, **kwargs) -> List[Dict[str, Any]]:
        """
        获取资讯列表并下载附件

        Args:
            **kwargs: 查询参数

        Returns:
            资讯列表
        """
        information = self.get_information(**kwargs)

        if information and self.enable_attachments:
            self._download_attachments_for_items(information, "information")

            for info in information:
                info["local_attachments"] = self.get_local_attachments(
                    "information", str(info.get("id", ""))
                )

        return information

    def get_distribution_orders_with_attachments(
        self, **kwargs
    ) -> List[Dict[str, Any]]:
        """
        获取配单列表并下载附件

        Args:
            **kwargs: 查询参数

        Returns:
            配单列表
        """
        orders = self.get_distribution_orders(**kwargs)

        if orders and self.enable_attachments:
            self._download_attachments_for_items(orders, "distribution_orders")

            for order in orders:
                order["local_attachments"] = self.get_local_attachments(
                    "distribution_orders", str(order.get("id", ""))
                )

        return orders

    def get_local_attachments(self, module: str, item_id: str) -> List[Dict[str, Any]]:
        """
        获取项目的本地附件信息

        Args:
            module: 业务模块名
            item_id: 项目ID

        Returns:
            本地附件信息列表
        """
        if not self.enable_attachments:
            return []

        return self.attachment_db.get_attachments_by_item(module, item_id)

    def get_attachment_statistics(self) -> Dict[str, Any]:
        """
        获取附件统计信息

        Returns:
            附件统计数据
        """
        if not self.enable_attachments:
            return {}

        return self.attachment_db.get_attachment_statistics()

    def sync_all_attachments(self) -> Dict[str, int]:
        """
        同步所有模块的附件

        Returns:
            同步结果统计
        """
        if not self.enable_attachments:
            return {}

        results = {}
        modules = [
            "products",
            "cases",
            "programmes",
            "information",
            "distribution_orders",
        ]

        for module in modules:
            try:
                logger.info(f"开始同步{module}模块附件")

                # 获取该模块的所有数据
                if module == "products":
                    items = self.get_products(pageSize=1000)
                elif module == "cases":
                    items = self.get_cases(pageSize=1000)
                elif module == "programmes":
                    items = self.get_programmes(pageSize=1000)
                elif module == "information":
                    items = self.get_information(pageSize=1000)
                elif module == "distribution_orders":
                    items = self.get_distribution_orders(pageSize=1000)
                else:
                    items = []

                # 下载附件
                if items:
                    self._download_attachments_for_items(items, module)
                    results[module] = len(items)
                else:
                    results[module] = 0

                logger.info(f"{module}模块附件同步完成，处理{results[module]}个项目")

            except Exception as e:
                logger.error(f"{module}模块附件同步失败: {e}")
                results[module] = -1

        return results

    def cleanup_failed_downloads(self) -> int:
        """
        清理失败的下载记录

        Returns:
            清理的记录数
        """
        if not self.enable_attachments:
            return 0

        return self.attachment_db.cleanup_failed_downloads()

    def verify_attachment_integrity(self) -> Dict[str, int]:
        """
        验证附件完整性

        Returns:
            验证结果统计
        """
        results = {"total": 0, "verified": 0, "corrupted": 0, "missing": 0}

        try:
            if not self.enable_attachments:
                logger.warning("附件功能未启用")
                return results

            # 获取所有附件记录
            all_attachments = self.attachment_db.get_all_attachments()
            results["total"] = len(all_attachments)

            for attachment in all_attachments:
                local_path = attachment.get("local_path", "")

                if not local_path or not os.path.exists(local_path):
                    results["missing"] += 1
                    continue

                # 验证文件完整性（比较MD5哈希）
                stored_hash = attachment.get("md5_hash", "")
                if stored_hash:
                    actual_hash = self.attachment_service.calculate_md5(local_path)
                    if actual_hash == stored_hash:
                        results["verified"] += 1
                    else:
                        results["corrupted"] += 1
                        logger.warning(f"文件损坏: {local_path}")
                else:
                    # 没有存储哈希，只验证文件存在
                    results["verified"] += 1

            logger.info(
                f"完整性验证完成: 总计={results['total']}, 验证通过={results['verified']}, "
                f"损坏={results['corrupted']}, 缺失={results['missing']}"
            )

            return results

        except Exception as e:
            logger.error(f"验证附件完整性失败: {e}")
            return results

    def sync_module_attachments(self, module: str) -> int:
        """
        同步指定模块的附件

        Args:
            module: 模块名称

        Returns:
            处理的项目数量
        """
        if not self.enable_attachments:
            logger.warning("附件功能未启用")
            return -1

        try:
            count = 0

            # 根据模块类型获取数据
            if module == "products":
                data = self.get_products()
                if data and "data" in data:
                    for item in data["data"]:
                        self._process_attachments_for_item(item, module)
                        count += 1

            elif module == "cases":
                data = self.get_cases()
                if data and "data" in data:
                    for item in data["data"]:
                        self._process_attachments_for_item(item, module)
                        count += 1

            elif module == "programmes":
                data = self.get_programmes()
                if data and "data" in data:
                    for item in data["data"]:
                        self._process_attachments_for_item(item, module)
                        count += 1

            elif module == "information":
                data = self.get_information()
                if data and "data" in data:
                    for item in data["data"]:
                        self._process_attachments_for_item(item, module)
                        count += 1

            elif module == "distribution_orders":
                data = self.get_distribution_orders()
                if data and "data" in data:
                    for item in data["data"]:
                        self._process_attachments_for_item(item, module)
                        count += 1

            else:
                logger.error(f"未知模块: {module}")
                return -1

            logger.info(f"模块 {module} 同步完成，处理了 {count} 个项目")
            return count

        except Exception as e:
            logger.error(f"同步模块 {module} 失败: {e}")
            return -1

    def redownload_business_attachments(self, module: str, business_id: str) -> int:
        """
        重新下载指定业务记录的附件

        Args:
            module: 业务模块
            business_id: 业务ID

        Returns:
            重新下载的附件数量
        """
        if not self.enable_attachments:
            logger.warning("附件功能未启用")
            return 0

        try:
            count = 0

            # 获取业务记录的附件
            attachments = self.attachment_db.get_attachments_by_business(
                module, business_id
            )

            for attachment in attachments:
                original_url = attachment.get("original_url", "")
                if original_url:
                    # 删除本地文件（如果存在）
                    local_path = attachment.get("local_path", "")
                    if local_path and os.path.exists(local_path):
                        os.remove(local_path)

                    # 重新下载
                    result = self.attachment_service.download_attachment(
                        url=original_url,
                        business_module=module,
                        business_id=business_id,
                        field_name=attachment.get("field_name", ""),
                    )

                    if result and result.get("success"):
                        count += 1
                        logger.info(f"重新下载成功: {original_url}")
                    else:
                        logger.error(f"重新下载失败: {original_url}")

            logger.info(f"重新下载完成: {module}/{business_id}, 成功 {count} 个")
            return count

        except Exception as e:
            logger.error(f"重新下载业务附件失败: {e}")
            return 0

    def cleanup_orphaned_files(self) -> int:
        """
        清理孤立文件（数据库中没有记录的本地文件）

        Returns:
            清理的文件数量
        """
        if not self.enable_attachments:
            return 0

        try:
            cleaned_count = 0
            storage_dir = "data/attachments"

            if not os.path.exists(storage_dir):
                logger.warning(f"存储目录不存在: {storage_dir}")
                return 0

            # 获取数据库中所有附件的本地路径
            all_attachments = self.attachment_db.get_all_attachments()
            db_paths = set()

            for attachment in all_attachments:
                local_path = attachment.get("local_path", "")
                if local_path:
                    db_paths.add(os.path.abspath(local_path))

            # 遍历存储目录，查找孤立文件
            for root, dirs, files in os.walk(storage_dir):
                for file in files:
                    file_path = os.path.abspath(os.path.join(root, file))

                    if file_path not in db_paths:
                        # 这是孤立文件，删除它
                        try:
                            os.remove(file_path)
                            cleaned_count += 1
                            logger.info(f"删除孤立文件: {file_path}")
                        except Exception as e:
                            logger.error(f"删除孤立文件失败: {file_path}, 错误: {e}")

            logger.info(f"清理孤立文件完成，删除了 {cleaned_count} 个文件")
            return cleaned_count

        except Exception as e:
            logger.error(f"清理孤立文件失败: {e}")
            return 0
