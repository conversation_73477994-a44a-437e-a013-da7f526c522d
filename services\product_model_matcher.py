"""
产品型号匹配服务模块

该模块提供智能的产品型号匹配功能，支持多种匹配策略：
1. 精确匹配 - 直接匹配产品型号
2. 模糊匹配 - 支持部分差异的型号匹配
3. 正则匹配 - 基于正则表达式的格式匹配
4. 规格参数匹配 - 基于产品规格参数的匹配
"""

import json
import logging
import os
import re
import uuid
from datetime import datetime
from difflib import SequenceMatcher
from typing import Any, Dict, List, Optional, Tuple

import psycopg2
from utils.database import DatabaseManager

logger = logging.getLogger(__name__)


class ProductModelMatcher:
    """产品型号匹配器"""

    def __init__(self):
        """初始化匹配器"""
        self.db_manager = DatabaseManager()
        self.match_rules = self._load_match_rules()

    def _load_match_rules(self) -> List[Dict[str, Any]]:
        """
        加载匹配规则

        Returns:
            匹配规则列表，按优先级排序
        """
        try:
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(
                        """
                        SELECT id, rule_name, rule_type, pattern, priority, 
                               is_active, match_field, description
                        FROM product_model_match_rules 
                        WHERE is_active = true 
                        ORDER BY priority DESC, id ASC
                    """
                    )

                    rules = []
                    for row in cursor.fetchall():
                        rules.append(
                            {
                                "id": row[0],
                                "rule_name": row[1],
                                "rule_type": row[2],
                                "pattern": row[3],
                                "priority": row[4],
                                "is_active": row[5],
                                "match_field": row[6],
                                "description": row[7],
                            }
                        )

                    logger.info(f"加载了 {len(rules)} 条匹配规则")
                    return rules

        except Exception as e:
            logger.error(f"加载匹配规则失败: {e}")
            return []

    def match_single_model(self, model_text: str) -> Optional[Dict[str, Any]]:
        """
        精确匹配单个产品型号

        Args:
            model_text: 要匹配的型号文本

        Returns:
            匹配结果，如果未找到则返回None
        """
        try:
            if not model_text or not model_text.strip():
                return None

            model_text = model_text.strip()

            # 获取所有产品数据
            products = self._get_all_products()

            if not products:
                logger.warning("未找到产品数据")
                return None

            # 精确匹配产品型号
            for product in products:
                # 检查产品的各个型号字段
                model_fields = self._extract_product_models(product)

                for field_name, field_value in model_fields.items():
                    if (
                        field_value
                        and field_value.strip().lower() == model_text.lower()
                    ):
                        match_result = {
                            "product_id": product["id"],
                            "product_name": product.get("name", ""),
                            "product_data": product,
                            "matched_field": field_name,
                            "matched_value": field_value,
                            "input_model": model_text,
                            "match_type": "exact",
                            "confidence": 1.0,  # 精确匹配置信度为100%
                        }

                        logger.info(
                            f"型号 '{model_text}' 精确匹配成功，产品: {product.get('name', '')}"
                        )
                        return match_result

            # 如果没有精确匹配，返回None
            logger.info(f"型号 '{model_text}' 未找到精确匹配")
            return None

        except Exception as e:
            logger.error(f"匹配单个型号失败: {e}")
            return None

    def batch_match_models(
        self, models_data: List[Dict[str, Any]], batch_id: str, user_id: str = "system"
    ) -> Dict[str, Any]:
        """
        批量精确匹配产品型号

        Args:
            models_data: 型号数据列表，每个包含 row_number, model, raw_data
            batch_id: 批次ID
            user_id: 用户ID

        Returns:
            批量匹配结果统计，包含匹配和未匹配的详细信息
        """
        try:
            total_count = len(models_data)
            matched_count = 0
            failed_count = 0
            matched_results = []
            unmatched_results = []

            logger.info(f"开始批量精确匹配 {total_count} 个型号，批次ID: {batch_id}")

            for model_data in models_data:
                try:
                    model_text = model_data.get("model", "")
                    row_number = model_data.get("row_number", 0)
                    dataset_id = model_data.get("dataset_id", "")

                    if not model_text:
                        failed_count += 1
                        unmatched_results.append(
                            {
                                "row_number": row_number,
                                "model": model_text,
                                "dataset_id": dataset_id,
                                "status": "empty",
                                "error": "型号为空",
                                "raw_data": model_data.get("raw_data", {}),
                            }
                        )
                        continue

                    # 精确匹配型号
                    match_result = self.match_single_model(model_text)

                    if match_result:
                        # 保存匹配结果到同步队列
                        self._save_match_to_queue(
                            match_result,
                            batch_id,
                            row_number,
                            model_data.get("raw_data", {}),
                            user_id,
                            dataset_id,
                        )

                        matched_count += 1
                        matched_results.append(
                            {
                                "row_number": row_number,
                                "model": model_text,
                                "dataset_id": dataset_id,
                                "status": "matched",
                                "product_id": match_result["product_id"],
                                "product_name": match_result["product_name"],
                                "matched_field": match_result["matched_field"],
                                "matched_value": match_result["matched_value"],
                                "confidence": match_result["confidence"],
                                "raw_data": model_data.get("raw_data", {}),
                            }
                        )

                        logger.debug(
                            f"型号 '{model_text}' 精确匹配成功，产品: {match_result['product_name']}"
                        )

                    else:
                        failed_count += 1
                        unmatched_results.append(
                            {
                                "row_number": row_number,
                                "model": model_text,
                                "dataset_id": dataset_id,
                                "status": "unmatched",
                                "error": "未找到精确匹配的产品",
                                "raw_data": model_data.get("raw_data", {}),
                            }
                        )

                        logger.debug(f"型号 '{model_text}' 未找到精确匹配")

                except Exception as e:
                    failed_count += 1
                    unmatched_results.append(
                        {
                            "row_number": model_data.get("row_number", 0),
                            "model": model_data.get("model", ""),
                            "dataset_id": model_data.get("dataset_id", ""),
                            "status": "error",
                            "error": f"处理异常: {str(e)}",
                            "raw_data": model_data.get("raw_data", {}),
                        }
                    )

                    logger.error(f"处理型号异常: {e}")

            # 更新导入记录
            self._update_import_record(
                batch_id, total_count, matched_count, failed_count
            )

            # 返回详细结果
            result = {
                "batch_id": batch_id,
                "total_count": total_count,
                "matched_count": matched_count,
                "unmatched_count": failed_count,
                "success_rate": (
                    round((matched_count / total_count) * 100, 2)
                    if total_count > 0
                    else 0
                ),
                "matched_results": matched_results,
                "unmatched_results": unmatched_results,
                "summary": {
                    "精确匹配": matched_count,
                    "未匹配": failed_count,
                    "匹配率": (
                        f"{round((matched_count / total_count) * 100, 2)}%"
                        if total_count > 0
                        else "0%"
                    ),
                },
            }

            logger.info(
                f"批量匹配完成: 总数 {total_count}, 匹配 {matched_count}, 未匹配 {failed_count}, 匹配率 {result['success_rate']}%"
            )

            return result

        except Exception as e:
            logger.error(f"批量匹配异常: {e}")
            return {
                "batch_id": batch_id,
                "total_count": 0,
                "matched_count": 0,
                "unmatched_count": 0,
                "success_rate": 0,
                "matched_results": [],
                "unmatched_results": [],
                "error": str(e),
            }

    def _get_all_products(self) -> List[Dict[str, Any]]:
        """
        获取所有产品数据

        Returns:
            产品数据列表
        """
        try:
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(
                        """
                        SELECT p.id, p.name, p.spec, p.introduction, p.details,
                               p.param_info, p.use_to, p.other, p.price,
                               p.small_img, p.banner, p.status,
                               c.name as category_name, b.name as brand_name
                        FROM products p
                        LEFT JOIN categories c ON p.category_id = c.id
                        LEFT JOIN brands b ON p.brand_id = b.id
                        WHERE p.status = '1'
                    """
                    )

                    products = []
                    for row in cursor.fetchall():
                        # 解析paramInfo JSON数据
                        param_info = []
                        if row[5]:  # param_info
                            try:
                                param_info = json.loads(row[5])
                            except json.JSONDecodeError:
                                logger.warning(
                                    f"产品 {row[0]} 的param_info JSON解析失败"
                                )

                        product = {
                            "product_id": row[0],
                            "product_name": row[1],
                            "spec": row[2] or "",
                            "introduction": row[3] or "",
                            "details": row[4] or "",
                            "param_info": param_info,
                            "use_to": row[6] or "",
                            "other": row[7] or "",
                            "price": row[8] or 0,
                            "small_img": row[9] or "",
                            "banner": row[10] or "",
                            "status": row[11],
                            "category_name": row[12] or "",
                            "brand_name": row[13] or "",
                        }
                        products.append(product)

                    logger.info(f"获取到 {len(products)} 个产品")
                    return products

        except Exception as e:
            logger.error(f"获取产品数据失败: {e}")
            return []

    def _extract_product_models(self, product: Dict[str, Any]) -> Dict[str, str]:
        """
        从产品数据中提取可能的型号信息

        Args:
            product: 产品数据

        Returns:
            字段名到值的映射
        """
        models = {}

        # 从paramInfo中提取型号
        param_info = product.get("param_info", [])
        if isinstance(param_info, list):
            for param in param_info:
                if isinstance(param, dict):
                    param_name = param.get("params", "").lower()
                    param_content = param.get("content", "")

                    if param_content and any(
                        keyword in param_name
                        for keyword in ["型号", "model", "规格", "spec"]
                    ):
                        models[f"param_{param_name}"] = param_content

        # 提取其他可能包含型号的字段
        if product.get("spec"):
            models["spec"] = product["spec"]

        if product.get("product_name"):
            models["name"] = product["product_name"]

        if product.get("other"):
            models["other"] = product["other"]

        return models

    def _save_match_to_queue(
        self,
        match_result: Dict[str, Any],
        batch_id: str,
        row_number: int,
        raw_data: Dict[str, Any],
        user_id: str,
        dataset_id: str,
    ) -> None:
        """
        保存匹配结果到同步队列

        Args:
            match_result: 匹配结果
            batch_id: 批次ID
            row_number: Excel行号
            raw_data: 原始数据
            user_id: 用户ID
            dataset_id: 数据集ID
        """
        try:
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    # 获取产品详细信息
                    cursor.execute(
                        """
                        SELECT p.id, p.name, p.spec, p.introduction, p.details,
                               p.param_info, p.use_to, p.other, p.price,
                               p.small_img, p.banner,
                               c.name as category_name, b.name as brand_name
                        FROM products p
                        LEFT JOIN categories c ON p.category_id = c.id
                        LEFT JOIN brands b ON p.brand_id = b.id
                        WHERE p.id = %s
                    """,
                        (match_result["product_id"],),
                    )

                    product_row = cursor.fetchone()
                    if not product_row:
                        logger.error(f"未找到产品ID: {match_result['product_id']}")
                        return

                    # 插入到同步队列
                    cursor.execute(
                        """
                        INSERT INTO sync_products_queue (
                            product_id, product_name, product_model, brand_name,
                            category_name, introduction, details, param_info,
                            use_to, price, small_img, banner, other,
                            import_batch_id, excel_row_number, match_type,
                            match_confidence, matched_by, create_by, dataset_id
                        ) VALUES (
                            %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                            %s, %s, %s, %s, %s, %s, %s
                        ) ON CONFLICT (product_id, import_batch_id) DO UPDATE SET
                            match_confidence = EXCLUDED.match_confidence,
                            match_type = EXCLUDED.match_type,
                            matched_by = EXCLUDED.matched_by,
                            update_time = CURRENT_TIMESTAMP,
                            update_by = EXCLUDED.create_by
                    """,
                        (
                            product_row[0],  # product_id
                            product_row[1],  # product_name
                            match_result["matched_value"],  # product_model
                            product_row[12] or "",  # brand_name
                            product_row[11] or "",  # category_name
                            product_row[3] or "",  # introduction
                            product_row[4] or "",  # details
                            product_row[5] or "",  # param_info
                            product_row[6] or "",  # use_to
                            product_row[8] or 0,  # price
                            product_row[9] or "",  # small_img
                            product_row[10] or "",  # banner
                            product_row[7] or "",  # other
                            batch_id,  # import_batch_id
                            row_number,  # excel_row_number
                            match_result["match_type"],  # match_type
                            match_result["confidence"],  # match_confidence
                            match_result["matched_field"],  # matched_by
                            user_id,  # create_by
                            dataset_id,  # dataset_id
                        ),
                    )

                    conn.commit()
                    logger.debug(
                        f"保存匹配结果到同步队列: 产品ID {match_result['product_id']}"
                    )

        except Exception as e:
            logger.error(f"保存匹配结果失败: {e}")
            if conn:
                conn.rollback()

    def _update_import_record(
        self, batch_id: str, total_rows: int, matched_rows: int, failed_rows: int
    ) -> None:
        """
        更新Excel导入记录

        Args:
            batch_id: 批次ID
            total_rows: 总行数
            matched_rows: 匹配成功行数
            failed_rows: 匹配失败行数
        """
        try:
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(
                        """
                        UPDATE excel_import_records SET
                            processed_rows = %s,
                            matched_rows = %s,
                            failed_rows = %s,
                            import_status = %s,
                            update_time = CURRENT_TIMESTAMP
                        WHERE batch_id = %s
                    """,
                        (
                            total_rows,
                            matched_rows,
                            failed_rows,
                            "completed" if failed_rows == 0 else "partial_success",
                            batch_id,
                        ),
                    )

                    conn.commit()
                    logger.info(f"更新导入记录: {batch_id}")

        except Exception as e:
            logger.error(f"更新导入记录失败: {e}")

    def get_sync_queue_status(self, batch_id: str) -> Dict[str, Any]:
        """
        获取同步队列状态

        Args:
            batch_id: 批次ID

        Returns:
            同步队列状态信息
        """
        try:
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    # 获取队列统计
                    cursor.execute(
                        """
                        SELECT 
                            COUNT(*) as total,
                            SUM(CASE WHEN sync_status = 'pending' THEN 1 ELSE 0 END) as pending,
                            SUM(CASE WHEN sync_status = 'processing' THEN 1 ELSE 0 END) as processing,
                            SUM(CASE WHEN sync_status = 'completed' THEN 1 ELSE 0 END) as completed,
                            SUM(CASE WHEN sync_status = 'failed' THEN 1 ELSE 0 END) as failed,
                            AVG(match_confidence) as avg_confidence
                        FROM sync_products_queue 
                        WHERE import_batch_id = %s
                    """,
                        (batch_id,),
                    )

                    stats = cursor.fetchone()

                    # 获取详细列表
                    cursor.execute(
                        """
                        SELECT product_id, product_name, product_model, match_type,
                               match_confidence, sync_status, create_time
                        FROM sync_products_queue 
                        WHERE import_batch_id = %s
                        ORDER BY match_confidence DESC
                    """,
                        (batch_id,),
                    )

                    items = []
                    for row in cursor.fetchall():
                        items.append(
                            {
                                "product_id": row[0],
                                "product_name": row[1],
                                "product_model": row[2],
                                "match_type": row[3],
                                "match_confidence": float(row[4]) if row[4] else 0,
                                "sync_status": row[5],
                                "create_time": row[6].isoformat() if row[6] else None,
                            }
                        )

                    return {
                        "batch_id": batch_id,
                        "total": stats[0] if stats else 0,
                        "pending": stats[1] if stats else 0,
                        "processing": stats[2] if stats else 0,
                        "completed": stats[3] if stats else 0,
                        "failed": stats[4] if stats else 0,
                        "avg_confidence": float(stats[5]) if stats and stats[5] else 0,
                        "items": items,
                    }

        except Exception as e:
            logger.error(f"获取同步队列状态失败: {e}")
            return {
                "batch_id": batch_id,
                "total": 0,
                "pending": 0,
                "processing": 0,
                "completed": 0,
                "failed": 0,
                "avg_confidence": 0,
                "items": [],
                "error": str(e),
            }
