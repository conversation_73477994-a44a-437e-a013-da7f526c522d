# FastGPT API 接口文档

## 概述

本文档基于 FastGPT 官方开发文档整理，详细说明了 FastGPT 数据集相关的 API 接口。

**官方文档地址**: https://doc.tryfastgpt.ai/docs/development/openapi/dataset/

## 接口基础信息

- **基础URL**: `http://localhost:3000` (本地部署) 或 `https://api.fastgpt.in` (云服务)
- **认证方式**: Bearer Token
- **请求头**: 
  - `Authorization: Bearer {{authorization}}`
  - `Content-Type: application/json`

---

## 1. 获取知识库列表

### 接口描述
获取用户有权限访问的知识库列表。

### 请求信息
- **请求方法**: GET
- **请求路径**: `/api/core/dataset/list`

### 请求示例
```bash
curl --location --request GET 'http://localhost:3000/api/core/dataset/list' \
--header 'Authorization: Bearer {{authorization}}'
```

### 参数说明
无需传递参数，通过 Authorization 头部验证用户权限。

### 响应示例
```json
{
    "code": 200,
    "statusText": "",
    "data": [
        {
            "_id": "6554684f7f9ed18a39a4d15c",
            "parentId": null,
            "userId": "6540aaa5f9b93bcbbaea52c4",
            "teamId": "6540aaa5f9b93bcbbaea52c3",
            "tmbId": "6540aaa5f9b93bcbbaea52c5",
            "updateTime": "2023-11-15T07:17:22.804Z",
            "type": "dataset",
            "avatar": "core/dataset/datasetFill",
            "name": "知识库",
            "intro": "",
            "status": "active",
            "permission": "inherit",
            "isOwner": true,
            "canWrite": true
        }
    ]
}
```

---

## 2. 获取集合列表

### 接口描述
获取指定知识库下的集合列表，支持分页。

### 请求信息
- **请求方法**: POST
- **请求路径**: `/api/core/dataset/collection/list`

### 请求示例
```bash
curl --location --request POST 'http://localhost:3000/api/core/dataset/collection/list' \
--header 'Authorization: Bearer {{authorization}}' \
--header 'Content-Type: application/json' \
--data-raw '{
    "datasetId": "65abc9bd9d1448617cba5e6c",
    "pageNum": 1,
    "pageSize": 10,
    "searchText": ""
}'
```

### 参数说明
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| datasetId | string | 是 | 知识库的ID |
| pageNum | number | 否 | 页码，默认为1 |
| pageSize | number | 否 | 每页大小，默认为10 |
| searchText | string | 否 | 搜索文本，用于筛选集合 |

### 响应示例
```json
{
    "code": 200,
    "statusText": "",
    "data": {
        "pageNum": 1,
        "pageSize": 10,
        "data": [
            {
                "_id": "65abd4ac9d1448617cba6171",
                "parentId": "65abc9bd9d1448617cba5e6c",
                "userId": "64ca2e3a00749f6fffb7dd83",
                "teamId": "64ca2e3a00749f6fffb7dd82",
                "tmbId": "64ca2e3a00749f6fffb7dd84",
                "updateTime": "2023-11-21T08:47:24.503Z",
                "type": "virtual",
                "name": "中文-AIGC白皮书2022.pdf",
                "tags": ["manual"],
                "forbid": false,
                "dataAmount": 105,
                "trainingAmount": 0,
                "canWrite": true
            }
        ],
        "total": 1
    }
}
```

---

## 3. 为集合批量添加数据

### 接口描述
向指定集合批量添加训练数据，支持问答对和索引数据。

### 请求信息
- **请求方法**: POST
- **请求路径**: `/api/core/dataset/data/pushData`

### 请求示例
```bash
curl --location --request POST 'http://localhost:3000/api/core/dataset/data/pushData' \
--header 'Authorization: Bearer {{authorization}}' \
--header 'Content-Type: application/json' \
--data-raw '{
    "collectionId": "65abd4ac9d1448617cba6171",
    "data": [
        {
            "q": "导演是谁",
            "a": "xxx",
            "indexes": [
                {
                    "type": "default",
                    "text": "默认索引"
                },
                {
                    "type": "custom",
                    "text": "自定义索引1"
                }
            ]
        }
    ]
}'
```

### 参数说明
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| collectionId | string | 是 | 集合的ID |
| data | array | 是 | 数据数组 |
| data[].q | string | 是 | 问题文本 |
| data[].a | string | 否 | 答案文本 |
| data[].indexes | array | 否 | 索引数组 |
| data[].indexes[].type | string | 是 | 索引类型：default 或 custom |
| data[].indexes[].text | string | 是 | 索引文本内容 |

### 响应示例
```json
{
    "code": 200,
    "statusText": "",
    "message": "",
    "data": {
        "insertLen": 1,
        "overWrite": false
    }
}
```

---

## 4. 获取集合的数据

### 接口描述
获取指定集合下的训练数据，支持分页和搜索。

### 请求信息
- **请求方法**: POST
- **请求路径**: `/api/core/dataset/data/list`

### 请求示例
```bash
curl --location --request POST 'http://localhost:3000/api/core/dataset/data/list' \
--header 'Authorization: Bearer {{authorization}}' \
--header 'Content-Type: application/json' \
--data-raw '{
    "pageNum": 1,
    "pageSize": 10,
    "collectionId": "65abd4ac9d1448617cba6171",
    "searchText": ""
}'
```

### 参数说明
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| pageNum | number | 否 | 页码，默认为1 |
| pageSize | number | 否 | 每页大小，默认为10 |
| collectionId | string | 是 | 集合的ID |
| searchText | string | 否 | 搜索文本 |

### 响应示例
```json
{
    "code": 200,
    "statusText": "",
    "data": {
        "pageNum": 1,
        "pageSize": 10,
        "data": [
            {
                "_id": "65abd4b29d1448617cba61db",
                "q": "N o . 2 0 2 2 1 2中 国 信 息 通 信 研 究 院京东探索研究院2022年 9月人工智能生成内容（AIGC）白皮书(2022 年)版权声明本白皮书版权属于中国信息通信研究院和京东探索研究院，并受法律保护。转载、摘编或利用其它方式使用本白皮书文字或者观点的，应注明"来源：中国信息通信研究院和京东探索研究院"。违反上述声明者，编者将追究其相关法律责任。前 言习近平总书记曾指出，"数字技术正以新理念、新业态、新模式全面融入人类经济、政治、文化、社会、生态文明建设各领域和全过程"。在当前数字世界和物理世界加速融合的大背景下，人工智能生成内容（Artificial Intelligence Generated Content，简称 AIGC）正在悄然引导着一场深刻的变革，重塑甚至颠覆数字内容的生产方式和消费模式，将极大地丰富人们的数字生活，是未来全面迈向数字文明新时代不可或缺的支撑力量。",
                "a": "",
                "chunkIndex": 0,
                "indexes": [
                    {
                        "type": "default",
                        "dataId": "3720083",
                        "text": "N o . 2 0 2 2 1 2中 国 信 息 通 信 研 究 院京东探索研究院2022年 9月人工智能生成内容（AIGC）白皮书(2022 年)版权声明本白皮书版权属于中国信息通信研究院和京东探索研究院，并受法律保护。转载、摘编或利用其它方式使用本白皮书文字或者观点的，应注明"来源：中国信息通信研究院和京东探索研究院"。违反上述声明者，编者将追究其相关法律责任。前 言习近平总书记曾指出，"数字技术正以新理念、新业态、新模式全面融入人类经济、政治、文化、社会、生态文明建设各领域和全过程"。在当前数字世界和物理世界加速融合的大背景下，人工智能生成内容（Artificial Intelligence Generated Content，简称 AIGC）正在悄然引导着一场深刻的变革，重塑甚至颠覆数字内容的生产方式和消费模式，将极大地丰富人们的数字生活，是未来全面迈向数字文明新时代不可或缺的支撑力量。",
                        "_id": "65abd4b29d1448617cba61dc"
                    }
                ],
                "datasetId": "65abc9bd9d1448617cba5e6c",
                "collectionId": "65abd4ac9d1448617cba6171",
                "sourceName": "中文-AIGC白皮书2022.pdf",
                "sourceId": "65abd4ac9d1448617cba6166",
                "isOwner": true,
                "canWrite": true
            }
        ],
        "total": 105
    }
}
```

---

## 5. 修改单条数据

### 接口描述
修改集合中的单条训练数据。

### 请求信息
- **请求方法**: PUT
- **请求路径**: `/api/core/dataset/data/update`

### 请求示例
```bash
curl --location --request PUT 'http://localhost:3000/api/core/dataset/data/update' \
--header 'Authorization: Bearer {{authorization}}' \
--header 'Content-Type: application/json' \
--data-raw '{
    "dataId":"65abd4b29d1448617cba61db",
    "q":"测试111",
    "a":"sss",
    "indexes":[
        {
            "dataId": "xxxx",
            "type": "default",
            "text": "默认索引"
        },
        {
            "dataId": "xxx",
            "type": "custom",
            "text": "旧的自定义索引1"
        },
        {
            "type":"custom",
            "text":"新增的自定义索引"
        }
    ]
}'
```

### 参数说明
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| dataId | string | 是 | 数据的ID |
| q | string | 否 | 主要数据（问题） |
| a | string | 否 | 辅助数据（答案） |
| indexes | array | 否 | 自定义索引数组 |

**索引说明**：
- 如果创建时有自定义索引，更新时需要传入完整的索引数组
- 带有 `dataId` 的索引会被更新
- 不带 `dataId` 的索引会被新增
- 未在数组中的现有索引会被删除

### 响应示例
```json
{
    "code": 200,
    "statusText": "",
    "message": "",
    "data": null
}
```

---

## 6. 删除单条数据

### 接口描述
删除集合中的单条训练数据。

### 请求信息
- **请求方法**: DELETE
- **请求路径**: `/api/core/dataset/data/delete`

### 请求示例
```bash
curl --location --request DELETE 'http://localhost:3000/api/core/dataset/data/delete?id=65abd4b39d1448617cba624d' \
--header 'Authorization: Bearer {{authorization}}'
```

### 参数说明
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| id | string | 是 | 数据的ID（通过查询参数传递） |

### 响应示例
```json
{
    "code": 200,
    "statusText": "",
    "message": "",
    "data": "success"
}
```

---

## 7. 搜索测试

### 接口描述
测试知识库的搜索功能，用于验证搜索效果和参数调优。

### 请求信息
- **请求方法**: POST
- **请求路径**: `/api/core/dataset/searchTest`

### 请求示例
```bash
curl --location --request POST 'https://api.fastgpt.in/api/core/dataset/searchTest' \
--header 'Authorization: Bearer fastgpt-xxxxx' \
--header 'Content-Type: application/json' \
--data-raw '{
    "datasetId": "知识库的ID",
    "text": "导演是谁",
    "limit": 5000,
    "similarity": 0,
    "searchMode": "embedding",
    "usingReRank": false,
    "datasetSearchUsingExtensionQuery": true,
    "datasetSearchExtensionModel": "gpt-4o-mini",
    "datasetSearchExtensionBg": ""
}'
```

### 参数说明
| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| datasetId | string | 是 | 知识库ID |
| text | string | 是 | 需要测试的文本 |
| limit | number | 否 | 最大 tokens 数量，最多 20000 |
| similarity | number | 否 | 最低相关度（0~1，可选） |
| searchMode | string | 否 | 搜索模式：embedding / fullTextRecall / mixedRecall |
| usingReRank | boolean | 否 | 是否使用重排 |
| datasetSearchUsingExtensionQuery | boolean | 否 | 是否使用问题优化 |
| datasetSearchExtensionModel | string | 否 | 问题优化模型 |
| datasetSearchExtensionBg | string | 否 | 问题优化背景描述 |

### 响应示例
```json
{
    "code": 200,
    "statusText": "",
    "data": [
        {
            "id": "65599c54a5c814fb803363cb",
            "q": "你是谁",
            "a": "我是FastGPT助手",
            "datasetId": "6554684f7f9ed18a39a4d15c",
            "collectionId": "6556cd795e4b663e770bb66d",
            "sourceName": "GBT 15104-2021 装饰单板贴面人造板.pdf",
            "sourceId": "6556cd775e4b663e770bb65c",
            "score": 0.8050316572189331
        }
    ]
}
```

**说明**：返回 top k 结果，limit 为最大 Tokens 数量，最多 20000 tokens。

---

## 状态码说明

### 成功状态码
- `200`: 请求成功

### 错误状态码
- `400`: 请求参数错误
- `401`: 未授权，Token 无效或过期
- `403`: 禁止访问，权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

---

## 认证说明

### Token 获取
1. 登录 FastGPT 系统
2. 在个人设置中生成 API Key
3. 使用生成的 API Key 作为 Bearer Token

### Token 使用
在所有 API 请求的 Header 中添加：
```
Authorization: Bearer YOUR_API_KEY
```

---

## 注意事项

1. **数据限制**：单次批量添加数据建议不超过 100 条
2. **Rate Limiting**：API 请求频率限制，建议控制在每秒 10 次以内
3. **Token 数量**：搜索测试接口的 limit 参数最大值为 20000 tokens
4. **文件大小**：上传文件大小限制根据部署配置而定
5. **编码格式**：所有文本数据应使用 UTF-8 编码

---

## 更新日志

- **2024-12-19**: 基于官方文档创建初始版本
- 文档基于 FastGPT 官方开发文档：https://doc.tryfastgpt.ai/docs/development/openapi/dataset/

---

## 相关链接

- [FastGPT 官方文档](https://doc.tryfastgpt.ai/)
- [FastGPT GitHub](https://github.com/labring/FastGPT)
- [FastGPT 对话接口](https://doc.tryfastgpt.ai/docs/development/openapi/chat/) 