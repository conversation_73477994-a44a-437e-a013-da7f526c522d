import streamlit as st
import logging
import pandas as pd
from typing import Optional, Dict, Any, List
from utils.session import SessionManager
from utils.logging_config import get_logger
from utils.api_client import ZKMallClient

# 配置日志
logger = get_logger()


def main():
    """方案管理页面主函数"""
    # 页面配置
    st.set_page_config(
        page_title="方案管理 - 云商系统",
        page_icon="🎯",
        layout="wide",
        initial_sidebar_state="expanded",
    )

    # 初始化session
    initialize_session()

    # 渲染页面
    render_header()
    render_sidebar()
    render_content()
    render_footer()


def initialize_session():
    """初始化页面session状态"""
    defaults = {
        "current_programme": None,
        "search_term": "",
        "filter_category": "all",
        "current_page": 1,
        "page_size": 20,
    }

    for key, value in defaults.items():
        if f"programme_management_{key}" not in st.session_state:
            SessionManager.set(f"programme_management_{key}", value)


@st.cache_data(ttl=300)
def load_programmes_from_api(
    search_term: str = "",
    current_page: int = 1,
    page_size: int = 20,
    product_id: Optional[int] = None,
    category: Optional[str] = None,
) -> Dict[str, Any]:
    """
    从API加载方案数据

    Args:
        search_term: 搜索关键词
        current_page: 当前页码
        page_size: 每页数量
        product_id: 产品ID（可选）
        category: 分类（可选）

    Returns:
        包含方案数据和分页信息的字典
    """
    try:
        client = ZKMallClient()

        # 构建查询参数
        params = {"current": current_page, "pageSize": page_size}

        if product_id:
            params["productId"] = product_id

        # 暂时注释掉分类过滤，先测试基础数据获取
        # if category and category != "all":
        #     # 根据category映射到具体的分类ID
        #     category_mapping = {
        #         "security": 563,  # 安防方案分类ID
        #         "access": 564,  # 门禁方案分类ID
        #         "time": 565,  # 考勤方案分类ID
        #         "system": 566,  # 系统方案分类ID
        #     }
        #     if category in category_mapping:
        #         params["category"] = category_mapping[category]

        logger.info(f"方案API调用参数: {params}")

        # 调用API获取方案数据
        programmes = client.get_programmes(**params)

        logger.info(
            f"方案API返回数据类型: {type(programmes)}, 数量: {len(programmes) if isinstance(programmes, list) else 'N/A'}"
        )

        # 如果返回为空，记录详细信息
        if not programmes:
            logger.warning(
                "方案API返回空数据，可能的原因：1) API端点错误，2) 参数错误，3) 服务器无数据"
            )

        # 如果有搜索关键词，进行本地过滤
        if search_term and programmes:
            filtered_programmes = []
            for programme in programmes:
                if (
                    search_term.lower() in programme.get("name", "").lower()
                    or search_term.lower() in programme.get("introduction", "").lower()
                    or search_term.lower() in programme.get("categoryName", "").lower()
                ):
                    filtered_programmes.append(programme)
            programmes = filtered_programmes
            logger.info(f"搜索关键词'{search_term}'过滤后数量: {len(programmes)}")

        return {
            "programmes": programmes,
            "total": len(programmes),
            "current_page": current_page,
            "page_size": page_size,
        }

    except Exception as e:
        logger.error(f"获取方案数据失败: {e}")
        st.error(f"获取方案数据失败: {e}")
        return {
            "programmes": [],
            "total": 0,
            "current_page": current_page,
            "page_size": page_size,
        }


def render_header():
    """渲染页面头部"""
    st.title("🎯 方案管理")
    st.markdown("管理和查看云商系统中的方案信息")

    # 导航面包屑
    st.markdown("🏢 [首页](/) > 🎯 方案管理")


def render_sidebar():
    """渲染侧边栏"""
    with st.sidebar:
        st.subheader("🎯 方案管理")

        # 搜索功能
        search_term = st.text_input(
            "搜索方案",
            value=SessionManager.get("programme_management_search_term", ""),
            placeholder="输入方案名称或关键词",
        )
        SessionManager.set("programme_management_search_term", search_term)

        # 类别筛选
        filter_category = st.selectbox(
            "方案类别",
            options=["all", "security", "access", "time", "system"],
            format_func=lambda x: {
                "all": "全部类别",
                "security": "安防方案",
                "access": "门禁方案",
                "time": "考勤方案",
                "system": "系统方案",
            }[x],
            index=["all", "security", "access", "time", "system"].index(
                SessionManager.get("programme_management_filter_category", "all")
            ),
        )
        SessionManager.set("programme_management_filter_category", filter_category)

        # 分页设置
        page_size = st.selectbox(
            "每页显示数量",
            options=[10, 20, 50, 100],
            index=[10, 20, 50, 100].index(
                SessionManager.get("programme_management_page_size", 20)
            ),
        )
        SessionManager.set("programme_management_page_size", page_size)

        if st.button("🔄 刷新数据", use_container_width=True):
            st.cache_data.clear()
            st.rerun()

        if st.button("🏠 返回首页", use_container_width=True):
            st.switch_page("main.py")


def render_content():
    """渲染主要内容"""
    tab1, tab2, tab3 = st.tabs(["方案列表", "方案详情", "操作记录"])

    with tab1:
        render_programme_list()

    with tab2:
        render_programme_detail()

    with tab3:
        render_operation_log()


def render_programme_list():
    """渲染方案列表"""
    st.subheader("方案列表")

    # 操作按钮
    col1, col2, col3 = st.columns([1, 1, 2])
    with col1:
        if st.button("➕ 新增方案"):
            st.info("新增方案功能开发中...")

    with col2:
        if st.button("📤 导出数据"):
            st.info("导出功能开发中...")

    # 获取搜索参数
    search_term = SessionManager.get("programme_management_search_term", "")
    filter_category = SessionManager.get("programme_management_filter_category", "all")
    # 确保页码和页大小是整数类型
    current_page = int(SessionManager.get("programme_management_current_page", 1))
    page_size = int(SessionManager.get("programme_management_page_size", 20))

    # 数据展示区域
    with st.container(border=True):
        with st.spinner("正在加载方案数据..."):
            # 从API获取方案数据
            data = load_programmes_from_api(
                search_term=search_term,
                current_page=current_page,
                page_size=page_size,
                category=filter_category,
            )

            programmes = data["programmes"]
            total = data["total"]

            if programmes:
                st.success(f"✅ 成功加载 {len(programmes)} 个方案")

                # 创建可选择的数据表格
                programme_df = pd.DataFrame(programmes)

                # 选择要显示的列
                display_columns = []
                available_columns = programme_df.columns.tolist()

                # 根据可用列动态选择显示列
                column_mapping = {
                    "id": "ID",
                    "name": "方案名称",
                    "categoryName": "分类",
                    "introduction": "简介",
                    "count": "浏览量",
                    "likeCount": "点赞数",
                    "favoriteCount": "收藏数",
                    "isSuggest": "推荐状态",
                    "status": "状态",
                }

                for col, display_name in column_mapping.items():
                    if col in available_columns:
                        display_columns.append(col)
                        programme_df[col] = programme_df[col].astype(str)

                if display_columns:
                    # 重命名列名
                    display_df = programme_df[display_columns].copy()
                    display_df.columns = [
                        column_mapping.get(col, col) for col in display_columns
                    ]

                    # 显示数据表格，支持选择
                    selected_rows = st.dataframe(
                        display_df,
                        use_container_width=True,
                        hide_index=True,
                        on_select="rerun",
                        selection_mode="single-row",
                    )

                    # 处理选择的行
                    if selected_rows.selection.rows:
                        selected_index = selected_rows.selection.rows[0]
                        selected_programme = programmes[selected_index]
                        SessionManager.set(
                            "programme_management_current_programme", selected_programme
                        )
                        st.success(
                            f"已选择方案: {selected_programme.get('name', 'N/A')}"
                        )

                    # 分页控制
                    render_pagination(
                        current_page, total, page_size, "programme_management"
                    )

                else:
                    st.warning("方案数据格式异常，无法显示")

            else:
                st.warning("暂无方案数据")


def render_programme_detail():
    """渲染方案详情"""
    st.subheader("方案详情")

    current_programme = SessionManager.get("programme_management_current_programme")

    if current_programme:
        # 显示方案基本信息
        col1, col2 = st.columns([1, 1])

        with col1:
            st.write("**基本信息**")
            st.write(f"方案ID: {current_programme.get('id', 'N/A')}")
            st.write(f"方案名称: {current_programme.get('name', 'N/A')}")
            st.write(f"分类: {current_programme.get('categoryName', 'N/A')}")
            st.write(f"状态: {current_programme.get('status', 'N/A')}")

        with col2:
            st.write("**统计信息**")
            st.write(f"浏览量: {current_programme.get('count', 0)}")
            st.write(f"点赞数: {current_programme.get('likeCount', 0)}")
            st.write(f"收藏数: {current_programme.get('favoriteCount', 0)}")
            st.write(
                f"推荐状态: {'是' if current_programme.get('isSuggest') == '1' else '否'}"
            )

        # 显示方案简介
        if current_programme.get("introduction"):
            st.write("**方案简介**")
            st.write(current_programme["introduction"])

        # 显示方案内容
        if current_programme.get("content"):
            st.write("**方案内容**")
            st.markdown(current_programme["content"], unsafe_allow_html=True)

        # 显示方案图片
        if current_programme.get("smallImg"):
            st.write("**方案图片**")
            try:
                st.image(current_programme["smallImg"], width=300)
            except Exception as e:
                st.warning(f"图片加载失败: {e}")

        # 显示轮播图
        if current_programme.get("banner"):
            st.write("**轮播图**")
            banner_urls = current_programme["banner"].split(",")
            for i, url in enumerate(banner_urls):
                if url.strip():
                    try:
                        st.image(url.strip(), caption=f"轮播图 {i+1}", width=300)
                    except Exception as e:
                        st.warning(f"轮播图 {i+1} 加载失败: {e}")

        # 显示产品列表
        if current_programme.get("list"):
            st.write("**关联产品**")
            product_list = current_programme["list"]
            if isinstance(product_list, list) and product_list:
                product_df = pd.DataFrame(product_list)
                st.dataframe(product_df, use_container_width=True)

    else:
        st.info("请从方案列表中选择一个方案查看详情")


def render_pagination(current_page: int, total: int, page_size: int, prefix: str):
    """渲染分页控件"""
    # 确保所有值都是整数类型
    current_page = int(current_page)
    total = int(total)
    page_size = int(page_size)
    total_pages = max(1, (total + page_size - 1) // page_size)

    if total_pages > 1:
        st.markdown("---")
        col1, col2, col3, col4, col5 = st.columns([1, 1, 2, 1, 1])

        with col1:
            if st.button("⏮️ 首页", disabled=(current_page <= 1)):
                SessionManager.set(f"{prefix}_current_page", 1)
                st.rerun()

        with col2:
            if st.button("⬅️ 上页", disabled=(current_page <= 1)):
                SessionManager.set(f"{prefix}_current_page", current_page - 1)
                st.rerun()

        with col3:
            st.write(
                f"第 {current_page} 页，共 {total_pages} 页（总计 {total} 条记录）"
            )

        with col4:
            if st.button("➡️ 下页", disabled=(current_page >= total_pages)):
                SessionManager.set(f"{prefix}_current_page", current_page + 1)
                st.rerun()

        with col5:
            if st.button("⏭️ 末页", disabled=(current_page >= total_pages)):
                SessionManager.set(f"{prefix}_current_page", total_pages)
                st.rerun()


def render_operation_log():
    """渲染操作记录"""
    st.subheader("操作记录")
    st.info("操作记录功能开发中...")


def render_footer():
    """渲染页面脚部"""
    st.markdown("---")
    st.markdown("© 2024 云商系统 - 方案管理模块")


if __name__ == "__main__":
    main()
