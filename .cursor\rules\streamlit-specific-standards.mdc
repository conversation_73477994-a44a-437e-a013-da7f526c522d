---
description: 
globs: 
alwaysApply: true
---
# Streamlit 开发规范

## 📋 概述

本文档定义了云商系统项目中Streamlit应用的开发规范。Streamlit是一个Python库，用于创建数据科学和机器学习的Web应用。本规范旨在确保Streamlit应用的一致性、可维护性和性能。

## 🏗️ 页面结构

### 1. 标准页面模板

每个Streamlit页面应当遵循以下结构：

```python
import streamlit as st
import logging
from typing import Optional, Dict, Any
from utils.session import SessionManager
from utils.auth import require_auth

# 配置日志
logger = logging.getLogger(__name__)

@require_auth
def main():
    """页面主函数"""
    # 页面配置
    st.set_page_config(
        page_title="页面标题",
        page_icon="🏢",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # 初始化session
    initialize_session()
    
    # 渲染页面
    render_header()
    render_sidebar()
    render_content()
    render_footer()

def initialize_session():
    """初始化页面session状态"""
    # 设置默认值
    defaults = {
        'key1': 'value1',
        'key2': 'value2'
    }
    
    for key, value in defaults.items():
        if key not in st.session_state:
            SessionManager.set(key, value)

def render_header():
    """渲染页面头部"""
    st.title("页面标题")
    st.markdown("页面描述")

def render_sidebar():
    """渲染侧边栏"""
    with st.sidebar:
        st.subheader("侧边栏标题")
        # 侧边栏内容

def render_content():
    """渲染主要内容"""
    # 主要内容

def render_footer():
    """渲染页面脚部"""
    st.markdown("---")
    st.markdown("© 2024 云商系统")

if __name__ == "__main__":
    main()
```

### 2. 文件命名和组织

- 页面文件应当使用英文命名，如 `product_management.py`
- 可以使用数字前缀表示顺序，如 `01_product_management.py`
- 页面文件应当放在 `pages` 目录下
- 可复用组件应当放在 `components` 目录下

## 🔄 状态管理

### 1. Session State 使用规范

- 使用 `SessionManager` 类管理session状态，而不是直接操作 `st.session_state`
- 在页面初始化时设置默认值
- 避免在渲染函数中修改session状态，可能导致重新渲染循环

```python
# 正确的做法
if st.button("更新"):
    SessionManager.set("key", "value")
    st.rerun()  # 使用rerun而不是直接修改状态

# 避免的做法
if "key" not in st.session_state:
    st.session_state.key = "value"  # 直接操作session_state
```

### 2. 页面间导航

- 使用 `st.switch_page()` 进行页面导航
- 在导航前保存必要的状态
- 使用英文文件路径，避免中文路径

```python
# 正确的做法
if st.button("查看详情"):
    SessionManager.set("current_product", product)
    st.switch_page("pages/02_product_detail.py")

# 避免的做法
if st.button("查看详情"):
    st.switch_page("pages/02_📋_产品详情.py")  # 使用中文和特殊字符
```

## 💾 缓存优化

### 1. 缓存装饰器

- 使用 `@st.cache_data` 缓存数据加载和处理函数
- 使用 `@st.cache_resource` 缓存资源（如模型、数据库连接）
- 为缓存设置适当的TTL（生存时间）

```python
@st.cache_data(ttl=300)  # 5分钟缓存
def load_products(search_term: str = "") -> List[Dict]:
    # 数据加载逻辑
    return products

@st.cache_resource
def get_database_connection():
    # 创建数据库连接
    return connection
```

### 2. 缓存失效

- 在数据更新时主动清理相关缓存
- 使用 `st.cache_data.clear()` 清理数据缓存
- 使用 `st.cache_resource.clear()` 清理资源缓存

## 📊 UI组件最佳实践

### 1. 响应式布局

- 使用 `st.columns()` 创建响应式列布局
- 使用 `st.container()` 组织相关元素
- 使用 `st.expander()` 折叠不常用的内容

```python
col1, col2, col3 = st.columns([1, 2, 1])  # 比例为1:2:1

with col1:
    st.subheader("左侧栏")
    # 左侧内容

with col2:
    st.subheader("主内容")
    # 主要内容

with col3:
    st.subheader("右侧栏")
    # 右侧内容
```

### 2. 用户反馈

- 使用 `st.spinner()` 显示加载状态
- 使用 `st.success()`, `st.error()`, `st.warning()`, `st.info()` 提供反馈
- 使用 `st.progress()` 显示进度

```python
with st.spinner("加载数据中..."):
    try:
        data = load_data()
        st.success("数据加载成功！")
    except Exception as e:
        st.error(f"数据加载失败: {e}")
        logger.error(f"数据加载失败: {e}")
```

### 3. 表单处理

- 使用 `st.form()` 组织表单元素
- 使用 `st.form_submit_button()` 提交表单
- 验证表单输入

```python
with st.form("login_form"):
    username = st.text_input("用户名")
    password = st.text_input("密码", type="password")
    submitted = st.form_submit_button("登录")
    
    if submitted:
        if not username or not password:
            st.error("用户名和密码不能为空")
        else:
            # 处理登录逻辑
```

## 🚀 性能优化

### 1. 避免重复计算

- 使用缓存减少重复计算
- 避免在循环中创建Streamlit组件
- 使用批处理而不是逐项处理

```python
# 避免的做法
for item in items:
    st.write(item)  # 每次调用都会触发重新渲染

# 推荐的做法
st.write(pd.DataFrame(items))  # 一次性显示所有项
```

### 2. 懒加载

- 使用条件渲染延迟加载不立即需要的内容
- 使用分页减少一次性加载的数据量
- 使用 `st.empty()` 预留空间，稍后填充内容

```python
# 预留空间
result_container = st.empty()

# 用户操作后填充内容
if st.button("计算"):
    with st.spinner("计算中..."):
        result = perform_calculation()
    result_container.success(f"计算结果: {result}")
```

## 📱 多设备支持

### 1. 响应式设计

- 使用百分比而不是固定像素值设置宽度
- 使用 `use_column_width=True` 参数使图片自适应列宽
- 测试不同屏幕尺寸下的显示效果

```python
st.image("image.jpg", use_column_width=True)  # 自适应列宽
```

### 2. 移动设备优化

- 简化移动设备上的UI
- 确保按钮和输入框足够大，易于触摸操作
- 考虑在移动设备上隐藏非必要内容

## 🧪 测试和调试

### 1. 本地测试

- 使用 `streamlit run file.py` 本地运行和测试
- 使用 `st.write(var)` 或 `st.json(var)` 调试变量
- 使用 `st.echo()` 显示代码和执行结果

### 2. 错误处理

- 捕获并处理可能的异常
- 提供用户友好的错误消息
- 记录详细的错误日志

```python
try:
    result = api_client.get_data()
    st.write(result)
except Exception as e:
    st.error("无法获取数据，请稍后重试")
    logger.error(f"API调用失败: {e}")
```

## ✅ Streamlit应用检查清单

在部署Streamlit应用前，请检查以下项目：

- [ ] 页面布局是否响应式
- [ ] 是否正确使用缓存优化性能
- [ ] 是否处理了加载状态和错误情况
- [ ] 用户交互是否流畅直观
- [ ] 页面导航是否正常工作
- [ ] 是否适配不同设备尺寸
- [ ] 是否遵循统一的设计风格

---

**文档版本**: v1.0  
**最后更新**: 2024-12-19  
**维护者**: 开发团队


