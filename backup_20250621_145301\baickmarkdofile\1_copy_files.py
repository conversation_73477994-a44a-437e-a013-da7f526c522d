#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件拷贝脚本
将 output 文件夹中每个解析文件里的 md 文件和 images 文件夹拷贝到 output_new 文件夹
"""

import os
import shutil
from pathlib import Path

def copy_md_and_images(source_dir="output", target_dir="output_new"):
    """
    从source_dir中的每个子文件夹的vlm目录中拷贝md文件和images文件夹到target_dir
    
    Args:
        source_dir (str): 源目录，默认为"output"
        target_dir (str): 目标目录，默认为"output_new"
    """
    source_path = Path(source_dir)
    target_path = Path(target_dir)
    
    # 创建目标目录
    target_path.mkdir(exist_ok=True)
    
    if not source_path.exists():
        print(f"源目录 {source_dir} 不存在！")
        return
    
    # 统计信息
    processed_count = 0
    success_count = 0
    error_count = 0
    
    print(f"开始处理从 {source_dir} 到 {target_dir} 的文件拷贝...")
    print("-" * 60)
    
    # 遍历源目录中的所有子文件夹
    for folder in source_path.iterdir():
        if not folder.is_dir():
            continue
            
        folder_name = folder.name
        vlm_path = folder / "vlm"
        
        print(f"处理文件夹: {folder_name}")
        processed_count += 1
        
        # 检查vlm文件夹是否存在
        if not vlm_path.exists():
            print(f"  ⚠️  跳过: vlm文件夹不存在")
            continue
        
        try:
            # 创建目标文件夹
            target_folder = target_path / folder_name
            target_folder.mkdir(exist_ok=True)
            
            # 查找md文件
            md_files = list(vlm_path.glob("*.md"))
            images_folder = vlm_path / "images"
            
            copied_items = []
            
            # 拷贝md文件
            for md_file in md_files:
                target_md = target_folder / md_file.name
                shutil.copy2(md_file, target_md)
                copied_items.append(f"MD文件: {md_file.name}")
                print(f"  ✅ 已拷贝 MD文件: {md_file.name}")
            
            # 拷贝images文件夹
            if images_folder.exists() and images_folder.is_dir():
                target_images = target_folder / "images"
                if target_images.exists():
                    shutil.rmtree(target_images)
                shutil.copytree(images_folder, target_images)
                
                # 统计图片数量
                image_count = len(list(target_images.glob("*.*")))
                copied_items.append(f"images文件夹 (包含{image_count}个文件)")
                print(f"  ✅ 已拷贝 images文件夹 (包含{image_count}个文件)")
            else:
                print(f"  ⚠️  images文件夹不存在")
            
            if copied_items:
                success_count += 1
                print(f"  ✅ 成功处理: {', '.join(copied_items)}")
            else:
                print(f"  ⚠️  没有找到可拷贝的文件")
                
        except Exception as e:
            error_count += 1
            print(f"  ❌ 处理失败: {str(e)}")
        
        print()
    
    # 输出统计信息
    print("-" * 60)
    print("处理完成！")
    print(f"总共处理文件夹数: {processed_count}")
    print(f"成功处理数: {success_count}")
    print(f"失败数: {error_count}")
    print(f"目标目录: {target_path.absolute()}")

def main():
    """主函数"""
    print("PDF解析文件拷贝工具")
    print("=" * 60)
    
    # 检查当前目录
    current_dir = Path.cwd()
    print(f"当前工作目录: {current_dir}")
    
    # 检查output目录是否存在
    if not (current_dir / "output").exists():
        print("错误: 当前目录下没有找到 'output' 文件夹")
        print("请确保在包含 'output' 文件夹的目录中运行此脚本")
        return
    
    # 执行拷贝操作
    copy_md_and_images()
    
    print("\n脚本执行完毕！")

if __name__ == "__main__":
    main() 