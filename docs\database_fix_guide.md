# 数据库架构修复指南

## 问题概述

根据错误日志分析，您的同步系统存在以下数据库架构问题：

1. **缺失 `created_at` 字段** - 多个表缺少该字段
2. **缺失 `orders` 表** - 系统尝试查询不存在的表
3. **事务中断** - 由于前面的错误导致事务回滚

## 错误分析

### 主要错误类型

```
2025-06-21 08:22:37,029 - __main__ - ERROR - 获取性能监控数据失败: column "created_at" does not exist
2025-06-21 08:22:38,056 - __main__ - WARNING - 获取订单数据统计失败: relation "orders" does not exist
2025-06-21 08:22:38,087 - __main__ - WARNING - 获取客户数据统计失败: current transaction is aborted, commands ignored until end of transaction block
```

### 错误影响

- 数据同步过程中断
- 统计功能无法正常工作
- 后续数据库操作失败

## 解决方案

### 方案一：使用自动修复脚本（推荐）

1. **数据库配置说明**
   
   脚本已默认配置为使用106服务器，如需自定义可设置环境变量：
   ```bash
   export DB_HOST=***********  # 默认已设置为106服务器IP
   export DB_PORT=5432
   export DB_NAME=product      # 默认数据库名
   export DB_USER=username     # 默认用户名
   export DB_PASSWORD=password # 默认密码
   ```

2. **运行修复脚本**
   ```bash
   # 方法1：直接运行修复脚本（使用106服务器）
   python scripts/fix_database_schema.py
   
   # 方法2：使用运行器
   python scripts/run_database_fix.py
   ```

3. **验证修复结果**
   脚本会自动检查并报告修复状态

### 方案二：手动SQL修复

如果自动脚本无法使用，可以手动执行以下SQL：

#### 1. 创建缺失的表

```sql
-- 创建 orders 表
CREATE TABLE IF NOT EXISTS orders (
    id SERIAL PRIMARY KEY,
    order_number VARCHAR(100) UNIQUE NOT NULL,
    customer_id INTEGER,
    customer_name VARCHAR(255),
    total_amount DECIMAL(12,2) DEFAULT 0,
    order_status VARCHAR(20) DEFAULT 'pending',
    order_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    payment_status VARCHAR(20) DEFAULT 'unpaid',
    shipping_address TEXT,
    notes TEXT,
    create_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(100),
    update_by VARCHAR(100)
);

-- 创建 customers 表（如果需要）
CREATE TABLE IF NOT EXISTS customers (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(20),
    address TEXT,
    status VARCHAR(20) DEFAULT 'active',
    create_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    create_by VARCHAR(100),
    update_by VARCHAR(100)
);
```

#### 2. 添加缺失的字段

```sql
-- 为 products 表添加 created_at 字段
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'products' AND column_name = 'created_at'
    ) THEN
        ALTER TABLE products ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP;
        UPDATE products SET created_at = create_time WHERE create_time IS NOT NULL;
    END IF;
END$$;

-- 为 categories 表添加 created_at 字段
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'categories' AND column_name = 'created_at'
    ) THEN
        ALTER TABLE categories ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP;
        UPDATE categories SET created_at = create_time WHERE create_time IS NOT NULL;
    END IF;
END$$;

-- 重复以上步骤为以下表添加字段：
-- brands, labels, cases, programmes, information, distribution_orders, sync_status, attachments
```

#### 3. 创建索引

```sql
-- 为 orders 表创建索引
CREATE INDEX IF NOT EXISTS idx_orders_customer_id ON orders(customer_id);
CREATE INDEX IF NOT EXISTS idx_orders_order_status ON orders(order_status);
CREATE INDEX IF NOT EXISTS idx_orders_order_date ON orders(order_date);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);

-- 为其他表创建 created_at 索引
CREATE INDEX IF NOT EXISTS idx_products_created_at ON products(created_at);
CREATE INDEX IF NOT EXISTS idx_categories_created_at ON categories(created_at);
-- 等等...
```

## 修复脚本功能

### 自动检测

脚本会自动检测以下问题：
- 缺失的表
- 缺失的 `created_at` 字段
- 缺失的 `updated_at` 字段

### 自动修复

- 创建缺失的表（`orders`, `customers`）
- 添加缺失的时间字段
- 从现有的 `create_time` 复制数据到 `created_at`
- 创建必要的索引

### 安全特性

- 使用 `IF NOT EXISTS` 避免重复创建
- 事务安全操作
- 详细的日志记录
- 修复前后状态检查

## 验证修复

修复完成后，可以运行以下查询验证：

```sql
-- 检查表是否存在
SELECT table_name FROM information_schema.tables 
WHERE table_name IN ('orders', 'customers', 'products', 'categories');

-- 检查字段是否存在
SELECT table_name, column_name FROM information_schema.columns 
WHERE column_name = 'created_at' 
AND table_name IN ('products', 'categories', 'orders');

-- 测试查询（应该不再报错）
SELECT COUNT(*) FROM orders;
SELECT created_at FROM categories LIMIT 1;
```

## 预防措施

### 1. 数据库迁移规范

- 使用标准的迁移工具
- 每次架构变更都要创建迁移脚本
- 在开发环境先测试迁移

### 2. 监控数据库架构

```python
# 添加架构检查到应用启动流程
def check_database_schema():
    """检查数据库架构完整性"""
    required_tables = ['products', 'categories', 'orders']
    required_columns = {'products': ['created_at'], 'categories': ['created_at']}
    
    # 检查逻辑...
```

### 3. 错误处理改进

```python
# 改进的数据库查询错误处理
def safe_database_query(query, params=None):
    try:
        return execute_query(query, params)
    except psycopg2.errors.UndefinedColumn as e:
        logger.error(f"数据库字段不存在: {e}")
        # 触发架构修复或返回默认值
    except psycopg2.errors.UndefinedTable as e:
        logger.error(f"数据库表不存在: {e}")
        # 触发表创建或返回空结果
```

## 故障排除

### 常见问题

1. **权限不足**
   ```
   ERROR: permission denied for table xxx
   ```
   解决：确保数据库用户有 CREATE TABLE 和 ALTER TABLE 权限

2. **连接失败**
   ```
   FATAL: password authentication failed
   ```
   解决：检查数据库连接配置和凭据

3. **表锁定**
   ```
   ERROR: could not obtain lock on relation
   ```
   解决：停止应用，在维护窗口执行修复

### 回滚计划

如果修复出现问题，可以使用以下方法回滚：

```sql
-- 删除新增的字段（如果需要）
ALTER TABLE products DROP COLUMN IF EXISTS created_at;

-- 删除新创建的表（如果需要）
DROP TABLE IF EXISTS orders;
DROP TABLE IF EXISTS customers;
```

## 总结

通过执行数据库架构修复，您可以解决当前的同步错误。建议：

1. 优先使用自动修复脚本
2. 在生产环境执行前先在测试环境验证
3. 做好数据备份
4. 建立长期的数据库架构管理流程 