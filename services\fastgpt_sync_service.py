"""
FastGPT知识库同步服务

此模块负责将云商系统中的产品、方案、案例数据同步到FastGPT知识库。
严格遵循开发规范，使用真实API调用，无模拟数据。

主要功能：
1. FastGPT API客户端
2. 产品信息同步
3. 方案信息同步
4. 案例信息同步
5. 同步状态管理
6. 错误处理和重试机制
"""

import json
import logging
import time
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from config import get_config
from utils.database import get_db_connection, return_db_connection

logger = logging.getLogger(__name__)
config = get_config()


class FastGPTClient:
    """FastGPT API客户端"""

    def __init__(self, api_base: str = None, api_token: str = None):
        """
        初始化FastGPT客户端

        Args:
            api_base: FastGPT API基础URL
            api_token: FastGPT API认证令牌
        """
        fastgpt_config = config.get_fastgpt_config()
        self.api_base = api_base or fastgpt_config.get("api_base")
        self.api_token = api_token or fastgpt_config.get("api_key")

        if not self.api_token:
            raise ValueError("FastGPT API Token is required")

        # 创建会话并配置重试策略
        self.session = requests.Session()
        retry_strategy = Retry(
            total=3,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "OPTIONS", "POST", "PUT", "DELETE"],
            backoff_factor=1,
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

        # 设置默认请求头
        self.session.headers.update(
            {
                "Authorization": f"Bearer {self.api_token}",
                "Content-Type": "application/json",
            }
        )

        logger.info(f"FastGPT客户端初始化成功，API地址: {self.api_base}")

    def get_datasets(self) -> List[Dict]:
        """
        获取知识库列表

        Returns:
            知识库列表
        """
        try:
            response = self.session.get(f"{self.api_base}/api/core/dataset/list")
            response.raise_for_status()

            result = response.json()
            if result.get("code") == 200:
                datasets = result.get("data", [])
                logger.info(f"获取到 {len(datasets)} 个知识库")
                return datasets
            else:
                logger.error(f"获取知识库列表失败: {result}")
                return []

        except Exception as e:
            logger.error(f"获取知识库列表异常: {e}")
            return []

    def get_collections(
        self, dataset_id: str, page_num: int = 1, page_size: int = 50
    ) -> Dict:
        """
        获取集合列表

        Args:
            dataset_id: 知识库ID
            page_num: 页码
            page_size: 每页大小

        Returns:
            集合列表和分页信息
        """
        try:
            payload = {
                "datasetId": dataset_id,
                "pageNum": page_num,
                "pageSize": page_size,
                "searchText": "",
            }

            response = self.session.post(
                f"{self.api_base}/api/core/dataset/collection/list", json=payload
            )
            response.raise_for_status()

            result = response.json()
            if result.get("code") == 200:
                data = result.get("data", {})
                collections = data.get("data", [])
                total = data.get("total", 0)
                logger.info(
                    f"知识库 {dataset_id} 中获取到 {len(collections)} 个集合，总计 {total} 个"
                )
                return data
            else:
                logger.error(f"获取集合列表失败: {result}")
                return {"data": [], "total": 0}

        except Exception as e:
            logger.error(f"获取集合列表异常: {e}")
            return {"data": [], "total": 0}

    def push_data(self, collection_id: str, data: List[Dict]) -> Dict:
        """
        批量添加数据到集合

        Args:
            collection_id: 集合ID
            data: 数据列表，每个元素包含q、a、indexes字段

        Returns:
            添加结果
        """
        try:
            payload = {"collectionId": collection_id, "data": data}

            response = self.session.post(
                f"{self.api_base}/api/core/dataset/data/pushData", json=payload
            )
            response.raise_for_status()

            result = response.json()
            if result.get("code") == 200:
                insert_len = result.get("data", {}).get("insertLen", 0)
                logger.info(f"成功向集合 {collection_id} 添加 {insert_len} 条数据")
                return result.get("data", {})
            else:
                logger.error(f"添加数据失败: {result}")
                return {"insertLen": 0, "error": result}

        except Exception as e:
            logger.error(f"添加数据异常: {e}")
            return {"insertLen": 0, "error": str(e)}

    def get_data(
        self,
        collection_id: str,
        page_num: int = 1,
        page_size: int = 50,
        search_text: str = "",
    ) -> Dict:
        """
        获取集合中的数据

        Args:
            collection_id: 集合ID
            page_num: 页码
            page_size: 每页大小
            search_text: 搜索文本

        Returns:
            数据列表和分页信息
        """
        try:
            payload = {
                "collectionId": collection_id,
                "pageNum": page_num,
                "pageSize": page_size,
                "searchText": search_text,
            }

            response = self.session.post(
                f"{self.api_base}/api/core/dataset/data/list", json=payload
            )
            response.raise_for_status()

            result = response.json()
            if result.get("code") == 200:
                data = result.get("data", {})
                items = data.get("data", [])
                total = data.get("total", 0)
                logger.info(
                    f"集合 {collection_id} 中获取到 {len(items)} 条数据，总计 {total} 条"
                )
                return data
            else:
                logger.error(f"获取数据失败: {result}")
                return {"data": [], "total": 0}

        except Exception as e:
            logger.error(f"获取数据异常: {e}")
            return {"data": [], "total": 0}

    def update_data(
        self,
        data_id: str,
        q: Optional[str] = None,
        a: Optional[str] = None,
        indexes: Optional[List[Dict]] = None,
    ) -> bool:
        """
        更新单条数据

        Args:
            data_id: 数据ID
            q: 问题
            a: 答案
            indexes: 索引列表

        Returns:
            是否更新成功
        """
        try:
            payload = {"dataId": data_id}

            if q is not None:
                payload["q"] = q
            if a is not None:
                payload["a"] = a
            if indexes is not None:
                payload["indexes"] = indexes

            response = self.session.put(
                f"{self.api_base}/api/core/dataset/data/update", json=payload
            )
            response.raise_for_status()

            result = response.json()
            if result.get("code") == 200:
                logger.info(f"成功更新数据 {data_id}")
                return True
            else:
                logger.error(f"更新数据失败: {result}")
                return False

        except Exception as e:
            logger.error(f"更新数据异常: {e}")
            return False

    def delete_data(self, data_id: str) -> bool:
        """
        删除单条数据

        Args:
            data_id: 数据ID

        Returns:
            是否删除成功
        """
        try:
            response = self.session.delete(
                f"{self.api_base}/api/core/dataset/data/delete", params={"id": data_id}
            )
            response.raise_for_status()

            result = response.json()
            if result.get("code") == 200:
                logger.info(f"成功删除数据 {data_id}")
                return True
            else:
                logger.error(f"删除数据失败: {result}")
                return False

        except Exception as e:
            logger.error(f"删除数据异常: {e}")
            return False

    def search_test(
        self, dataset_id: str, text: str, limit: int = 5000, similarity: float = 0.0
    ) -> List[Dict]:
        """
        搜索测试

        Args:
            dataset_id: 知识库ID
            text: 搜索文本
            limit: 最大tokens数量
            similarity: 最低相关度

        Returns:
            搜索结果列表
        """
        try:
            payload = {
                "datasetId": dataset_id,
                "text": text,
                "limit": limit,
                "similarity": similarity,
                "searchMode": "embedding",
                "usingReRank": False,
                "datasetSearchUsingExtensionQuery": True,
                "datasetSearchExtensionModel": "gpt-4o-mini",
                "datasetSearchExtensionBg": "",
            }

            response = self.session.post(
                f"{self.api_base}/api/core/dataset/searchTest", json=payload
            )
            response.raise_for_status()

            result = response.json()
            if result.get("code") == 200:
                results = result.get("data", [])
                logger.info(f"搜索到 {len(results)} 条相关数据")
                return results
            else:
                logger.error(f"搜索测试失败: {result}")
                return []

        except Exception as e:
            logger.error(f"搜索测试异常: {e}")
            return []


class FastGPTSyncService:
    """FastGPT同步服务"""

    def __init__(self):
        """初始化同步服务"""
        self.client = FastGPTClient()
        # 移除实例级连接，改为使用连接池模式
        # self.conn = get_db_connection()

        # 默认集合ID配置（从环境变量获取，如果没有则使用默认值）
        import os

        self.product_collection_id = os.getenv("FASTGPT_PRODUCT_COLLECTION_ID", "")
        self.solution_collection_id = os.getenv("FASTGPT_SOLUTION_COLLECTION_ID", "")
        self.case_collection_id = os.getenv("FASTGPT_CASE_COLLECTION_ID", "")

        logger.info("FastGPT同步服务初始化成功")

    def _execute_with_connection(self, operation_func, *args, **kwargs):
        """
        使用连接池执行数据库操作

        Args:
            operation_func: 需要执行的操作函数
            *args: 位置参数
            **kwargs: 关键字参数

        Returns:
            操作结果
        """
        conn = None
        try:
            conn = get_db_connection()
            result = operation_func(conn, *args, **kwargs)
            conn.commit()
            return result
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"数据库操作失败: {e}")
            raise
        finally:
            if conn:
                return_db_connection(conn)

    def _log_sync_operation(
        self,
        sync_type: str,
        item_id: str,
        operation: str,
        status: str,
        message: str = "",
        fastgpt_data_id: str = "",
    ) -> None:
        """
        记录同步操作日志

        Args:
            sync_type: 同步类型 (product/solution/case)
            item_id: 项目ID
            operation: 操作类型 (create/update/delete)
            status: 状态 (success/failed)
            message: 消息
            fastgpt_data_id: FastGPT数据ID
        """

        def _log_operation(conn):
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    INSERT INTO fastgpt_sync_logs 
                    (sync_type, item_id, operation, status, message, fastgpt_data_id, synced_at)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """,
                    (
                        sync_type,
                        item_id,
                        operation,
                        status,
                        message,
                        fastgpt_data_id,
                        datetime.now(),
                    ),
                )

        try:
            self._execute_with_connection(_log_operation)
        except Exception as e:
            logger.error(f"记录同步日志失败: {e}")

    def _format_product_for_fastgpt(self, product: Dict[str, Any]) -> Dict[str, Any]:
        """
        将产品信息格式化为FastGPT知识库格式（Markdown）
        一个产品型号对应一个集合，每个集合只有一个知识片段

        Args:
            product: 产品信息字典

        Returns:
            格式化后的产品数据
        """
        try:
            # 获取产品基础信息
            product_name = product.get("name", "未知产品")
            product_model = product.get("model", "")
            product_id = product.get("id", "")

            # 从paramInfoList中提取产品型号（如果存在）
            param_info = product.get("paramInfoList", [])
            if isinstance(param_info, str):
                try:
                    param_info = json.loads(param_info)
                except:
                    param_info = []

            # 提取型号信息
            model_from_params = ""
            for param in param_info:
                if param.get("paramName") in ["型号", "Model", "产品型号"]:
                    model_from_params = param.get("paramValue", "")
                    break

            # 使用参数中的型号或产品model字段
            final_model = model_from_params or product_model

            # 创建索引名称：型号 + 产品名称
            index_name = (
                f"{final_model}_{product_name}" if final_model else product_name
            )

            # 组织Markdown格式的产品信息
            markdown_content = self._build_product_markdown(product, final_model)

            # 返回FastGPT格式的数据
            return {
                "q": index_name,  # 作为FastGPT片段的索引
                "a": markdown_content,  # Markdown格式的产品详细信息
                "chunkIndex": 0,  # 每个集合只有一个片段
                "datasetId": "",  # 将在同步时指定
                "collectionId": "",  # 将在同步时指定
                "product_id": product_id,
                "product_model": final_model,
                "product_name": product_name,
            }

        except Exception as e:
            logger.error(f"产品信息格式化失败: {str(e)}")
            raise

    def _build_product_markdown(self, product: Dict[str, Any], model: str) -> str:
        """
        构建产品信息的Markdown内容

        Args:
            product: 产品信息
            model: 产品型号

        Returns:
            Markdown格式的产品信息
        """
        content_parts = []

        # 产品标题
        product_name = product.get("name", "未知产品")
        content_parts.append(f"# {product_name}")

        # 基础信息
        content_parts.append("## 基础信息")
        if model:
            content_parts.append(f"**产品型号**: {model}")

        content_parts.append(f"**产品名称**: {product_name}")

        if product.get("brandName"):
            content_parts.append(f"**品牌**: {product.get('brandName')}")

        if product.get("categoryName"):
            content_parts.append(f"**分类**: {product.get('categoryName')}")

        # 产品描述
        if product.get("description"):
            content_parts.append("## 产品描述")
            content_parts.append(product.get("description"))

        # 产品规格参数
        param_info = product.get("paramInfoList", [])
        if isinstance(param_info, str):
            try:
                param_info = json.loads(param_info)
            except:
                param_info = []

        if param_info:
            content_parts.append("## 技术规格")
            for param in param_info:
                param_name = param.get("paramName", "")
                param_value = param.get("paramValue", "")
                if param_name and param_value:
                    content_parts.append(f"**{param_name}**: {param_value}")

        # 价格信息
        if product.get("price"):
            content_parts.append("## 价格信息")
            content_parts.append(f"**价格**: ¥{product.get('price')}")

        # 状态信息
        content_parts.append("## 状态信息")
        status_map = {1: "正常", 0: "停用", 2: "缺货"}
        status = status_map.get(product.get("status", 1), "未知")
        content_parts.append(f"**状态**: {status}")

        if product.get("createTime"):
            content_parts.append(f"**创建时间**: {product.get('createTime')}")

        if product.get("updateTime"):
            content_parts.append(f"**更新时间**: {product.get('updateTime')}")

        # 其他信息
        if product.get("memo"):
            content_parts.append("## 备注信息")
            content_parts.append(product.get("memo"))

        return "\n\n".join(content_parts)

    def _format_solution_for_fastgpt(self, solution: Dict) -> Dict:
        """
        格式化方案数据用于FastGPT同步

        Args:
            solution: 方案数据

        Returns:
            格式化后的数据
        """
        solution_name = solution.get("name", "未知方案")
        solution_desc = solution.get("description", "")
        industry = solution.get("industry", "通用")

        question = f"{solution_name} {industry} 解决方案"

        answer_parts = [f"方案名称: {solution_name}", f"应用行业: {industry}"]

        if solution_desc:
            answer_parts.append(f"方案描述: {solution_desc}")

        answer = "\n".join(answer_parts)

        indexes = [
            {"type": "default", "text": solution_name},
            {"type": "custom", "text": f"解决方案: {solution_name}"},
            {"type": "custom", "text": f"应用行业: {industry}"},
        ]

        return {"q": question, "a": answer, "indexes": indexes}

    def _format_case_for_fastgpt(self, case: Dict) -> Dict:
        """
        格式化案例数据用于FastGPT同步

        Args:
            case: 案例数据

        Returns:
            格式化后的数据
        """
        case_name = case.get("name", "未知案例")
        case_desc = case.get("description", "")
        industry = case.get("industry", "通用")
        customer = case.get("customer", "")

        question = f"{case_name} {industry} 应用案例"

        answer_parts = [f"案例名称: {case_name}", f"应用行业: {industry}"]

        if customer:
            answer_parts.append(f"客户: {customer}")

        if case_desc:
            answer_parts.append(f"案例描述: {case_desc}")

        answer = "\n".join(answer_parts)

        indexes = [
            {"type": "default", "text": case_name},
            {"type": "custom", "text": f"应用案例: {case_name}"},
            {"type": "custom", "text": f"应用行业: {industry}"},
        ]

        if customer:
            indexes.append({"type": "custom", "text": f"客户: {customer}"})

        return {"q": question, "a": answer, "indexes": indexes}

    def sync_products_to_fastgpt(
        self,
        product_ids: Optional[List[int]] = None,
        batch_size: int = 10,
        dataset_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        同步产品信息到FastGPT知识库
        一个产品型号对应一个集合，每个集合只有一个知识片段

        Args:
            product_ids: 要同步的产品ID列表，None表示同步所有待同步产品
            batch_size: 批处理大小
            dataset_id: 目标知识库ID，如果不指定则从配置获取

        Returns:
            同步结果统计
        """
        logger.info(f"开始同步产品信息到FastGPT, 批次大小: {batch_size}")

        result = {
            "total": 0,
            "success": 0,
            "failed": 0,
            "errors": [],
            "created_collections": [],
            "failed_products": [],
        }

        try:
            # 获取目标知识库ID
            target_dataset_id = dataset_id or os.getenv(
                "FASTGPT_DEFAULT_DATASET_ID", ""
            )
            if not target_dataset_id:
                raise ValueError("未指定目标知识库ID")

            # 获取待同步的产品列表
            def _get_products_to_sync(conn):
                with conn.cursor() as cursor:
                    if product_ids:
                        # 同步指定的产品
                        placeholders = ",".join(["%s"] * len(product_ids))
                        query = f"""
                            SELECT DISTINCT sp.*, p.name, p.description, p.price, p.status,
                                   p.paramInfoList, p.createTime, p.updateTime, p.memo,
                                   c.name as categoryName, b.name as brandName
                            FROM sync_products_queue sp
                            LEFT JOIN products p ON sp.matched_product_id = p.id
                            LEFT JOIN categories c ON p.categoryId = c.id
                            LEFT JOIN brands b ON p.brandId = b.id
                            WHERE sp.matched_product_id IN ({placeholders})
                                AND sp.sync_status = 'pending'
                        """
                        cursor.execute(query, product_ids)
                    else:
                        # 同步所有待同步产品
                        query = """
                            SELECT DISTINCT sp.*, p.name, p.description, p.price, p.status,
                                   p.paramInfoList, p.createTime, p.updateTime, p.memo,
                                   c.name as categoryName, b.name as brandName
                            FROM sync_products_queue sp
                            LEFT JOIN products p ON sp.matched_product_id = p.id
                            LEFT JOIN categories c ON p.categoryId = c.id
                            LEFT JOIN brands b ON p.brandId = b.id
                            WHERE sp.sync_status = 'pending'
                                AND sp.matched_product_id IS NOT NULL
                        """
                        cursor.execute(query)

                    return cursor.fetchall()

            products = self._execute_with_connection(_get_products_to_sync)

            result["total"] = len(products)

            if not products:
                logger.info("没有待同步的产品")
                return result

            # 批量处理产品
            for i in range(0, len(products), batch_size):
                batch_products = products[i : i + batch_size]

                for product_data in batch_products:
                    try:
                        # 使用产品指定的知识库ID或默认知识库ID
                        sync_dataset_id = (
                            product_data.get("dataset_id") or target_dataset_id
                        )

                        # 为每个产品创建一个集合
                        success = self._sync_single_product_to_fastgpt(
                            product_data, sync_dataset_id
                        )

                        if success:
                            result["success"] += 1
                            # 更新同步状态
                            self._update_product_sync_status(
                                product_data["id"],
                                "completed",
                                f"成功同步到知识库 {sync_dataset_id}",
                            )
                        else:
                            result["failed"] += 1
                            result["failed_products"].append(
                                {
                                    "id": product_data["id"],
                                    "model": product_data.get("original_model", ""),
                                    "error": "同步失败",
                                }
                            )

                    except Exception as e:
                        error_msg = f"产品 {product_data.get('id', 'unknown')} 同步失败: {str(e)}"
                        logger.error(error_msg)
                        result["failed"] += 1
                        result["errors"].append(error_msg)
                        result["failed_products"].append(
                            {
                                "id": product_data.get("id", "unknown"),
                                "model": product_data.get("original_model", ""),
                                "error": str(e),
                            }
                        )

                        # 更新同步状态为失败
                        try:
                            self._update_product_sync_status(
                                product_data["id"], "failed", str(e)
                            )
                        except Exception as update_error:
                            logger.error(f"更新同步状态失败: {update_error}")

                # 批次间隔
                time.sleep(0.1)

            logger.info(
                f"产品同步完成: 总计 {result['total']}, 成功 {result['success']}, 失败 {result['failed']}"
            )
            return result

        except Exception as e:
            error_msg = f"产品同步过程失败: {str(e)}"
            logger.error(error_msg)
            result["errors"].append(error_msg)
            return result

    def _sync_single_product_to_fastgpt(
        self, product_data: Dict[str, Any], dataset_id: str
    ) -> bool:
        """
        同步单个产品到FastGPT
        为产品创建一个专用集合，并添加一个知识片段

        Args:
            product_data: 产品数据
            dataset_id: 目标知识库ID

        Returns:
            是否同步成功
        """
        try:
            # 格式化产品数据
            formatted_data = self._format_product_for_fastgpt(product_data)

            # 创建集合名称（使用产品型号）
            product_model = formatted_data.get("product_model", "")
            product_name = formatted_data.get("product_name", "")
            collection_name = (
                f"{product_model}_{product_name}" if product_model else product_name
            )

            # 创建集合
            collection_result = self.client.create_collection(
                dataset_id=dataset_id,
                name=collection_name,
                description=f"产品型号 {product_model} 的详细信息",
            )

            if not collection_result.get("success"):
                logger.error(
                    f"创建集合失败: {collection_result.get('message', 'Unknown error')}"
                )
                return False

            collection_id = collection_result["data"].get("id")
            if not collection_id:
                logger.error("创建集合成功但未返回集合ID")
                return False

            # 添加知识片段到集合
            formatted_data["datasetId"] = dataset_id
            formatted_data["collectionId"] = collection_id

            sync_result = self.client.push_data_to_dataset(
                dataset_id=dataset_id, data=[formatted_data]  # 只有一个片段
            )

            if sync_result.get("success"):
                logger.info(f"产品 {product_model} 同步成功到集合 {collection_id}")
                return True
            else:
                error_msg = sync_result.get("message", "同步失败")
                logger.error(f"产品 {product_model} 同步失败: {error_msg}")
                return False

        except Exception as e:
            logger.error(f"单个产品同步失败: {str(e)}")
            return False

    def _update_product_sync_status(
        self, queue_id: int, status: str, message: str = ""
    ) -> None:
        """
        更新产品同步状态

        Args:
            queue_id: 队列记录ID
            status: 同步状态
            message: 状态消息
        """

        def _update_status(conn):
            with conn.cursor() as cursor:
                update_query = """
                    UPDATE sync_products_queue 
                    SET sync_status = %s, 
                        sync_message = %s,
                        sync_time = %s,
                        update_time = %s
                    WHERE id = %s
                """
                cursor.execute(
                    update_query,
                    (
                        status,
                        message,
                        datetime.now() if status == "completed" else None,
                        datetime.now(),
                        queue_id,
                    ),
                )

        try:
            self._execute_with_connection(_update_status)
        except Exception as e:
            logger.error(f"更新产品同步状态失败: {e}")

    def sync_solutions_to_fastgpt(self, limit: int = 100) -> Dict:
        """
        同步方案信息到FastGPT

        Args:
            limit: 每次同步的数量限制

        Returns:
            同步结果统计
        """
        if not self.solution_collection_id:
            logger.error("方案集合ID未配置，无法进行同步")
            return {"success": 0, "failed": 0, "error": "方案集合ID未配置"}

        result = {"success": 0, "failed": 0, "errors": []}

        def _sync_solutions_operation(conn):
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT * FROM sync_solutions_queue
                    WHERE sync_status = 'pending'
                    ORDER BY created_at
                    LIMIT %s
                """,
                    (limit,),
                )

                solutions = cursor.fetchall()

                if not solutions:
                    logger.info("没有待同步的方案数据")
                    return result

                logger.info(f"开始同步 {len(solutions)} 个方案到FastGPT")

                # 批量处理方案数据
                batch_data = []
                solution_ids = []

                for solution in solutions:
                    try:
                        # 格式化方案数据
                        formatted_data = self._format_solution_for_fastgpt(
                            dict(solution)
                        )
                        batch_data.append(formatted_data)
                        solution_ids.append(solution["id"])

                    except Exception as e:
                        logger.error(f"格式化方案数据失败 (ID: {solution['id']}): {e}")
                        result["failed"] += 1
                        result["errors"].append(f"方案 {solution['id']}: {str(e)}")

                        # 更新同步状态为失败
                        cursor.execute(
                            """
                            UPDATE sync_solutions_queue 
                            SET sync_status = 'failed', error_message = %s, synced_at = %s
                            WHERE id = %s
                        """,
                            (str(e), datetime.now(), solution["id"]),
                        )

                # 批量推送到FastGPT
                if batch_data:
                    push_result = self.client.push_data(
                        self.solution_collection_id, batch_data
                    )

                    if push_result.get("insertLen", 0) > 0:
                        success_count = push_result["insertLen"]
                        result["success"] = success_count

                        # 更新成功的记录
                        for i, solution_id in enumerate(solution_ids[:success_count]):
                            cursor.execute(
                                """
                                UPDATE sync_solutions_queue 
                                SET sync_status = 'completed', synced_at = %s
                                WHERE id = %s
                            """,
                                (datetime.now(), solution_id),
                            )

                            self._log_sync_operation(
                                "solution",
                                str(solution_id),
                                "create",
                                "success",
                                f"成功同步方案到FastGPT集合 {self.solution_collection_id}",
                            )

                        # 处理失败的记录
                        for solution_id in solution_ids[success_count:]:
                            cursor.execute(
                                """
                                UPDATE sync_solutions_queue 
                                SET sync_status = 'failed', 
                                    error_message = 'FastGPT推送部分失败', 
                                    synced_at = %s
                                WHERE id = %s
                            """,
                                (datetime.now(), solution_id),
                            )
                            result["failed"] += 1

                    else:
                        # 全部失败
                        error_msg = push_result.get("error", "Unknown error")
                        result["failed"] = len(solution_ids)
                        result["errors"].append(f"FastGPT推送失败: {error_msg}")

                        for solution_id in solution_ids:
                            cursor.execute(
                                """
                                UPDATE sync_solutions_queue 
                                SET sync_status = 'failed', 
                                    error_message = %s, 
                                    synced_at = %s
                                WHERE id = %s
                            """,
                                (str(error_msg), datetime.now(), solution_id),
                            )

                logger.info(
                    f"方案同步完成: 成功 {result['success']}, 失败 {result['failed']}"
                )
                return result

        try:
            result = self._execute_with_connection(_sync_solutions_operation)

        except Exception as e:
            logger.error(f"方案同步异常: {e}")
            result["errors"].append(f"同步异常: {str(e)}")

        return result

    def sync_cases_to_fastgpt(self, limit: int = 100) -> Dict:
        """
        同步案例信息到FastGPT

        Args:
            limit: 每次同步的数量限制

        Returns:
            同步结果统计
        """
        if not self.case_collection_id:
            logger.error("案例集合ID未配置，无法进行同步")
            return {"success": 0, "failed": 0, "error": "案例集合ID未配置"}

        result = {"success": 0, "failed": 0, "errors": []}

        def _sync_cases_operation(conn):
            with conn.cursor() as cursor:
                cursor.execute(
                    """
                    SELECT * FROM sync_cases_queue
                    WHERE sync_status = 'pending'
                    ORDER BY created_at
                    LIMIT %s
                """,
                    (limit,),
                )

                cases = cursor.fetchall()

                if not cases:
                    logger.info("没有待同步的案例数据")
                    return result

                logger.info(f"开始同步 {len(cases)} 个案例到FastGPT")

                # 批量处理案例数据
                batch_data = []
                case_ids = []

                for case in cases:
                    try:
                        # 格式化案例数据
                        formatted_data = self._format_case_for_fastgpt(dict(case))
                        batch_data.append(formatted_data)
                        case_ids.append(case["id"])

                    except Exception as e:
                        logger.error(f"格式化案例数据失败 (ID: {case['id']}): {e}")
                        result["failed"] += 1
                        result["errors"].append(f"案例 {case['id']}: {str(e)}")

                        # 更新同步状态为失败
                        cursor.execute(
                            """
                            UPDATE sync_cases_queue 
                            SET sync_status = 'failed', error_message = %s, synced_at = %s
                            WHERE id = %s
                        """,
                            (str(e), datetime.now(), case["id"]),
                        )

                # 批量推送到FastGPT
                if batch_data:
                    push_result = self.client.push_data(
                        self.case_collection_id, batch_data
                    )

                    if push_result.get("insertLen", 0) > 0:
                        success_count = push_result["insertLen"]
                        result["success"] = success_count

                        # 更新成功的记录
                        for i, case_id in enumerate(case_ids[:success_count]):
                            cursor.execute(
                                """
                                UPDATE sync_cases_queue 
                                SET sync_status = 'completed', synced_at = %s
                                WHERE id = %s
                            """,
                                (datetime.now(), case_id),
                            )

                            self._log_sync_operation(
                                "case",
                                str(case_id),
                                "create",
                                "success",
                                f"成功同步案例到FastGPT集合 {self.case_collection_id}",
                            )

                        # 处理失败的记录
                        for case_id in case_ids[success_count:]:
                            cursor.execute(
                                """
                                UPDATE sync_cases_queue 
                                SET sync_status = 'failed', 
                                    error_message = 'FastGPT推送部分失败', 
                                    synced_at = %s
                                WHERE id = %s
                            """,
                                (datetime.now(), case_id),
                            )
                            result["failed"] += 1

                    else:
                        # 全部失败
                        error_msg = push_result.get("error", "Unknown error")
                        result["failed"] = len(case_ids)
                        result["errors"].append(f"FastGPT推送失败: {error_msg}")

                        for case_id in case_ids:
                            cursor.execute(
                                """
                                UPDATE sync_cases_queue 
                                SET sync_status = 'failed', 
                                    error_message = %s, 
                                    synced_at = %s
                                WHERE id = %s
                            """,
                                (str(error_msg), datetime.now(), case_id),
                            )

                logger.info(
                    f"案例同步完成: 成功 {result['success']}, 失败 {result['failed']}"
                )
                return result

        try:
            result = self._execute_with_connection(_sync_cases_operation)

        except Exception as e:
            logger.error(f"案例同步异常: {e}")
            result["errors"].append(f"同步异常: {str(e)}")

        return result

    def get_sync_status(self) -> Dict:
        """
        获取同步状态统计

        Returns:
            同步状态统计信息
        """

        def _get_sync_status_operation(conn):
            with conn.cursor() as cursor:
                # 统计各类型待同步数据
                stats = {
                    "products": {"pending": 0, "completed": 0, "failed": 0},
                    "solutions": {"pending": 0, "completed": 0, "failed": 0},
                    "cases": {"pending": 0, "completed": 0, "failed": 0},
                }

                # 产品统计
                cursor.execute(
                    """
                    SELECT sync_status, COUNT(*) as count
                    FROM sync_products_queue
                    GROUP BY sync_status
                """
                )
                for row in cursor.fetchall():
                    stats["products"][row["sync_status"]] = row["count"]

                # 方案统计
                cursor.execute(
                    """
                    SELECT sync_status, COUNT(*) as count
                    FROM sync_solutions_queue
                    GROUP BY sync_status
                """
                )
                for row in cursor.fetchall():
                    stats["solutions"][row["sync_status"]] = row["count"]

                # 案例统计
                cursor.execute(
                    """
                    SELECT sync_status, COUNT(*) as count
                    FROM sync_cases_queue
                    GROUP BY sync_status
                """
                )
                for row in cursor.fetchall():
                    stats["cases"][row["sync_status"]] = row["count"]

                # 最近同步日志
                cursor.execute(
                    """
                    SELECT sync_type, operation, status, COUNT(*) as count,
                           MAX(synced_at) as last_sync
                    FROM fastgpt_sync_logs
                    WHERE synced_at >= NOW() - INTERVAL '24 hours'
                    GROUP BY sync_type, operation, status
                    ORDER BY last_sync DESC
                """
                )

                recent_logs = cursor.fetchall()

                return {
                    "queue_stats": stats,
                    "recent_logs": [dict(log) for log in recent_logs],
                }

        try:
            return self._execute_with_connection(_get_sync_status_operation)
        except Exception as e:
            logger.error(f"获取同步状态失败: {e}")
            return {"error": str(e)}

    def run_full_sync(self, batch_size: int = 50) -> Dict:
        """
        执行完整同步（产品、方案、案例）

        Args:
            batch_size: 每批次处理数量

        Returns:
            完整同步结果
        """
        logger.info("开始执行完整FastGPT同步")

        results = {
            "products": {"success": 0, "failed": 0, "errors": []},
            "solutions": {"success": 0, "failed": 0, "errors": []},
            "cases": {"success": 0, "failed": 0, "errors": []},
            "total_time": 0,
        }

        start_time = time.time()

        try:
            # 同步产品
            logger.info("=== 开始同步产品 ===")
            results["products"] = self.sync_products_to_fastgpt(batch_size=batch_size)

            # 同步方案
            logger.info("=== 开始同步方案 ===")
            results["solutions"] = self.sync_solutions_to_fastgpt(batch_size)

            # 同步案例
            logger.info("=== 开始同步案例 ===")
            results["cases"] = self.sync_cases_to_fastgpt(batch_size)

            # 计算总耗时
            results["total_time"] = round(time.time() - start_time, 2)

            # 汇总统计
            total_success = (
                results["products"]["success"]
                + results["solutions"]["success"]
                + results["cases"]["success"]
            )
            total_failed = (
                results["products"]["failed"]
                + results["solutions"]["failed"]
                + results["cases"]["failed"]
            )

            logger.info(f"=== 完整同步完成 ===")
            logger.info(
                f"总成功: {total_success}, 总失败: {total_failed}, 耗时: {results['total_time']}秒"
            )

        except Exception as e:
            logger.error(f"完整同步异常: {e}")
            results["error"] = str(e)

        return results


# 便捷函数
def get_fastgpt_sync_service() -> FastGPTSyncService:
    """获取FastGPT同步服务实例"""
    return FastGPTSyncService()


def test_fastgpt_connection() -> bool:
    """测试FastGPT连接"""
    try:
        client = FastGPTClient()
        datasets = client.get_datasets()
        logger.info(f"FastGPT连接测试成功，获取到 {len(datasets)} 个知识库")
        return True
    except Exception as e:
        logger.error(f"FastGPT连接测试失败: {e}")
        return False


if __name__ == "__main__":
    # 测试代码
    print("测试FastGPT连接...")
    if test_fastgpt_connection():
        print("连接成功！")

        # 测试同步服务
        sync_service = get_fastgpt_sync_service()
        status = sync_service.get_sync_status()
        print(f"同步状态: {status}")
    else:
        print("连接失败，请检查配置！")
