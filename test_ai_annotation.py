#!/usr/bin/env python3
"""
AI图片标注服务测试脚本

测试AI多模态图片标注功能，包括：
1. 单张图片标注
2. 批量图片标注
3. 缓存功能测试
4. 不同标注类型测试

严格遵循开发规范，使用真实API接口，禁止模拟数据。
"""

import logging
import os
import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.ai_annotation_service import AIAnnotationService

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def test_ai_annotation_service():
    """测试AI图片标注服务"""
    try:
        logger.info("开始测试AI图片标注服务")

        # 初始化服务
        ai_service = AIAnnotationService()

        # 测试图片路径（需要实际存在的图片）
        test_images = [
            "test_images/product_diagram.jpg",
            "test_images/installation_guide.png",
            "test_images/technical_spec.jpg",
        ]

        # 创建测试图片目录（如果不存在）
        test_dir = Path("test_images")
        test_dir.mkdir(exist_ok=True)

        # 如果没有测试图片，创建一些示例
        if not any(Path(img).exists() for img in test_images):
            logger.warning("未找到测试图片，请在 test_images/ 目录下放置一些图片文件")
            logger.info("支持的格式: .jpg, .jpeg, .png, .gif, .webp")
            return False

        # 测试单张图片标注
        logger.info("测试单张图片标注")
        for image_path in test_images:
            if Path(image_path).exists():
                success, annotation, details = ai_service.annotate_image(
                    image_path=image_path,
                    product_model="ZK3969",
                    context="这是产品技术文档中的图片",
                    annotation_type="technical",
                )

                if success:
                    logger.info(f"✅ 图片 '{image_path}' 标注成功")
                    logger.info(f"标注结果: {annotation[:100]}...")
                    logger.info(f"模型: {details.get('model', 'N/A')}")
                else:
                    logger.error(
                        f"❌ 图片 '{image_path}' 标注失败: {details.get('error', '未知错误')}"
                    )

                break  # 只测试第一张找到的图片

        # 测试批量标注
        logger.info("测试批量图片标注")
        existing_images = [img for img in test_images if Path(img).exists()]

        if existing_images:
            results = ai_service.batch_annotate_images(
                image_paths=existing_images,
                product_model="ZK3969",
                context="产品文档图片批量处理",
                annotation_type="general",
            )

            logger.info(
                f"批量标注结果: 总数 {results['total']}, 成功 {results['success']}, 失败 {results['failed']}"
            )

            for detail in results.get("details", []):
                logger.info(
                    f"- {detail['image_path']}: {detail['status']} (长度: {detail['length']})"
                )

            for error in results.get("errors", []):
                logger.error(f"- {error['image_path']}: {error['error']}")

        # 测试缓存功能
        logger.info("测试缓存功能")
        cache_count = ai_service.clear_cache(days_old=0)  # 清理所有缓存
        logger.info(f"清理了 {cache_count} 个缓存文件")

        # 测试不同标注类型
        logger.info("测试不同标注类型")
        annotation_types = ["general", "technical", "installation", "operation"]

        for image_path in test_images:
            if Path(image_path).exists():
                for ann_type in annotation_types:
                    success, annotation, details = ai_service.annotate_image(
                        image_path=image_path,
                        product_model="TEST-001",
                        context=f"测试{ann_type}类型标注",
                        annotation_type=ann_type,
                    )

                    if success:
                        logger.info(
                            f"✅ {ann_type}类型标注成功: {len(annotation)} 字符"
                        )
                    else:
                        logger.error(
                            f"❌ {ann_type}类型标注失败: {details.get('error', '未知错误')}"
                        )

                break  # 只测试第一张图片

        logger.info("AI图片标注服务测试完成")
        return True

    except Exception as e:
        logger.error(f"AI图片标注服务测试失败: {e}")
        return False


def create_test_images():
    """创建测试图片（占位符）"""
    try:
        test_dir = Path("test_images")
        test_dir.mkdir(exist_ok=True)

        # 创建一些占位符文件说明
        readme_content = """# 测试图片目录

请在此目录下放置一些测试图片文件，用于测试AI图片标注功能。

支持的格式:
- .jpg / .jpeg
- .png
- .gif
- .webp

建议的测试图片类型:
1. product_diagram.jpg - 产品结构图
2. installation_guide.png - 安装指导图
3. technical_spec.jpg - 技术规格图
4. operation_interface.png - 操作界面图

注意: 请确保图片内容适合用于AI标注测试。
"""

        readme_file = test_dir / "README.md"
        with open(readme_file, "w", encoding="utf-8") as f:
            f.write(readme_content)

        logger.info(f"测试图片目录已创建: {test_dir}")
        logger.info("请在该目录下放置测试图片文件")

    except Exception as e:
        logger.error(f"创建测试图片目录失败: {e}")


if __name__ == "__main__":
    logger.info("=" * 60)
    logger.info("AI图片标注服务测试")
    logger.info("=" * 60)

    # 检查是否有测试图片
    test_images_exist = any(
        Path(f"test_images/{name}").exists()
        for name in [
            "product_diagram.jpg",
            "installation_guide.png",
            "technical_spec.jpg",
        ]
    )

    if not test_images_exist:
        logger.warning("未找到测试图片，创建测试目录...")
        create_test_images()
        logger.info("请在 test_images/ 目录下放置测试图片后重新运行")
        sys.exit(0)

    # 运行测试
    success = test_ai_annotation_service()

    if success:
        logger.info("✅ 所有测试通过")
    else:
        logger.error("❌ 部分测试失败")
        sys.exit(1)
