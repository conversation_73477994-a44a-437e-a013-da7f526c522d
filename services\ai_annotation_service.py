#!/usr/bin/env python3
"""
AI多模态图片标注服务

该服务负责：
1. 使用AI多模态大模型对图片进行自动标注
2. 支持多种AI服务提供商（OpenAI、Claude、本地模型等）
3. 针对产品文档图片进行专业化标注
4. 提供批量处理和缓存功能

严格遵循开发规范，使用真实API接口，禁止模拟数据。
"""

import base64
import hashlib
import json
import logging
import os
import requests
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any

from config import config

logger = logging.getLogger(__name__)


class AIAnnotationService:
    """AI多模态图片标注服务"""

    def __init__(self):
        """初始化服务"""
        self.ai_config = config.get_ai_config()
        self.api_base = self.ai_config.get("api_base", "")
        self.api_key = self.ai_config.get("api_key", "")
        self.model_name = self.ai_config.get("model", "gpt-4-vision-preview")
        self.timeout = self.ai_config.get("timeout", 60)

        # 缓存配置
        self.cache_dir = Path("cache/ai_annotations")
        self.cache_dir.mkdir(parents=True, exist_ok=True)

        # 请求头
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
        }

        if not self.api_key:
            logger.warning("AI API Key未配置，图片标注功能不可用")

        logger.info("AI多模态图片标注服务初始化成功")

    def annotate_image(
        self,
        image_path: str,
        product_model: str = "",
        context: str = "",
        annotation_type: str = "general",
    ) -> Tuple[bool, str, Dict[str, Any]]:
        """
        对图片进行AI标注

        Args:
            image_path: 图片文件路径
            product_model: 产品型号
            context: 上下文信息
            annotation_type: 标注类型 (general, technical, installation, operation)

        Returns:
            Tuple[是否成功, 标注结果, 详细信息]
        """
        try:
            if not self.api_key:
                return False, "", {"error": "AI API Key未配置"}

            # 检查图片文件
            if not os.path.exists(image_path):
                return False, "", {"error": f"图片文件不存在: {image_path}"}

            # 检查缓存
            cache_key = self._generate_cache_key(
                image_path, product_model, context, annotation_type
            )
            cached_result = self._get_cached_annotation(cache_key)
            if cached_result:
                logger.info(f"使用缓存的标注结果: {image_path}")
                return True, cached_result["annotation"], cached_result

            # 读取图片
            image_data = self._read_image_file(image_path)
            if not image_data:
                return False, "", {"error": "无法读取图片文件"}

            # 构建提示词
            prompt = self._build_annotation_prompt(
                product_model, context, annotation_type
            )

            # 调用AI服务
            success, annotation, response_data = self._call_ai_service(
                image_data, prompt
            )

            if success:
                # 缓存结果
                result_data = {
                    "annotation": annotation,
                    "image_path": image_path,
                    "product_model": product_model,
                    "annotation_type": annotation_type,
                    "timestamp": datetime.now().isoformat(),
                    "model": self.model_name,
                    **response_data,
                }
                self._cache_annotation(cache_key, result_data)

                logger.info(f"图片标注成功: {image_path}")
                return True, annotation, result_data
            else:
                logger.error(f"图片标注失败: {image_path}")
                return False, "", response_data

        except Exception as e:
            logger.error(f"图片标注异常: {e}")
            return False, "", {"error": str(e)}

    def batch_annotate_images(
        self,
        image_paths: List[str],
        product_model: str = "",
        context: str = "",
        annotation_type: str = "general",
    ) -> Dict[str, Any]:
        """
        批量标注图片

        Args:
            image_paths: 图片路径列表
            product_model: 产品型号
            context: 上下文信息
            annotation_type: 标注类型

        Returns:
            批量处理结果
        """
        try:
            logger.info(f"开始批量标注 {len(image_paths)} 张图片")

            results = {
                "total": len(image_paths),
                "success": 0,
                "failed": 0,
                "details": [],
                "errors": [],
            }

            for image_path in image_paths:
                try:
                    success, annotation, detail = self.annotate_image(
                        image_path, product_model, context, annotation_type
                    )

                    if success:
                        results["success"] += 1
                        results["details"].append(
                            {
                                "image_path": image_path,
                                "status": "success",
                                "annotation": annotation,
                                "length": len(annotation),
                            }
                        )
                    else:
                        results["failed"] += 1
                        results["errors"].append(
                            {
                                "image_path": image_path,
                                "error": detail.get("error", "未知错误"),
                            }
                        )

                except Exception as e:
                    results["failed"] += 1
                    results["errors"].append(
                        {"image_path": image_path, "error": str(e)}
                    )

            logger.info(
                f"批量标注完成: 成功 {results['success']}, 失败 {results['failed']}"
            )
            return results

        except Exception as e:
            logger.error(f"批量标注异常: {e}")
            return {"error": str(e)}

    def _read_image_file(self, image_path: str) -> Optional[str]:
        """读取图片文件并转换为base64"""
        try:
            with open(image_path, "rb") as f:
                image_bytes = f.read()

            # 转换为base64
            image_base64 = base64.b64encode(image_bytes).decode("utf-8")

            # 检测图片格式
            file_ext = Path(image_path).suffix.lower()
            if file_ext in [".jpg", ".jpeg"]:
                mime_type = "image/jpeg"
            elif file_ext == ".png":
                mime_type = "image/png"
            elif file_ext == ".gif":
                mime_type = "image/gif"
            elif file_ext == ".webp":
                mime_type = "image/webp"
            else:
                mime_type = "image/jpeg"  # 默认

            return f"data:{mime_type};base64,{image_base64}"

        except Exception as e:
            logger.error(f"读取图片文件失败: {e}")
            return None

    def _build_annotation_prompt(
        self, product_model: str, context: str, annotation_type: str
    ) -> str:
        """构建AI标注提示词"""
        try:
            base_prompt = """请分析这张图片，这是一张产品技术文档中的图片。"""

            if product_model:
                base_prompt += f"\n\n产品型号: {product_model}"

            if context:
                base_prompt += f"\n\n文档上下文:\n{context[:500]}..."

            # 根据标注类型定制提示词
            if annotation_type == "technical":
                specific_prompt = """
请提供详细的技术分析，包括：
1. 图片中显示的技术组件和结构
2. 技术参数和规格信息
3. 工作原理和技术特点
4. 与产品功能的关联性
"""
            elif annotation_type == "installation":
                specific_prompt = """
请提供安装相关的说明，包括：
1. 安装步骤和流程
2. 所需工具和材料
3. 注意事项和安全提醒
4. 安装位置和环境要求
"""
            elif annotation_type == "operation":
                specific_prompt = """
请提供操作相关的说明，包括：
1. 操作步骤和流程
2. 界面元素和功能说明
3. 操作注意事项
4. 常见问题和解决方法
"""
            else:  # general
                specific_prompt = """
请提供全面的图片说明，包括：
1. 图片内容的详细描述
2. 关键信息和要点
3. 与产品的关联性
4. 对用户的指导意义
"""

            full_prompt = (
                base_prompt
                + specific_prompt
                + """

请用中文回答，保持专业和准确。回答应该简洁明了，重点突出。
"""
            )

            return full_prompt

        except Exception as e:
            logger.error(f"构建提示词失败: {e}")
            return "请描述这张图片的内容。"

    def _call_ai_service(
        self, image_data: str, prompt: str
    ) -> Tuple[bool, str, Dict[str, Any]]:
        """调用AI服务进行图片标注"""
        try:
            if not self.api_base or not self.api_key:
                return False, "", {"error": "AI服务配置不完整"}

            # 构建请求数据
            payload = {
                "model": self.model_name,
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {"type": "image_url", "image_url": {"url": image_data}},
                        ],
                    }
                ],
                "max_tokens": 1000,
                "temperature": 0.1,
            }

            # 发送请求
            response = requests.post(
                f"{self.api_base}/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=self.timeout,
            )

            if response.status_code == 200:
                result = response.json()
                annotation = (
                    result.get("choices", [{}])[0].get("message", {}).get("content", "")
                )

                if annotation:
                    return (
                        True,
                        annotation,
                        {
                            "model": self.model_name,
                            "usage": result.get("usage", {}),
                            "response_time": response.elapsed.total_seconds(),
                        },
                    )
                else:
                    return False, "", {"error": "AI服务返回空结果"}
            else:
                error_msg = f"AI服务请求失败: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return False, "", {"error": error_msg}

        except Exception as e:
            logger.error(f"调用AI服务失败: {e}")
            return False, "", {"error": str(e)}

    def _generate_cache_key(
        self, image_path: str, product_model: str, context: str, annotation_type: str
    ) -> str:
        """生成缓存键"""
        try:
            # 获取图片文件的修改时间和大小
            stat = os.stat(image_path)
            file_info = f"{stat.st_mtime}_{stat.st_size}"

            # 组合所有参数
            key_data = f"{image_path}_{file_info}_{product_model}_{context[:100]}_{annotation_type}_{self.model_name}"

            # 生成MD5哈希
            return hashlib.md5(key_data.encode("utf-8")).hexdigest()

        except Exception as e:
            logger.error(f"生成缓存键失败: {e}")
            return hashlib.md5(image_path.encode("utf-8")).hexdigest()

    def _get_cached_annotation(self, cache_key: str) -> Optional[Dict[str, Any]]:
        """获取缓存的标注结果"""
        try:
            cache_file = self.cache_dir / f"{cache_key}.json"

            if cache_file.exists():
                with open(cache_file, "r", encoding="utf-8") as f:
                    cached_data = json.load(f)

                # 检查缓存是否过期（7天）
                cache_time = datetime.fromisoformat(cached_data.get("timestamp", ""))
                if (datetime.now() - cache_time).days < 7:
                    return cached_data
                else:
                    # 删除过期缓存
                    cache_file.unlink()

            return None

        except Exception as e:
            logger.error(f"获取缓存失败: {e}")
            return None

    def _cache_annotation(self, cache_key: str, result_data: Dict[str, Any]) -> None:
        """缓存标注结果"""
        try:
            cache_file = self.cache_dir / f"{cache_key}.json"

            with open(cache_file, "w", encoding="utf-8") as f:
                json.dump(result_data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.error(f"缓存标注结果失败: {e}")

    def clear_cache(self, days_old: int = 7) -> int:
        """清理过期缓存"""
        try:
            cleared_count = 0
            cutoff_time = datetime.now().timestamp() - (days_old * 24 * 3600)

            for cache_file in self.cache_dir.glob("*.json"):
                if cache_file.stat().st_mtime < cutoff_time:
                    cache_file.unlink()
                    cleared_count += 1

            logger.info(f"清理了 {cleared_count} 个过期缓存文件")
            return cleared_count

        except Exception as e:
            logger.error(f"清理缓存失败: {e}")
            return 0
