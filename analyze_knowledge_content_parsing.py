#!/usr/bin/env python3
"""
产品知识内容字段解析分析脚本

检查产品的introduction、details、paramInfo、useTo、qualifications、instructions、guide、commonProblem等知识内容字段的解析情况
"""

import sys
import os
import json
import logging
from typing import Dict, Any, List, Optional

# 添加项目路径
sys.path.append(".")

# 设置环境变量
os.environ["DATABASE_HOST"] = "***********"
os.environ["DATABASE_PORT"] = "5432"
os.environ["DATABASE_NAME"] = "product"
os.environ["DATABASE_USER"] = "username"
os.environ["DATABASE_PASSWORD"] = "123456"
os.environ["ZKMALL_API_BASE"] = "https://zkmall.zktecoiot.com"
os.environ["ZKMALL_USERNAME"] = "18929343717"
os.environ["ZKMALL_PASSWORD"] = "Zk@123456"

from utils.auth import AuthManager
from utils.api_client import ZKMallClient
from utils.api_response_normalizer import ApiResponseNormalizer

# 设置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class KnowledgeContentAnalyzer:
    """产品知识内容分析器"""

    def __init__(self):
        """初始化分析器"""
        self.client = None
        self.normalizer = ApiResponseNormalizer()
        self.analysis_results = {
            "api_field_mapping": {},
            "content_analysis": {},
            "parsing_issues": [],
            "recommendations": [],
        }

    def analyze_knowledge_content_parsing(self) -> Dict[str, Any]:
        """分析产品知识内容字段解析"""
        print("🔍 开始产品知识内容字段解析分析...")
        print("=" * 60)

        try:
            # 1. 确保API认证
            if not AuthManager.ensure_authenticated():
                print("❌ API认证失败")
                return self.analysis_results

            print("✅ API认证成功")
            self.client = ZKMallClient()

            # 2. 获取产品数据样本
            products = self.client.get_products(pageSize=5, current=1)
            if not products:
                print("❌ 未能获取产品数据")
                return self.analysis_results

            print(f"✅ 获取到 {len(products)} 个产品样本")

            # 3. 分析API字段映射
            self._analyze_api_field_mapping(products)

            # 4. 分析内容质量
            self._analyze_content_quality(products)

            # 5. 检查解析问题
            self._check_parsing_issues(products)

            # 6. 生成建议
            self._generate_recommendations()

            # 7. 输出分析报告
            self._print_analysis_report()

            return self.analysis_results

        except Exception as e:
            logger.error(f"分析过程出错: {e}")
            import traceback

            traceback.print_exc()
            return self.analysis_results

    def _analyze_api_field_mapping(self, products: List[Dict[str, Any]]):
        """分析API字段映射"""
        print("\n1. 📊 API字段映射分析")
        print("-" * 40)

        # 定义知识内容字段映射
        knowledge_fields = {
            # API字段名: (数据库字段名, 显示名称)
            "introduction": ("introduction", "产品介绍"),
            "details": ("details", "产品详情"),
            "parameterInfo": ("param_info", "参数信息(HTML)"),
            "newParam": ("new_param", "参数信息(JSON)"),
            "paramInfoList": ("param_info_list", "参数信息列表"),
            "usage": ("use_to", "适用场景"),
            "qualificationUrl": ("qualifications", "资质文件"),
            "instructionIds": ("instructions", "说明书ID"),
            "operationGuide": ("guide", "操作指南"),
            "commonIssues": ("common_problem", "常见问题ID"),
            "attachments": ("other", "附件地址"),
            "specification": ("spec", "产品规格"),
            "videoExplanation": ("video_explanation", "视频说明"),
            "videoInstallation": ("video_installation", "安装视频"),
            "videoTroubleshooting": ("video_troubleshooting", "故障排除视频"),
        }

        # 分析每个字段的存在情况
        field_stats = {}

        for api_field, (db_field, display_name) in knowledge_fields.items():
            field_info = {
                "api_field": api_field,
                "db_field": db_field,
                "display_name": display_name,
                "exists_count": 0,
                "has_content_count": 0,
                "samples": [],
            }

            for product in products:
                if api_field in product:
                    field_info["exists_count"] += 1
                    value = product[api_field]

                    if value and str(value).strip():
                        field_info["has_content_count"] += 1
                        # 保存样本数据（截取前100字符）
                        sample = (
                            str(value)[:100] + "..."
                            if len(str(value)) > 100
                            else str(value)
                        )
                        field_info["samples"].append(sample)

            field_stats[api_field] = field_info

            # 输出分析结果
            exists_rate = (field_info["exists_count"] / len(products)) * 100
            content_rate = (field_info["has_content_count"] / len(products)) * 100

            status = "✅" if content_rate > 70 else "⚠️" if content_rate > 30 else "❌"
            print(f"   {status} {display_name} ({api_field})")
            print(
                f"      存在率: {field_info['exists_count']}/{len(products)} ({exists_rate:.1f}%)"
            )
            print(
                f"      有内容: {field_info['has_content_count']}/{len(products)} ({content_rate:.1f}%)"
            )

            if field_info["samples"]:
                print(f"      样本: {field_info['samples'][0]}")

        self.analysis_results["api_field_mapping"] = field_stats

    def _analyze_content_quality(self, products: List[Dict[str, Any]]):
        """分析内容质量"""
        print("\n2. 📚 内容质量分析")
        print("-" * 40)

        content_analysis = {
            "html_content_fields": [],
            "json_content_fields": [],
            "url_content_fields": [],
            "id_reference_fields": [],
            "plain_text_fields": [],
        }

        for product in products[:1]:  # 分析第一个产品作为样本
            print(f"   分析产品: {product.get('name', '未知')}")

            # HTML内容字段
            html_fields = ["details", "parameterInfo", "operationGuide"]
            for field in html_fields:
                if field in product and product[field]:
                    content_analysis["html_content_fields"].append(
                        {
                            "field": field,
                            "content_length": len(str(product[field])),
                            "has_html_tags": "<" in str(product[field])
                            and ">" in str(product[field]),
                            "sample": (
                                str(product[field])[:200] + "..."
                                if len(str(product[field])) > 200
                                else str(product[field])
                            ),
                        }
                    )
                    print(f"     ✅ HTML字段 {field}: {len(str(product[field]))} 字符")

            # JSON内容字段
            json_fields = ["newParam", "paramInfoList"]
            for field in json_fields:
                if field in product and product[field]:
                    try:
                        if isinstance(product[field], str):
                            json_data = json.loads(product[field])
                        else:
                            json_data = product[field]

                        content_analysis["json_content_fields"].append(
                            {
                                "field": field,
                                "is_valid_json": True,
                                "item_count": (
                                    len(json_data)
                                    if isinstance(json_data, (list, dict))
                                    else 1
                                ),
                                "sample": (
                                    str(json_data)[:200] + "..."
                                    if len(str(json_data)) > 200
                                    else str(json_data)
                                ),
                            }
                        )
                        print(
                            f"     ✅ JSON字段 {field}: {len(json_data) if isinstance(json_data, (list, dict)) else 1} 项"
                        )
                    except:
                        content_analysis["json_content_fields"].append(
                            {
                                "field": field,
                                "is_valid_json": False,
                                "error": "JSON解析失败",
                                "sample": (
                                    str(product[field])[:200] + "..."
                                    if len(str(product[field])) > 200
                                    else str(product[field])
                                ),
                            }
                        )
                        print(f"     ❌ JSON字段 {field}: 解析失败")

            # URL内容字段
            url_fields = ["attachments", "qualificationUrl", "thumbnail", "bannerImage"]
            for field in url_fields:
                if field in product and product[field]:
                    urls = (
                        str(product[field]).split(",")
                        if "," in str(product[field])
                        else [str(product[field])]
                    )
                    content_analysis["url_content_fields"].append(
                        {
                            "field": field,
                            "url_count": len(urls),
                            "urls": urls[:3],  # 只保存前3个URL作为样本
                            "has_multiple_urls": len(urls) > 1,
                        }
                    )
                    print(f"     ✅ URL字段 {field}: {len(urls)} 个URL")

            # ID引用字段
            id_fields = ["instructionIds", "commonIssues"]
            for field in id_fields:
                if field in product and product[field]:
                    content_analysis["id_reference_fields"].append(
                        {
                            "field": field,
                            "value": product[field],
                            "is_numeric": str(product[field]).isdigit(),
                            "has_multiple_ids": "," in str(product[field]),
                        }
                    )
                    print(f"     ✅ ID字段 {field}: {product[field]}")

            # 纯文本字段
            text_fields = ["introduction", "usage", "specification"]
            for field in text_fields:
                if field in product and product[field]:
                    content_analysis["plain_text_fields"].append(
                        {
                            "field": field,
                            "content_length": len(str(product[field])),
                            "word_count": len(str(product[field]).split()),
                            "sample": (
                                str(product[field])[:100] + "..."
                                if len(str(product[field])) > 100
                                else str(product[field])
                            ),
                        }
                    )
                    print(f"     ✅ 文本字段 {field}: {len(str(product[field]))} 字符")

        self.analysis_results["content_analysis"] = content_analysis

    def _check_parsing_issues(self, products: List[Dict[str, Any]]):
        """检查解析问题"""
        print("\n3. 🔍 解析问题检查")
        print("-" * 40)

        issues = []

        # 检查数据标准化器的字段映射
        try:
            sample_product = products[0]
            normalized_data = self.normalizer.normalize([sample_product], "product")
            normalized_data = normalized_data[0] if normalized_data else {}

            print("   检查数据标准化器映射:")

            # 检查关键字段是否正确映射
            key_mappings = {
                "introduction": "introduction",
                "details": "details",
                "parameterInfo": "param_info",
                "usage": "use_to",
                "attachments": "other",
                "specification": "spec",
            }

            for api_field, db_field in key_mappings.items():
                api_value = sample_product.get(api_field)
                db_value = normalized_data.get(db_field)

                if api_value and not db_value:
                    issue = f"字段映射缺失: {api_field} -> {db_field}"
                    issues.append(issue)
                    print(f"     ❌ {issue}")
                elif api_value and db_value:
                    print(f"     ✅ {api_field} -> {db_field}: 映射正确")
                else:
                    print(f"     ⚠️ {api_field}: API无数据")

        except Exception as e:
            issue = f"数据标准化器测试失败: {e}"
            issues.append(issue)
            print(f"   ❌ {issue}")

        # 检查特殊字段处理
        print("\n   检查特殊字段处理:")

        # 检查参数信息的处理
        sample_product = products[0]
        if "paramInfoList" in sample_product and sample_product["paramInfoList"]:
            try:
                param_list = sample_product["paramInfoList"]
                if isinstance(param_list, list) and len(param_list) > 0:
                    print(f"     ✅ paramInfoList: 包含 {len(param_list)} 个参数项")
                else:
                    issue = "paramInfoList格式异常"
                    issues.append(issue)
                    print(f"     ❌ {issue}")
            except Exception as e:
                issue = f"paramInfoList处理失败: {e}"
                issues.append(issue)
                print(f"     ❌ {issue}")

        # 检查附件URL处理
        if "attachments" in sample_product and sample_product["attachments"]:
            attachments = sample_product["attachments"]
            if isinstance(attachments, str) and attachments.startswith("http"):
                print(f"     ✅ attachments: URL格式正确")
            else:
                issue = "attachments URL格式异常"
                issues.append(issue)
                print(f"     ❌ {issue}")

        self.analysis_results["parsing_issues"] = issues

    def _generate_recommendations(self):
        """生成改进建议"""
        print("\n4. 💡 改进建议")
        print("-" * 40)

        recommendations = []

        # 基于字段映射分析生成建议
        field_stats = self.analysis_results.get("api_field_mapping", {})
        for field, stats in field_stats.items():
            content_rate = (stats["has_content_count"] / 5) * 100  # 假设总共5个样本

            if content_rate < 50:
                recommendations.append(
                    {
                        "category": "字段映射",
                        "priority": "medium",
                        "issue": f'{stats["display_name"]}字段内容覆盖率低({content_rate:.1f}%)',
                        "solution": f"检查API字段{field}的数据来源和映射逻辑",
                    }
                )

        # 基于解析问题生成建议
        issues = self.analysis_results.get("parsing_issues", [])
        for issue in issues:
            recommendations.append(
                {
                    "category": "解析问题",
                    "priority": "high",
                    "issue": issue,
                    "solution": "修复数据标准化器中的字段映射逻辑",
                }
            )

        # 输出建议
        if recommendations:
            for i, rec in enumerate(recommendations, 1):
                priority_icon = (
                    "🔴"
                    if rec["priority"] == "high"
                    else "🟡" if rec["priority"] == "medium" else "🟢"
                )
                print(f"   {i}. {priority_icon} [{rec['category']}] {rec['issue']}")
                print(f"      解决方案: {rec['solution']}")
        else:
            print("   ✅ 知识内容解析良好，暂无改进建议")

        self.analysis_results["recommendations"] = recommendations

    def _print_analysis_report(self):
        """输出分析报告摘要"""
        print("\n" + "=" * 60)
        print("📊 产品知识内容字段解析分析报告摘要")
        print("=" * 60)

        # 字段映射摘要
        field_stats = self.analysis_results.get("api_field_mapping", {})
        total_fields = len(field_stats)
        good_fields = sum(
            1
            for stats in field_stats.values()
            if (stats["has_content_count"] / 5) * 100 > 70
        )
        print(f"📊 字段映射状态: {good_fields}/{total_fields} 个字段内容充足")

        # 内容质量摘要
        content_analysis = self.analysis_results.get("content_analysis", {})
        html_fields = len(content_analysis.get("html_content_fields", []))
        json_fields = len(content_analysis.get("json_content_fields", []))
        url_fields = len(content_analysis.get("url_content_fields", []))
        print(
            f"📚 内容类型分布: HTML({html_fields}) JSON({json_fields}) URL({url_fields})"
        )

        # 解析问题摘要
        issues = self.analysis_results.get("parsing_issues", [])
        print(f"🔍 解析问题: {len(issues)} 个问题需要修复")

        # 建议摘要
        recommendations = self.analysis_results.get("recommendations", [])
        high_priority = sum(1 for r in recommendations if r.get("priority") == "high")
        medium_priority = sum(
            1 for r in recommendations if r.get("priority") == "medium"
        )

        if recommendations:
            print(
                f"💡 改进建议: {len(recommendations)} 项 (高优先级: {high_priority}, 中优先级: {medium_priority})"
            )
        else:
            print("💡 改进建议: 知识内容解析良好")

        print("=" * 60)


def main():
    """主函数"""
    analyzer = KnowledgeContentAnalyzer()
    results = analyzer.analyze_knowledge_content_parsing()

    # 保存分析结果
    with open("knowledge_content_analysis_report.json", "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2, default=str)

    print(f"\n📊 分析报告已保存到: knowledge_content_analysis_report.json")


if __name__ == "__main__":
    main()
