---
description: 
globs: 
alwaysApply: true
---
# 项目结构标准规范

## 📋 概述

本文档基于云商系统项目结构分析结果，制定严格的项目组织标准。当前项目存在根目录散落大量临时文件、多个虚拟环境并存、文件命名不规范等问题，急需进行结构重构和规范化管理。

## 🚨 当前问题分析

### 发现的结构问题
1. **根目录混乱**: 散落50+个测试文件和报告文件
2. **虚拟环境冗余**: 存在venv、venv_new、venv_yunshang三个环境目录
3. **文件命名违规**: 使用中文文件名和特殊字符（如📦、📋等表情符号）
4. **临时文件堆积**: 大量test_*.py、*_fix.py、*_report.md文件未清理
5. **目录职责不清**: 缺乏明确的功能目录划分

## 🏗️ 标准项目结构

### 强制目录结构
```
yunshang/                           # 项目根目录
├── README.md                       # 项目说明文档
├── requirements.txt                # Python依赖管理
├── .env.example                    # 环境变量模板
├── .gitignore                      # Git忽略规则
├── .cursorignore                   # Cursor忽略规则
├── setup.py                        # 项目安装配置
├── pyproject.toml                  # 现代Python项目配置
├── main.py                         # 应用主入口点
├── config.py                       # 配置管理模块
│
├── .streamlit/                     # Streamlit配置目录
│   ├── config.toml                 # Streamlit应用配置
│   └── secrets.toml               # 敏感信息配置（不提交）
│
├── .cursor/                        # Cursor IDE配置
│   └── rules/                      # Cursor规则文件
│       ├── code-quality-standards.mdc
│       ├── project-structure-standards.mdc
│       └── ...
│
├── pages/                          # Streamlit多页面应用
│   ├── __init__.py
│   ├── 01_product_management.py    # 产品管理页面（英文命名）
│   ├── 02_product_detail.py        # 产品详情页面
│   ├── 03_case_management.py       # 案例管理页面
│   ├── 04_solution_management.py   # 方案管理页面
│   ├── 05_news_management.py       # 资讯管理页面
│   ├── 06_order_management.py      # 配单管理页面
│   ├── 07_data_sync.py            # 数据同步页面
│   └── 08_data_query.py           # 数据查询页面
│
├── components/                     # 可复用UI组件
│   ├── __init__.py
│   ├── data_table.py              # 数据表格组件
│   ├── search_filter.py           # 搜索筛选组件
│   ├── product_card.py            # 产品卡片组件
│   ├── pagination.py              # 分页组件
│   ├── upload_widget.py           # 文件上传组件
│   └── chart_widgets.py           # 图表组件
│
├── services/                       # 业务服务层
│   ├── __init__.py
│   ├── product_service.py          # 产品相关业务逻辑
│   ├── case_service.py             # 案例相关业务逻辑
│   ├── solution_service.py         # 方案相关业务逻辑
│   ├── news_service.py             # 资讯相关业务逻辑
│   ├── order_service.py            # 配单相关业务逻辑
│   ├── sync_service.py             # 数据同步服务
│   ├── user_service.py             # 用户管理服务
│   └── notification_service.py     # 通知服务
│
├── utils/                          # 工具函数和辅助模块
│   ├── __init__.py
│   ├── api_client.py              # API客户端封装
│   ├── database.py                # 数据库操作工具
│   ├── auth.py                    # 认证相关工具
│   ├── validators.py              # 数据验证工具
│   ├── exceptions.py              # 自定义异常类
│   ├── logging_config.py          # 日志配置
│   ├── cache_manager.py           # 缓存管理
│   ├── file_utils.py              # 文件操作工具
│   └── session_manager.py         # Session管理
│
├── models/                         # 数据模型定义
│   ├── __init__.py
│   ├── product.py                 # 产品数据模型
│   ├── case.py                    # 案例数据模型
│   ├── solution.py                # 方案数据模型
│   ├── news.py                    # 资讯数据模型
│   ├── order.py                   # 配单数据模型
│   └── user.py                    # 用户数据模型
│
├── tests/                          # 测试文件统一目录
│   ├── __init__.py
│   ├── conftest.py                # pytest配置
│   ├── unit/                      # 单元测试
│   │   ├── test_services/         # 服务层测试
│   │   ├── test_utils/            # 工具函数测试
│   │   └── test_models/           # 数据模型测试
│   ├── integration/               # 集成测试
│   │   ├── test_api_integration.py
│   │   ├── test_database_integration.py
│   │   └── test_end_to_end.py
│   ├── fixtures/                  # 测试数据和夹具
│   │   ├── sample_data.json
│   │   ├── test_products.csv
│   │   └── mock_responses.py
│   └── performance/               # 性能测试
│       ├── test_load_performance.py
│       └── test_memory_usage.py
│
├── docs/                          # 项目文档
│   ├── README.md                  # 文档索引
│   ├── api/                       # API文档
│   │   ├── endpoints.md           # API端点说明
│   │   ├── authentication.md     # 认证文档
│   │   └── examples.md            # 使用示例
│   ├── deployment/                # 部署文档
│   │   ├── production.md          # 生产环境部署
│   │   ├── staging.md             # 测试环境部署
│   │   └── docker.md              # Docker部署
│   ├── development/               # 开发文档
│   │   ├── setup.md               # 开发环境搭建
│   │   ├── coding_standards.md    # 编码规范
│   │   └── troubleshooting.md     # 问题排查
│   └── user_guide/               # 用户指南
│       ├── getting_started.md    # 快速开始
│       ├── features.md            # 功能说明
│       └── faq.md                 # 常见问题
│
├── scripts/                       # 脚本文件
│   ├── setup/                     # 环境搭建脚本
│   │   ├── install_dependencies.py
│   │   ├── setup_database.py
│   │   └── create_env_file.py
│   ├── migration/                 # 数据迁移脚本
│   │   ├── migrate_v1_to_v2.py
│   │   └── backup_database.py
│   ├── maintenance/               # 维护脚本
│   │   ├── cleanup_temp_files.py
│   │   ├── optimize_database.py
│   │   └── health_check.py
│   └── quality_check.py           # 代码质量检查脚本
│
├── data/                          # 数据文件目录
│   ├── raw/                       # 原始数据
│   │   ├── products/              # 产品原始数据
│   │   ├── cases/                 # 案例原始数据
│   │   └── solutions/             # 方案原始数据
│   ├── processed/                 # 处理后数据
│   │   ├── products_cleaned.csv
│   │   ├── cases_processed.json
│   │   └── solutions_formatted.xlsx
│   ├── cache/                     # 缓存数据
│   │   ├── api_cache/
│   │   └── computed_data/
│   └── exports/                   # 导出数据
│       ├── reports/
│       └── backups/
│
├── logs/                          # 日志文件目录
│   ├── app.log                    # 应用日志
│   ├── error.log                  # 错误日志
│   ├── access.log                 # 访问日志
│   └── debug.log                  # 调试日志
│
├── static/                        # 静态资源
│   ├── css/                       # 样式文件
│   ├── js/                        # JavaScript文件
│   ├── images/                    # 图片资源
│   │   ├── logos/                 # Logo图片
│   │   ├── icons/                 # 图标文件
│   │   └── placeholders/          # 占位图片
│   └── fonts/                     # 字体文件
│
├── templates/                     # 模板文件
│   ├── email/                     # 邮件模板
│   ├── reports/                   # 报告模板
│   └── notifications/             # 通知模板
│
├── migrations/                    # 数据库迁移文件
│   ├── 001_initial_schema.sql
│   ├── 002_add_product_tables.sql
│   └── 003_add_indexes.sql
│
├── docker/                        # Docker相关文件
│   ├── Dockerfile                 # Docker镜像定义
│   ├── docker-compose.yml         # 容器编排配置
│   ├── docker-compose.prod.yml    # 生产环境配置
│   └── nginx.conf                 # Nginx配置
│
├── .github/                       # GitHub配置
│   ├── workflows/                 # GitHub Actions
│   │   ├── ci.yml                 # 持续集成
│   │   ├── cd.yml                 # 持续部署
│   │   └── quality_check.yml      # 代码质量检查
│   ├── ISSUE_TEMPLATE/            # Issue模板
│   └── PULL_REQUEST_TEMPLATE.md   # PR模板
│
└── venv/                          # 虚拟环境（统一使用）
    ├── bin/                       # 可执行文件
    ├── lib/                       # 库文件
    └── include/                   # 头文件
```

## 📁 目录职责说明

### 核心应用目录
- **pages/**: Streamlit页面文件，每个页面负责一个主要功能模块
- **components/**: 可复用的UI组件，提高代码复用性
- **services/**: 业务逻辑层，处理具体的业务操作
- **utils/**: 工具函数和辅助模块，提供通用功能
- **models/**: 数据模型定义，统一数据结构

### 开发支持目录
- **tests/**: 所有测试文件的统一存放位置
- **docs/**: 项目文档，包括API文档、部署指南等
- **scripts/**: 自动化脚本，用于环境搭建、数据迁移等
- **migrations/**: 数据库迁移脚本

### 数据和资源目录
- **data/**: 数据文件，按处理状态分类存放
- **static/**: 静态资源文件，如CSS、JS、图片等
- **templates/**: 模板文件，用于生成邮件、报告等
- **logs/**: 日志文件，按类型分类存放

### 配置和部署目录
- **.streamlit/**: Streamlit应用配置
- **.cursor/**: Cursor IDE配置和规则
- **docker/**: Docker容器化相关文件
- **.github/**: GitHub集成配置

## 🚫 禁止的文件和目录

### 严格禁止的文件模式
```bash
# ❌ 禁止在根目录存放
test_*.py                          # 测试文件必须在tests/目录
*_test.py                          # 测试文件必须在tests/目录
*_fix.py                           # 修复文件应在适当目录或删除
*_backup.py                        # 备份文件应删除或移至备份目录
*_old.py                           # 旧文件应删除
*_temp.py                          # 临时文件应删除
debug_*.py                         # 调试文件应删除
experimental_*.py                  # 实验性文件应在专门目录
```

### 禁止的目录模式
```bash
# ❌ 禁止多个虚拟环境
venv_new/                          # 应统一使用venv/
venv_yunshang/                     # 应统一使用venv/
env/                               # 应统一使用venv/
.venv/                             # 应统一使用venv/

# ❌ 禁止混乱的临时目录
temp/                              # 临时文件应在data/cache/
tmp/                               # 临时文件应在data/cache/
backup/                            # 备份应在data/backups/
old/                               # 旧文件应删除
```

## 📝 文件命名强制规范

### Python文件命名
```bash
# ✅ 正确的命名模式
product_management.py              # 功能描述性命名
data_table_component.py            # 组件命名
api_client.py                      # 工具类命名
test_product_service.py            # 测试文件命名

# ❌ 禁止的命名模式
01_📦_产品管理.py                  # 中文+表情符号
ProductManagement.py               # 大驼峰命名（Python不推荐）
product-management.py              # 连字符（应使用下划线）
productManagement.py               # 小驼峰命名（应使用下划线）
产品管理.py                        # 纯中文命名
```

### 配置文件命名
```bash
# ✅ 标准配置文件
config.py                          # Python配置
config.toml                        # TOML配置
.env                               # 环境变量
requirements.txt                   # 依赖列表
pyproject.toml                     # 项目配置

# ❌ 避免的配置文件
config.ini                         # 推荐使用TOML
settings.py                        # 应使用config.py
```

## 🔧 项目重构实施计划

### 阶段1: 清理和整理（优先级：紧急）
```bash
# 1. 创建标准目录结构
mkdir -p tests/{unit,integration,fixtures,performance}
mkdir -p docs/{api,deployment,development,user_guide}
mkdir -p scripts/{setup,migration,maintenance}
mkdir -p data/{raw,processed,cache,exports}
mkdir -p static/{css,js,images,fonts}
mkdir -p templates/{email,reports,notifications}

# 2. 移动测试文件
mv test_*.py tests/unit/
mv *_test.py tests/unit/

# 3. 清理临时文件
rm -f *_fix.py
rm -f *_backup.py
rm -f *_old.py
rm -f *_temp.py
rm -f debug_*.py

# 4. 统一虚拟环境
rm -rf venv_new/ venv_yunshang/
# 保留venv/作为标准虚拟环境
```

### 阶段2: 文件重命名（优先级：高）
```python
# 重命名脚本示例
import os
import shutil

rename_mapping = {
    "pages/01_📦_产品管理.py": "pages/01_product_management.py",
    "pages/02_📋_产品详情.py": "pages/02_product_detail.py",
    "pages/03_📋_案例管理.py": "pages/03_case_management.py",
    "pages/04_🎯_方案管理.py": "pages/04_solution_management.py",
    "pages/05_📰_资讯管理.py": "pages/05_news_management.py",
    "pages/06_📊_配单管理.py": "pages/06_order_management.py",
}

for old_name, new_name in rename_mapping.items():
    if os.path.exists(old_name):
        shutil.move(old_name, new_name)
        print(f"重命名: {old_name} -> {new_name}")
```

### 阶段3: 更新引用（优先级：高）
```python
# 更新文件引用脚本
import re
import os

def update_file_references(file_path: str, mapping: dict):
    """更新文件中的路径引用"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    for old_path, new_path in mapping.items():
        # 更新st.switch_page引用
        content = re.sub(
            rf'st\.switch_page\(["\']?{re.escape(old_path)}["\']?\)',
            f'st.switch_page("{new_path}")',
            content
        )
        
        # 更新import引用
        old_module = old_path.replace('/', '.').replace('.py', '')
        new_module = new_path.replace('/', '.').replace('.py', '')
        content = re.sub(
            rf'from {re.escape(old_module)} import',
            f'from {new_module} import',
            content
        )
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
```

### 阶段4: 配置文件更新（优先级：中）
```bash
# 更新.gitignore
echo "
# 虚拟环境
venv/
venv_*/
env/
.env

# 临时文件
*.tmp
*.temp
*_backup.*
*_old.*
debug_*

# 日志文件
logs/
*.log

# 缓存文件
__pycache__/
*.pyc
*.pyo
.pytest_cache/
.coverage

# 数据文件
data/cache/
data/exports/

# IDE配置
.vscode/
.idea/
*.swp
*.swo

# 系统文件
.DS_Store
Thumbs.db
" >> .gitignore

# 更新.cursorignore
echo "
venv/
logs/
data/cache/
data/exports/
__pycache__/
*.pyc
*.log
.pytest_cache/
" >> .cursorignore
```

## 📋 项目结构检查清单

### 日常维护检查
- [ ] **根目录清洁**: 根目录只包含必要的配置文件和主入口
- [ ] **目录职责明确**: 每个目录都有明确的功能定位
- [ ] **文件命名规范**: 所有文件使用英文命名，遵循snake_case
- [ ] **虚拟环境统一**: 只使用一个venv/目录
- [ ] **测试文件归位**: 所有测试文件在tests/目录下
- [ ] **临时文件清理**: 没有临时文件和备份文件堆积

### 新文件创建检查
- [ ] **位置正确**: 新文件创建在合适的目录
- [ ] **命名规范**: 文件名符合命名规范
- [ ] **引用更新**: 相关引用已正确更新
- [ ] **文档更新**: 必要时更新相关文档

### 重构验证检查
- [ ] **功能完整**: 重构后所有功能正常工作
- [ ] **路径正确**: 所有文件路径引用正确
- [ ] **导入正常**: 所有模块导入正常
- [ ] **测试通过**: 所有测试用例通过

## 🛠️ 自动化维护工具

### 项目结构检查脚本
```python
# scripts/check_project_structure.py
import os
import re
from typing import List, Dict, Any

class ProjectStructureChecker:
    """项目结构检查器"""
    
    def __init__(self, project_root: str):
        self.project_root = project_root
        self.issues = []
        
    def check_all(self) -> Dict[str, Any]:
        """执行所有结构检查"""
        self.check_root_directory_cleanliness()
        self.check_file_naming_conventions()
        self.check_directory_structure()
        self.check_virtual_environments()
        
        return {
            'passed': len(self.issues) == 0,
            'issues': self.issues
        }
    
    def check_root_directory_cleanliness(self):
        """检查根目录清洁度"""
        allowed_root_files = {
            'main.py', 'config.py', 'requirements.txt', 
            'README.md', '.gitignore', '.cursorignore',
            'setup.py', 'pyproject.toml', '.env.example'
        }
        
        for item in os.listdir(self.project_root):
            if os.path.isfile(item) and item not in allowed_root_files:
                if item.endswith('.py'):
                    self.issues.append(f"根目录存在不规范的Python文件: {item}")
                elif item.startswith('test_'):
                    self.issues.append(f"测试文件应移至tests/目录: {item}")
                elif any(suffix in item for suffix in ['_backup', '_old', '_temp', '_fix']):
                    self.issues.append(f"临时文件应清理: {item}")
    
    def check_file_naming_conventions(self):
        """检查文件命名规范"""
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
        emoji_pattern = re.compile(r'[\U0001F600-\U0001F64F\U0001F300-\U0001F5FF\U0001F680-\U0001F6FF\U0001F1E0-\U0001F1FF]')
        
        for root, dirs, files in os.walk(self.project_root):
            # 跳过虚拟环境
            dirs[:] = [d for d in dirs if d not in ['venv', '__pycache__', '.git']]
            
            for file in files:
                if file.endswith('.py'):
                    if chinese_pattern.search(file):
                        self.issues.append(f"文件名包含中文字符: {os.path.join(root, file)}")
                    if emoji_pattern.search(file):
                        self.issues.append(f"文件名包含表情符号: {os.path.join(root, file)}")
                    if '-' in file and file != '__init__.py':
                        self.issues.append(f"文件名应使用下划线而非连字符: {os.path.join(root, file)}")
    
    def check_virtual_environments(self):
        """检查虚拟环境"""
        venv_dirs = ['venv_new', 'venv_yunshang', 'env', '.venv']
        for venv_dir in venv_dirs:
            if os.path.exists(venv_dir):
                self.issues.append(f"发现多余的虚拟环境目录: {venv_dir}，应统一使用venv/")

if __name__ == "__main__":
    checker = ProjectStructureChecker(".")
    result = checker.check_all()
    
    if result['passed']:
        print("✅ 项目结构检查通过")
    else:
        print("❌ 项目结构存在问题:")
        for issue in result['issues']:
            print(f"  - {issue}")
```

### 自动清理脚本
```python
# scripts/cleanup_project.py
import os
import shutil
import glob
from typing import List

class ProjectCleaner:
    """项目清理工具"""
    
    def __init__(self, project_root: str, dry_run: bool = True):
        self.project_root = project_root
        self.dry_run = dry_run
        
    def cleanup_all(self):
        """执行所有清理操作"""
        self.cleanup_temp_files()
        self.cleanup_redundant_venvs()
        self.organize_test_files()
        
    def cleanup_temp_files(self):
        """清理临时文件"""
        temp_patterns = [
            '*_backup.*', '*_old.*', '*_temp.*', 
            '*_fix.*', 'debug_*', 'test_*'
        ]
        
        for pattern in temp_patterns:
            files = glob.glob(pattern)
            for file in files:
                if self.dry_run:
                    print(f"[DRY RUN] 将删除: {file}")
                else:
                    os.remove(file)
                    print(f"已删除: {file}")
    
    def cleanup_redundant_venvs(self):
        """清理多余的虚拟环境"""
        redundant_venvs = ['venv_new', 'venv_yunshang', 'env', '.venv']
        
        for venv_dir in redundant_venvs:
            if os.path.exists(venv_dir):
                if self.dry_run:
                    print(f"[DRY RUN] 将删除虚拟环境: {venv_dir}")
                else:
                    shutil.rmtree(venv_dir)
                    print(f"已删除虚拟环境: {venv_dir}")
    
    def organize_test_files(self):
        """整理测试文件"""
        if not os.path.exists('tests'):
            if self.dry_run:
                print("[DRY RUN] 将创建tests目录")
            else:
                os.makedirs('tests')
                print("已创建tests目录")
        
        test_files = glob.glob('test_*.py')
        for test_file in test_files:
            target = os.path.join('tests', test_file)
            if self.dry_run:
                print(f"[DRY RUN] 将移动: {test_file} -> {target}")
            else:
                shutil.move(test_file, target)
                print(f"已移动: {test_file} -> {target}")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='项目清理工具')
    parser.add_argument('--execute', action='store_true', help='执行清理操作（默认为预览模式）')
    args = parser.parse_args()
    
    cleaner = ProjectCleaner(".", dry_run=not args.execute)
    cleaner.cleanup_all()
    
    if not args.execute:
        print("\n这是预览模式，使用 --execute 参数执行实际清理")
```

---

**重要提醒**: 项目结构是代码质量的基础，必须严格遵循本规范。任何违反结构标准的代码提交都将被拒绝，直到符合规范为止。

**文档版本**: v1.0  
**最后更新**: 2024-12-19  
**维护者**: 开发团队

