"""
API响应标准化工具测试模块

测试ApiResponseNormalizer类的各种功能，验证API响应格式标准化和字段映射的正确性。
"""

from typing import Dict, Any, List
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(
    os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
)
from utils.api_response_normalizer import (
    ApiResponseNormalizer,
    FieldMappingValidator,
    normalize_api_response,
    normalize_single_api_response,
    get_pagination_info,
    validate_api_consistency,
)


class TestApiResponseNormalizer:
    """API响应标准化工具测试类"""

    def test_normalize_standard_response(self):
        """测试标准格式响应的处理"""
        # 标准格式: {"code": 200, "data": {"total": x, "rows": [...]}}
        standard_response = {
            "code": 200,
            "data": {
                "total": 2,
                "rows": [
                    {"id": 1, "productName": "产品1", "category": 1},
                    {"id": 2, "productName": "产品2", "category": 2},
                ],
            },
        }

        result = ApiResponseNormalizer.normalize_list_response(
            standard_response, data_type="product"
        )

        assert len(result) == 2
        assert result[0]["name"] == "产品1"  # 字段映射生效
        assert result[0]["categoryId"] == 1
        assert result[1]["name"] == "产品2"
        assert result[1]["categoryId"] == 2

    def test_normalize_simplified_response(self):
        """测试简化格式响应的处理"""
        # 简化格式: {"total": x, "rows": [...]}
        simplified_response = {
            "total": 1,
            "rows": [{"id": 1, "caseName": "案例1", "category": 1}],
        }

        result = ApiResponseNormalizer.normalize_list_response(
            simplified_response, data_type="case"
        )

        assert len(result) == 1
        assert result[0]["title"] == "案例1"  # 字段映射生效
        assert result[0]["categoryId"] == 1

    def test_normalize_list_response(self):
        """测试列表格式响应的处理"""
        # 列表格式: [...]
        list_response = [
            {"id": 1, "programmeName": "方案1"},
            {"id": 2, "programmeName": "方案2"},
        ]

        result = ApiResponseNormalizer.normalize_list_response(
            list_response, data_type="programme"
        )

        assert len(result) == 2
        assert result[0]["title"] == "方案1"  # 字段映射生效
        assert result[1]["title"] == "方案2"

    def test_normalize_single_response(self):
        """测试单个对象响应的处理"""
        # 标准格式
        single_response = {
            "code": 200,
            "data": {"id": 1, "productName": "产品详情", "spec": "规格说明"},
        }

        result = ApiResponseNormalizer.normalize_single_response(
            single_response, data_type="product"
        )

        assert result is not None
        assert result["name"] == "产品详情"
        assert result["specification"] == "规格说明"

    def test_normalize_pagination_response(self):
        """测试分页信息的处理"""
        paginated_response = {
            "code": 200,
            "data": {"total": 100, "current": 2, "pageSize": 20, "rows": []},
        }

        pagination = ApiResponseNormalizer.normalize_pagination_response(
            paginated_response
        )

        assert pagination["total"] == 100
        assert pagination["current"] == 2
        assert pagination["size"] == 20
        assert pagination["pages"] == 5

    def test_field_mapping_validation(self):
        """测试字段映射验证"""
        test_data = [
            {"id": 1, "productName": "产品1", "category": 1},
            {"id": 2, "productName": "产品2", "category": 2},
        ]

        required_fields = ["id", "productName", "category"]
        validation = FieldMappingValidator.validate_response_format(
            test_data, required_fields
        )

        assert validation["valid"] is True
        assert validation["format_type"] == "list"
        assert validation["data_count"] == 2
        assert len(validation["missing_fields"]) == 0

    def test_error_handling(self):
        """测试错误处理"""
        # 空响应
        empty_result = ApiResponseNormalizer.normalize_list_response(
            None, data_type="product"
        )
        assert empty_result == []

        # 无效响应
        invalid_result = ApiResponseNormalizer.normalize_list_response(
            "invalid_response", data_type="product"
        )
        assert invalid_result == []

    def test_convenience_functions(self):
        """测试便捷函数"""
        test_response = {"code": 200, "data": {"rows": [{"id": 1, "title": "资讯1"}]}}

        # 测试normalize_api_response
        result = normalize_api_response(test_response, "information")
        assert len(result) == 1
        assert result[0]["title"] == "资讯1"

        # 测试normalize_single_api_response
        single_result = normalize_single_api_response(
            {"code": 200, "data": {"id": 1, "title": "单个资讯"}}, "information"
        )
        assert single_result is not None
        assert single_result["title"] == "单个资讯"

        # 测试get_pagination_info
        pagination = get_pagination_info(test_response)
        assert "total" in pagination

    def test_api_consistency_validation(self):
        """测试API一致性验证"""
        api_responses = {
            "products": {
                "code": 200,
                "data": {"rows": [{"id": 1, "productName": "产品1"}]},
            },
            "cases": [{"id": 1, "caseName": "案例1"}],
            "empty": [],
        }

        validation_result = validate_api_consistency(api_responses)

        assert "products" in validation_result
        assert "cases" in validation_result
        assert "empty" in validation_result

        assert validation_result["products"]["format_type"] == "standard"
        assert validation_result["cases"]["format_type"] == "list"
        assert validation_result["empty"]["data_count"] == 0


def test_real_api_integration():
    """测试真实API集成（需要API服务运行）"""
    try:
        # 确保导入路径正确
        from utils.api_client import ZKMallClient

        client = ZKMallClient()

        # 测试产品API
        products = client.get_products(current=1, pageSize=5)
        assert isinstance(products, list)

        # 测试带分页信息的产品API
        products_with_count = client.get_products_with_count(current=1, pageSize=5)
        assert "products" in products_with_count
        assert "pagination" in products_with_count
        assert "total" in products_with_count

        print("✅ 真实API集成测试通过")
        print(f"   产品数量: {len(products)}")
        print(f"   总数: {products_with_count['total']}")
        print(f"   分页信息: {products_with_count['pagination']}")

    except Exception as e:
        print(f"⚠️ 真实API测试跳过 (API服务可能未运行): {e}")


if __name__ == "__main__":
    # 运行所有测试
    print("🧪 开始API响应标准化工具测试...")

    test_suite = TestApiResponseNormalizer()

    try:
        # 基础功能测试
        test_suite.test_normalize_standard_response()
        print("✅ 标准格式响应处理测试通过")

        test_suite.test_normalize_simplified_response()
        print("✅ 简化格式响应处理测试通过")

        test_suite.test_normalize_list_response()
        print("✅ 列表格式响应处理测试通过")

        test_suite.test_normalize_single_response()
        print("✅ 单个对象响应处理测试通过")

        test_suite.test_normalize_pagination_response()
        print("✅ 分页信息处理测试通过")

        test_suite.test_field_mapping_validation()
        print("✅ 字段映射验证测试通过")

        test_suite.test_error_handling()
        print("✅ 错误处理测试通过")

        test_suite.test_convenience_functions()
        print("✅ 便捷函数测试通过")

        test_suite.test_api_consistency_validation()
        print("✅ API一致性验证测试通过")

        # 真实API集成测试
        test_real_api_integration()

        print("\n🎉 所有测试完成！API响应标准化工具运行正常。")

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback

        traceback.print_exc()
