import logging
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from psycopg2.extras import RealDictCursor
from urllib.parse import urlparse
import os
from typing import List, Dict, Any, Optional, Tuple
from utils.db_base import DatabaseService

logger = logging.getLogger(__name__)


class DBInitService(DatabaseService):
    """数据库初始化服务"""

    def __init__(self):
        """初始化服务"""
        super().__init__("DBInitService")

    def create_tables(self) -> bool:
        """
        创建所有必要的数据表

        Returns:
            bool: 是否成功创建
        """
        try:
            # 创建表
            self._create_products_table()
            self._create_categories_table()
            self._create_labels_table()
            self._create_brands_table()
            self._create_cases_table()
            self._create_product_cases_table()
            self._create_programmes_table()
            self._create_programme_products_table()
            self._create_information_table()
            self._create_distribution_orders_table()
            self._create_sync_status_table()
            self._create_attachments_table()
            self._create_attachment_logs_table()

            logger.info("所有数据表创建成功")
            return True
        except Exception as e:
            logger.error(f"创建数据表失败: {e}")
            return False

    def check_table_exists(self, table_name: str) -> bool:
        """
        检查表是否存在

        Args:
            table_name: 表名

        Returns:
            bool: 表是否存在
        """
        try:
            query = """
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = %s
                )
            """
            result = self.execute_query(query, (table_name,))
            return result[0]["exists"] if result else False
        except Exception as e:
            logger.error(f"检查表 {table_name} 是否存在失败: {e}")
            return False

    def _create_products_table(self) -> None:
        """创建产品表"""
        if self.check_table_exists("products"):
            logger.info("产品表已存在，跳过创建")
            return

        query = """
            CREATE TABLE products (
                id SERIAL PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                spec VARCHAR(100),
                introduction TEXT,
                details TEXT,
                small_img TEXT,
                banner TEXT,
                category_id INTEGER,
                label_id INTEGER,
                attribute VARCHAR(10) DEFAULT '0',
                show_for TEXT,
                param_info TEXT,
                param_info_list TEXT,
                new_param TEXT,
                spec_list TEXT,
                spec_search_list TEXT,
                parameter_info TEXT,
                specification_info TEXT,
                use_to TEXT,
                qualifications TEXT,
                instructions TEXT,
                instructions_list TEXT,
                other TEXT,
                guide TEXT,
                common_problem TEXT,
                common_problem_list TEXT,
                video_explanation TEXT,
                video_installation TEXT,
                video_troubleshooting TEXT,
                accessory TEXT,
                accessory_list TEXT,
                show_for_company TEXT,
                show_for_company_name TEXT,
                category_name VARCHAR(255),
                label_name VARCHAR(255),
                label_list TEXT,
                size_img TEXT,
                unit VARCHAR(50),
                unit_name VARCHAR(100),
                product_gen_map TEXT,
                question_list TEXT,
                hard_list TEXT,
                soft_list TEXT,
                company_id INTEGER,
                product_id_list TEXT,
                price DECIMAL(10,2) DEFAULT 0,
                is_suggest VARCHAR(10) DEFAULT '0',
                is_hot VARCHAR(10) DEFAULT '0',
                is_new VARCHAR(10) DEFAULT '0',
                count INTEGER DEFAULT 0,
                like_count INTEGER DEFAULT 0,
                favorite_count INTEGER DEFAULT 0,
                status VARCHAR(10) DEFAULT '0',
                site_id INTEGER DEFAULT 999,
                brand_id INTEGER,
                show_time TIMESTAMP,
                sort INTEGER DEFAULT 0,
                create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                create_by VARCHAR(100),
                update_by VARCHAR(100),
                last_sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """
        self.execute_update(query)
        logger.info("产品表创建成功")

    def _create_categories_table(self) -> None:
        """创建分类表"""
        if self.check_table_exists("categories"):
            logger.info("分类表已存在，跳过创建")
            return

        query = """
            CREATE TABLE categories (
                id SERIAL PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                parent_id INTEGER,
                level INTEGER DEFAULT 1,
                sort INTEGER DEFAULT 0,
                status VARCHAR(10) DEFAULT '0',
                site_id INTEGER DEFAULT 999,
                create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                create_by VARCHAR(100),
                update_by VARCHAR(100),
                last_sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """
        self.execute_update(query)
        logger.info("分类表创建成功")

    def _create_labels_table(self) -> None:
        """创建标签表"""
        if self.check_table_exists("labels"):
            logger.info("标签表已存在，跳过创建")
            return

        query = """
            CREATE TABLE labels (
                id SERIAL PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                show_day INTEGER,
                sort INTEGER DEFAULT 0,
                status VARCHAR(10) DEFAULT '0',
                site_id INTEGER DEFAULT 999,
                create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                create_by VARCHAR(100),
                update_by VARCHAR(100),
                last_sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """
        self.execute_update(query)
        logger.info("标签表创建成功")

    def _create_brands_table(self) -> None:
        """创建品牌表"""
        if self.check_table_exists("brands"):
            logger.info("品牌表已存在，跳过创建")
            return

        query = """
            CREATE TABLE brands (
                id SERIAL PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                logo TEXT,
                description TEXT,
                status VARCHAR(10) DEFAULT '0',
                site_id INTEGER DEFAULT 999,
                create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                create_by VARCHAR(100),
                update_by VARCHAR(100),
                last_sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """
        self.execute_update(query)
        logger.info("品牌表创建成功")

    def _create_cases_table(self) -> None:
        """创建案例表"""
        if self.check_table_exists("cases"):
            logger.info("案例表已存在，跳过创建")
            return

        query = """
            CREATE TABLE cases (
                id SERIAL PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                content TEXT,
                image TEXT,
                status VARCHAR(10) DEFAULT '0',
                site_id INTEGER DEFAULT 999,
                create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                create_by VARCHAR(100),
                update_by VARCHAR(100),
                last_sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """
        self.execute_update(query)
        logger.info("案例表创建成功")

    def _create_product_cases_table(self) -> None:
        """创建产品案例关联表"""
        if self.check_table_exists("product_cases"):
            logger.info("产品案例关联表已存在，跳过创建")
            return

        query = """
            CREATE TABLE product_cases (
                id SERIAL PRIMARY KEY,
                product_id INTEGER,
                case_id INTEGER,
                create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """
        self.execute_update(query)
        logger.info("产品案例关联表创建成功")

    def _create_programmes_table(self) -> None:
        """创建方案表"""
        if self.check_table_exists("programmes"):
            logger.info("方案表已存在，跳过创建")
            return

        query = """
            CREATE TABLE programmes (
                id SERIAL PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                introduction TEXT,
                content TEXT,
                banner TEXT,
                small_img TEXT,
                video TEXT,
                other TEXT,
                category_id INTEGER,
                category_scene_id INTEGER,
                company_id INTEGER,
                province VARCHAR(100),
                city VARCHAR(100),
                county VARCHAR(100),
                count INTEGER DEFAULT 0,
                like_count INTEGER DEFAULT 0,
                favorite_count INTEGER DEFAULT 0,
                collect_size INTEGER DEFAULT 0,
                is_suggest INTEGER DEFAULT 0,
                is_hot VARCHAR(10) DEFAULT '0',
                is_new INTEGER DEFAULT 0,
                is_push INTEGER DEFAULT 0,
                top VARCHAR(10) DEFAULT '0',
                type INTEGER DEFAULT 0,
                sort INTEGER DEFAULT 0,
                status VARCHAR(10) DEFAULT '0',
                site_id INTEGER DEFAULT 999,
                create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                create_by VARCHAR(100),
                update_by VARCHAR(100),
                last_sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """
        self.execute_update(query)
        logger.info("方案表创建成功")

    def _create_programme_products_table(self) -> None:
        """创建方案产品关联表"""
        if self.check_table_exists("programme_products"):
            logger.info("方案产品关联表已存在，跳过创建")
            return

        query = """
            CREATE TABLE programme_products (
                id SERIAL PRIMARY KEY,
                programme_id INTEGER,
                product_id INTEGER,
                quantity INTEGER DEFAULT 1,
                type VARCHAR(20) DEFAULT 'hardware',
                create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """
        self.execute_update(query)
        logger.info("方案产品关联表创建成功")

    def _create_information_table(self) -> None:
        """创建资讯表"""
        if self.check_table_exists("information"):
            logger.info("资讯表已存在，跳过创建")
            return

        query = """
            CREATE TABLE information (
                id SERIAL PRIMARY KEY,
                title VARCHAR(255) NOT NULL,
                details TEXT,
                pic_video TEXT,
                small_img TEXT,
                images TEXT,
                other_url TEXT,
                video_url TEXT,
                category_id INTEGER,
                product_id INTEGER,
                belong_id INTEGER,
                is_hot INTEGER DEFAULT 0,
                is_suggest VARCHAR(10) DEFAULT '0',
                show_type INTEGER DEFAULT 0,
                top VARCHAR(50) DEFAULT '0',
                watch INTEGER DEFAULT 0,
                like_count INTEGER DEFAULT 0,
                favorite_count INTEGER DEFAULT 0,
                status VARCHAR(10) DEFAULT '0',
                site_id INTEGER DEFAULT 999,
                create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                create_by VARCHAR(100),
                update_by VARCHAR(100),
                last_sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """
        self.execute_update(query)
        logger.info("资讯表创建成功")

    def _create_distribution_orders_table(self) -> None:
        """创建配单表"""
        if self.check_table_exists("distribution_orders"):
            logger.info("配单表已存在，跳过创建")
            return

        query = """
            CREATE TABLE distribution_orders (
                id SERIAL PRIMARY KEY,
                status VARCHAR(10) DEFAULT '0',
                name VARCHAR(255) NOT NULL,
                type VARCHAR(50),
                customer_name VARCHAR(100),
                phone VARCHAR(20),
                contacts VARCHAR(100),
                company_id INTEGER,
                user_id INTEGER,
                hide_price INTEGER DEFAULT 0,
                fir_category_id INTEGER,
                sec_category_id INTEGER,
                source_type INTEGER DEFAULT 0,
                from_user_id INTEGER DEFAULT -1,
                from_user_name VARCHAR(100),
                platform_order_id VARCHAR(100),
                category_scene VARCHAR(100),
                like_count INTEGER DEFAULT 0,
                favorite_count INTEGER DEFAULT 0,
                fir_category_name VARCHAR(255),
                sec_category_name VARCHAR(255),
                category_scene_name VARCHAR(255),
                other TEXT,
                details TEXT,
                company_name VARCHAR(255),
                has_expire BOOLEAN DEFAULT FALSE,
                site_id INTEGER DEFAULT 999,
                create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """
        self.execute_update(query)
        logger.info("配单表创建成功")

    def _create_sync_status_table(self) -> None:
        """创建同步状态表"""
        if self.check_table_exists("sync_status"):
            logger.info("同步状态表已存在，跳过创建")
            return

        # 创建表
        create_table_query = """
            CREATE TABLE sync_status (
                id SERIAL PRIMARY KEY,
                entity_type VARCHAR(50) NOT NULL,
                last_sync_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status VARCHAR(20) DEFAULT 'success',
                total_count INTEGER DEFAULT 0,
                success_count INTEGER DEFAULT 0,
                error_count INTEGER DEFAULT 0,
                progress INTEGER DEFAULT 0,
                error_message TEXT,
                create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """
        self.execute_update(create_table_query)

        # 初始化同步状态记录
        entity_types = [
            "products",
            "categories",
            "labels",
            "brands",
            "cases",
            "product_cases",
            "programmes",
            "programme_products",
            "information",
            "distribution_orders",
        ]

        insert_query = """
            INSERT INTO sync_status (entity_type)
            VALUES (%s)
        """

        for entity_type in entity_types:
            self.execute_update(insert_query, (entity_type,))

        logger.info("同步状态表创建成功")

    def _create_attachments_table(self) -> None:
        """创建附件管理表"""
        if self.check_table_exists("attachments"):
            logger.info("附件管理表已存在，跳过创建")
            return

        query = """
            CREATE TABLE attachments (
                id SERIAL PRIMARY KEY,
                original_url VARCHAR(1000) NOT NULL,
                local_path VARCHAR(500) NOT NULL,
                filename VARCHAR(255) NOT NULL,
                file_type VARCHAR(50) NOT NULL,
                file_size BIGINT DEFAULT 0,
                content_type VARCHAR(100),
                field_name VARCHAR(100) NOT NULL,
                category VARCHAR(50) NOT NULL,
                item_id INTEGER NOT NULL,
                downloaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                is_active BOOLEAN DEFAULT TRUE,
                checksum VARCHAR(64),
                create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """
        self.execute_update(query)

        # 创建索引 - 注意：PostgreSQL使用不同的语法
        index_queries = [
            "CREATE INDEX IF NOT EXISTS idx_attachments_category_item ON attachments (category, item_id)",
            "CREATE INDEX IF NOT EXISTS idx_attachments_original_url ON attachments (original_url)",
            "CREATE INDEX IF NOT EXISTS idx_attachments_local_path ON attachments (local_path)",
        ]

        for index_query in index_queries:
            self.execute_update(index_query)

        logger.info("附件管理表创建成功")

    def _create_attachment_logs_table(self) -> None:
        """创建附件下载日志表"""
        if self.check_table_exists("attachment_logs"):
            logger.info("附件下载日志表已存在，跳过创建")
            return

        query = """
            CREATE TABLE attachment_logs (
                id SERIAL PRIMARY KEY,
                attachment_id INTEGER REFERENCES attachments(id),
                operation VARCHAR(50) NOT NULL,
                status VARCHAR(20) NOT NULL,
                error_message TEXT,
                details JSONB,
                create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """
        self.execute_update(query)
        logger.info("附件下载日志表创建成功")


if __name__ == "__main__":
    # 执行数据库初始化
    logging.basicConfig(level=logging.INFO)
    db_init = DBInitService()
    try:
        success = db_init.create_tables()
        if success:
            print("数据库表初始化完成！")
        else:
            print("数据库表初始化失败！")
    finally:
        # 连接管理由基类自动处理，无需手动关闭
        pass
