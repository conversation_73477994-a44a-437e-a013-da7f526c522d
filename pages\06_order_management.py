import streamlit as st
import logging
import pandas as pd
from typing import Optional, Dict, Any, List
from utils.session import SessionManager
from utils.logging_config import get_logger
from utils.api_client import ZKMallClient

# 配置日志
logger = get_logger()


def main():
    """配单管理页面主函数"""
    # 页面配置
    st.set_page_config(
        page_title="配单管理 - 云商系统",
        page_icon="📊",
        layout="wide",
        initial_sidebar_state="expanded",
    )

    # 初始化session
    initialize_session()

    # 渲染页面
    render_header()
    render_sidebar()
    render_content()
    render_footer()


def initialize_session():
    """初始化页面session状态"""
    defaults = {
        "current_order": None,
        "search_term": "",
        "filter_status": "all",
        "current_page": 1,
        "page_size": 20,
    }

    for key, value in defaults.items():
        if f"distribution_management_{key}" not in st.session_state:
            SessionManager.set(f"distribution_management_{key}", value)


@st.cache_data(ttl=300)
def load_distribution_orders_from_api(
    search_term: str = "",
    current_page: int = 1,
    page_size: int = 20,
    product_id: Optional[int] = None,
    status_filter: Optional[str] = None,
) -> Dict[str, Any]:
    """
    从API加载配单数据

    Args:
        search_term: 搜索关键词
        current_page: 当前页码
        page_size: 每页数量
        product_id: 产品ID（可选）
        status_filter: 状态筛选（可选）

    Returns:
        包含配单数据和分页信息的字典
    """
    try:
        client = ZKMallClient()

        # 构建查询参数
        params = {"current": current_page, "pageSize": page_size}

        if product_id:
            params["productId"] = product_id

        # 调用API获取配单数据
        orders = client.get_distribution_orders(**params)

        logger.info(f"成功获取配单数据，数量: {len(orders)}")

        # 如果有搜索关键词或状态筛选，进行本地过滤
        filtered_orders = []
        for order in orders:
            # 搜索过滤
            if search_term:
                searchable_text = (
                    f"{order.get('productName', '')} "
                    f"{order.get('companyName', '')} "
                    f"{order.get('firCategoryName', '')} "
                    f"{order.get('secCategoryName', '')} "
                    f"{order.get('orderNo', '')}"
                ).lower()

                if search_term.lower() not in searchable_text:
                    continue

            # 状态过滤（根据实际API字段调整）
            if status_filter and status_filter != "all":
                order_status = order.get("status", "").lower()
                if status_filter != order_status:
                    continue

            filtered_orders.append(order)

        return {
            "orders": filtered_orders,
            "total": len(filtered_orders),
            "current_page": current_page,
            "page_size": page_size,
        }

    except Exception as e:
        logger.error(f"获取配单数据失败: {e}")
        st.error(f"获取配单数据失败: {e}")
        return {
            "orders": [],
            "total": 0,
            "current_page": current_page,
            "page_size": page_size,
        }


def render_header():
    """渲染页面头部"""
    st.title("📊 配单管理")
    st.markdown("管理和查看云商系统中的配单信息")

    # 导航面包屑
    st.markdown("🏢 [首页](/) > 📊 配单管理")


def render_sidebar():
    """渲染侧边栏"""
    with st.sidebar:
        st.subheader("📊 配单管理")

        # 搜索功能
        search_term = st.text_input(
            "搜索配单",
            value=SessionManager.get("distribution_management_search_term", ""),
            placeholder="输入产品名称、公司名称或配单号",
        )
        SessionManager.set("distribution_management_search_term", search_term)

        # 状态筛选
        filter_status = st.selectbox(
            "配单状态",
            options=["all", "pending", "confirmed", "completed", "cancelled"],
            format_func=lambda x: {
                "all": "全部状态",
                "pending": "待确认",
                "confirmed": "已确认",
                "completed": "已完成",
                "cancelled": "已取消",
            }[x],
            index=["all", "pending", "confirmed", "completed", "cancelled"].index(
                SessionManager.get("distribution_management_filter_status", "all")
            ),
        )
        SessionManager.set("distribution_management_filter_status", filter_status)

        # 分页设置
        page_size = st.selectbox(
            "每页显示数量",
            options=[10, 20, 50, 100],
            index=[10, 20, 50, 100].index(
                SessionManager.get("distribution_management_page_size", 20)
            ),
        )
        SessionManager.set("distribution_management_page_size", page_size)

        if st.button("🔄 刷新数据", use_container_width=True):
            st.cache_data.clear()
            st.rerun()

        if st.button("🏠 返回首页", use_container_width=True):
            st.switch_page("main.py")


def render_content():
    """渲染主要内容"""
    tab1, tab2, tab3 = st.tabs(["配单列表", "配单详情", "操作记录"])

    with tab1:
        render_order_list()

    with tab2:
        render_order_detail()

    with tab3:
        render_operation_log()


def render_order_list():
    """渲染配单列表"""
    st.subheader("配单列表")

    # 操作按钮
    col1, col2, col3 = st.columns([1, 1, 2])
    with col1:
        if st.button("➕ 新增配单"):
            st.info("新增配单功能开发中...")

    with col2:
        if st.button("📤 导出数据"):
            st.info("导出功能开发中...")

    # 获取搜索参数
    search_term = SessionManager.get("distribution_management_search_term", "")
    filter_status = SessionManager.get("distribution_management_filter_status", "all")
    current_page = int(SessionManager.get("distribution_management_current_page", 1))
    page_size = int(SessionManager.get("distribution_management_page_size", 20))

    # 数据展示区域
    with st.container(border=True):
        with st.spinner("正在加载配单数据..."):
            # 从API获取真实数据
            data = load_distribution_orders_from_api(
                search_term=search_term,
                current_page=current_page,
                page_size=page_size,
                status_filter=filter_status,
            )

            orders = data["orders"]
            total = data["total"]

            if orders:
                # 显示数据统计
                st.info(f"共找到 {total} 个配单")

                # 转换为DataFrame显示
                df_data = []
                for order in orders:
                    df_data.append(
                        {
                            "ID": order.get("id", ""),
                            "配单号": order.get("orderNo", ""),
                            "产品名称": order.get("productName", ""),
                            "一级分类": order.get("firCategoryName", ""),
                            "二级分类": order.get("secCategoryName", ""),
                            "公司名称": order.get("companyName", ""),
                            "产品型号": order.get("productModel", ""),
                            "数量": order.get("quantity", 0),
                            "单价": f"¥{order.get('unitPrice', 0):.2f}",
                            "总价": f"¥{order.get('totalPrice', 0):.2f}",
                            "状态": order.get("status", "未知"),
                            "创建时间": order.get("createTime", ""),
                        }
                    )

                if df_data:
                    df = pd.DataFrame(df_data)

                    # 显示数据表格
                    selected_rows = st.dataframe(
                        df,
                        use_container_width=True,
                        hide_index=True,
                        selection_mode="single-row",
                        on_select="rerun",
                    )

                    # 处理行选择
                    if selected_rows.selection.rows:
                        selected_index = selected_rows.selection.rows[0]
                        selected_order = orders[selected_index]
                        SessionManager.set(
                            "distribution_management_current_order", selected_order
                        )
                        st.success(f"已选择配单: {selected_order.get('orderNo', '')}")

                        # 显示快速操作按钮
                        col1, col2, col3 = st.columns(3)
                        with col1:
                            if st.button("查看详情"):
                                st.info("请切换到'配单详情'标签页查看完整信息")
                        with col2:
                            if st.button("编辑配单"):
                                st.info("编辑功能开发中...")
                        with col3:
                            if st.button("删除配单"):
                                st.warning("删除功能需要管理员权限")
                else:
                    st.warning("暂无配单数据")

                # 分页控制
                if total > page_size:
                    total_pages = (total + page_size - 1) // page_size

                    col1, col2, col3, col4, col5 = st.columns([1, 1, 2, 1, 1])

                    with col1:
                        if st.button("⬅️ 上一页", disabled=current_page <= 1):
                            SessionManager.set(
                                "distribution_management_current_page", current_page - 1
                            )
                            st.rerun()

                    with col2:
                        st.write(f"第 {current_page} 页")

                    with col3:
                        new_page = st.number_input(
                            "跳转到页面",
                            min_value=1,
                            max_value=total_pages,
                            value=current_page,
                            key="order_page_input",
                        )
                        if new_page != current_page:
                            SessionManager.set(
                                "distribution_management_current_page", new_page
                            )
                            st.rerun()

                    with col4:
                        st.write(f"共 {total_pages} 页")

                    with col5:
                        if st.button("下一页 ➡️", disabled=current_page >= total_pages):
                            SessionManager.set(
                                "distribution_management_current_page", current_page + 1
                            )
                            st.rerun()
            else:
                if search_term:
                    st.warning(f"未找到包含'{search_term}'的配单")
                else:
                    st.warning("暂无配单数据")


def render_order_detail():
    """渲染配单详情"""
    st.subheader("配单详情")

    current_order = SessionManager.get("distribution_management_current_order")

    if current_order:
        # 显示配单详情
        col1, col2 = st.columns([2, 1])

        with col1:
            st.markdown(f"### {current_order.get('productName', 'N/A')}")
            st.markdown(f"**配单号**: {current_order.get('orderNo', 'N/A')}")
            st.markdown(f"**产品型号**: {current_order.get('productModel', 'N/A')}")
            st.markdown(f"**一级分类**: {current_order.get('firCategoryName', 'N/A')}")
            st.markdown(f"**二级分类**: {current_order.get('secCategoryName', 'N/A')}")

            # 显示详细描述
            if current_order.get("description"):
                st.markdown("**详细描述**:")
                st.markdown(current_order["description"])

            # 显示规格参数
            if current_order.get("specifications"):
                st.markdown("**规格参数**:")
                st.json(current_order["specifications"])

        with col2:
            st.markdown("**基本信息**")
            st.write(f"配单ID: {current_order.get('id', 'N/A')}")
            st.write(f"公司名称: {current_order.get('companyName', 'N/A')}")
            st.write(f"数量: {current_order.get('quantity', 0)}")
            st.write(f"单价: ¥{current_order.get('unitPrice', 0):.2f}")
            st.write(f"总价: ¥{current_order.get('totalPrice', 0):.2f}")
            st.write(f"状态: {current_order.get('status', 'N/A')}")
            st.write(f"创建时间: {current_order.get('createTime', 'N/A')}")

            # 显示产品图片
            if current_order.get("productImage"):
                st.image(
                    current_order["productImage"],
                    caption="产品图片",
                    use_container_width=True,
                )

            # 显示文档附件
            if current_order.get("documents"):
                st.markdown("**相关文档**:")
                for doc in current_order["documents"]:
                    st.markdown(f"- [下载]({doc['url']}) {doc['name']}")

    else:
        st.info("请从配单列表中选择一个配单查看详情")


def render_operation_log():
    """渲染操作记录"""
    st.subheader("操作记录")
    st.info("操作记录功能开发中...")


def render_footer():
    """渲染页面脚部"""
    st.markdown("---")
    st.markdown("© 2024 云商系统 - 配单管理模块")


if __name__ == "__main__":
    main()
