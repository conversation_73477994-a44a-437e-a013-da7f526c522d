#!/usr/bin/env python3
"""
测试数据同步服务优化效果
"""

import sys
import os

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.database import get_db_connection
from utils.api_client import ZKMallClient
import json


def test_knowledge_content_parsing():
    """测试产品知识内容字段解析"""
    try:
        print("开始测试产品知识内容解析...")

        # 创建API客户端
        api_client = ZKMallClient()

        # 获取产品数据
        print("获取产品数据...")
        response = api_client.get_products(page=1, size=5)

        # 处理响应数据
        if not response:
            print("❌ 无法获取产品数据")
            return False

        if isinstance(response, dict) and "data" in response:
            products = response["data"]
        elif isinstance(response, list):
            products = response
        else:
            print("❌ 响应格式不正确")
            return False
        print(f"获取到 {len(products)} 个产品")

        # 分析每个产品的知识内容字段
        knowledge_fields = [
            "introduction",
            "details",
            "paramInfo",
            "useTo",
            "qualifications",
            "instructions",
            "guide",
            "commonProblem",
            "newParam",
            "paramInfoList",
            "videoExplanation",
            "videoInstallation",
            "videoTroubleshooting",
            "accessory",
        ]

        field_stats = {}
        for field in knowledge_fields:
            field_stats[field] = {"count": 0, "non_empty": 0}

        for i, product in enumerate(products):
            print(f"\n产品 {i+1}: {product.get('name', 'Unknown')}")
            print(f"ID: {product.get('id')}")

            for field in knowledge_fields:
                field_stats[field]["count"] += 1
                value = product.get(field, "")
                if value and str(value).strip():
                    field_stats[field]["non_empty"] += 1
                    print(f"  {field}: {str(value)[:100]}...")
                else:
                    print(f"  {field}: (空)")

        # 输出统计结果
        print("\n=== 知识内容字段统计 ===")
        for field, stats in field_stats.items():
            percentage = (
                (stats["non_empty"] / stats["count"] * 100) if stats["count"] > 0 else 0
            )
            print(f"{field}: {stats['non_empty']}/{stats['count']} ({percentage:.1f}%)")

        return True

    except Exception as e:
        print(f"测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_database_storage():
    """测试数据库存储情况"""
    try:
        print("\n开始测试数据库存储...")

        conn = get_db_connection()
        cursor = conn.cursor()

        # 检查产品表中的知识内容字段
        cursor.execute(
            """
            SELECT id, name, introduction, details, param_info, use_to, 
                   qualifications, instructions, guide, common_problem,
                   new_param, param_info_list, video_explanation
            FROM products 
            LIMIT 5
        """
        )

        products = cursor.fetchall()
        columns = [desc[0] for desc in cursor.description]

        print(f"数据库中有 {len(products)} 个产品记录")

        for product in products:
            product_dict = dict(zip(columns, product))
            print(f"\n产品: {product_dict['name']}")

            for field in [
                "introduction",
                "details",
                "param_info",
                "use_to",
                "qualifications",
                "instructions",
                "guide",
                "common_problem",
            ]:
                value = product_dict.get(field, "")
                if value and str(value).strip():
                    print(f"  {field}: ✅ 有内容 ({len(str(value))} 字符)")
                else:
                    print(f"  {field}: ❌ 无内容")

        cursor.close()
        conn.close()

        return True

    except Exception as e:
        print(f"数据库测试失败: {e}")
        import traceback

        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("=== 数据同步优化测试 ===")

    # 测试API数据解析
    api_test = test_knowledge_content_parsing()

    # 测试数据库存储
    db_test = test_database_storage()

    # 总结
    print("\n=== 测试结果总结 ===")
    print(f"API数据解析: {'✅ 通过' if api_test else '❌ 失败'}")
    print(f"数据库存储: {'✅ 通过' if db_test else '❌ 失败'}")

    if api_test and db_test:
        print("🎉 数据同步优化测试全部通过！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步优化")
        return False


if __name__ == "__main__":
    main()
