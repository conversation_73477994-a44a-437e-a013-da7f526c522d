"""
数据库迁移服务
负责数据库架构的升级和迁移操作
"""

import logging
import os
import sys
from typing import Dict, List, Optional, Any
from utils.database import get_db_connection, return_db_connection
from utils.database import get_db_connection_context
from utils.db_base import DatabaseService

logger = logging.getLogger(__name__)


class DBMigrationService(DatabaseService):
    """数据库迁移服务"""

    def __init__(self):
        super().__init__()
        self.migrations = [
            self._add_created_at_fields,
            self._add_updated_at_fields,
            self._create_missing_tables,
            self._add_missing_indexes,
        ]

    def run_migrations(self) -> bool:
        """运行所有迁移"""
        logger.info("开始数据库迁移...")

        try:
            for i, migration in enumerate(self.migrations, 1):
                logger.info(
                    f"执行迁移 {i}/{len(self.migrations)}: {migration.__name__}"
                )
                if not migration():
                    logger.error(f"迁移 {migration.__name__} 失败")
                    return False

            logger.info("所有数据库迁移完成")
            return True

        except Exception as e:
            logger.error(f"数据库迁移失败: {e}")
            return False

    def _add_created_at_fields(self) -> bool:
        """为所有表添加 created_at 字段"""
        tables = [
            "products",
            "categories",
            "labels",
            "brands",
            "cases",
            "product_cases",
            "programmes",
            "programme_products",
            "information",
            "distribution_orders",
            "sync_status",
            "attachments",
            "attachment_logs",
        ]

        try:
            with get_db_connection_context() as conn:
                with conn.cursor() as cursor:
                    for table in tables:
                        # 检查表是否存在
                        cursor.execute(
                            """
                            SELECT EXISTS (
                                SELECT FROM information_schema.tables 
                                WHERE table_name = %s
                            )
                        """,
                            (table,),
                        )

                        if not cursor.fetchone()[0]:
                            logger.debug(f"表 {table} 不存在，跳过")
                            continue

                        # 检查 created_at 字段是否存在
                        cursor.execute(
                            """
                            SELECT EXISTS (
                                SELECT FROM information_schema.columns 
                                WHERE table_name = %s AND column_name = 'created_at'
                            )
                        """,
                            (table,),
                        )

                        if cursor.fetchone()[0]:
                            logger.debug(f"表 {table} 已有 created_at 字段")
                            continue

                        # 添加 created_at 字段
                        cursor.execute(
                            f"""
                            ALTER TABLE {table} 
                            ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        """
                        )
                        logger.info(f"为表 {table} 添加 created_at 字段")

                        # 如果有 create_time 字段，复制数据
                        cursor.execute(
                            """
                            SELECT EXISTS (
                                SELECT FROM information_schema.columns 
                                WHERE table_name = %s AND column_name = 'create_time'
                            )
                        """,
                            (table,),
                        )

                        if cursor.fetchone()[0]:
                            cursor.execute(
                                f"""
                                UPDATE {table} 
                                SET created_at = create_time 
                                WHERE create_time IS NOT NULL
                            """
                            )
                            logger.info(
                                f"复制 {table} 表的 create_time 数据到 created_at"
                            )

            return True

        except Exception as e:
            logger.error(f"添加 created_at 字段失败: {e}")
            return False

    def _add_updated_at_fields(self) -> bool:
        """为所有表添加 updated_at 字段"""
        tables = [
            "products",
            "categories",
            "labels",
            "brands",
            "cases",
            "product_cases",
            "programmes",
            "programme_products",
            "information",
            "distribution_orders",
            "sync_status",
            "attachments",
        ]

        try:
            with get_db_connection_context() as conn:
                with conn.cursor() as cursor:
                    for table in tables:
                        # 检查表是否存在
                        cursor.execute(
                            """
                            SELECT EXISTS (
                                SELECT FROM information_schema.tables 
                                WHERE table_name = %s
                            )
                        """,
                            (table,),
                        )

                        if not cursor.fetchone()[0]:
                            logger.debug(f"表 {table} 不存在，跳过")
                            continue

                        # 检查 updated_at 字段是否存在
                        cursor.execute(
                            """
                            SELECT EXISTS (
                                SELECT FROM information_schema.columns 
                                WHERE table_name = %s AND column_name = 'updated_at'
                            )
                        """,
                            (table,),
                        )

                        if cursor.fetchone()[0]:
                            logger.debug(f"表 {table} 已有 updated_at 字段")
                            continue

                        # 添加 updated_at 字段
                        cursor.execute(
                            f"""
                            ALTER TABLE {table} 
                            ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        """
                        )
                        logger.info(f"为表 {table} 添加 updated_at 字段")

                        # 如果有 update_time 字段，复制数据
                        cursor.execute(
                            """
                            SELECT EXISTS (
                                SELECT FROM information_schema.columns 
                                WHERE table_name = %s AND column_name = 'update_time'
                            )
                        """,
                            (table,),
                        )

                        if cursor.fetchone()[0]:
                            cursor.execute(
                                f"""
                                UPDATE {table} 
                                SET updated_at = update_time 
                                WHERE update_time IS NOT NULL
                            """
                            )
                            logger.info(
                                f"复制 {table} 表的 update_time 数据到 updated_at"
                            )

            return True

        except Exception as e:
            logger.error(f"添加 updated_at 字段失败: {e}")
            return False

    def _create_missing_tables(self) -> bool:
        """创建缺失的表"""
        try:
            with get_db_connection_context() as conn:
                with conn.cursor() as cursor:
                    # 检查并创建 orders 表
                    cursor.execute(
                        """
                        SELECT EXISTS (
                            SELECT FROM information_schema.tables 
                            WHERE table_name = 'orders'
                        )
                    """
                    )

                    if not cursor.fetchone()[0]:
                        cursor.execute(
                            """
                            CREATE TABLE orders (
                                id SERIAL PRIMARY KEY,
                                order_number VARCHAR(100) UNIQUE NOT NULL,
                                customer_id INTEGER,
                                customer_name VARCHAR(255),
                                total_amount DECIMAL(12,2) DEFAULT 0,
                                order_status VARCHAR(20) DEFAULT 'pending',
                                order_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                payment_status VARCHAR(20) DEFAULT 'unpaid',
                                shipping_address TEXT,
                                notes TEXT,
                                create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                                create_by VARCHAR(100),
                                update_by VARCHAR(100)
                            )
                        """
                        )
                        logger.info("创建 orders 表")

            return True

        except Exception as e:
            logger.error(f"创建缺失表失败: {e}")
            return False

    def _add_missing_indexes(self) -> bool:
        """添加缺失的索引"""
        try:
            with get_db_connection_context() as conn:
                with conn.cursor() as cursor:
                    # 为常用查询字段添加索引
                    indexes = [
                        ("idx_products_category", "products", "category_id"),
                        ("idx_products_status", "products", "status"),
                        ("idx_cases_category", "cases", "category_id"),
                        ("idx_information_category", "information", "category_id"),
                        (
                            "idx_attachments_category_item",
                            "attachments",
                            "category, item_id",
                        ),
                        ("idx_sync_status_entity", "sync_status", "entity_type"),
                    ]

                    for index_name, table_name, columns in indexes:
                        # 检查索引是否存在
                        cursor.execute(
                            """
                            SELECT EXISTS (
                                SELECT FROM pg_indexes 
                                WHERE indexname = %s
                            )
                        """,
                            (index_name,),
                        )

                        if cursor.fetchone()[0]:
                            logger.debug(f"索引 {index_name} 已存在")
                            continue

                        # 检查表是否存在
                        cursor.execute(
                            """
                            SELECT EXISTS (
                                SELECT FROM information_schema.tables 
                                WHERE table_name = %s
                            )
                        """,
                            (table_name,),
                        )

                        if not cursor.fetchone()[0]:
                            logger.debug(f"表 {table_name} 不存在，跳过索引创建")
                            continue

                        # 创建索引
                        cursor.execute(
                            f"""
                            CREATE INDEX IF NOT EXISTS {index_name} 
                            ON {table_name} ({columns})
                        """
                        )
                        logger.info(f"创建索引 {index_name}")

            return True

        except Exception as e:
            logger.error(f"创建索引失败: {e}")
            return False

    def check_migration_status(self) -> Dict[str, Any]:
        """检查迁移状态"""
        status = {
            "tables_checked": 0,
            "missing_created_at": [],
            "missing_updated_at": [],
            "missing_tables": [],
            "missing_indexes": [],
        }

        try:
            with get_db_connection_context() as conn:
                with conn.cursor() as cursor:
                    # 检查表和字段
                    tables = [
                        "products",
                        "categories",
                        "labels",
                        "brands",
                        "cases",
                        "product_cases",
                        "programmes",
                        "programme_products",
                        "information",
                        "distribution_orders",
                        "sync_status",
                        "attachments",
                        "attachment_logs",
                        "orders",
                    ]

                    for table in tables:
                        # 检查表是否存在
                        cursor.execute(
                            """
                            SELECT EXISTS (
                                SELECT FROM information_schema.tables 
                                WHERE table_name = %s
                            )
                        """,
                            (table,),
                        )

                        if not cursor.fetchone()[0]:
                            status["missing_tables"].append(table)
                            continue

                        status["tables_checked"] += 1

                        # 检查 created_at 字段
                        cursor.execute(
                            """
                            SELECT EXISTS (
                                SELECT FROM information_schema.columns 
                                WHERE table_name = %s AND column_name = 'created_at'
                            )
                        """,
                            (table,),
                        )

                        if not cursor.fetchone()[0]:
                            status["missing_created_at"].append(table)

                        # 检查 updated_at 字段
                        if table != "attachment_logs":  # 日志表不需要 updated_at
                            cursor.execute(
                                """
                                SELECT EXISTS (
                                    SELECT FROM information_schema.columns 
                                    WHERE table_name = %s AND column_name = 'updated_at'
                                )
                            """,
                                (table,),
                            )

                            if not cursor.fetchone()[0]:
                                status["missing_updated_at"].append(table)

        except Exception as e:
            logger.error(f"检查迁移状态失败: {e}")
            status["error"] = str(e)

        return status


if __name__ == "__main__":
    # 可以直接运行迁移
    migration_service = DBMigrationService()

    # 检查状态
    status = migration_service.check_migration_status()
    print("迁移状态检查:")
    print(f"  已检查表数: {status['tables_checked']}")
    print(f"  缺失 created_at 字段: {status['missing_created_at']}")
    print(f"  缺失 updated_at 字段: {status['missing_updated_at']}")
    print(f"  缺失表: {status['missing_tables']}")

    # 运行迁移
    if input("是否运行迁移? (y/N): ").lower() == "y":
        success = migration_service.run_migrations()
        print(f"迁移结果: {'成功' if success else '失败'}")
