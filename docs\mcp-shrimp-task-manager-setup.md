# MCP Shrimp Task Manager 安装配置指南

## 📋 概述

mcp-shrimp-task-manager 是一个基于 Model Context Protocol (MCP) 的智能任务管理系统，已成功安装并配置为全局工具。

## ✅ 安装状态

- **安装版本**: v1.0.20
- **安装方式**: 全局安装 (`npm install -g mcp-shrimp-task-manager`)
- **配置状态**: 已配置到 Cursor IDE
- **数据目录**: `/home/<USER>/Documents/shrimp-tasks/`

## 🛠️ 功能特性

### 核心功能
1. **智能任务规划** - 结构化的任务分解和依赖管理
2. **Chain-of-Thought推理** - 引导AI进行系统性思考
3. **任务记忆机制** - 避免重复工作，累积经验
4. **研究模式** - 系统性技术调研能力
5. **项目感知能力** - 理解项目上下文，生成符合项目的代码
6. **任务完整性验证** - 确保任务完成质量
7. **任务复杂度评估** - 智能评估任务难度
8. **自动任务总结更新** - 自动生成任务报告

### 高级功能
- **依赖管理** - 智能处理任务间依赖关系
- **执行状态跟踪** - 实时监控任务进度
- **项目规则初始化** - 自动配置项目开发规范
- **多语言支持** - 支持中文模板和界面

## 🔧 Cursor IDE 配置

已在 `~/.cursor/mcp.json` 中配置：

```json
{
    "mcpServers": {
        "shrimp-task-manager": {
            "command": "npx",
            "args": ["-y", "mcp-shrimp-task-manager"],
            "env": {
                "DATA_DIR": "/home/<USER>/Documents/shrimp-tasks",
                "TEMPLATES_USE": "zh",
                "ENABLE_GUI": "false"
            }
        }
    }
}
```

### 配置说明
- **DATA_DIR**: 任务数据存储目录
- **TEMPLATES_USE**: 使用中文模板
- **ENABLE_GUI**: 禁用GUI模式（命令行模式）

## 🚀 使用方法

### 1. 在Cursor中使用
重启Cursor IDE后，mcp-shrimp-task-manager工具将自动可用。您可以通过以下方式使用：

- 在对话中提及任务管理需求
- 请求任务分解和规划
- 要求进行技术调研
- 需要项目规则初始化

### 2. 常用场景

#### 任务规划
```
请使用shrimp-task-manager帮我规划一个新功能的开发任务
```

#### 技术调研
```
请使用研究模式调研React 19的新特性和兼容性问题
```

#### 项目规则初始化
```
请为当前项目初始化开发规范和规则
```

#### 任务分解
```
请将这个复杂的需求分解为可执行的子任务
```

## 📁 数据目录结构

```
~/Documents/shrimp-tasks/
├── tasks/              # 任务定义文件
├── projects/           # 项目配置文件
├── templates/          # 模板文件
├── research/           # 调研结果
└── logs/              # 执行日志
```

## 🔄 与现有工具的协同

mcp-shrimp-task-manager 与项目中已有的MCP工具形成良好互补：

### 工具协同矩阵
| 工具 | 主要功能 | 协同关系 |
|------|----------|----------|
| **Think Tool** | 思考记录 | 为任务规划提供思考基础 |
| **CodeLF** | 项目管理 | 结合项目信息进行任务规划 |
| **Feedback Enhanced** | 用户反馈 | 收集任务执行反馈 |
| **Shrimp Task Manager** | 任务管理 | 统筹协调所有工具 |

### 推荐工作流
1. **Think Tool** 记录初始想法
2. **Shrimp Task Manager** 进行任务规划和分解
3. **CodeLF** 更新项目信息
4. **Feedback Enhanced** 收集执行反馈
5. **Shrimp Task Manager** 更新任务状态和总结

## ⚠️ 注意事项

1. **重启要求**: 配置更改后需要重启Cursor IDE
2. **权限问题**: 确保数据目录有读写权限
3. **网络依赖**: 首次使用时需要网络连接下载依赖
4. **版本兼容**: 当前使用Node.js v18.20.8，可能有兼容性警告但不影响功能

## 🔍 故障排除

### 常见问题

#### 1. 工具不可用
- 检查Cursor是否重启
- 验证MCP配置文件格式
- 确认npm全局安装成功

#### 2. 数据目录问题
```bash
# 检查目录权限
ls -la ~/Documents/shrimp-tasks/

# 重新创建目录
mkdir -p ~/Documents/shrimp-tasks/
```

#### 3. 配置文件问题
```bash
# 验证配置文件
cat ~/.cursor/mcp.json | python3 -m json.tool
```

## 📈 性能优化建议

1. **定期清理**: 清理过期的任务数据和日志
2. **合理分组**: 将相关任务组织到项目中
3. **及时总结**: 完成任务后及时生成总结
4. **规范使用**: 遵循项目开发规范使用工具

## 🎯 最佳实践

### 任务命名规范
- 使用描述性名称
- 包含优先级标识
- 遵循项目命名约定

### 任务分解原则
- 单一职责原则
- 可测试性
- 明确的完成标准
- 合理的时间估算

### 协作规范
- 及时更新任务状态
- 记录重要决策过程
- 分享最佳实践经验
- 持续优化工作流程

---

**安装完成时间**: 2024-06-20  
**配置状态**: ✅ 已完成  
**测试状态**: ✅ 配置验证通过  
**文档版本**: v1.0 