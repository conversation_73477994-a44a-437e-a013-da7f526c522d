#!/usr/bin/env python3
"""
产品知识库管理页面

该页面提供：
1. 产品知识库的创建和管理
2. 文档处理和展示功能
3. FastGPT知识库同步
4. 产品文档类型选择和展示

严格遵循开发规范，使用真实API接口，禁止模拟数据。
"""

import json
import logging
import streamlit as st
from datetime import datetime
from typing import Dict, List, Optional, Any

from services.product_knowledge_manager import ProductKnowledgeManager
from services.document_processor import DocumentProcessor
from utils.database import DatabaseManager
from utils.api_client import ZKMallClient

logger = logging.getLogger(__name__)


def show_product_knowledge_management():
    """显示产品知识库管理页面"""
    try:
        st.title("🧠 产品知识库管理")
        st.markdown("---")
        
        # 初始化服务
        if 'knowledge_manager' not in st.session_state:
            st.session_state.knowledge_manager = ProductKnowledgeManager()
        if 'doc_processor' not in st.session_state:
            st.session_state.doc_processor = DocumentProcessor()
        if 'api_client' not in st.session_state:
            st.session_state.api_client = ZKMallClient()
        
        # 侧边栏功能选择
        with st.sidebar:
            st.header("功能选择")
            operation = st.selectbox(
                "选择操作",
                [
                    "产品知识库概览",
                    "创建产品知识块",
                    "批量处理产品",
                    "文档管理",
                    "知识库配置",
                    "同步状态查看"
                ]
            )
        
        # 根据选择显示对应功能
        if operation == "产品知识库概览":
            show_knowledge_overview()
        elif operation == "创建产品知识块":
            show_create_knowledge_block()
        elif operation == "批量处理产品":
            show_batch_processing()
        elif operation == "文档管理":
            show_document_management()
        elif operation == "知识库配置":
            show_knowledge_config()
        elif operation == "同步状态查看":
            show_sync_status()
            
    except Exception as e:
        st.error(f"页面加载失败: {e}")
        logger.error(f"产品知识库管理页面错误: {e}")


def show_knowledge_overview():
    """显示知识库概览"""
    try:
        st.header("📊 产品知识库概览")
        
        # 获取产品统计信息
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("总产品数", "加载中...")
        with col2:
            st.metric("已处理产品", "加载中...")
        with col3:
            st.metric("知识库数量", "加载中...")
        with col4:
            st.metric("文档总数", "加载中...")
        
        # 产品类别分布
        st.subheader("📈 产品类别分布")
        
        # 获取产品列表
        if st.button("刷新数据", key="refresh_overview"):
            with st.spinner("正在获取产品数据..."):
                products = st.session_state.api_client.get_products(pageSize=1000)
                
                if products:
                    # 统计产品类别
                    category_stats = {}
                    for product in products:
                        category = product.get("categoryName", "未分类")
                        category_stats[category] = category_stats.get(category, 0) + 1
                    
                    # 显示统计图表
                    st.bar_chart(category_stats)
                    
                    # 更新指标
                    col1.metric("总产品数", len(products))
                    
                    # 显示产品列表
                    st.subheader("📋 产品列表")
                    
                    # 搜索和筛选
                    search_term = st.text_input("搜索产品", placeholder="输入产品名称或型号")
                    category_filter = st.selectbox("筛选类别", ["全部"] + list(category_stats.keys()))
                    
                    # 过滤产品
                    filtered_products = products
                    if search_term:
                        filtered_products = [
                            p for p in filtered_products 
                            if search_term.lower() in p.get("name", "").lower() 
                            or search_term.lower() in p.get("model", "").lower()
                        ]
                    
                    if category_filter != "全部":
                        filtered_products = [
                            p for p in filtered_products 
                            if p.get("categoryName") == category_filter
                        ]
                    
                    # 显示产品表格
                    if filtered_products:
                        product_data = []
                        for product in filtered_products[:50]:  # 限制显示数量
                            product_data.append({
                                "产品名称": product.get("name", ""),
                                "产品型号": product.get("model", ""),
                                "类别": product.get("categoryName", ""),
                                "状态": product.get("status", ""),
                                "价格": product.get("price", 0)
                            })
                        
                        st.dataframe(product_data, use_container_width=True)
                    else:
                        st.info("未找到匹配的产品")
                else:
                    st.error("获取产品数据失败")
        
    except Exception as e:
        st.error(f"显示知识库概览失败: {e}")
        logger.error(f"知识库概览错误: {e}")


def show_create_knowledge_block():
    """显示创建产品知识块页面"""
    try:
        st.header("🔧 创建产品知识块")
        
        # 产品选择
        st.subheader("1. 选择产品")
        
        # 获取产品列表
        if 'products_list' not in st.session_state:
            with st.spinner("正在获取产品列表..."):
                st.session_state.products_list = st.session_state.api_client.get_products(pageSize=1000)
        
        if st.session_state.products_list:
            # 产品选择器
            product_options = {}
            for product in st.session_state.products_list:
                key = f"{product.get('name', '')} ({product.get('model', '')})"
                product_options[key] = product
            
            selected_product_key = st.selectbox(
                "选择产品",
                options=list(product_options.keys()),
                help="选择要创建知识块的产品"
            )
            
            if selected_product_key:
                selected_product = product_options[selected_product_key]
                
                # 显示产品信息
                st.subheader("2. 产品信息预览")
                
                col1, col2 = st.columns(2)
                with col1:
                    st.write("**产品名称:**", selected_product.get("name", ""))
                    st.write("**产品型号:**", selected_product.get("model", ""))
                    st.write("**产品类别:**", selected_product.get("categoryName", ""))
                
                with col2:
                    st.write("**产品状态:**", selected_product.get("status", ""))
                    st.write("**产品价格:**", selected_product.get("price", 0))
                    st.write("**品牌:**", selected_product.get("brand", ""))
                
                # 产品描述
                if selected_product.get("description"):
                    st.write("**产品描述:**")
                    st.text_area("", selected_product.get("description", ""), height=100, disabled=True)
                
                # 文档检测
                st.subheader("3. 相关文档检测")
                
                product_model = selected_product.get("model", "") or selected_product.get("name", "")
                
                if st.button("检测产品文档", key="detect_docs"):
                    with st.spinner("正在检测产品相关文档..."):
                        doc_types = st.session_state.doc_processor.get_document_types_for_display(product_model)
                        
                        if doc_types:
                            st.success(f"找到 {len(doc_types)} 种类型的文档")
                            
                            for doc_type_info in doc_types:
                                with st.expander(f"📄 {doc_type_info['document_type']} ({doc_type_info['file_count']} 个文件)"):
                                    for file_info in doc_type_info['files']:
                                        col1, col2, col3 = st.columns([3, 1, 1])
                                        with col1:
                                            st.write(f"📎 {file_info['file_name']}")
                                        with col2:
                                            st.write(f"{file_info['file_size']} bytes")
                                        with col3:
                                            if st.button("处理", key=f"process_{file_info['file_name']}"):
                                                process_single_document(product_model, file_info['file_path'])
                        else:
                            st.info("未找到相关文档")
                
                # 知识块创建
                st.subheader("4. 创建知识块")
                
                if st.button("创建产品知识块", type="primary", key="create_knowledge"):
                    with st.spinner("正在创建产品知识块..."):
                        try:
                            # 创建知识块
                            knowledge_block = st.session_state.knowledge_manager.create_product_knowledge_block(
                                product_model, selected_product
                            )
                            
                            if knowledge_block:
                                st.success("产品知识块创建成功！")
                                
                                # 显示知识块信息
                                with st.expander("📋 知识块详情", expanded=True):
                                    st.json(knowledge_block)
                                
                                # 同步到FastGPT
                                if st.button("同步到FastGPT", key="sync_to_fastgpt"):
                                    with st.spinner("正在同步到FastGPT..."):
                                        success, data_id, response = st.session_state.knowledge_manager.sync_product_to_fastgpt(
                                            product_model, knowledge_block
                                        )
                                        
                                        if success:
                                            st.success(f"同步到FastGPT成功！数据ID: {data_id}")
                                        else:
                                            st.error(f"同步到FastGPT失败: {response}")
                            else:
                                st.error("创建产品知识块失败")
                                
                        except Exception as e:
                            st.error(f"创建知识块时发生错误: {e}")
                            logger.error(f"创建知识块错误: {e}")
        else:
            st.error("获取产品列表失败")
            
    except Exception as e:
        st.error(f"显示创建知识块页面失败: {e}")
        logger.error(f"创建知识块页面错误: {e}")


def show_batch_processing():
    """显示批量处理页面"""
    try:
        st.header("⚡ 批量处理产品知识库")
        
        st.info("批量处理将为所有产品创建知识块并同步到FastGPT")
        
        # 处理配置
        col1, col2 = st.columns(2)
        
        with col1:
            batch_size = st.number_input("批次大小", min_value=1, max_value=50, value=10, help="每批处理的产品数量")
        
        with col2:
            auto_sync = st.checkbox("自动同步到FastGPT", value=True, help="是否自动同步到FastGPT知识库")
        
        # 开始批量处理
        if st.button("开始批量处理", type="primary"):
            with st.spinner("正在批量处理产品知识库..."):
                try:
                    # 创建进度条
                    progress_bar = st.progress(0)
                    status_text = st.empty()
                    
                    # 执行批量处理
                    results = st.session_state.knowledge_manager.process_all_products(batch_size=batch_size)
                    
                    # 显示结果
                    if "error" not in results:
                        st.success("批量处理完成！")
                        
                        # 显示统计信息
                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.metric("总数", results.get("total", 0))
                        with col2:
                            st.metric("成功", results.get("success", 0))
                        with col3:
                            st.metric("失败", results.get("failed", 0))
                        
                        # 显示详细结果
                        if results.get("details"):
                            with st.expander("📋 处理详情"):
                                st.json(results["details"])
                        
                        # 显示错误信息
                        if results.get("errors"):
                            with st.expander("❌ 错误信息"):
                                for error in results["errors"]:
                                    st.error(error)
                    else:
                        st.error(f"批量处理失败: {results['error']}")
                        
                except Exception as e:
                    st.error(f"批量处理时发生错误: {e}")
                    logger.error(f"批量处理错误: {e}")
        
    except Exception as e:
        st.error(f"显示批量处理页面失败: {e}")
        logger.error(f"批量处理页面错误: {e}")


def process_single_document(product_model: str, file_path: str):
    """处理单个文档"""
    try:
        with st.spinner(f"正在处理文档: {file_path}"):
            result = st.session_state.doc_processor._process_single_document(product_model, file_path)
            
            if result and result.get("status") == "success":
                st.success(f"文档处理成功: {result.get('output_path', '')}")
                
                # 显示处理结果
                if result.get("content_preview"):
                    with st.expander("📄 内容预览"):
                        st.text(result["content_preview"])
            else:
                st.error(f"文档处理失败: {result.get('error', '未知错误')}")
                
    except Exception as e:
        st.error(f"处理文档时发生错误: {e}")
        logger.error(f"处理文档错误: {e}")


def show_document_management():
    """显示文档管理页面"""
    try:
        st.header("📁 文档管理")
        st.info("管理产品相关文档，支持PDF、Word等格式的处理和转换")
        
        # 文档上传
        st.subheader("📤 文档上传")
        
        uploaded_files = st.file_uploader(
            "选择文档文件",
            type=['pdf', 'doc', 'docx', 'txt', 'md'],
            accept_multiple_files=True,
            help="支持PDF、Word、文本等格式"
        )
        
        if uploaded_files:
            product_model = st.text_input("产品型号", help="指定文档关联的产品型号")
            
            if st.button("处理上传的文档") and product_model:
                for uploaded_file in uploaded_files:
                    # 保存上传的文件
                    file_path = f"temp_docs/{uploaded_file.name}"
                    with open(file_path, "wb") as f:
                        f.write(uploaded_file.getbuffer())
                    
                    # 处理文档
                    process_single_document(product_model, file_path)
        
    except Exception as e:
        st.error(f"显示文档管理页面失败: {e}")
        logger.error(f"文档管理页面错误: {e}")


def show_knowledge_config():
    """显示知识库配置页面"""
    try:
        st.header("⚙️ 知识库配置")
        
        st.subheader("📊 产品类别与知识库映射")
        
        # 显示当前配置
        knowledge_manager = st.session_state.knowledge_manager
        current_mapping = knowledge_manager.category_knowledge_mapping
        
        if current_mapping:
            st.write("**当前配置:**")
            for category, dataset_id in current_mapping.items():
                col1, col2 = st.columns([1, 2])
                with col1:
                    st.write(f"**{category}**")
                with col2:
                    st.code(dataset_id)
        else:
            st.warning("未配置产品类别与知识库的映射关系")
        
        # 配置说明
        st.subheader("📝 配置说明")
        st.markdown("""
        通过环境变量配置产品类别与FastGPT知识库的映射关系：
        
        ```bash
        # 门禁类产品
        FASTGPT_ACCESS_CONTROL_DATASET_ID=your_dataset_id_here
        FASTGPT_ATTENDANCE_DATASET_ID=your_dataset_id_here
        FASTGPT_FACE_RECOGNITION_DATASET_ID=your_dataset_id_here
        
        # 安检类产品
        FASTGPT_SECURITY_CHECK_DATASET_ID=your_dataset_id_here
        FASTGPT_XRAY_DATASET_ID=your_dataset_id_here
        FASTGPT_METAL_DETECTOR_DATASET_ID=your_dataset_id_here
        
        # 智能硬件
        FASTGPT_SMART_LOCK_DATASET_ID=your_dataset_id_here
        FASTGPT_CAMERA_DATASET_ID=your_dataset_id_here
        FASTGPT_SENSOR_DATASET_ID=your_dataset_id_here
        
        # 默认分类
        FASTGPT_DEFAULT_PRODUCT_DATASET_ID=your_dataset_id_here
        ```
        """)
        
    except Exception as e:
        st.error(f"显示知识库配置页面失败: {e}")
        logger.error(f"知识库配置页面错误: {e}")


def show_sync_status():
    """显示同步状态页面"""
    try:
        st.header("🔄 同步状态查看")
        st.info("查看产品知识库与FastGPT的同步状态")
        
        # 这里可以添加同步状态的查询和显示逻辑
        st.write("功能开发中...")
        
    except Exception as e:
        st.error(f"显示同步状态页面失败: {e}")
        logger.error(f"同步状态页面错误: {e}")


if __name__ == "__main__":
    show_product_knowledge_management()
