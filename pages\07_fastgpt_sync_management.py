"""
FastGPT知识库同步管理页面

此页面提供完整的FastGPT知识库同步功能：
1. Excel文件上传和解析（第二列为知识库ID）
2. 产品型号智能匹配
3. 同步到FastGPT知识库（一个产品型号对应一个集合）
4. 同步状态监控和日志查看

注意：案例只在现有模块页面展示，暂时不做同步
"""

import streamlit as st
import pandas as pd
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional

from services.excel_import_service import ExcelImportService
from services.product_model_matcher import ProductModelMatcher
from services.fastgpt_sync_service import FastGPTSyncService
from utils.session import SessionManager
from config import get_config

logger = logging.getLogger(__name__)
config = get_config()


def main():
    """主函数"""
    st.set_page_config(
        page_title="FastGPT知识库同步管理",
        page_icon="🔄",
        layout="wide",
        initial_sidebar_state="expanded",
    )

    # 初始化session
    initialize_session()

    # 渲染页面
    render_header()
    render_sidebar()
    render_content()
    render_footer()


def initialize_session():
    """初始化页面session状态"""
    defaults = {
        "current_tab": "excel_import",
        "import_batch_id": "",
        "matched_products": [],
        "sync_results": {},
        "excel_file_uploaded": False,
        "matching_completed": False,
        "sync_completed": False,
    }

    for key, value in defaults.items():
        if key not in st.session_state:
            SessionManager.set(key, value)


def render_header():
    """渲染页面头部"""
    st.title("🔄 FastGPT知识库同步管理")
    st.markdown("管理产品信息同步到FastGPT知识库的完整流程")

    # 重要说明
    with st.expander("📋 重要说明"):
        st.markdown(
            """
        **同步规则：**
        - 📄 **Excel格式**：第一列为产品型号，第二列为FastGPT知识库ID
        - 🔗 **同步方向**：从云商系统同步到FastGPT知识库
        - 📦 **产品组织**：一个产品型号对应一个FastGPT集合
        - 📝 **内容格式**：每个集合只有一个知识片段，内容为Markdown格式
        - 🏷️ **索引规则**：型号+名称拼接作为知识片段的索引
        - ⚠️ **案例模块**：暂时只在现有页面展示，不参与FastGPT同步
        """
        )


def render_sidebar():
    """渲染侧边栏"""
    with st.sidebar:
        st.subheader("📋 操作流程")

        # 流程步骤指示器
        steps = [
            ("1️⃣", "Excel导入", SessionManager.get("excel_file_uploaded")),
            ("2️⃣", "型号匹配", SessionManager.get("matching_completed")),
            ("3️⃣", "同步FastGPT", SessionManager.get("sync_completed")),
            ("4️⃣", "查看日志", False),
        ]

        for icon, title, completed in steps:
            if completed:
                st.markdown(f"{icon} {title} ✅")
            else:
                st.markdown(f"{icon} {title}")

        st.markdown("---")

        # 快速统计
        st.subheader("📊 快速统计")
        try:
            sync_service = FastGPTSyncService()
            pending_count = sync_service.get_pending_sync_count()
            st.metric("待同步产品", pending_count)
        except Exception as e:
            st.error(f"获取统计失败: {e}")


def render_content():
    """渲染主要内容"""
    tab1, tab2, tab3, tab4 = st.tabs(
        ["📁 Excel导入", "🔍 型号匹配", "🔄 FastGPT同步", "📋 同步日志"]
    )

    with tab1:
        render_excel_import_tab()

    with tab2:
        render_model_matching_tab()

    with tab3:
        render_fastgpt_sync_tab()

    with tab4:
        render_sync_logs_tab()


def render_excel_import_tab():
    """渲染Excel导入标签页"""
    st.subheader("📁 Excel文件导入")
    st.markdown("上传包含产品型号和知识库ID的Excel文件")

    # 文件格式说明
    with st.expander("📖 文件格式说明"):
        st.markdown(
            """
        **Excel文件格式要求：**
        - **第一列**：产品型号（必填）
        - **第二列**：FastGPT知识库ID（可选，空值使用默认知识库）
        - 支持.xlsx和.xls格式
        - 第一行为标题行
        
        **示例：**
        | 产品型号 | 知识库ID |
        |---------|---------|
        | ZK-C3-001  | 676a1dac3f9f3c0018a4b123 |
        | ZK-D4-002  |  |
        """
        )

    # 文件上传
    uploaded_file = st.file_uploader(
        "选择Excel文件", type=["xlsx", "xls"], help="第二列为FastGPT知识库ID"
    )

    if uploaded_file is not None:
        # 配置选项
        col1, col2, col3 = st.columns(3)
        with col1:
            header_row = st.number_input("标题行号", min_value=1, value=1)
        with col2:
            model_column = st.text_input("产品型号列名", value="产品型号")
        with col3:
            dataset_column = st.text_input("知识库ID列名", value="知识库ID")

        # 预览数据
        if st.button("🔍 预览Excel数据"):
            try:
                excel_service = ExcelImportService()
                success, preview_data = excel_service.preview_excel_data(
                    uploaded_file, header_row=header_row, preview_rows=10
                )

                if success and preview_data.get("preview_data"):
                    st.success("✅ Excel文件预览成功")
                    df = pd.DataFrame(preview_data["preview_data"])
                    st.dataframe(df, use_container_width=True)
                else:
                    st.error("❌ 预览失败")
            except Exception as e:
                st.error(f"❌ 预览异常: {str(e)}")

        # 执行导入
        if st.button("📥 开始导入", type="primary"):
            try:
                with st.spinner("正在处理Excel文件..."):
                    excel_service = ExcelImportService()
                    batch_id, result = excel_service.process_uploaded_file(
                        uploaded_file,
                        header_row=header_row,
                        model_column=model_column,
                        dataset_id_column=dataset_column,
                        user_id="system",
                    )

                if result.get("success"):
                    st.success(f"✅ 导入成功！批次ID: {batch_id}")
                    SessionManager.set("import_batch_id", batch_id)
                    SessionManager.set("excel_file_uploaded", True)

                    # 显示导入结果
                    col1, col2 = st.columns(2)
                    with col1:
                        st.metric("导入行数", result["total_rows"])
                    with col2:
                        st.metric("批次ID", batch_id)
                else:
                    st.error(f"❌ 导入失败: {result.get('message', '未知错误')}")
            except Exception as e:
                st.error(f"❌ 导入异常: {str(e)}")


def render_model_matching_tab():
    """渲染型号匹配标签页"""
    st.subheader("🔍 产品型号匹配")

    batch_id = SessionManager.get("import_batch_id")
    if not batch_id:
        st.warning("⚠️ 请先完成Excel文件导入")
        return

    st.info(f"📋 当前批次: {batch_id}")

    # 匹配配置
    col1, col2 = st.columns(2)
    with col1:
        confidence_threshold = st.slider("匹配置信度阈值", 0.1, 1.0, 0.8, 0.1)
    with col2:
        max_results = st.number_input("每个型号最大匹配数", 1, 10, 3)

    # 执行匹配
    if st.button("🎯 开始匹配", type="primary"):
        try:
            with st.spinner("正在进行产品型号匹配..."):
                matcher = ProductModelMatcher()
                match_result = matcher.match_models_from_excel(
                    batch_id=batch_id,
                    confidence_threshold=confidence_threshold,
                    max_results_per_model=max_results,
                )

            if match_result.get("success"):
                st.success("✅ 匹配完成！")
                SessionManager.set("matching_completed", True)

                # 显示匹配统计
                col1, col2, col3 = st.columns(3)
                with col1:
                    st.metric("总型号数", match_result.get("total_models", 0))
                with col2:
                    st.metric("成功匹配", match_result.get("matched_count", 0))
                with col3:
                    st.metric("未匹配", match_result.get("unmatched_count", 0))
            else:
                st.error(f"❌ 匹配失败: {match_result.get('message', '未知错误')}")
        except Exception as e:
            st.error(f"❌ 匹配异常: {str(e)}")


def render_fastgpt_sync_tab():
    """渲染FastGPT同步标签页"""
    st.subheader("🔄 FastGPT知识库同步")

    if not SessionManager.get("matching_completed"):
        st.warning("⚠️ 请先完成产品型号匹配")
        return

    # 同步规则说明
    with st.expander("📋 同步规则说明"):
        st.markdown(
            """
        **FastGPT同步规则：**
        - 🔗 **一对一映射**：一个产品型号对应FastGPT中的一个集合
        - 📝 **单一知识片段**：每个集合只包含一个知识片段
        - 📄 **Markdown格式**：产品信息组织成Markdown格式
        - 🏷️ **索引规则**：使用"型号 + 产品名称"作为知识片段的索引
        """
        )

    # 获取待同步统计
    try:
        sync_service = FastGPTSyncService()
        pending_count = sync_service.get_pending_sync_count()

        col1, col2 = st.columns(2)
        with col1:
            st.metric("待同步产品", pending_count)
        with col2:
            completed_count = sync_service.get_completed_sync_count()
            st.metric("已同步产品", completed_count)
    except Exception as e:
        st.error(f"❌ 获取统计失败: {str(e)}")
        return

    # 执行同步
    if pending_count > 0:
        if st.button("🚀 开始同步", type="primary"):
            try:
                with st.spinner("正在同步到FastGPT..."):
                    sync_service = FastGPTSyncService()
                    sync_result = sync_service.sync_products_to_fastgpt(batch_size=5)

                if sync_result.get("success", 0) > 0:
                    st.success("✅ 同步成功！")
                    SessionManager.set("sync_completed", True)

                    # 显示同步统计
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("总数", sync_result.get("total", 0))
                    with col2:
                        st.metric("成功", sync_result.get("success", 0))
                    with col3:
                        st.metric("失败", sync_result.get("failed", 0))
                else:
                    st.warning("⚠️ 没有产品成功同步")
            except Exception as e:
                st.error(f"❌ 同步异常: {str(e)}")
    else:
        st.info("📭 暂无待同步的产品")


def render_sync_logs_tab():
    """渲染同步日志标签页"""
    st.subheader("📋 同步日志")

    # 日志筛选
    col1, col2 = st.columns(2)
    with col1:
        status_filter = st.selectbox(
            "状态筛选",
            ["all", "success", "failed"],
            format_func=lambda x: {
                "all": "全部",
                "success": "成功",
                "failed": "失败",
            }.get(x, x),
        )
    with col2:
        limit = st.number_input("显示条数", min_value=10, max_value=100, value=20)

    # 获取日志
    if st.button("🔍 查询日志"):
        try:
            sync_service = FastGPTSyncService()
            logs = sync_service.get_sync_logs(
                status=None if status_filter == "all" else status_filter, limit=limit
            )

            if logs:
                logs_df = pd.DataFrame(logs)
                st.dataframe(logs_df, use_container_width=True)
            else:
                st.info("📭 暂无日志记录")
        except Exception as e:
            st.error(f"❌ 查询日志失败: {str(e)}")


def render_footer():
    """渲染页面脚部"""
    st.markdown("---")
    st.markdown("© 2024 云商系统 - FastGPT知识库同步管理")


if __name__ == "__main__":
    main()
