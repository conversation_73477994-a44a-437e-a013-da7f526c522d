"""
FastGPT知识库管理服务

该服务用于管理FastGPT知识库，包括创建知识库、上传文档、查询文档等功能。
严格遵循开发规范，使用真实API接口，禁止模拟数据。
"""

import hashlib
import json
import logging
import requests
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from config import config

logger = logging.getLogger(__name__)


class FastGPTKnowledgeService:
    """FastGPT知识库管理服务"""

    def __init__(self):
        """初始化服务"""
        fastgpt_config = config.get_fastgpt_config()
        self.api_base = fastgpt_config.get("api_base")
        self.api_token = fastgpt_config.get("api_key")
        self.headers = {
            "Authorization": f"Bearer {self.api_token}",
            "Content-Type": "application/json",
        }

        if not self.api_token:
            logger.warning("FastGPT API Token未配置，某些功能可能不可用")

    def create_dataset(
        self,
        name: str,
        description: str = "",
        tags: Optional[List[str]] = None,
        parent_id: str = "",
        avatar: str = "",
    ) -> Tuple[bool, str, Dict[str, Any]]:
        """
        创建FastGPT知识库

        Args:
            name: 知识库名称
            description: 知识库描述
            tags: 标签列表
            parent_id: 父知识库ID
            avatar: 头像URL

        Returns:
            Tuple[是否成功, 知识库ID, 响应数据]
        """
        try:
            if not self.api_token:
                return False, "", {"error": "FastGPT API Token未配置"}

            # 构建请求数据
            payload = {
                "name": name,
                "type": "dataset",  # 知识库类型
            }

            if description:
                payload["intro"] = description

            if tags:
                payload["tags"] = tags

            if parent_id:
                payload["parentId"] = parent_id

            if avatar:
                payload["avatar"] = avatar

            # 发送创建请求
            url = f"{self.api_base}/api/core/dataset/create"

            logger.info(f"创建FastGPT知识库: {name}")
            response = requests.post(
                url, headers=self.headers, json=payload, timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                dataset_id = result.get("data", {}).get("id", "")

                if dataset_id:
                    logger.info(f"FastGPT知识库创建成功: {name} (ID: {dataset_id})")
                    return True, dataset_id, result
                else:
                    logger.error(f"FastGPT知识库创建失败，未返回ID: {result}")
                    return False, "", result
            else:
                error_msg = (
                    f"FastGPT API请求失败: {response.status_code} - {response.text}"
                )
                logger.error(error_msg)
                return False, "", {"error": error_msg}

        except Exception as e:
            error_msg = f"创建FastGPT知识库异常: {str(e)}"
            logger.error(error_msg)
            return False, "", {"error": error_msg}

    def upload_text_data(
        self,
        dataset_id: str,
        content: str,
        title: str = "",
        source: str = "",
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Tuple[bool, str, Dict[str, Any]]:
        """
        上传文本数据到知识库

        Args:
            dataset_id: 知识库ID
            content: 文本内容
            title: 文档标题
            source: 数据来源
            metadata: 元数据

        Returns:
            Tuple[是否成功, 数据ID, 响应数据]
        """
        try:
            if not self.api_token:
                return False, "", {"error": "FastGPT API Token未配置"}

            if not content.strip():
                return False, "", {"error": "文本内容不能为空"}

            # 构建请求数据
            payload = {
                "datasetId": dataset_id,
                "data": [
                    {
                        "q": title or "产品信息",  # 问题/标题
                        "a": content,  # 答案/内容
                        "source": source or "Excel导入",
                    }
                ],
            }

            if metadata:
                payload["data"][0]["metadata"] = metadata

            # 发送上传请求
            url = f"{self.api_base}/api/core/dataset/data/pushData"

            logger.info(f"上传文本数据到FastGPT知识库: {dataset_id}")
            response = requests.post(
                url, headers=self.headers, json=payload, timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                data_id = result.get("data", {}).get("insertId", "")

                logger.info(f"文本数据上传成功: {title} (ID: {data_id})")
                return True, data_id, result
            else:
                error_msg = (
                    f"FastGPT数据上传失败: {response.status_code} - {response.text}"
                )
                logger.error(error_msg)
                return False, "", {"error": error_msg}

        except Exception as e:
            error_msg = f"上传文本数据异常: {str(e)}"
            logger.error(error_msg)
            return False, "", {"error": error_msg}

    def upload_markdown_data(
        self,
        dataset_id: str,
        markdown_content: str,
        title: str = "",
        source: str = "Excel转换",
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Tuple[bool, str, Dict[str, Any]]:
        """
        上传Markdown格式数据到知识库

        Args:
            dataset_id: 知识库ID
            markdown_content: Markdown内容
            title: 文档标题
            source: 数据来源
            metadata: 元数据

        Returns:
            Tuple[是否成功, 数据ID, 响应数据]
        """
        return self.upload_text_data(
            dataset_id=dataset_id,
            content=markdown_content,
            title=title,
            source=source,
            metadata=metadata,
        )

    def get_dataset_list(
        self, parent_id: str = ""
    ) -> Tuple[bool, List[Dict[str, Any]]]:
        """
        获取知识库列表

        Args:
            parent_id: 父知识库ID

        Returns:
            Tuple[是否成功, 知识库列表]
        """
        try:
            if not self.api_token:
                return False, []

            url = f"{self.api_base}/api/core/dataset/list"
            params = {}
            if parent_id:
                params["parentId"] = parent_id

            response = requests.get(
                url, headers=self.headers, params=params, timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                datasets = result.get("data", [])
                logger.info(f"获取到 {len(datasets)} 个知识库")
                return True, datasets
            else:
                error_msg = (
                    f"获取知识库列表失败: {response.status_code} - {response.text}"
                )
                logger.error(error_msg)
                return False, []

        except Exception as e:
            error_msg = f"获取知识库列表异常: {str(e)}"
            logger.error(error_msg)
            return False, []

    def get_dataset_info(self, dataset_id: str) -> Tuple[bool, Dict[str, Any]]:
        """
        获取知识库详细信息

        Args:
            dataset_id: 知识库ID

        Returns:
            Tuple[是否成功, 知识库信息]
        """
        try:
            if not self.api_token:
                return False, {}

            url = f"{self.api_base}/api/core/dataset/detail"
            params = {"id": dataset_id}

            response = requests.get(
                url, headers=self.headers, params=params, timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                dataset_info = result.get("data", {})
                logger.info(f"获取知识库信息成功: {dataset_id}")
                return True, dataset_info
            else:
                error_msg = (
                    f"获取知识库信息失败: {response.status_code} - {response.text}"
                )
                logger.error(error_msg)
                return False, {}

        except Exception as e:
            error_msg = f"获取知识库信息异常: {str(e)}"
            logger.error(error_msg)
            return False, {}

    def delete_dataset(self, dataset_id: str) -> Tuple[bool, Dict[str, Any]]:
        """
        删除知识库

        Args:
            dataset_id: 知识库ID

        Returns:
            Tuple[是否成功, 响应数据]
        """
        try:
            if not self.api_token:
                return False, {"error": "FastGPT API Token未配置"}

            url = f"{self.api_base}/api/core/dataset/delete"
            payload = {"id": dataset_id}

            response = requests.post(
                url, headers=self.headers, json=payload, timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                logger.info(f"知识库删除成功: {dataset_id}")
                return True, result
            else:
                error_msg = f"删除知识库失败: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return False, {"error": error_msg}

        except Exception as e:
            error_msg = f"删除知识库异常: {str(e)}"
            logger.error(error_msg)
            return False, {"error": error_msg}

    def search_dataset_data(
        self, dataset_id: str, query: str, limit: int = 10
    ) -> Tuple[bool, List[Dict[str, Any]]]:
        """
        搜索知识库数据

        Args:
            dataset_id: 知识库ID
            query: 搜索查询
            limit: 结果数量限制

        Returns:
            Tuple[是否成功, 搜索结果列表]
        """
        try:
            if not self.api_token:
                return False, []

            url = f"{self.api_base}/api/core/dataset/searchTest"
            payload = {
                "datasetId": dataset_id,
                "text": query,
                "limit": limit,
            }

            response = requests.post(
                url, headers=self.headers, json=payload, timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                search_results = result.get("data", [])
                logger.info(f"搜索到 {len(search_results)} 条结果")
                return True, search_results
            else:
                error_msg = (
                    f"搜索知识库数据失败: {response.status_code} - {response.text}"
                )
                logger.error(error_msg)
                return False, []

        except Exception as e:
            error_msg = f"搜索知识库数据异常: {str(e)}"
            logger.error(error_msg)
            return False, []

    def batch_upload_product_data(
        self,
        dataset_id: str,
        products_data: List[Dict[str, Any]],
        source: str = "Excel批量导入",
    ) -> Tuple[bool, List[str], Dict[str, Any]]:
        """
        批量上传产品数据到知识库

        Args:
            dataset_id: 知识库ID
            products_data: 产品数据列表
            source: 数据来源

        Returns:
            Tuple[是否成功, 数据ID列表, 结果统计]
        """
        success_ids = []
        failed_count = 0
        results = {
            "total": len(products_data),
            "success": 0,
            "failed": 0,
            "details": [],
        }

        for i, product in enumerate(products_data):
            try:
                # 转换产品数据为Markdown格式
                markdown_content = self._convert_product_to_markdown(product)

                title = f"产品信息 - {product.get('model', product.get('name', f'Product_{i+1}'))}"

                success, data_id, response = self.upload_markdown_data(
                    dataset_id=dataset_id,
                    markdown_content=markdown_content,
                    title=title,
                    source=source,
                    metadata={"product_model": product.get("model", ""), "index": i},
                )

                if success:
                    success_ids.append(data_id)
                    results["success"] += 1
                    logger.info(f"产品 {title} 上传成功")
                else:
                    failed_count += 1
                    results["failed"] += 1
                    logger.error(f"产品 {title} 上传失败: {response}")

                results["details"].append(
                    {
                        "index": i,
                        "title": title,
                        "success": success,
                        "data_id": data_id if success else "",
                        "error": response.get("error", "") if not success else "",
                    }
                )

            except Exception as e:
                failed_count += 1
                results["failed"] += 1
                error_msg = f"处理产品数据异常: {str(e)}"
                logger.error(error_msg)
                results["details"].append(
                    {
                        "index": i,
                        "title": f"Product_{i+1}",
                        "success": False,
                        "data_id": "",
                        "error": error_msg,
                    }
                )

        # 计算成功率
        success_rate = (
            (results["success"] / results["total"]) * 100 if results["total"] > 0 else 0
        )
        results["success_rate"] = round(success_rate, 2)

        logger.info(
            f"批量上传完成: 总数 {results['total']}, 成功 {results['success']}, 失败 {results['failed']}, 成功率 {results['success_rate']}%"
        )

        return len(success_ids) > 0, success_ids, results

    def _convert_product_to_markdown(self, product: Dict[str, Any]) -> str:
        """
        将产品数据转换为Markdown格式

        Args:
            product: 产品数据字典

        Returns:
            Markdown格式的产品信息
        """
        markdown_lines = []

        # 产品标题
        name = product.get("name", product.get("productName", "未知产品"))
        model = product.get("model", product.get("productModel", ""))

        if model:
            markdown_lines.append(f"# {name} (型号: {model})")
        else:
            markdown_lines.append(f"# {name}")

        markdown_lines.append("")

        # 基本信息
        markdown_lines.append("## 基本信息")
        markdown_lines.append("")

        # 产品型号
        if model:
            markdown_lines.append(f"**产品型号**: {model}")

        # 产品名称
        markdown_lines.append(f"**产品名称**: {name}")

        # 分类信息
        if product.get("categoryName"):
            markdown_lines.append(f"**产品分类**: {product.get('categoryName')}")

        # 品牌信息
        if product.get("brandName"):
            markdown_lines.append(f"**品牌**: {product.get('brandName')}")

        # 价格信息
        if product.get("price"):
            markdown_lines.append(f"**价格**: ¥{product.get('price')}")

        markdown_lines.append("")

        # 产品描述
        if product.get("description") or product.get("detail"):
            markdown_lines.append("## 产品描述")
            markdown_lines.append("")
            description = product.get("description", product.get("detail", ""))
            markdown_lines.append(description)
            markdown_lines.append("")

        # 规格参数
        if product.get("specification") or product.get("spec"):
            markdown_lines.append("## 规格参数")
            markdown_lines.append("")
            spec = product.get("specification", product.get("spec", ""))
            markdown_lines.append(spec)
            markdown_lines.append("")

        # 应用场景
        if product.get("application"):
            markdown_lines.append("## 应用场景")
            markdown_lines.append("")
            markdown_lines.append(product.get("application"))
            markdown_lines.append("")

        # 技术特性
        if product.get("features"):
            markdown_lines.append("## 技术特性")
            markdown_lines.append("")
            features = product.get("features")
            if isinstance(features, list):
                for feature in features:
                    markdown_lines.append(f"- {feature}")
            else:
                markdown_lines.append(features)
            markdown_lines.append("")

        # 其他信息
        other_fields = {
            "manufacturer": "制造商",
            "warranty": "质保信息",
            "certification": "认证信息",
            "weight": "重量",
            "dimensions": "尺寸",
            "power": "功率",
            "voltage": "电压",
            "temperature": "工作温度",
            "humidity": "工作湿度",
        }

        other_info = []
        for field, label in other_fields.items():
            if product.get(field):
                other_info.append(f"**{label}**: {product.get(field)}")

        if other_info:
            markdown_lines.append("## 其他信息")
            markdown_lines.append("")
            markdown_lines.extend(other_info)
            markdown_lines.append("")

        # 更新时间
        markdown_lines.append("---")
        markdown_lines.append(
            f"*数据更新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*"
        )

        return "\n".join(markdown_lines)

    def create_product_knowledge_block(
        self,
        product_model: str,
        product_data: Dict[str, Any],
        documents: Optional[List[Dict[str, Any]]] = None,
        category_dataset_id: str = "",
    ) -> Tuple[bool, str, Dict[str, Any]]:
        """
        创建产品知识块，包含产品信息、文档索引、标签和摘要

        Args:
            product_model: 产品型号
            product_data: 产品基础数据
            documents: 相关文档列表
            category_dataset_id: 分类知识库ID

        Returns:
            Tuple[是否成功, 知识块ID, 详细结果]
        """
        try:
            logger.info(f"开始创建产品知识块: {product_model}")

            # 1. 生成产品标签
            tags = self._generate_product_tags(product_data, documents)

            # 2. 生成产品摘要
            summary = self._generate_product_summary(product_data, documents)

            # 3. 构建完整的产品知识内容
            knowledge_content = self._build_comprehensive_product_content(
                product_model, product_data, documents, summary
            )

            # 4. 生成索引关键词
            index_keywords = self._generate_index_keywords(product_data, documents)

            # 5. 确定目标知识库
            target_dataset_id = category_dataset_id or self._get_default_dataset_id()

            if not target_dataset_id:
                # 创建新的产品知识库
                success, dataset_id, _ = self.create_dataset(
                    name=f"产品知识库-{product_model}",
                    description=f"{product_model}产品的完整知识库，包含产品信息、文档、规格等",
                    tags=tags[:5],  # 限制标签数量
                )

                if not success:
                    return False, "", {"error": "创建产品知识库失败"}

                target_dataset_id = dataset_id

            # 6. 上传知识块内容
            title = f"{product_model} 产品知识块"

            metadata = {
                "product_model": product_model,
                "tags": tags,
                "summary": summary,
                "index_keywords": index_keywords,
                "document_count": len(documents) if documents else 0,
                "created_at": datetime.now().isoformat(),
                "content_type": "product_knowledge_block",
            }

            success, data_id, response = self.upload_markdown_data(
                dataset_id=target_dataset_id,
                markdown_content=knowledge_content,
                title=title,
                source="产品知识块生成",
                metadata=metadata,
            )

            if success:
                result = {
                    "knowledge_block_id": data_id,
                    "dataset_id": target_dataset_id,
                    "product_model": product_model,
                    "tags": tags,
                    "summary": summary,
                    "index_keywords": index_keywords,
                    "content_length": len(knowledge_content),
                    "document_count": len(documents) if documents else 0,
                }

                logger.info(f"产品知识块创建成功: {product_model} (ID: {data_id})")
                return True, data_id, result
            else:
                logger.error(f"产品知识块上传失败: {response}")
                return False, "", response

        except Exception as e:
            error_msg = f"创建产品知识块失败: {str(e)}"
            logger.error(error_msg)
            return False, "", {"error": error_msg}

    def _generate_product_tags(
        self,
        product_data: Dict[str, Any],
        documents: Optional[List[Dict[str, Any]]] = None,
    ) -> List[str]:
        """生成产品标签以提升召回率"""
        try:
            tags = set()

            # 基础标签
            if product_data.get("model"):
                tags.add(product_data["model"])
                # 提取型号中的数字和字母组合
                import re

                model_parts = re.findall(r"[A-Z]+|\d+", product_data["model"])
                tags.update(model_parts)

            if product_data.get("categoryName"):
                tags.add(product_data["categoryName"])

            if product_data.get("brandName"):
                tags.add(product_data["brandName"])

            # 从产品名称提取关键词
            if product_data.get("name"):
                name_keywords = self._extract_keywords_from_text(product_data["name"])
                tags.update(name_keywords)

            # 从描述提取关键词
            if product_data.get("description"):
                desc_keywords = self._extract_keywords_from_text(
                    product_data["description"]
                )
                tags.update(desc_keywords[:5])  # 限制数量

            # 从文档类型生成标签
            if documents:
                doc_types = set()
                for doc in documents:
                    if doc.get("document_type"):
                        doc_types.add(doc["document_type"])
                tags.update(doc_types)

            # 技术特性标签
            tech_keywords = [
                "门禁",
                "考勤",
                "人脸识别",
                "指纹",
                "刷卡",
                "密码",
                "生物识别",
                "安检",
                "金属探测",
                "X光",
                "安全检查",
                "智能锁",
                "摄像头",
                "传感器",
            ]

            product_text = (
                f"{product_data.get('name', '')} {product_data.get('description', '')}"
            )
            for keyword in tech_keywords:
                if keyword in product_text:
                    tags.add(keyword)

            return list(tags)[:20]  # 限制标签总数

        except Exception as e:
            logger.error(f"生成产品标签失败: {e}")
            return [product_data.get("model", "未知产品")]

    def _generate_product_summary(
        self,
        product_data: Dict[str, Any],
        documents: Optional[List[Dict[str, Any]]] = None,
    ) -> str:
        """生成产品摘要"""
        try:
            summary_parts = []

            # 基本信息摘要
            name = product_data.get("name", "")
            model = product_data.get("model", "")
            category = product_data.get("categoryName", "")

            if name and model:
                summary_parts.append(f"{name}（型号：{model}）")
            elif name:
                summary_parts.append(name)
            elif model:
                summary_parts.append(f"型号：{model}")

            if category:
                summary_parts.append(f"属于{category}类产品")

            # 功能特性摘要
            if product_data.get("description"):
                desc = product_data["description"][:100]  # 限制长度
                if len(product_data["description"]) > 100:
                    desc += "..."
                summary_parts.append(desc)

            # 文档信息摘要
            if documents:
                doc_count = len(documents)
                doc_types = set(doc.get("document_type", "文档") for doc in documents)
                doc_types_str = "、".join(list(doc_types)[:3])
                summary_parts.append(f"包含{doc_count}个相关文档（{doc_types_str}等）")

            summary = "。".join(summary_parts)
            if not summary.endswith("。"):
                summary += "。"

            return summary

        except Exception as e:
            logger.error(f"生成产品摘要失败: {e}")
            return f"{product_data.get('name', product_data.get('model', '产品'))}的相关信息。"

    def _generate_index_keywords(
        self,
        product_data: Dict[str, Any],
        documents: Optional[List[Dict[str, Any]]] = None,
    ) -> List[str]:
        """生成索引关键词以提升搜索召回率"""
        try:
            keywords = set()

            # 产品型号的各种变体
            if product_data.get("model"):
                model = product_data["model"]
                keywords.add(model)
                keywords.add(model.upper())
                keywords.add(model.lower())
                keywords.add(model.replace("-", ""))
                keywords.add(model.replace("_", ""))

                # 型号分解
                import re

                parts = re.split(r"[-_]", model)
                keywords.update(parts)

            # 产品名称关键词
            if product_data.get("name"):
                name_keywords = self._extract_keywords_from_text(product_data["name"])
                keywords.update(name_keywords)

            # 分类关键词
            if product_data.get("categoryName"):
                keywords.add(product_data["categoryName"])

            # 品牌关键词
            if product_data.get("brandName"):
                keywords.add(product_data["brandName"])

            # 从文档标题提取关键词
            if documents:
                for doc in documents:
                    if doc.get("file_name"):
                        doc_keywords = self._extract_keywords_from_text(
                            doc["file_name"]
                        )
                        keywords.update(doc_keywords[:3])  # 限制每个文档的关键词数量

            return list(keywords)[:30]  # 限制总关键词数量

        except Exception as e:
            logger.error(f"生成索引关键词失败: {e}")
            return [product_data.get("model", "产品")]

    def _extract_keywords_from_text(self, text: str) -> List[str]:
        """从文本中提取关键词"""
        try:
            import re

            # 清理文本
            text = re.sub(r"[^\w\s\u4e00-\u9fff]", " ", text)

            # 分词（简单的基于空格和常见分隔符）
            words = re.split(r"[\s\-_]+", text)

            # 过滤关键词
            keywords = []
            for word in words:
                word = word.strip()
                if len(word) >= 2 and word not in [
                    "的",
                    "和",
                    "与",
                    "或",
                    "及",
                    "等",
                    "型",
                    "款",
                ]:
                    keywords.append(word)

            return keywords[:10]  # 限制数量

        except Exception as e:
            logger.error(f"提取关键词失败: {e}")
            return []

    def _build_comprehensive_product_content(
        self,
        product_model: str,
        product_data: Dict[str, Any],
        documents: Optional[List[Dict[str, Any]]] = None,
        summary: str = "",
    ) -> str:
        """构建完整的产品知识内容"""
        try:
            content_lines = []

            # 标题和摘要
            content_lines.append(f"# {product_model} 产品知识块")
            content_lines.append("")

            if summary:
                content_lines.append("## 产品摘要")
                content_lines.append("")
                content_lines.append(summary)
                content_lines.append("")

            # 基础产品信息
            product_markdown = self._convert_product_to_markdown(product_data)
            content_lines.append(product_markdown)
            content_lines.append("")

            # 文档索引
            if documents:
                content_lines.append("## 相关文档索引")
                content_lines.append("")

                # 按文档类型分组
                doc_groups = {}
                for doc in documents:
                    doc_type = doc.get("document_type", "其他文档")
                    if doc_type not in doc_groups:
                        doc_groups[doc_type] = []
                    doc_groups[doc_type].append(doc)

                for doc_type, type_docs in doc_groups.items():
                    content_lines.append(f"### {doc_type}")
                    content_lines.append("")

                    for doc in type_docs:
                        file_name = doc.get("file_name", "未知文档")
                        file_path = doc.get("file_path", "")
                        file_size = doc.get("file_size", 0)

                        size_str = f"({file_size} bytes)" if file_size else ""
                        content_lines.append(f"- **{file_name}** {size_str}")

                        if file_path:
                            content_lines.append(f"  - 路径: `{file_path}`")

                        if doc.get("relevance_score"):
                            content_lines.append(
                                f"  - 相关性: {doc['relevance_score']:.2f}"
                            )

                    content_lines.append("")

            # 搜索优化关键词（隐藏在注释中）
            index_keywords = self._generate_index_keywords(product_data, documents)
            if index_keywords:
                content_lines.append("<!-- 搜索关键词:")
                content_lines.append(" ".join(index_keywords))
                content_lines.append("-->")
                content_lines.append("")

            # 元数据信息
            content_lines.append("---")
            content_lines.append("")
            content_lines.append("## 元数据信息")
            content_lines.append("")
            content_lines.append(f"- **产品型号**: {product_model}")
            content_lines.append(f"- **知识块类型**: 产品综合信息")
            content_lines.append(
                f"- **文档数量**: {len(documents) if documents else 0}"
            )
            content_lines.append(
                f"- **创建时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            )
            content_lines.append("")

            return "\n".join(content_lines)

        except Exception as e:
            logger.error(f"构建产品知识内容失败: {e}")
            return self._convert_product_to_markdown(product_data)

    def _get_default_dataset_id(self) -> str:
        """获取默认的产品知识库ID"""
        try:
            # 从环境变量获取默认知识库ID
            import os

            return os.getenv("FASTGPT_DEFAULT_PRODUCT_DATASET_ID", "")
        except Exception:
            return ""
