#!/usr/bin/env python3
"""
数据库架构修复脚本
解决同步系统中的数据库架构问题：
1. 添加缺失的 created_at 和 updated_at 字段
2. 创建缺失的 orders 表
3. 确保所有表都有正确的字段结构
"""

import os
import sys
import logging
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT
from psycopg2.extras import RealDictCursor
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.FileHandler("database_schema_fix.log"), logging.StreamHandler()],
)
logger = logging.getLogger(__name__)


class DatabaseSchemaFixer:
    """数据库架构修复器"""

    def __init__(self):
        self.conn = None
        self.cursor = None

    def get_connection(self):
        """获取数据库连接"""
        try:
            # 从环境变量获取数据库配置，默认使用106服务器
            db_config = {
                "host": os.getenv("DB_HOST", "***********"),
                "port": os.getenv("DB_PORT", "5432"),
                "database": os.getenv("DB_NAME", "product"),
                "user": os.getenv("DB_USER", "username"),
                "password": os.getenv("DB_PASSWORD", "password"),
            }

            self.conn = psycopg2.connect(**db_config)
            self.conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
            self.cursor = self.conn.cursor(cursor_factory=RealDictCursor)
            logger.info("数据库连接成功")
            return True

        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            return False

    def check_table_exists(self, table_name):
        """检查表是否存在"""
        try:
            self.cursor.execute(
                """
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = %s
                )
            """,
                (table_name,),
            )
            return self.cursor.fetchone()[0]
        except Exception as e:
            logger.error(f"检查表 {table_name} 失败: {e}")
            return False

    def check_column_exists(self, table_name, column_name):
        """检查列是否存在"""
        try:
            self.cursor.execute(
                """
                SELECT EXISTS (
                    SELECT FROM information_schema.columns 
                    WHERE table_name = %s AND column_name = %s
                )
            """,
                (table_name, column_name),
            )
            return self.cursor.fetchone()[0]
        except Exception as e:
            logger.error(f"检查表 {table_name} 列 {column_name} 失败: {e}")
            return False

    def add_created_at_column(self, table_name):
        """为表添加 created_at 字段"""
        try:
            if not self.check_table_exists(table_name):
                logger.warning(f"表 {table_name} 不存在，跳过")
                return True

            if self.check_column_exists(table_name, "created_at"):
                logger.info(f"表 {table_name} 已有 created_at 字段")
                return True

            # 添加 created_at 字段
            self.cursor.execute(
                f"""
                ALTER TABLE {table_name} 
                ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            """
            )
            logger.info(f"为表 {table_name} 添加 created_at 字段")

            # 如果有 create_time 字段，复制数据
            if self.check_column_exists(table_name, "create_time"):
                self.cursor.execute(
                    f"""
                    UPDATE {table_name} 
                    SET created_at = create_time 
                    WHERE create_time IS NOT NULL AND created_at IS NULL
                """
                )
                logger.info(f"复制 {table_name} 表的 create_time 数据到 created_at")

            return True

        except Exception as e:
            logger.error(f"为表 {table_name} 添加 created_at 字段失败: {e}")
            return False

    def add_updated_at_column(self, table_name):
        """为表添加 updated_at 字段"""
        try:
            if not self.check_table_exists(table_name):
                logger.warning(f"表 {table_name} 不存在，跳过")
                return True

            if self.check_column_exists(table_name, "updated_at"):
                logger.info(f"表 {table_name} 已有 updated_at 字段")
                return True

            # 添加 updated_at 字段
            self.cursor.execute(
                f"""
                ALTER TABLE {table_name} 
                ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            """
            )
            logger.info(f"为表 {table_name} 添加 updated_at 字段")

            # 如果有 update_time 字段，复制数据
            if self.check_column_exists(table_name, "update_time"):
                self.cursor.execute(
                    f"""
                    UPDATE {table_name} 
                    SET updated_at = update_time 
                    WHERE update_time IS NOT NULL AND updated_at IS NULL
                """
                )
                logger.info(f"复制 {table_name} 表的 update_time 数据到 updated_at")

            return True

        except Exception as e:
            logger.error(f"为表 {table_name} 添加 updated_at 字段失败: {e}")
            return False

    def create_orders_table(self):
        """创建 orders 表"""
        try:
            if self.check_table_exists("orders"):
                logger.info("orders 表已存在")
                return True

            create_sql = """
                CREATE TABLE orders (
                    id SERIAL PRIMARY KEY,
                    order_number VARCHAR(100) UNIQUE NOT NULL,
                    customer_id INTEGER,
                    customer_name VARCHAR(255),
                    total_amount DECIMAL(12,2) DEFAULT 0,
                    order_status VARCHAR(20) DEFAULT 'pending',
                    order_date TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    payment_status VARCHAR(20) DEFAULT 'unpaid',
                    shipping_address TEXT,
                    notes TEXT,
                    create_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    update_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    create_by VARCHAR(100),
                    update_by VARCHAR(100)
                )
            """

            self.cursor.execute(create_sql)
            logger.info("创建 orders 表成功")

            # 创建索引
            indexes = [
                "CREATE INDEX idx_orders_customer_id ON orders(customer_id)",
                "CREATE INDEX idx_orders_order_status ON orders(order_status)",
                "CREATE INDEX idx_orders_order_date ON orders(order_date)",
                "CREATE INDEX idx_orders_created_at ON orders(created_at)",
            ]

            for index_sql in indexes:
                try:
                    self.cursor.execute(index_sql)
                except Exception as e:
                    logger.warning(f"创建索引失败（可能已存在）: {e}")

            logger.info("orders 表索引创建完成")
            return True

        except Exception as e:
            logger.error(f"创建 orders 表失败: {e}")
            return False

    def create_customers_table(self):
        """创建 customers 表（如果不存在）"""
        try:
            if self.check_table_exists("customers"):
                logger.info("customers 表已存在")
                return True

            create_sql = """
                CREATE TABLE customers (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(255) NOT NULL,
                    email VARCHAR(255),
                    phone VARCHAR(20),
                    address TEXT,
                    status VARCHAR(20) DEFAULT 'active',
                    create_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    update_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                    create_by VARCHAR(100),
                    update_by VARCHAR(100)
                )
            """

            self.cursor.execute(create_sql)
            logger.info("创建 customers 表成功")
            return True

        except Exception as e:
            logger.error(f"创建 customers 表失败: {e}")
            return False

    def fix_all_tables(self):
        """修复所有表的架构"""
        # 需要添加 created_at 和 updated_at 字段的表
        tables = [
            "products",
            "categories",
            "labels",
            "brands",
            "cases",
            "product_cases",
            "programmes",
            "programme_products",
            "information",
            "distribution_orders",
            "sync_status",
            "attachments",
            "attachment_logs",
            "orders",
            "customers",
        ]

        success_count = 0

        for table in tables:
            logger.info(f"正在处理表: {table}")

            # 添加 created_at 字段
            if self.add_created_at_column(table):
                success_count += 1

            # 添加 updated_at 字段（attachment_logs 表除外）
            if table != "attachment_logs":
                if self.add_updated_at_column(table):
                    success_count += 1

        logger.info(f"表字段修复完成，成功处理 {success_count} 个操作")
        return success_count > 0

    def check_system_status(self):
        """检查系统状态"""
        logger.info("=== 数据库状态检查 ===")

        # 检查重要表
        important_tables = [
            "products",
            "categories",
            "labels",
            "brands",
            "cases",
            "programmes",
            "information",
            "distribution_orders",
            "orders",
            "customers",
            "sync_status",
            "attachments",
        ]

        missing_tables = []
        missing_created_at = []
        missing_updated_at = []

        for table in important_tables:
            if not self.check_table_exists(table):
                missing_tables.append(table)
                continue

            if not self.check_column_exists(table, "created_at"):
                missing_created_at.append(table)

            if table != "attachment_logs" and not self.check_column_exists(
                table, "updated_at"
            ):
                missing_updated_at.append(table)

        logger.info(f"缺失的表: {missing_tables if missing_tables else '无'}")
        logger.info(
            f"缺失 created_at 字段的表: {missing_created_at if missing_created_at else '无'}"
        )
        logger.info(
            f"缺失 updated_at 字段的表: {missing_updated_at if missing_updated_at else '无'}"
        )

        return {
            "missing_tables": missing_tables,
            "missing_created_at": missing_created_at,
            "missing_updated_at": missing_updated_at,
        }

    def run_full_fix(self):
        """运行完整修复"""
        logger.info("开始数据库架构修复...")

        try:
            # 1. 检查当前状态
            status = self.check_system_status()

            if not any(
                [
                    status["missing_tables"],
                    status["missing_created_at"],
                    status["missing_updated_at"],
                ]
            ):
                logger.info("数据库架构正常，无需修复")
                return True

            # 2. 创建缺失的表
            logger.info("创建缺失的表...")
            if "orders" in status["missing_tables"]:
                self.create_orders_table()

            if "customers" in status["missing_tables"]:
                self.create_customers_table()

            # 3. 修复所有表字段
            logger.info("修复表字段...")
            self.fix_all_tables()

            # 4. 再次检查状态
            logger.info("验证修复结果...")
            final_status = self.check_system_status()

            if not any(
                [
                    final_status["missing_tables"],
                    final_status["missing_created_at"],
                    final_status["missing_updated_at"],
                ]
            ):
                logger.info("✅ 数据库架构修复成功！")
                return True
            else:
                logger.warning("⚠️  部分问题未能完全解决，请检查日志")
                return False

        except Exception as e:
            logger.error(f"数据库架构修复失败: {e}")
            return False

    def close(self):
        """关闭数据库连接"""
        if self.cursor:
            self.cursor.close()
        if self.conn:
            self.conn.close()
        logger.info("数据库连接已关闭")


def main():
    """主函数"""
    print("=" * 60)
    print("数据库架构修复工具")
    print("=" * 60)

    fixer = DatabaseSchemaFixer()

    try:
        # 连接数据库
        if not fixer.get_connection():
            print("❌ 无法连接数据库，请检查配置")
            return 1

        # 运行修复
        if fixer.run_full_fix():
            print("✅ 数据库架构修复完成")
            return 0
        else:
            print("❌ 数据库架构修复失败")
            return 1

    except KeyboardInterrupt:
        print("\n用户中断操作")
        return 1
    except Exception as e:
        logger.error(f"程序执行出错: {e}")
        return 1
    finally:
        fixer.close()


if __name__ == "__main__":
    sys.exit(main())
