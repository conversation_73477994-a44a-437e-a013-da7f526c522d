---
description: 
globs: 
alwaysApply: true
---
# 环境兼容性标准规范

## 📋 概述

本文档基于云商系统历史环境兼容性问题分析，制定严格的环境兼容性标准。重点解决Windows批处理编码问题、跨平台路径差异、React版本兼容性等关键问题，确保系统在不同环境下的稳定运行。

## 🚨 历史环境兼容性问题分析

### 发现的关键问题
1. **Windows批处理脚本编码问题**：中文字符和表情符号导致命令截断('oduct', 'rname'等)
2. **React 19兼容性问题**：Dialog组件ref访问方式变更，element.ref被移除
3. **数据库类型误用**：SQLite误用问题，应使用PostgreSQL
4. **跨平台路径问题**：Windows和Linux路径分隔符不一致
5. **Python版本兼容性**：不同Python版本间的语法和库差异

## 🖥️ 跨平台兼容性标准

### 1. 平台检测和配置管理器
```python
import os
import sys
import platform
from pathlib import Path
from typing import Dict, Any, Optional
import logging
from dataclasses import dataclass
from enum import Enum

class PlatformType(Enum):
    WINDOWS = "windows"
    LINUX = "linux"
    MACOS = "macos"
    UNKNOWN = "unknown"

@dataclass
class PlatformConfig:
    platform_type: PlatformType
    path_separator: str
    line_ending: str
    encoding: str
    shell: str
    python_executable: str
    package_manager: str

class PlatformManager:
    """平台管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.current_platform = self._detect_platform()
        self.config = self._get_platform_config()
    
    def _detect_platform(self) -> PlatformType:
        """检测当前平台"""
        system = platform.system().lower()
        if system == 'windows':
            return PlatformType.WINDOWS
        elif system == 'linux':
            return PlatformType.LINUX
        elif system == 'darwin':
            return PlatformType.MACOS
        else:
            self.logger.warning(f"未知平台: {system}")
            return PlatformType.UNKNOWN
    
    def _get_platform_config(self) -> PlatformConfig:
        """获取平台配置"""
        configs = {
            PlatformType.WINDOWS: PlatformConfig(
                platform_type=PlatformType.WINDOWS,
                path_separator='\\',
                line_ending='\r\n',
                encoding='utf-8-sig',  # Windows需要BOM
                shell='cmd',
                python_executable='python.exe',
                package_manager='pip'
            ),
            PlatformType.LINUX: PlatformConfig(
                platform_type=PlatformType.LINUX,
                path_separator='/',
                line_ending='\n',
                encoding='utf-8',
                shell='bash',
                python_executable='python3',
                package_manager='pip3'
            ),
            PlatformType.MACOS: PlatformConfig(
                platform_type=PlatformType.MACOS,
                path_separator='/',
                line_ending='\n',
                encoding='utf-8',
                shell='bash',
                python_executable='python3',
                package_manager='pip3'
            )
        }
        
        return configs.get(self.current_platform, configs[PlatformType.LINUX])
    
    def create_cross_platform_path(self, *path_parts: str) -> str:
        """创建跨平台路径"""
        return str(Path(*path_parts))
    
    def normalize_path(self, path: str) -> str:
        """标准化路径"""
        return str(Path(path).resolve())
    
    def get_executable_extension(self) -> str:
        """获取可执行文件扩展名"""
        return '.exe' if self.current_platform == PlatformType.WINDOWS else ''
    
    def get_script_extension(self) -> str:
        """获取脚本文件扩展名"""
        return '.bat' if self.current_platform == PlatformType.WINDOWS else '.sh'
    
    def create_platform_specific_command(self, command: str, args: list = None) -> list:
        """创建平台特定命令"""
        args = args or []
        
        if self.current_platform == PlatformType.WINDOWS:
            return ['cmd', '/c', command] + args
        else:
            return [command] + args
    
    def get_environment_variables(self) -> Dict[str, str]:
        """获取环境变量"""
        env_vars = os.environ.copy()
        
        # 设置平台特定的环境变量
        env_vars['PLATFORM_TYPE'] = self.current_platform.value
        env_vars['PATH_SEPARATOR'] = self.config.path_separator
        env_vars['LINE_ENDING'] = repr(self.config.line_ending)
        env_vars['PREFERRED_ENCODING'] = self.config.encoding
        
        return env_vars
    
    def validate_environment(self) -> Dict[str, Any]:
        """验证环境配置"""
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'info': {}
        }
        
        # 检查Python版本
        python_version = sys.version_info
        validation_result['info']['python_version'] = f"{python_version.major}.{python_version.minor}.{python_version.micro}"
        
        if python_version < (3, 8):
            validation_result['errors'].append("Python版本过低，要求3.8+")
            validation_result['valid'] = False
        elif python_version < (3, 9):
            validation_result['warnings'].append("建议使用Python 3.9+")
        
        # 检查必需的环境变量
        required_vars = ['DATABASE_URL', 'API_BASE_URL']
        for var in required_vars:
            if not os.getenv(var):
                validation_result['errors'].append(f"缺少环境变量: {var}")
                validation_result['valid'] = False
        
        # 检查路径权限
        try:
            test_file = Path.cwd() / 'test_write_permission.tmp'
            test_file.write_text('test')
            test_file.unlink()
            validation_result['info']['write_permission'] = True
        except Exception as e:
            validation_result['errors'].append(f"当前目录无写权限: {e}")
            validation_result['valid'] = False
        
        # 检查网络连接
        try:
            import urllib.request
            urllib.request.urlopen('https://www.google.com', timeout=5)
            validation_result['info']['network_access'] = True
        except Exception:
            validation_result['warnings'].append("网络连接检查失败")
        
        return validation_result
```

### 2. 文件编码处理标准
```python
import codecs
import chardet
from typing import Optional, Tuple

class FileEncodingManager:
    """文件编码管理器"""
    
    def __init__(self, platform_manager: PlatformManager):
        self.platform_manager = platform_manager
        self.logger = logging.getLogger(__name__)
        self.default_encoding = platform_manager.config.encoding
    
    def detect_file_encoding(self, file_path: str) -> str:
        """检测文件编码"""
        try:
            with open(file_path, 'rb') as f:
                raw_data = f.read()
            
            detected = chardet.detect(raw_data)
            encoding = detected.get('encoding', self.default_encoding)
            confidence = detected.get('confidence', 0)
            
            self.logger.debug(f"文件 {file_path} 检测编码: {encoding} (置信度: {confidence})")
            
            # 如果置信度太低，使用默认编码
            if confidence < 0.7:
                self.logger.warning(f"编码检测置信度较低，使用默认编码: {self.default_encoding}")
                return self.default_encoding
            
            return encoding
            
        except Exception as e:
            self.logger.error(f"编码检测失败: {e}")
            return self.default_encoding
    
    def read_file_safe(self, file_path: str, encoding: Optional[str] = None) -> str:
        """安全读取文件"""
        if encoding is None:
            encoding = self.detect_file_encoding(file_path)
        
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                return f.read()
        except UnicodeDecodeError:
            # 尝试其他编码
            fallback_encodings = ['utf-8', 'utf-8-sig', 'gbk', 'gb2312', 'latin1']
            for fallback in fallback_encodings:
                if fallback != encoding:
                    try:
                        with open(file_path, 'r', encoding=fallback) as f:
                            self.logger.warning(f"使用fallback编码 {fallback} 读取文件 {file_path}")
                            return f.read()
                    except UnicodeDecodeError:
                        continue
            
            # 最后尝试忽略错误
            with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
                self.logger.error(f"忽略编码错误读取文件 {file_path}")
                return f.read()
    
    def write_file_safe(self, file_path: str, content: str, encoding: Optional[str] = None) -> None:
        """安全写入文件"""
        if encoding is None:
            encoding = self.default_encoding
        
        try:
            # 确保目录存在
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'w', encoding=encoding) as f:
                f.write(content)
                
        except Exception as e:
            self.logger.error(f"写入文件失败 {file_path}: {e}")
            raise
    
    def convert_file_encoding(self, file_path: str, target_encoding: str) -> None:
        """转换文件编码"""
        try:
            # 读取原文件
            content = self.read_file_safe(file_path)
            
            # 备份原文件
            backup_path = f"{file_path}.backup"
            Path(file_path).rename(backup_path)
            
            # 写入新编码
            self.write_file_safe(file_path, content, target_encoding)
            
            self.logger.info(f"文件编码转换成功: {file_path} -> {target_encoding}")
            
        except Exception as e:
            self.logger.error(f"文件编码转换失败: {e}")
            # 恢复备份
            if Path(f"{file_path}.backup").exists():
                Path(f"{file_path}.backup").rename(file_path)
            raise
    
    def normalize_line_endings(self, content: str) -> str:
        """标准化行结束符"""
        # 统一转换为\n，然后根据平台转换
        content = content.replace('\r\n', '\n').replace('\r', '\n')
        
        if self.platform_manager.current_platform == PlatformType.WINDOWS:
            return content.replace('\n', '\r\n')
        else:
            return content
```

## 🐍 Python版本兼容性标准

### 1. Python兼容性检查器
```python
import sys
import importlib
from typing import List, Dict, Any, Tuple
from packaging import version

class PythonCompatibilityChecker:
    """Python兼容性检查器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.min_python_version = (3, 8, 0)
        self.recommended_python_version = (3, 9, 0)
    
    def check_python_version(self) -> Dict[str, Any]:
        """检查Python版本"""
        current_version = sys.version_info
        result = {
            'current_version': f"{current_version.major}.{current_version.minor}.{current_version.micro}",
            'is_compatible': True,
            'warnings': [],
            'errors': []
        }
        
        if current_version < self.min_python_version:
            result['is_compatible'] = False
            result['errors'].append(
                f"Python版本过低: {result['current_version']}, "
                f"最低要求: {'.'.join(map(str, self.min_python_version))}"
            )
        elif current_version < self.recommended_python_version:
            result['warnings'].append(
                f"建议升级Python版本: {result['current_version']} -> "
                f"{'.'.join(map(str, self.recommended_python_version))}+"
            )
        
        return result
    
    def check_required_packages(self, requirements: List[Tuple[str, str]]) -> Dict[str, Any]:
        """
        检查必需的包
        
        Args:
            requirements: [(package_name, min_version), ...]
        """
        result = {
            'all_satisfied': True,
            'packages': {},
            'missing': [],
            'outdated': []
        }
        
        for package_name, min_version in requirements:
            try:
                module = importlib.import_module(package_name)
                installed_version = getattr(module, '__version__', 'unknown')
                
                package_info = {
                    'installed_version': installed_version,
                    'required_version': min_version,
                    'satisfied': True
                }
                
                if installed_version != 'unknown' and min_version:
                    try:
                        if version.parse(installed_version) < version.parse(min_version):
                            package_info['satisfied'] = False
                            result['all_satisfied'] = False
                            result['outdated'].append({
                                'name': package_name,
                                'installed': installed_version,
                                'required': min_version
                            })
                    except Exception as e:
                        self.logger.warning(f"版本比较失败 {package_name}: {e}")
                
                result['packages'][package_name] = package_info
                
            except ImportError:
                result['all_satisfied'] = False
                result['missing'].append(package_name)
                result['packages'][package_name] = {
                    'installed_version': None,
                    'required_version': min_version,
                    'satisfied': False
                }
        
        return result
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        return {
            'python_version': sys.version,
            'python_executable': sys.executable,
            'platform': platform.platform(),
            'architecture': platform.architecture(),
            'processor': platform.processor(),
            'python_implementation': platform.python_implementation(),
            'python_compiler': platform.python_compiler(),
        }
```

### 2. 依赖管理标准
```python
class DependencyManager:
    """依赖管理器"""
    
    def __init__(self, platform_manager: PlatformManager):
        self.platform_manager = platform_manager
        self.logger = logging.getLogger(__name__)
    
    def create_requirements_file(self, packages: Dict[str, str], file_path: str = 'requirements.txt'):
        """创建requirements.txt文件"""
        content_lines = []
        
        # 添加头部注释
        content_lines.extend([
            "# 云商系统依赖包列表",
            f"# 生成时间: {datetime.now().isoformat()}",
            f"# Python版本要求: >=3.8.0",
            "",
            "# 核心依赖"
        ])
        
        # 按类别组织依赖
        core_packages = ['streamlit', 'psycopg2-binary', 'sqlalchemy', 'requests']
        dev_packages = ['pytest', 'black', 'flake8', 'mypy']
        
        # 核心依赖
        for package in core_packages:
            if package in packages:
                content_lines.append(f"{package}>={packages[package]}")
        
        content_lines.append("\n# 开发依赖")
        for package in dev_packages:
            if package in packages:
                content_lines.append(f"{package}>={packages[package]}")
        
        # 其他依赖
        other_packages = set(packages.keys()) - set(core_packages) - set(dev_packages)
        if other_packages:
            content_lines.append("\n# 其他依赖")
            for package in sorted(other_packages):
                content_lines.append(f"{package}>={packages[package]}")
        
        # 写入文件
        content = '\n'.join(content_lines)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        self.logger.info(f"requirements.txt已生成: {file_path}")
    
    def install_dependencies(self, requirements_file: str = 'requirements.txt') -> bool:
        """安装依赖"""
        try:
            import subprocess
            
            pip_cmd = [
                self.platform_manager.config.python_executable,
                '-m', 'pip', 'install', '-r', requirements_file
            ]
            
            if self.platform_manager.current_platform == PlatformType.WINDOWS:
                # Windows下需要特殊处理
                result = subprocess.run(
                    pip_cmd,
                    shell=True,
                    capture_output=True,
                    text=True,
                    encoding='utf-8'
                )
            else:
                result = subprocess.run(
                    pip_cmd,
                    capture_output=True,
                    text=True
                )
            
            if result.returncode == 0:
                self.logger.info("依赖安装成功")
                return True
            else:
                self.logger.error(f"依赖安装失败: {result.stderr}")
                return False
                
        except Exception as e:
            self.logger.error(f"依赖安装异常: {e}")
            return False
```

## 🌐 Web框架兼容性标准

### 1. React版本兼容性处理
```typescript
// React版本兼容性工具
interface ReactCompatibilityConfig {
  version: string;
  features: {
    useRef: boolean;
    forwardRef: boolean;
    strictMode: boolean;
    concurrentFeatures: boolean;
  };
}

class ReactCompatibilityManager {
  private version: string;
  private config: ReactCompatibilityConfig;
  
  constructor() {
    this.version = this.detectReactVersion();
    this.config = this.getCompatibilityConfig();
  }
  
  private detectReactVersion(): string {
    try {
      // 尝试从package.json或React本身获取版本
      const React = require('react');
      return React.version || '18.0.0';
    } catch {
      return '18.0.0'; // 默认版本
    }
  }
  
  private getCompatibilityConfig(): ReactCompatibilityConfig {
    const majorVersion = parseInt(this.version.split('.')[0]);
    
    return {
      version: this.version,
      features: {
        useRef: majorVersion >= 16,
        forwardRef: majorVersion >= 16,
        strictMode: majorVersion >= 18,
        concurrentFeatures: majorVersion >= 18
      }
    };
  }
  
  // 兼容性ref访问
  public getElementRef(element: any): any {
    if (this.config.features.useRef) {
      // React 16.8+ 使用 useRef
      return element?.current;
    } else {
      // 旧版本直接访问
      return element;
    }
  }
  
  // 安全的ref设置
  public setElementRef(ref: any, element: any): void {
    if (typeof ref === 'function') {
      ref(element);
    } else if (ref && 'current' in ref) {
      ref.current = element;
    }
  }
  
  // Dialog组件兼容性处理
  public createCompatibleDialog(props: any): any {
    const majorVersion = parseInt(this.version.split('.')[0]);
    
    if (majorVersion >= 19) {
      // React 19+ 新的ref处理方式
      return {
        ...props,
        ref: (element: any) => {
          if (props.onRef) {
            props.onRef(element);
          }
        }
      };
    } else {
      // React 18及以下的传统方式
      return {
        ...props,
        ref: props.dialogRef
      };
    }
  }
}

// 使用示例
const reactCompat = new ReactCompatibilityManager();

// 在组件中使用
function MyDialog({ onClose, children }: { onClose: () => void; children: React.ReactNode }) {
  const dialogRef = useRef<HTMLDialogElement>(null);
  
  const handleOpen = () => {
    const element = reactCompat.getElementRef(dialogRef);
    if (element && element.showModal) {
      element.showModal();
    }
  };
  
  const compatibleProps = reactCompat.createCompatibleDialog({
    onRef: (element: HTMLDialogElement) => {
      reactCompat.setElementRef(dialogRef, element);
    },
    onClose
  });
  
  return (
    <dialog {...compatibleProps}>
      {children}
    </dialog>
  );
}
```

## 🗄️ 数据库兼容性标准

### 1. 数据库适配器
```python
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
import os

class DatabaseAdapter(ABC):
    """数据库适配器基类"""
    
    @abstractmethod
    def get_connection_string(self) -> str:
        pass
    
    @abstractmethod
    def create_engine_config(self) -> Dict[str, Any]:
        pass
    
    @abstractmethod
    def get_migration_commands(self) -> List[str]:
        pass

class PostgreSQLAdapter(DatabaseAdapter):
    """PostgreSQL适配器"""
    
    def __init__(self):
        self.host = os.getenv('DB_HOST', 'localhost')
        self.port = os.getenv('DB_PORT', '5432')
        self.database = os.getenv('DB_NAME', 'yunshang')
        self.username = os.getenv('DB_USER', 'postgres')
        self.password = os.getenv('DB_PASSWORD', '')
    
    def get_connection_string(self) -> str:
        return f"postgresql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"
    
    def create_engine_config(self) -> Dict[str, Any]:
        return {
            'pool_size': 10,
            'max_overflow': 20,
            'pool_timeout': 30,
            'pool_recycle': 3600,
            'echo': False
        }
    
    def get_migration_commands(self) -> List[str]:
        return [
            'CREATE EXTENSION IF NOT EXISTS "uuid-ossp";',
            'CREATE EXTENSION IF NOT EXISTS "pgcrypto";'
        ]

class SQLiteAdapter(DatabaseAdapter):
    """SQLite适配器（仅用于开发测试）"""
    
    def __init__(self):
        self.db_path = os.getenv('SQLITE_PATH', 'yunshang.db')
    
    def get_connection_string(self) -> str:
        return f"sqlite:///{self.db_path}"
    
    def create_engine_config(self) -> Dict[str, Any]:
        return {
            'echo': False,
            'connect_args': {'check_same_thread': False}
        }
    
    def get_migration_commands(self) -> List[str]:
        return [
            'PRAGMA foreign_keys = ON;',
            'PRAGMA journal_mode = WAL;'
        ]

class DatabaseCompatibilityManager:
    """数据库兼容性管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.adapter = self._create_adapter()
    
    def _create_adapter(self) -> DatabaseAdapter:
        """创建数据库适配器"""
        db_type = os.getenv('DATABASE_TYPE', 'postgresql').lower()
        
        if db_type == 'postgresql':
            return PostgreSQLAdapter()
        elif db_type == 'sqlite':
            self.logger.warning("使用SQLite数据库，仅适用于开发测试环境")
            return SQLiteAdapter()
        else:
            raise ValueError(f"不支持的数据库类型: {db_type}")
    
    def get_database_url(self) -> str:
        """获取数据库连接URL"""
        return self.adapter.get_connection_string()
    
    def create_engine(self):
        """创建数据库引擎"""
        from sqlalchemy import create_engine
        
        url = self.get_database_url()
        config = self.adapter.create_engine_config()
        
        return create_engine(url, **config)
    
    def validate_database_connection(self) -> bool:
        """验证数据库连接"""
        try:
            engine = self.create_engine()
            with engine.connect() as conn:
                conn.execute("SELECT 1")
            return True
        except Exception as e:
            self.logger.error(f"数据库连接验证失败: {e}")
            return False
```

## 📝 脚本兼容性标准

### 1. 跨平台脚本生成器
```python
class CrossPlatformScriptGenerator:
    """跨平台脚本生成器"""
    
    def __init__(self, platform_manager: PlatformManager):
        self.platform_manager = platform_manager
        self.logger = logging.getLogger(__name__)
    
    def generate_startup_script(self, script_name: str = 'start') -> str:
        """生成启动脚本"""
        if self.platform_manager.current_platform == PlatformType.WINDOWS:
            return self._generate_windows_startup_script(script_name)
        else:
            return self._generate_unix_startup_script(script_name)
    
    def _generate_windows_startup_script(self, script_name: str) -> str:
        """生成Windows启动脚本"""
        script_content = f"""@echo off
REM 云商系统启动脚本 - Windows版本
REM 编码: UTF-8 (确保使用UTF-8编码保存此文件)

echo Starting YunShang System...

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found. Please install Python 3.8+
    pause
    exit /b 1
)

REM 检查虚拟环境
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
)

REM 激活虚拟环境
call venv\\Scripts\\activate.bat

REM 安装依赖
if exist "requirements.txt" (
    echo Installing dependencies...
    pip install -r requirements.txt
)

REM 启动应用
echo Starting application...
streamlit run main.py --server.port 8501

pause
"""
        
        script_path = f"{script_name}.bat"
        with open(script_path, 'w', encoding='utf-8-sig') as f:  # 使用BOM避免编码问题
            f.write(script_content)
        
        self.logger.info(f"Windows启动脚本已生成: {script_path}")
        return script_path
    
    def _generate_unix_startup_script(self, script_name: str) -> str:
        """生成Unix启动脚本"""
        script_content = f"""#!/bin/bash
# 云商系统启动脚本 - Unix版本

set -e  # 遇到错误立即退出

echo "Starting YunShang System..."

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "Error: Python3 not found. Please install Python 3.8+"
    exit 1
fi

# 检查虚拟环境
if [ ! -d "venv" ]; then
    echo "Creating virtual environment..."
    python3 -m venv venv
fi

# 激活虚拟环境
source venv/bin/activate

# 安装依赖
if [ -f "requirements.txt" ]; then
    echo "Installing dependencies..."
    pip install -r requirements.txt
fi

# 启动应用
echo "Starting application..."
streamlit run main.py --server.port 8501
"""
        
        script_path = f"{script_name}.sh"
        with open(script_path, 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        # 设置执行权限
        os.chmod(script_path, 0o755)
        
        self.logger.info(f"Unix启动脚本已生成: {script_path}")
        return script_path
    
    def generate_install_script(self) -> str:
        """生成安装脚本"""
        if self.platform_manager.current_platform == PlatformType.WINDOWS:
            return self._generate_windows_install_script()
        else:
            return self._generate_unix_install_script()
    
    def _generate_windows_install_script(self) -> str:
        """生成Windows安装脚本"""
        script_content = """@echo off
REM 云商系统安装脚本 - Windows版本

echo Installing YunShang System...

REM 检查管理员权限
net session >nul 2>&1
if errorlevel 1 (
    echo This script requires administrator privileges.
    echo Please run as administrator.
    pause
    exit /b 1
)

REM 安装Python（如果需要）
python --version >nul 2>&1
if errorlevel 1 (
    echo Python not found. Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

REM 安装Git（如果需要）
git --version >nul 2>&1
if errorlevel 1 (
    echo Git not found. Please install Git from https://git-scm.com
    pause
    exit /b 1
)

REM 创建项目目录
if not exist "yunshang" (
    mkdir yunshang
)
cd yunshang

REM 克隆代码（如果是首次安装）
if not exist ".git" (
    git clone https://github.com/your-org/yunshang.git .
)

REM 运行启动脚本
call start.bat

echo Installation completed!
pause
"""
        
        with open('install.bat', 'w', encoding='utf-8-sig') as f:
            f.write(script_content)
        
        return 'install.bat'
    
    def _generate_unix_install_script(self) -> str:
        """生成Unix安装脚本"""
        script_content = """#!/bin/bash
# 云商系统安装脚本 - Unix版本

set -e

echo "Installing YunShang System..."

# 检查Python
if ! command -v python3 &> /dev/null; then
    echo "Installing Python3..."
    if command -v apt-get &> /dev/null; then
        sudo apt-get update
        sudo apt-get install -y python3 python3-pip python3-venv
    elif command -v yum &> /dev/null; then
        sudo yum install -y python3 python3-pip
    elif command -v brew &> /dev/null; then
        brew install python3
    else
        echo "Please install Python3 manually"
        exit 1
    fi
fi

# 检查Git
if ! command -v git &> /dev/null; then
    echo "Installing Git..."
    if command -v apt-get &> /dev/null; then
        sudo apt-get install -y git
    elif command -v yum &> /dev/null; then
        sudo yum install -y git
    elif command -v brew &> /dev/null; then
        brew install git
    else
        echo "Please install Git manually"
        exit 1
    fi
fi

# 创建项目目录
mkdir -p yunshang
cd yunshang

# 克隆代码（如果是首次安装）
if [ ! -d ".git" ]; then
    git clone https://github.com/your-org/yunshang.git .
fi

# 运行启动脚本
chmod +x start.sh
./start.sh

echo "Installation completed!"
"""
        
        with open('install.sh', 'w', encoding='utf-8') as f:
            f.write(script_content)
        
        os.chmod('install.sh', 0o755)
        return 'install.sh'
```

## 🚫 禁止模式

### 1. 禁止的兼容性反模式
```python
# ❌ 禁止：硬编码路径分隔符
def get_config_path():
    return "config\\settings.json"  # Windows特定

# ❌ 禁止：假设特定编码
def read_config():
    with open('config.txt', 'r') as f:  # 没有指定编码
        return f.read()

# ❌ 禁止：平台特定的命令
def run_command():
    os.system("dir")  # Windows特定命令

# ❌ 禁止：不检查Python版本
import asyncio
async def some_function():  # 可能在旧版本Python中不支持
    pass
```

### 2. 必须的兼容性模式
```python
# ✅ 正确：使用跨平台路径
def get_config_path():
    return str(Path("config") / "settings.json")

# ✅ 正确：指定编码并处理异常
def read_config():
    encoding_manager = FileEncodingManager(platform_manager)
    return encoding_manager.read_file_safe('config.txt')

# ✅ 正确：跨平台命令
def run_command():
    platform_manager = PlatformManager()
    if platform_manager.current_platform == PlatformType.WINDOWS:
        subprocess.run(['dir'], shell=True)
    else:
        subprocess.run(['ls'])

# ✅ 正确：检查Python版本
def check_compatibility():
    checker = PythonCompatibilityChecker()
    result = checker.check_python_version()
    if not result['is_compatible']:
        raise RuntimeError("Python版本不兼容")
```

## 📋 环境兼容性检查清单

### 开发阶段
- [ ] 是否使用跨平台路径处理？
- [ ] 是否正确处理文件编码？
- [ ] 是否检查Python版本兼容性？
- [ ] 是否使用平台特定的配置？
- [ ] 是否避免硬编码平台特定的命令？

### 测试阶段
- [ ] 是否在多个平台上测试？
- [ ] 是否测试不同编码的文件？
- [ ] 是否测试不同Python版本？
- [ ] 是否测试环境变量配置？
- [ ] 是否测试依赖包兼容性？

### 部署阶段
- [ ] 是否提供平台特定的安装脚本？
- [ ] 是否配置正确的环境变量？
- [ ] 是否验证目标环境兼容性？
- [ ] 是否准备环境问题的解决方案？

---

**重要提醒**: 本规范是强制性的，所有代码必须严格遵循环境兼容性标准。违反此规范的代码将不被接受，需要重新修改后才能合并。

**文档版本**: v1.0  
**最后更新**: 2024-12-19  
**维护者**: 开发团队
