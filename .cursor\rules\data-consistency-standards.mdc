---
description: 
globs: 
alwaysApply: true
---
# 数据一致性标准规范

## 📋 概述

本文档基于云商系统历史数据一致性问题分析，制定严格的数据一致性标准。重点解决字段映射不一致、数据格式不统一、同步机制不完善等关键问题，确保数据的准确性和可靠性。

## 🚨 历史数据一致性问题分析

### 发现的关键问题
1. **字段命名不一致**：emailVerified vs email_verified, productId vs id
2. **数据类型不匹配**：字符串ID与整型ID混用
3. **模拟数据混入生产**：占位符数据和真实数据混合
4. **API响应格式不统一**：不同接口返回格式差异
5. **数据库字段规范缺失**：缺乏统一的字段命名和类型规范

## 🗃️ 统一字段映射标准

### 1. 字段映射管理器
```python
from typing import Dict, Any, Optional, List, Union
from enum import Enum
import json
import logging
from dataclasses import dataclass

class FieldType(Enum):
    STRING = "string"
    INTEGER = "integer"
    FLOAT = "float"
    BOOLEAN = "boolean"
    DATETIME = "datetime"
    JSON = "json"
    UUID = "uuid"

@dataclass
class FieldMapping:
    source_field: str
    target_field: str
    field_type: FieldType
    required: bool = True
    default_value: Any = None
    validator: Optional[callable] = None
    transformer: Optional[callable] = None

class FieldMappingManager:
    """字段映射管理器"""
    
    def __init__(self):
        self.mappings: Dict[str, Dict[str, FieldMapping]] = {}
        self.logger = logging.getLogger(__name__)
        self._initialize_standard_mappings()
    
    def _initialize_standard_mappings(self):
        """初始化标准字段映射"""
        
        # 用户信息映射
        user_mappings = {
            'id': FieldMapping('id', 'user_id', FieldType.INTEGER),
            'email': FieldMapping('email', 'email_address', FieldType.STRING),
            'emailVerified': FieldMapping('emailVerified', 'email_verified', FieldType.BOOLEAN),
            'name': FieldMapping('name', 'full_name', FieldType.STRING),
            'avatar': FieldMapping('avatar', 'avatar_url', FieldType.STRING, required=False),
            'createdAt': FieldMapping('createdAt', 'created_at', FieldType.DATETIME),
            'updatedAt': FieldMapping('updatedAt', 'updated_at', FieldType.DATETIME)
        }
        
        # 产品信息映射
        product_mappings = {
            'id': FieldMapping('id', 'product_id', FieldType.INTEGER),
            'productId': FieldMapping('productId', 'product_id', FieldType.INTEGER),
            'name': FieldMapping('name', 'product_name', FieldType.STRING),
            'productName': FieldMapping('productName', 'product_name', FieldType.STRING),
            'description': FieldMapping('description', 'product_description', FieldType.STRING),
            'price': FieldMapping('price', 'unit_price', FieldType.FLOAT),
            'category': FieldMapping('category', 'category_name', FieldType.STRING),
            'categoryId': FieldMapping('categoryId', 'category_id', FieldType.INTEGER),
            'brand': FieldMapping('brand', 'brand_name', FieldType.STRING),
            'brandId': FieldMapping('brandId', 'brand_id', FieldType.INTEGER),
            'status': FieldMapping('status', 'product_status', FieldType.STRING),
            'images': FieldMapping('images', 'image_urls', FieldType.JSON, required=False),
            'specifications': FieldMapping('specifications', 'product_specs', FieldType.JSON, required=False)
        }
        
        # 订单信息映射
        order_mappings = {
            'id': FieldMapping('id', 'order_id', FieldType.INTEGER),
            'orderId': FieldMapping('orderId', 'order_id', FieldType.INTEGER),
            'orderNumber': FieldMapping('orderNumber', 'order_number', FieldType.STRING),
            'userId': FieldMapping('userId', 'customer_id', FieldType.INTEGER),
            'customerId': FieldMapping('customerId', 'customer_id', FieldType.INTEGER),
            'totalAmount': FieldMapping('totalAmount', 'total_amount', FieldType.FLOAT),
            'orderStatus': FieldMapping('orderStatus', 'order_status', FieldType.STRING),
            'orderDate': FieldMapping('orderDate', 'order_date', FieldType.DATETIME),
            'items': FieldMapping('items', 'order_items', FieldType.JSON)
        }
        
        self.mappings = {
            'user': user_mappings,
            'product': product_mappings,
            'order': order_mappings
        }
    
    def add_mapping(self, entity_type: str, mapping: FieldMapping):
        """添加字段映射"""
        if entity_type not in self.mappings:
            self.mappings[entity_type] = {}
        self.mappings[entity_type][mapping.source_field] = mapping
    
    def get_mapping(self, entity_type: str, source_field: str) -> Optional[FieldMapping]:
        """获取字段映射"""
        return self.mappings.get(entity_type, {}).get(source_field)
    
    def transform_data(self, entity_type: str, source_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        转换数据格式
        
        Args:
            entity_type: 实体类型
            source_data: 源数据
            
        Returns:
            转换后的数据
            
        Raises:
            ValidationError: 数据验证失败
        """
        if entity_type not in self.mappings:
            raise ValueError(f"未知的实体类型: {entity_type}")
        
        target_data = {}
        entity_mappings = self.mappings[entity_type]
        
        for source_field, mapping in entity_mappings.items():
            try:
                # 获取源值
                source_value = source_data.get(source_field)
                
                # 处理必需字段
                if mapping.required and source_value is None:
                    if mapping.default_value is not None:
                        source_value = mapping.default_value
                    else:
                        raise ValueError(f"必需字段 {source_field} 缺失")
                
                # 跳过空值（非必需字段）
                if source_value is None:
                    continue
                
                # 类型转换
                converted_value = self._convert_field_type(source_value, mapping.field_type)
                
                # 数据验证
                if mapping.validator and not mapping.validator(converted_value):
                    raise ValueError(f"字段 {source_field} 验证失败")
                
                # 数据转换
                if mapping.transformer:
                    converted_value = mapping.transformer(converted_value)
                
                target_data[mapping.target_field] = converted_value
                
            except Exception as e:
                self.logger.error(f"字段映射失败 {source_field}: {e}")
                raise ValueError(f"字段映射失败 {source_field}: {e}")
        
        return target_data
    
    def _convert_field_type(self, value: Any, field_type: FieldType) -> Any:
        """转换字段类型"""
        if value is None:
            return None
        
        try:
            if field_type == FieldType.STRING:
                return str(value)
            elif field_type == FieldType.INTEGER:
                return int(float(value))  # 支持字符串数字转换
            elif field_type == FieldType.FLOAT:
                return float(value)
            elif field_type == FieldType.BOOLEAN:
                if isinstance(value, str):
                    return value.lower() in ('true', '1', 'yes', 'on')
                return bool(value)
            elif field_type == FieldType.DATETIME:
                from datetime import datetime
                if isinstance(value, str):
                    # 尝试多种日期格式
                    formats = [
                        '%Y-%m-%d %H:%M:%S',
                        '%Y-%m-%dT%H:%M:%S',
                        '%Y-%m-%dT%H:%M:%S.%fZ',
                        '%Y-%m-%d'
                    ]
                    for fmt in formats:
                        try:
                            return datetime.strptime(value, fmt)
                        except ValueError:
                            continue
                    raise ValueError(f"无法解析日期格式: {value}")
                return value
            elif field_type == FieldType.JSON:
                if isinstance(value, str):
                    return json.loads(value)
                return value
            elif field_type == FieldType.UUID:
                import uuid
                if isinstance(value, str):
                    return str(uuid.UUID(value))
                return str(value)
            else:
                return value
        except Exception as e:
            raise ValueError(f"类型转换失败 {value} -> {field_type.value}: {e}")
    
    def validate_data_consistency(
        self,
        entity_type: str,
        data_list: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        验证数据一致性
        
        Args:
            entity_type: 实体类型
            data_list: 数据列表
            
        Returns:
            验证结果
        """
        validation_result = {
            'valid': True,
            'errors': [],
            'warnings': [],
            'statistics': {}
        }
        
        if not data_list:
            validation_result['warnings'].append("数据列表为空")
            return validation_result
        
        entity_mappings = self.mappings.get(entity_type, {})
        field_stats = {}
        
        for mapping in entity_mappings.values():
            field_stats[mapping.source_field] = {
                'present_count': 0,
                'null_count': 0,
                'type_errors': 0,
                'validation_errors': 0
            }
        
        # 检查每条数据
        for i, data in enumerate(data_list):
            for source_field, mapping in entity_mappings.items():
                stats = field_stats[source_field]
                value = data.get(source_field)
                
                if source_field in data:
                    stats['present_count'] += 1
                    
                    if value is None:
                        stats['null_count'] += 1
                        if mapping.required:
                            validation_result['errors'].append(
                                f"第{i+1}条数据: 必需字段 {source_field} 为空"
                            )
                    else:
                        # 类型检查
                        try:
                            self._convert_field_type(value, mapping.field_type)
                        except Exception:
                            stats['type_errors'] += 1
                            validation_result['errors'].append(
                                f"第{i+1}条数据: 字段 {source_field} 类型错误"
                            )
                        
                        # 验证检查
                        if mapping.validator:
                            try:
                                converted_value = self._convert_field_type(value, mapping.field_type)
                                if not mapping.validator(converted_value):
                                    stats['validation_errors'] += 1
                                    validation_result['errors'].append(
                                        f"第{i+1}条数据: 字段 {source_field} 验证失败"
                                    )
                            except Exception:
                                pass
                else:
                    if mapping.required:
                        validation_result['errors'].append(
                            f"第{i+1}条数据: 缺少必需字段 {source_field}"
                        )
        
        # 生成统计信息
        validation_result['statistics'] = field_stats
        
        # 检查一致性
        total_records = len(data_list)
        for field, stats in field_stats.items():
            if stats['present_count'] < total_records * 0.9:  # 90%阈值
                validation_result['warnings'].append(
                    f"字段 {field} 在{stats['present_count']}/{total_records}条记录中存在，完整性较低"
                )
        
        if validation_result['errors']:
            validation_result['valid'] = False
        
        return validation_result
```

### 2. 数据验证器
```python
import re
from typing import Any, List, Dict, Callable
from datetime import datetime

class DataValidator:
    """数据验证器"""
    
    @staticmethod
    def email_validator(email: str) -> bool:
        """邮箱验证"""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return re.match(pattern, email) is not None
    
    @staticmethod
    def phone_validator(phone: str) -> bool:
        """手机号验证"""
        pattern = r'^1[3-9]\d{9}$'
        return re.match(pattern, phone) is not None
    
    @staticmethod
    def price_validator(price: float) -> bool:
        """价格验证"""
        return price >= 0 and price <= 999999.99
    
    @staticmethod
    def status_validator(status: str) -> bool:
        """状态验证"""
        valid_statuses = ['active', 'inactive', 'pending', 'deleted']
        return status.lower() in valid_statuses
    
    @staticmethod
    def id_validator(id_value: int) -> bool:
        """ID验证"""
        return isinstance(id_value, int) and id_value > 0
    
    @staticmethod
    def url_validator(url: str) -> bool:
        """URL验证"""
        pattern = r'^https?://[^\s/$.?#].[^\s]*$'
        return re.match(pattern, url) is not None
    
    @staticmethod
    def json_validator(json_data: Any) -> bool:
        """JSON验证"""
        try:
            if isinstance(json_data, str):
                json.loads(json_data)
            return True
        except:
            return False

# 数据转换器
class DataTransformer:
    """数据转换器"""
    
    @staticmethod
    def normalize_email(email: str) -> str:
        """标准化邮箱"""
        return email.lower().strip()
    
    @staticmethod
    def normalize_phone(phone: str) -> str:
        """标准化手机号"""
        # 移除所有非数字字符
        digits_only = re.sub(r'\D', '', phone)
        # 如果是11位且以1开头，直接返回
        if len(digits_only) == 11 and digits_only.startswith('1'):
            return digits_only
        # 如果是13位且以86开头，移除86
        if len(digits_only) == 13 and digits_only.startswith('86'):
            return digits_only[2:]
        return digits_only
    
    @staticmethod
    def normalize_price(price: Union[str, float]) -> float:
        """标准化价格"""
        if isinstance(price, str):
            # 移除货币符号和空格
            price_str = re.sub(r'[￥$€£,\s]', '', price)
            return round(float(price_str), 2)
        return round(float(price), 2)
    
    @staticmethod
    def normalize_status(status: str) -> str:
        """标准化状态"""
        status_mapping = {
            '正常': 'active',
            '停用': 'inactive',
            '待审核': 'pending',
            '已删除': 'deleted',
            '1': 'active',
            '0': 'inactive'
        }
        return status_mapping.get(status, status.lower())
```

## 🔄 数据同步标准

### 1. 同步管理器
```python
from typing import Dict, Any, List, Optional
import asyncio
import time
from dataclasses import dataclass
from enum import Enum

class SyncStatus(Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    SUCCESS = "success"
    FAILED = "failed"
    PARTIAL = "partial"

@dataclass
class SyncResult:
    status: SyncStatus
    total_records: int
    success_count: int
    error_count: int
    errors: List[str]
    duration: float
    timestamp: datetime

class DataSyncManager:
    """数据同步管理器"""
    
    def __init__(self):
        self.field_mapping_manager = FieldMappingManager()
        self.logger = logging.getLogger(__name__)
        self.sync_history: List[SyncResult] = []
    
    async def sync_entity_data(
        self,
        entity_type: str,
        source_data: List[Dict[str, Any]],
        target_storage: Any,
        batch_size: int = 100
    ) -> SyncResult:
        """
        同步实体数据
        
        Args:
            entity_type: 实体类型
            source_data: 源数据列表
            target_storage: 目标存储
            batch_size: 批处理大小
            
        Returns:
            同步结果
        """
        start_time = time.time()
        total_records = len(source_data)
        success_count = 0
        error_count = 0
        errors = []
        
        try:
            # 数据一致性验证
            validation_result = self.field_mapping_manager.validate_data_consistency(
                entity_type, source_data
            )
            
            if not validation_result['valid']:
                return SyncResult(
                    status=SyncStatus.FAILED,
                    total_records=total_records,
                    success_count=0,
                    error_count=total_records,
                    errors=validation_result['errors'],
                    duration=time.time() - start_time,
                    timestamp=datetime.now()
                )
            
            # 批量处理数据
            for i in range(0, total_records, batch_size):
                batch = source_data[i:i + batch_size]
                batch_result = await self._process_data_batch(
                    entity_type, batch, target_storage
                )
                
                success_count += batch_result['success_count']
                error_count += batch_result['error_count']
                errors.extend(batch_result['errors'])
            
            # 确定同步状态
            if error_count == 0:
                status = SyncStatus.SUCCESS
            elif success_count == 0:
                status = SyncStatus.FAILED
            else:
                status = SyncStatus.PARTIAL
            
            result = SyncResult(
                status=status,
                total_records=total_records,
                success_count=success_count,
                error_count=error_count,
                errors=errors,
                duration=time.time() - start_time,
                timestamp=datetime.now()
            )
            
            self.sync_history.append(result)
            return result
            
        except Exception as e:
            self.logger.error(f"数据同步失败: {e}")
            result = SyncResult(
                status=SyncStatus.FAILED,
                total_records=total_records,
                success_count=0,
                error_count=total_records,
                errors=[str(e)],
                duration=time.time() - start_time,
                timestamp=datetime.now()
            )
            self.sync_history.append(result)
            return result
    
    async def _process_data_batch(
        self,
        entity_type: str,
        batch_data: List[Dict[str, Any]],
        target_storage: Any
    ) -> Dict[str, Any]:
        """处理数据批次"""
        success_count = 0
        error_count = 0
        errors = []
        
        for record in batch_data:
            try:
                # 字段映射转换
                transformed_data = self.field_mapping_manager.transform_data(
                    entity_type, record
                )
                
                # 保存到目标存储
                await self._save_to_storage(target_storage, transformed_data)
                success_count += 1
                
            except Exception as e:
                error_count += 1
                errors.append(f"记录处理失败: {e}")
                self.logger.error(f"记录处理失败: {e}")
        
        return {
            'success_count': success_count,
            'error_count': error_count,
            'errors': errors
        }
    
    async def _save_to_storage(self, storage: Any, data: Dict[str, Any]):
        """保存数据到存储"""
        # 这里需要根据实际的存储类型实现
        # 例如：数据库、文件系统、API等
        pass
    
    def get_sync_statistics(self) -> Dict[str, Any]:
        """获取同步统计信息"""
        if not self.sync_history:
            return {
                'total_syncs': 0,
                'success_rate': 0.0,
                'average_duration': 0.0,
                'total_records_synced': 0
            }
        
        total_syncs = len(self.sync_history)
        successful_syncs = len([r for r in self.sync_history if r.status == SyncStatus.SUCCESS])
        total_records = sum(r.total_records for r in self.sync_history)
        total_duration = sum(r.duration for r in self.sync_history)
        
        return {
            'total_syncs': total_syncs,
            'success_rate': successful_syncs / total_syncs,
            'average_duration': total_duration / total_syncs,
            'total_records_synced': total_records,
            'last_sync': self.sync_history[-1].timestamp.isoformat()
        }
```

## 🗄️ 数据库一致性标准

### 1. 数据库字段规范
```python
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Float, Text, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.sql import func

Base = declarative_base()

class StandardUserModel(Base):
    """标准用户模型"""
    __tablename__ = 'users'
    
    # 主键：统一使用user_id
    user_id = Column(Integer, primary_key=True, autoincrement=True)
    
    # 基本信息：统一字段命名
    email_address = Column(String(255), unique=True, nullable=False)
    email_verified = Column(Boolean, default=False, nullable=False)
    full_name = Column(String(100), nullable=False)
    avatar_url = Column(String(500), nullable=True)
    phone_number = Column(String(20), nullable=True)
    
    # 状态字段：统一使用枚举值
    user_status = Column(String(20), default='active', nullable=False)  # active, inactive, pending, deleted
    
    # 时间字段：统一命名和类型
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), nullable=True)
    last_login_at = Column(DateTime(timezone=True), nullable=True)
    
    # JSON字段：统一使用JSON类型
    user_preferences = Column(JSON, nullable=True)
    user_metadata = Column(JSON, nullable=True)

class StandardProductModel(Base):
    """标准产品模型"""
    __tablename__ = 'products'
    
    # 主键：统一使用product_id
    product_id = Column(Integer, primary_key=True, autoincrement=True)
    
    # 基本信息
    product_name = Column(String(200), nullable=False)
    product_description = Column(Text, nullable=True)
    product_code = Column(String(50), unique=True, nullable=False)
    
    # 分类和品牌：统一使用ID关联
    category_id = Column(Integer, nullable=False)
    brand_id = Column(Integer, nullable=True)
    
    # 价格：统一使用DECIMAL类型
    unit_price = Column(Float(precision=2), nullable=False)
    cost_price = Column(Float(precision=2), nullable=True)
    
    # 状态：统一枚举值
    product_status = Column(String(20), default='active', nullable=False)  # active, inactive, discontinued
    
    # 库存
    stock_quantity = Column(Integer, default=0, nullable=False)
    min_stock_level = Column(Integer, default=0, nullable=False)
    
    # 图片和规格：统一使用JSON
    image_urls = Column(JSON, nullable=True)
    product_specs = Column(JSON, nullable=True)
    
    # 时间字段
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), nullable=True)

class StandardOrderModel(Base):
    """标准订单模型"""
    __tablename__ = 'orders'
    
    # 主键：统一使用order_id
    order_id = Column(Integer, primary_key=True, autoincrement=True)
    
    # 订单编号：业务唯一标识
    order_number = Column(String(50), unique=True, nullable=False)
    
    # 客户信息：统一使用customer_id
    customer_id = Column(Integer, nullable=False)
    
    # 金额：统一精度
    total_amount = Column(Float(precision=2), nullable=False)
    discount_amount = Column(Float(precision=2), default=0.0, nullable=False)
    final_amount = Column(Float(precision=2), nullable=False)
    
    # 状态：统一枚举值
    order_status = Column(String(20), default='pending', nullable=False)  # pending, confirmed, shipped, delivered, cancelled
    payment_status = Column(String(20), default='unpaid', nullable=False)  # unpaid, paid, refunded
    
    # 订单项：使用JSON存储
    order_items = Column(JSON, nullable=False)
    
    # 地址信息：使用JSON存储
    shipping_address = Column(JSON, nullable=False)
    billing_address = Column(JSON, nullable=True)
    
    # 时间字段
    order_date = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    confirmed_at = Column(DateTime(timezone=True), nullable=True)
    shipped_at = Column(DateTime(timezone=True), nullable=True)
    delivered_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), nullable=True)
```

### 2. 数据库迁移标准
```python
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

def create_migration_script():
    """创建标准化迁移脚本"""
    
    # 用户表标准化
    op.execute("""
        -- 标准化用户表字段
        ALTER TABLE users RENAME COLUMN id TO user_id;
        ALTER TABLE users RENAME COLUMN email TO email_address;
        ALTER TABLE users RENAME COLUMN "emailVerified" TO email_verified;
        ALTER TABLE users RENAME COLUMN name TO full_name;
        ALTER TABLE users RENAME COLUMN avatar TO avatar_url;
        ALTER TABLE users RENAME COLUMN "createdAt" TO created_at;
        ALTER TABLE users RENAME COLUMN "updatedAt" TO updated_at;
        
        -- 添加缺失字段
        ALTER TABLE users ADD COLUMN IF NOT EXISTS user_status VARCHAR(20) DEFAULT 'active';
        ALTER TABLE users ADD COLUMN IF NOT EXISTS phone_number VARCHAR(20);
        ALTER TABLE users ADD COLUMN IF NOT EXISTS user_preferences JSONB;
        ALTER TABLE users ADD COLUMN IF NOT EXISTS user_metadata JSONB;
        ALTER TABLE users ADD COLUMN IF NOT EXISTS last_login_at TIMESTAMPTZ;
    """)
    
    # 产品表标准化
    op.execute("""
        -- 标准化产品表字段
        ALTER TABLE products RENAME COLUMN id TO product_id;
        ALTER TABLE products RENAME COLUMN name TO product_name;
        ALTER TABLE products RENAME COLUMN description TO product_description;
        ALTER TABLE products RENAME COLUMN price TO unit_price;
        ALTER TABLE products RENAME COLUMN status TO product_status;
        ALTER TABLE products RENAME COLUMN images TO image_urls;
        ALTER TABLE products RENAME COLUMN specifications TO product_specs;
        ALTER TABLE products RENAME COLUMN "categoryId" TO category_id;
        ALTER TABLE products RENAME COLUMN "brandId" TO brand_id;
        ALTER TABLE products RENAME COLUMN "createdAt" TO created_at;
        ALTER TABLE products RENAME COLUMN "updatedAt" TO updated_at;
        
        -- 添加缺失字段
        ALTER TABLE products ADD COLUMN IF NOT EXISTS product_code VARCHAR(50) UNIQUE;
        ALTER TABLE products ADD COLUMN IF NOT EXISTS cost_price DECIMAL(10,2);
        ALTER TABLE products ADD COLUMN IF NOT EXISTS stock_quantity INTEGER DEFAULT 0;
        ALTER TABLE products ADD COLUMN IF NOT EXISTS min_stock_level INTEGER DEFAULT 0;
    """)
    
    # 订单表标准化
    op.execute("""
        -- 标准化订单表字段
        ALTER TABLE orders RENAME COLUMN id TO order_id;
        ALTER TABLE orders RENAME COLUMN "orderId" TO order_number;
        ALTER TABLE orders RENAME COLUMN "userId" TO customer_id;
        ALTER TABLE orders RENAME COLUMN "totalAmount" TO total_amount;
        ALTER TABLE orders RENAME COLUMN "orderStatus" TO order_status;
        ALTER TABLE orders RENAME COLUMN "orderDate" TO order_date;
        ALTER TABLE orders RENAME COLUMN items TO order_items;
        ALTER TABLE orders RENAME COLUMN "createdAt" TO created_at;
        ALTER TABLE orders RENAME COLUMN "updatedAt" TO updated_at;
        
        -- 添加缺失字段
        ALTER TABLE orders ADD COLUMN IF NOT EXISTS discount_amount DECIMAL(10,2) DEFAULT 0.0;
        ALTER TABLE orders ADD COLUMN IF NOT EXISTS final_amount DECIMAL(10,2);
        ALTER TABLE orders ADD COLUMN IF NOT EXISTS payment_status VARCHAR(20) DEFAULT 'unpaid';
        ALTER TABLE orders ADD COLUMN IF NOT EXISTS shipping_address JSONB;
        ALTER TABLE orders ADD COLUMN IF NOT EXISTS billing_address JSONB;
        ALTER TABLE orders ADD COLUMN IF NOT EXISTS confirmed_at TIMESTAMPTZ;
        ALTER TABLE orders ADD COLUMN IF NOT EXISTS shipped_at TIMESTAMPTZ;
        ALTER TABLE orders ADD COLUMN IF NOT EXISTS delivered_at TIMESTAMPTZ;
    """)
```

## 🚫 禁止模式

### 1. 禁止的数据处理模式
```python
# ❌ 禁止：直接使用源字段名
def process_user_data(user_data):
    email = user_data['email']  # 应该使用标准字段名
    verified = user_data['emailVerified']  # 字段名不一致
    return {'email': email, 'verified': verified}

# ❌ 禁止：不进行类型转换
def save_product(product_data):
    price = product_data['price']  # 可能是字符串，应该转换为float
    category_id = product_data['categoryId']  # 字段名不一致
    
# ❌ 禁止：混用模拟数据
def get_user_info(user_id):
    if user_id == 'test':
        return {'id': 'test', 'name': 'Test User'}  # 模拟数据
    else:
        return api_client.get_user(user_id)  # 真实数据
```

### 2. 必须的数据处理模式
```python
# ✅ 正确：使用字段映射管理器
def process_user_data(user_data):
    mapper = FieldMappingManager()
    return mapper.transform_data('user', user_data)

# ✅ 正确：统一数据验证和转换
def save_product(product_data):
    mapper = FieldMappingManager()
    validated_data = mapper.transform_data('product', product_data)
    return database.save_product(validated_data)

# ✅ 正确：禁止模拟数据
def get_user_info(user_id):
    if not isinstance(user_id, int) or user_id <= 0:
        raise ValueError("无效的用户ID")
    return api_client.get_user(user_id)
```

## 📋 数据一致性检查清单

### 开发阶段
- [ ] 是否使用统一的字段映射管理器？
- [ ] 是否进行数据类型转换和验证？
- [ ] 是否使用标准的数据库字段命名？
- [ ] 是否禁止模拟数据混入？
- [ ] 是否实现数据同步机制？

### 测试阶段
- [ ] 是否测试字段映射的正确性？
- [ ] 是否测试数据类型转换？
- [ ] 是否测试数据验证规则？
- [ ] 是否测试数据同步功能？
- [ ] 是否验证数据一致性？

### 部署阶段
- [ ] 是否执行数据库迁移？
- [ ] 是否验证生产数据格式？
- [ ] 是否设置数据监控？
- [ ] 是否准备数据回滚方案？

---

**重要提醒**: 本规范是强制性的，所有数据处理代码必须严格遵循此标准。违反此规范的代码将不被接受，需要重新修改后才能合并。

**文档版本**: v1.0  
**最后更新**: 2024-12-19  
**维护者**: 开发团队
