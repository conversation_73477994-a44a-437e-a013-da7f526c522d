# API响应格式统一规范

## 📋 概述

本文档定义了云商系统中所有API响应的统一格式规范和字段映射标准，确保系统中API响应处理的一致性和可维护性。

## 🎯 设计原则

### 1. 统一性原则
- 所有API响应必须遵循统一的格式结构
- 字段名称必须保持一致的命名规范
- 错误处理机制必须标准化

### 2. 兼容性原则
- 支持多种第三方API响应格式的适配
- 提供降级处理机制
- 保持向后兼容性

### 3. 可扩展性原则
- 支持新增字段的扩展
- 支持新API端点的集成
- 预留自定义处理逻辑的接口

## 📊 标准响应格式

### 1. 成功响应格式

#### 标准格式 (推荐)
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        "total": 100,
        "rows": [...],
        "current": 1,
        "size": 20
    }
}
```

#### 简化格式 (支持)
```json
{
    "total": 100,
    "rows": [...],
    "current": 1,
    "size": 20
}
```

#### 列表格式 (支持)
```json
[...]
```

### 2. 错误响应格式

#### HTTP错误
```json
{
    "code": 400,
    "message": "请求参数错误",
    "data": null,
    "timestamp": "2024-12-19T12:00:00Z"
}
```

#### 业务错误
```json
{
    "code": 401,
    "message": "认证失败",
    "data": null,
    "timestamp": "2024-12-19T12:00:00Z"
}
```

#### 系统错误
```json
{
    "code": -1,
    "message": "系统内部错误",
    "data": null,
    "timestamp": "2024-12-19T12:00:00Z"
}
```

## 🔄 响应格式适配规则

### 1. 数据提取优先级

```python
# 1. 标准格式优先
if response.get("code") == 200 and "data" in response:
    data = response["data"]
    if isinstance(data, dict) and "rows" in data:
        return data["rows"]  # 分页数据
    elif isinstance(data, list):
        return data  # 直接列表数据
    else:
        return data  # 单个对象数据

# 2. 直接格式支持
elif isinstance(response, dict) and "rows" in response:
    return response["rows"]

# 3. 列表格式支持
elif isinstance(response, list):
    return response

# 4. 错误处理
else:
    logger.warning(f"未识别的响应格式: {response}")
    return []  # 或 None，根据具体情况
```

### 2. 分页信息提取规则

```python
def extract_pagination_info(response: Dict[str, Any]) -> Dict[str, Any]:
    """提取分页信息"""
    pagination = {
        "total": 0,
        "current": 1,
        "size": 20,
        "pages": 0
    }
    
    # 从标准格式提取
    if response.get("code") == 200 and "data" in response:
        data = response["data"]
        if isinstance(data, dict):
            pagination.update({
                "total": data.get("total", 0),
                "current": data.get("current", 1),
                "size": data.get("size", 20),
                "pages": data.get("pages", 0)
            })
    
    # 从直接格式提取
    elif isinstance(response, dict):
        pagination.update({
            "total": response.get("total", 0),
            "current": response.get("current", 1),
            "size": response.get("size", 20),
            "pages": response.get("pages", 0)
        })
    
    # 计算总页数
    if pagination["total"] > 0 and pagination["size"] > 0:
        pagination["pages"] = (pagination["total"] + pagination["size"] - 1) // pagination["size"]
    
    return pagination
```

## 📝 字段映射标准

### 1. 产品数据字段映射

| 源字段名 | 标准字段名 | 数据类型 | 说明 |
|----------|------------|----------|------|
| id | id | int | 产品ID |
| name | name | string | 产品名称 |
| productName | name | string | 产品名称 (备用字段) |
| category | categoryId | int | 分类ID |
| categoryName | categoryName | string | 分类名称 |
| label | labelId | int | 标签ID |
| labelName | labelName | string | 标签名称 |
| labelList | labels | array | 标签列表 |
| smallImg | thumbnail | string | 缩略图 |
| banner | banner | string | 横幅图 |
| status | status | string | 状态 |
| spec | specification | string | 规格 |
| introduction | introduction | string | 简介 |
| details | details | string | 详情 |
| price | price | float | 价格 |
| likeCount | likeCount | int | 点赞数 |
| favoriteCount | favoriteCount | int | 收藏数 |

### 2. 案例数据字段映射

| 源字段名 | 标准字段名 | 数据类型 | 说明 |
|----------|------------|----------|------|
| id | id | int | 案例ID |
| name | title | string | 案例标题 |
| caseName | title | string | 案例标题 (备用字段) |
| category | categoryId | int | 分类ID |
| categoryName | categoryName | string | 分类名称 |
| productId | productId | int | 关联产品ID |
| productName | productName | string | 关联产品名称 |
| companyName | companyName | string | 公司名称 |
| industry | industry | string | 行业 |
| location | location | string | 地点 |
| description | description | string | 描述 |
| images | images | array | 图片列表 |
| createTime | createTime | string | 创建时间 |

### 3. 方案数据字段映射

| 源字段名 | 标准字段名 | 数据类型 | 说明 |
|----------|------------|----------|------|
| id | id | int | 方案ID |
| name | title | string | 方案标题 |
| programmeName | title | string | 方案标题 (备用字段) |
| category | categoryId | int | 分类ID |
| categoryName | categoryName | string | 分类名称 |
| productId | productId | int | 关联产品ID |
| productName | productName | string | 关联产品名称 |
| content | content | string | 方案内容 |
| overview | overview | string | 方案概述 |
| features | features | string | 特性说明 |
| advantages | advantages | string | 优势介绍 |
| applications | applications | string | 应用场景 |

### 4. 资讯数据字段映射

| 源字段名 | 标准字段名 | 数据类型 | 说明 |
|----------|------------|----------|------|
| id | id | int | 资讯ID |
| title | title | string | 资讯标题 |
| categoryId | categoryId | int | 分类ID |
| categoryName | categoryName | string | 分类名称 |
| content | content | string | 资讯内容 |
| summary | summary | string | 资讯摘要 |
| author | author | string | 作者 |
| publishTime | publishTime | string | 发布时间 |
| readCount | readCount | int | 阅读数 |
| coverImage | coverImage | string | 封面图片 |

### 5. 配单数据字段映射

| 源字段名 | 标准字段名 | 数据类型 | 说明 |
|----------|------------|----------|------|
| id | id | int | 配单ID |
| name | name | string | 配单名称 |
| orderNo | orderNo | string | 配单编号 |
| companyId | companyId | int | 公司ID |
| companyName | companyName | string | 公司名称 |
| productId | productId | int | 产品ID |
| productName | productName | string | 产品名称 |
| firCategoryId | primaryCategoryId | int | 一级分类ID |
| firCategoryName | primaryCategoryName | string | 一级分类名称 |
| secCategoryId | secondaryCategoryId | int | 二级分类ID |
| secCategoryName | secondaryCategoryName | string | 二级分类名称 |
| status | status | string | 状态 |
| sourceType | sourceType | int | 来源类型 |
| hidePrice | hidePrice | int | 隐藏价格 |
| hasExpire | hasExpired | int | 是否过期 |

## 🛠️ 实现工具类

### 1. 响应格式标准化器

```python
class ApiResponseNormalizer:
    """API响应格式标准化器"""
    
    @staticmethod
    def normalize_list_response(response: Any, field_mapping: Dict[str, str] = None) -> List[Dict[str, Any]]:
        """标准化列表响应"""
        
    @staticmethod
    def normalize_single_response(response: Any, field_mapping: Dict[str, str] = None) -> Optional[Dict[str, Any]]:
        """标准化单个对象响应"""
        
    @staticmethod
    def normalize_pagination_response(response: Any) -> Dict[str, Any]:
        """标准化分页响应"""
        
    @staticmethod
    def apply_field_mapping(data: Dict[str, Any], field_mapping: Dict[str, str]) -> Dict[str, Any]:
        """应用字段映射"""
```

### 2. 字段映射验证器

```python
class FieldMappingValidator:
    """字段映射一致性验证器"""
    
    @staticmethod
    def validate_response_format(response: Any, expected_fields: List[str]) -> bool:
        """验证响应格式"""
        
    @staticmethod
    def check_field_consistency(data_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """检查字段一致性"""
        
    @staticmethod
    def generate_mapping_report(api_responses: Dict[str, Any]) -> str:
        """生成映射报告"""
```

## 🔍 一致性检查规则

### 1. 字段命名一致性

- **驼峰命名法**: 统一使用驼峰命名法 (camelCase)
- **字段前缀**: 避免使用无意义的前缀 (如fir, sec)
- **语义明确**: 字段名称必须语义明确，避免缩写

### 2. 数据类型一致性

- **ID字段**: 统一使用 int 类型
- **时间字段**: 统一使用 ISO 8601 格式的字符串
- **状态字段**: 统一使用 string 类型
- **计数字段**: 统一使用 int 类型

### 3. 空值处理一致性

- **必填字段**: 不允许为空，提供默认值
- **可选字段**: 允许为空，使用 null 或空字符串
- **数组字段**: 空数组而非 null

## 📋 质量保证清单

### 开发阶段检查
- [ ] 是否遵循统一的响应格式？
- [ ] 是否使用标准的字段映射？
- [ ] 是否实现了错误处理机制？
- [ ] 是否添加了响应格式验证？

### 测试阶段检查
- [ ] 是否测试了所有支持的响应格式？
- [ ] 是否验证了字段映射的正确性？
- [ ] 是否测试了错误情况的处理？
- [ ] 是否生成了一致性检查报告？

### 部署阶段检查
- [ ] 是否更新了API文档？
- [ ] 是否通知了相关开发人员？
- [ ] 是否建立了监控机制？
- [ ] 是否准备了回滚方案？

## 📚 相关文档

- [API接口文档](api-interface-documentation.md) - 具体接口说明
- [数据模型设计](data-models-design.md) - 数据库模型设计
- [系统架构设计](system-architecture.md) - 系统架构说明

---

**文档版本**: v1.0  
**最后更新**: 2024-12-19  
**维护者**: 开发团队  
**审核状态**: 待审核 