"""
云商系统图片处理器
处理从云商接口获取的图片数据，包括下载、本地存储、格式转换等功能
支持图片用途标识和分类管理
"""

import os
import logging
import hashlib
import mimetypes
import requests
from typing import Dict, List, Optional, Tuple, Any, Union
from pathlib import Path
from datetime import datetime
from PIL import Image, ImageOps
import base64
from urllib.parse import urlparse, unquote
import time
import json

logger = logging.getLogger(__name__)


class ImageInfo:
    """图片信息类"""

    def __init__(
        self,
        original_url: str,
        local_path: str,
        filename: str,
        file_size: int,
        format: str,
        purpose: str = "",
        width: int = 0,
        height: int = 0,
        md5_hash: str = "",
        download_time: str = "",
        is_thumbnail: bool = False,
        thumbnail_path: str = "",
        module: str = "",
        item_id: str = "",
        field_name: str = "",
    ):
        self.original_url = original_url
        self.local_path = local_path
        self.filename = filename
        self.file_size = file_size
        self.format = format
        self.purpose = purpose  # 图片用途
        self.width = width
        self.height = height
        self.md5_hash = md5_hash
        self.download_time = download_time
        self.is_thumbnail = is_thumbnail
        self.thumbnail_path = thumbnail_path
        self.module = module
        self.item_id = item_id
        self.field_name = field_name

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "original_url": self.original_url,
            "local_path": self.local_path,
            "filename": self.filename,
            "file_size": self.file_size,
            "format": self.format,
            "purpose": self.purpose,
            "width": self.width,
            "height": self.height,
            "md5_hash": self.md5_hash,
            "download_time": self.download_time,
            "is_thumbnail": self.is_thumbnail,
            "thumbnail_path": self.thumbnail_path,
            "module": self.module,
            "item_id": self.item_id,
            "field_name": self.field_name,
        }


class ImagePurposeManager:
    """图片用途管理器"""

    # 图片用途分类
    PURPOSE_CATEGORIES = {
        "展示类": ["缩略图", "轮播图", "案例图片", "方案图片", "资讯图片"],
        "技术类": ["尺寸图", "参数图", "规格图", "安装图"],
        "媒体类": ["视频封面", "动画图", "GIF图"],
        "文档类": ["说明图", "示意图", "流程图", "架构图"],
        "其他": ["未分类", "自定义"],
    }

    # 用途对应的存储策略
    PURPOSE_STORAGE_CONFIG = {
        "缩略图": {"quality": 85, "max_size": (300, 300), "format": "JPEG"},
        "轮播图": {"quality": 90, "max_size": (1200, 800), "format": "JPEG"},
        "尺寸图": {"quality": 95, "max_size": (800, 600), "format": "PNG"},
        "案例图片": {"quality": 90, "max_size": (1000, 750), "format": "JPEG"},
        "视频封面": {"quality": 85, "max_size": (640, 360), "format": "JPEG"},
        "资讯图片": {"quality": 88, "max_size": (800, 600), "format": "JPEG"},
    }

    @classmethod
    def get_category_by_purpose(cls, purpose: str) -> str:
        """根据用途获取分类"""
        for category, purposes in cls.PURPOSE_CATEGORIES.items():
            if purpose in purposes:
                return category
        return "其他"

    @classmethod
    def get_storage_config(cls, purpose: str) -> Dict[str, Any]:
        """获取用途对应的存储配置"""
        return cls.PURPOSE_STORAGE_CONFIG.get(
            purpose, {"quality": 85, "max_size": (800, 600), "format": "JPEG"}
        )

    @classmethod
    def validate_purpose(cls, purpose: str) -> bool:
        """验证用途是否有效"""
        all_purposes = []
        for purposes in cls.PURPOSE_CATEGORIES.values():
            all_purposes.extend(purposes)
        return purpose in all_purposes


class ImageProcessor:
    """图片处理器"""

    def __init__(self, storage_base_dir: str = "images"):
        """
        初始化图片处理器

        Args:
            storage_base_dir: 图片存储根目录
        """
        self.storage_base_dir = Path(storage_base_dir)
        self.storage_base_dir.mkdir(exist_ok=True)

        # 按业务模块创建子目录
        self.module_dirs = {
            "products": self.storage_base_dir / "products",
            "cases": self.storage_base_dir / "cases",
            "programmes": self.storage_base_dir / "programmes",
            "information": self.storage_base_dir / "information",
            "distribution_orders": self.storage_base_dir / "distribution_orders",
        }

        for module_dir in self.module_dirs.values():
            module_dir.mkdir(exist_ok=True)
            # 创建原图和缩略图子目录
            (module_dir / "original").mkdir(exist_ok=True)
            (module_dir / "thumbnails").mkdir(exist_ok=True)

        # 支持的图片格式
        self.supported_formats = {
            ".jpg",
            ".jpeg",
            ".png",
            ".gif",
            ".bmp",
            ".webp",
            ".tiff",
            ".svg",
        }

        # 下载配置
        self.timeout = 30
        self.max_retries = 3
        self.chunk_size = 8192
        self.max_file_size = 50 * 1024 * 1024  # 50MB

        # 缩略图配置
        self.thumbnail_size = (300, 300)
        self.thumbnail_quality = 85

        # 用途管理器
        self.purpose_manager = ImagePurposeManager()

        logger.info(f"图片处理器初始化完成 - 存储目录: {self.storage_base_dir}")

    def process_item_images(
        self, item_data: Dict[str, Any], module: str, item_id: str
    ) -> Dict[str, List[ImageInfo]]:
        """
        处理单个业务数据的所有图片

        Args:
            item_data: 业务数据
            module: 业务模块名
            item_id: 业务数据ID

        Returns:
            字段名到图片信息列表的映射
        """
        # 定义各模块的图片字段
        image_fields = {
            "products": ["smallImg", "banner", "sizeImg"],
            "cases": ["img", "banner", "smallImg", "video"],
            "programmes": ["smallImg", "banner"],
            "information": ["picVideo", "smallImg", "images"],
            "distribution_orders": [],
        }

        fields = image_fields.get(module, [])
        image_results = {}

        for field in fields:
            if field in item_data and item_data[field]:
                field_value = item_data[field]
                logger.info(f"处理 {module} ID:{item_id} 的图片字段 {field}")

                # 处理单个URL或多个URL
                if isinstance(field_value, list):
                    # 多个图片URL
                    field_images = []
                    for i, url in enumerate(field_value):
                        if isinstance(url, str) and self._is_valid_image_url(url):
                            image_info = self.download_image_with_purpose(
                                url, module, item_id, f"{field}_{i}", "未分类"
                            )
                            if image_info:
                                field_images.append(image_info)
                    if field_images:
                        image_results[field] = field_images

                elif isinstance(field_value, str):
                    if "," in field_value:
                        # 逗号分隔的多个URL
                        urls = [
                            url.strip() for url in field_value.split(",") if url.strip()
                        ]
                        field_images = []
                        for i, url in enumerate(urls):
                            if self._is_valid_image_url(url):
                                image_info = self.download_image_with_purpose(
                                    url, module, item_id, f"{field}_{i}", "未分类"
                                )
                                if image_info:
                                    field_images.append(image_info)
                        if field_images:
                            image_results[field] = field_images
                    else:
                        # 单个URL
                        if self._is_valid_image_url(field_value):
                            image_info = self.download_image_with_purpose(
                                field_value, module, item_id, field, "未分类"
                            )
                            if image_info:
                                image_results[field] = [image_info]

        logger.info(f"{module} {item_id} 图片处理完成: {len(image_results)} 个字段")
        return image_results

    def download_image_with_purpose(
        self,
        url: str,
        module: str,
        item_id: str,
        field_name: str,
        purpose: str = "未分类",
    ) -> Optional[ImageInfo]:
        """下载带用途标识的图片"""
        if not self._is_valid_image_url(url):
            logger.warning(f"无效的图片URL: {url}")
            return None

        # 验证用途
        if not self.purpose_manager.validate_purpose(purpose):
            logger.warning(f"无效的图片用途: {purpose}，使用默认用途")
            purpose = "未分类"

        try:
            # 获取用途分类
            category = self.purpose_manager.get_category_by_purpose(purpose)
            category_dir = category.replace("类", "")

            # 创建目标目录
            module_dir = self.module_dirs.get(module)
            if not module_dir:
                logger.error(f"无效的模块: {module}")
                return None

            item_dir = module_dir / "original" / category_dir / str(item_id)
            item_dir.mkdir(parents=True, exist_ok=True)

            # 生成文件名（包含用途信息）
            filename = self._generate_filename_with_purpose(url, field_name, purpose)
            target_path = item_dir / filename

            # 检查文件是否已存在
            if target_path.exists():
                logger.info(f"图片已存在: {filename}")
                return self._create_image_info_with_purpose(
                    url, target_path, field_name, purpose, module, item_id
                )

            # 下载图片
            headers = {"User-Agent": "YunShang-ImageProcessor/1.0"}
            response = requests.get(
                url, headers=headers, timeout=self.timeout, stream=True
            )
            response.raise_for_status()

            # 检查文件大小
            content_length = response.headers.get("content-length")
            if content_length and int(content_length) > self.max_file_size:
                logger.warning(f"图片文件过大: {url}")
                return None

            # 保存图片
            with open(target_path, "wb") as f:
                for chunk in response.iter_content(chunk_size=self.chunk_size):
                    if chunk:
                        f.write(chunk)

            # 验证文件
            if not target_path.exists() or target_path.stat().st_size == 0:
                logger.error(f"图片下载失败: {target_path}")
                if target_path.exists():
                    target_path.unlink()
                return None

            # 创建图片信息
            image_info = self._create_image_info_with_purpose(
                url, target_path, field_name, purpose, module, item_id
            )

            # 根据用途优化图片
            if image_info:
                self._optimize_image_by_purpose(target_path, purpose)

                # 生成缩略图
                if image_info.format.lower() in ["jpeg", "jpg", "png"]:
                    self._create_thumbnail_with_purpose(
                        target_path, module, item_id, field_name, purpose, category_dir
                    )

            logger.info(f"图片下载成功: {filename} (用途: {purpose})")
            return image_info

        except Exception as e:
            logger.error(f"下载图片失败 {url}: {e}")
            return None

    def _optimize_image_by_purpose(self, image_path: Path, purpose: str):
        """根据用途优化图片"""
        try:
            config = self.purpose_manager.get_storage_config(purpose)
            max_size = config.get("max_size", (800, 600))
            quality = config.get("quality", 85)
            target_format = config.get("format", "JPEG")

            with Image.open(image_path) as img:
                # 转换模式
                if target_format == "JPEG" and img.mode in ("RGBA", "P"):
                    img = img.convert("RGB")
                elif target_format == "PNG" and img.mode == "P":
                    img = img.convert("RGBA")

                # 调整尺寸
                if img.size[0] > max_size[0] or img.size[1] > max_size[1]:
                    img.thumbnail(max_size, Image.Resampling.LANCZOS)

                # 保存优化后的图片
                save_kwargs = {"optimize": True}
                if target_format == "JPEG":
                    save_kwargs["quality"] = quality

                img.save(image_path, target_format, **save_kwargs)

            logger.info(f"图片优化完成: {image_path.name} (用途: {purpose})")

        except Exception as e:
            logger.error(f"图片优化失败: {e}")

    def _create_thumbnail_with_purpose(
        self,
        image_path: Path,
        module: str,
        item_id: str,
        field_name: str,
        purpose: str,
        category_dir: str,
    ):
        """创建带用途的缩略图"""
        try:
            thumbnail_dir = (
                self.module_dirs[module] / "thumbnails" / category_dir / str(item_id)
            )
            thumbnail_dir.mkdir(parents=True, exist_ok=True)

            thumbnail_filename = f"{field_name}_{purpose}_thumb_{image_path.stem}.jpg"
            thumbnail_path = thumbnail_dir / thumbnail_filename

            if thumbnail_path.exists():
                return

            with Image.open(image_path) as img:
                if img.mode in ("RGBA", "P"):
                    img = img.convert("RGB")

                img.thumbnail(self.thumbnail_size, Image.Resampling.LANCZOS)
                img.save(thumbnail_path, "JPEG", quality=85, optimize=True)

            logger.info(f"缩略图创建成功: {thumbnail_filename}")

        except Exception as e:
            logger.error(f"创建缩略图失败: {e}")

    def _create_image_info_with_purpose(
        self,
        url: str,
        image_path: Path,
        field_name: str,
        purpose: str,
        module: str,
        item_id: str,
    ) -> Optional[ImageInfo]:
        """创建带用途的图片信息对象"""
        try:
            file_stats = image_path.stat()
            file_size = file_stats.st_size
            md5_hash = self._calculate_md5(image_path)

            # 获取图片信息
            width, height, format_name = 0, 0, ""
            try:
                with Image.open(image_path) as img:
                    width, height = img.size
                    format_name = img.format or ""
            except Exception:
                format_name = image_path.suffix.lstrip(".").upper()

            return ImageInfo(
                original_url=url,
                local_path=str(image_path.relative_to(self.storage_base_dir)),
                filename=image_path.name,
                file_size=file_size,
                format=format_name,
                purpose=purpose,
                width=width,
                height=height,
                md5_hash=md5_hash,
                download_time=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                module=module,
                item_id=item_id,
                field_name=field_name,
            )

        except Exception as e:
            logger.error(f"创建图片信息失败: {e}")
            return None

    def _generate_filename_with_purpose(
        self, url: str, field_name: str, purpose: str
    ) -> str:
        """生成包含用途的安全文件名"""
        try:
            parsed_url = urlparse(url)
            original_filename = unquote(Path(parsed_url.path).name)

            # 从参数中提取文件名
            if not original_filename or original_filename == "/":
                query_params = parsed_url.query
                if "file=" in query_params:
                    for param in query_params.split("&"):
                        if param.startswith("file="):
                            original_filename = unquote(param.split("=", 1)[1])
                            break

            # 使用默认名称
            if not original_filename or original_filename == "/":
                timestamp = int(time.time())
                original_filename = f"image_{timestamp}.jpg"

            # 清理文件名
            safe_filename = self._sanitize_filename(original_filename)

            # 添加字段名和用途前缀
            name_parts = safe_filename.rsplit(".", 1)
            if len(name_parts) == 2:
                safe_filename = (
                    f"{field_name}_{purpose}_{name_parts[0]}.{name_parts[1]}"
                )
            else:
                safe_filename = f"{field_name}_{purpose}_{safe_filename}"

            return safe_filename

        except Exception as e:
            timestamp = int(time.time())
            return f"{field_name}_{purpose}_image_{timestamp}.jpg"

    def _sanitize_filename(self, filename: str) -> str:
        """清理文件名"""
        unsafe_chars = '<>:"/\\|?*'
        for char in unsafe_chars:
            filename = filename.replace(char, "_")

        if len(filename) > 100:
            name_parts = filename.rsplit(".", 1)
            if len(name_parts) == 2:
                filename = f"{name_parts[0][:90]}.{name_parts[1]}"
            else:
                filename = filename[:100]

        return filename

    def _is_valid_image_url(self, url: str) -> bool:
        """检查是否为有效的图片URL"""
        if not isinstance(url, str) or not url.strip():
            return False

        url = url.strip()

        if not url.startswith(("http://", "https://")):
            return False

        try:
            parsed_url = urlparse(url)
            path = parsed_url.path.lower()

            # 检查扩展名
            for ext in self.supported_formats:
                if path.endswith(ext):
                    return True

            # 检查查询参数
            if "file=" in parsed_url.query:
                for param in parsed_url.query.split("&"):
                    if param.startswith("file="):
                        filename = unquote(param.split("=", 1)[1]).lower()
                        for ext in self.supported_formats:
                            if filename.endswith(ext):
                                return True

            return True

        except Exception:
            return False

    def _calculate_md5(self, file_path: Path) -> str:
        """计算文件MD5"""
        try:
            md5_hash = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    md5_hash.update(chunk)
            return md5_hash.hexdigest()
        except Exception:
            return ""

    def get_images_by_purpose(self, module: str, purpose: str) -> List[ImageInfo]:
        """根据用途获取图片列表"""
        try:
            category = self.purpose_manager.get_category_by_purpose(purpose)
            category_dir = category.replace("类", "")

            module_dir = self.module_dirs.get(module)
            if not module_dir:
                return []

            purpose_dir = module_dir / "original" / category_dir
            if not purpose_dir.exists():
                return []

            images = []
            for item_dir in purpose_dir.iterdir():
                if item_dir.is_dir():
                    for image_file in item_dir.glob("*"):
                        if image_file.is_file() and f"_{purpose}_" in image_file.name:
                            # 这里可以从文件名或数据库中恢复ImageInfo
                            # 简化实现，仅返回基本信息
                            pass

            return images

        except Exception as e:
            logger.error(f"获取图片列表失败: {e}")
            return []

    def get_storage_statistics(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        try:
            stats = {
                "total_size": 0,
                "total_files": 0,
                "by_module": {},
                "by_purpose": {},
            }

            for module_name, module_dir in self.module_dirs.items():
                if not module_dir.exists():
                    continue

                module_stats = {"size": 0, "files": 0, "purposes": {}}

                original_dir = module_dir / "original"
                if original_dir.exists():
                    for category_dir in original_dir.iterdir():
                        if category_dir.is_dir():
                            for item_dir in category_dir.iterdir():
                                if item_dir.is_dir():
                                    for image_file in item_dir.glob("*"):
                                        if image_file.is_file():
                                            file_size = image_file.stat().st_size
                                            module_stats["size"] += file_size
                                            module_stats["files"] += 1
                                            stats["total_size"] += file_size
                                            stats["total_files"] += 1

                                            # 从文件名提取用途
                                            filename_parts = image_file.stem.split("_")
                                            if len(filename_parts) >= 2:
                                                purpose = filename_parts[1]
                                                if (
                                                    purpose
                                                    not in module_stats["purposes"]
                                                ):
                                                    module_stats["purposes"][
                                                        purpose
                                                    ] = 0
                                                module_stats["purposes"][purpose] += 1

                                                if purpose not in stats["by_purpose"]:
                                                    stats["by_purpose"][purpose] = 0
                                                stats["by_purpose"][purpose] += 1

                stats["by_module"][module_name] = module_stats

            return stats

        except Exception as e:
            logger.error(f"获取存储统计失败: {e}")
            return {}


# 兼容旧接口的方法
def download_image(
    url: str, module: str, item_id: str, field_name: str
) -> Optional[ImageInfo]:
    """兼容旧接口的下载方法"""
    processor = ImageProcessor()
    return processor.download_image_with_purpose(
        url, module, item_id, field_name, "未分类"
    )


# 便捷函数
def process_api_images_with_purpose(
    api_data: List[Dict[str, Any]], module: str, purpose_mapping: Dict[str, str] = None
) -> Dict[str, Dict[str, List[ImageInfo]]]:
    """处理API响应数据中的所有图片（带用途）"""
    processor = ImageProcessor()
    results = {}

    # 默认用途映射
    default_purpose_mapping = {
        "smallImg": "缩略图",
        "banner": "轮播图",
        "sizeImg": "尺寸图",
        "img": "案例图片",
        "picVideo": "视频封面",
        "images": "资讯图片",
    }

    if purpose_mapping:
        default_purpose_mapping.update(purpose_mapping)

    for item in api_data:
        item_id = str(item.get("id", ""))
        if item_id:
            item_results = {}

            for field_name, purpose in default_purpose_mapping.items():
                if field_name in item and item[field_name]:
                    field_value = item[field_name]

                    if isinstance(field_value, str):
                        if "," in field_value:
                            # 多个URL
                            urls = [
                                url.strip()
                                for url in field_value.split(",")
                                if url.strip()
                            ]
                            field_images = []
                            for i, url in enumerate(urls):
                                image_info = processor.download_image_with_purpose(
                                    url, module, item_id, f"{field_name}_{i}", purpose
                                )
                                if image_info:
                                    field_images.append(image_info)
                            if field_images:
                                item_results[field_name] = field_images
                        else:
                            # 单个URL
                            image_info = processor.download_image_with_purpose(
                                field_value, module, item_id, field_name, purpose
                            )
                            if image_info:
                                item_results[field_name] = [image_info]

            if item_results:
                results[item_id] = item_results

    return results
