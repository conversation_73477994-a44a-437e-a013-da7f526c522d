#!/usr/bin/env python3
"""
检查品牌信息
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 加载环境变量
from config import load_env_file
load_env_file()

from utils.api_client import ZKMallClient
from utils.auth import AuthManager

def check_brand_info():
    """检查品牌信息"""
    try:
        # 登录并检查公司相关字段
        success, user_info = AuthManager.login("18929343717", "Zk@123456")
        if success:
            api_client = ZKMallClient()
            products = api_client.get_products(pageSize=5, current=1)
            
            print("=== 检查产品中的公司/厂商相关字段 ===")
            for i, product in enumerate(products[:3], 1):
                print(f"\n产品 {i}: {product.get('name', 'Unknown')}")
                
                # 检查公司相关字段
                company_fields = [
                    'show_for_company', 'show_for_company_name', 'show_for_company_list',
                    'company_id', 'company_name', 'manufacturer', 'vendor', 'supplier'
                ]
                
                for field in company_fields:
                    value = product.get(field)
                    if value is not None:
                        if isinstance(value, str) and len(value) > 50:
                            display_value = value[:50] + "..."
                        else:
                            display_value = value
                        print(f"  {field}: {display_value}")
                
                # 检查是否有其他可能包含厂商信息的字段
                print("  其他可能的厂商字段:")
                for key, value in product.items():
                    if any(keyword in key.lower() for keyword in ['company', 'brand', 'manufacturer', 'vendor', 'supplier', 'maker']):
                        if value is not None:
                            if isinstance(value, str) and len(value) > 50:
                                display_value = value[:50] + "..."
                            else:
                                display_value = value
                            print(f"    {key}: {display_value}")
                            
            # 检查是否可以从产品名称中推断品牌
            print("\n=== 尝试从产品名称推断品牌 ===")
            for i, product in enumerate(products[:5], 1):
                name = product.get('name', '')
                spec = product.get('spec', '')
                print(f"产品 {i}:")
                print(f"  名称: {name}")
                print(f"  型号: {spec}")
                
                # 简单的品牌推断逻辑
                if 'ZK' in name or 'ZK' in spec:
                    print(f"  推断品牌: 中控科技 (ZKTeco)")
                elif any(brand in name.upper() for brand in ['HIKVISION', '海康', 'DAHUA', '大华']):
                    print(f"  推断品牌: 从产品名称识别")
                else:
                    print(f"  推断品牌: 中控科技 (默认)")
                    
    except Exception as e:
        print(f"检查失败: {e}")

if __name__ == "__main__":
    check_brand_info()
