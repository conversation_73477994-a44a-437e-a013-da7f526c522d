#!/usr/bin/env python3
"""
全面系统分析器

在修复之前，先全面梳理云商数据获取和解析的现状，识别所有问题
"""

import sys
import os
import json
import logging
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path

# 添加项目路径
sys.path.append(".")

# 设置环境变量
os.environ["DATABASE_HOST"] = "***********"
os.environ["DATABASE_PORT"] = "5432"
os.environ["DATABASE_NAME"] = "product"
os.environ["DATABASE_USER"] = "username"
os.environ["DATABASE_PASSWORD"] = "123456"
os.environ["ZKMALL_API_BASE"] = "https://zkmall.zktecoiot.com"
os.environ["ZKMALL_USERNAME"] = "18929343717"
os.environ["ZKMALL_PASSWORD"] = "Zk@123456"

from utils.auth import AuthManager
from utils.api_client import ZKMallClient
from utils.database import DatabaseManager
from utils.api_response_normalizer import ApiResponseNormalizer

# 设置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class ComprehensiveSystemAnalyzer:
    """全面系统分析器"""

    def __init__(self):
        """初始化分析器"""
        self.client = None
        self.db_manager = None
        self.normalizer = ApiResponseNormalizer()
        self.analysis_results = {
            "api_data_structure": {},
            "database_structure": {},
            "field_mapping_analysis": {},
            "data_flow_test": {},
            "identified_issues": [],
            "fix_recommendations": [],
        }

    def run_comprehensive_analysis(self) -> Dict[str, Any]:
        """运行全面分析"""
        print("🔍 开始全面系统分析...")
        print("=" * 80)

        try:
            # 1. 环境检查
            if not self._check_environment():
                return self.analysis_results

            # 2. API数据结构分析
            self._analyze_api_data_structure()

            # 3. 数据库结构分析
            self._analyze_database_structure()

            # 4. 字段映射分析
            self._analyze_field_mappings()

            # 5. 数据流测试
            self._test_data_flow()

            # 6. 问题识别
            self._identify_issues()

            # 7. 生成修复建议
            self._generate_fix_recommendations()

            # 8. 输出分析报告
            self._print_comprehensive_report()

            return self.analysis_results

        except Exception as e:
            logger.error(f"分析过程出错: {e}")
            import traceback

            traceback.print_exc()
            return self.analysis_results

    def _check_environment(self) -> bool:
        """检查环境"""
        print("\n1. 🔧 环境检查")
        print("-" * 40)

        try:
            # 检查API认证
            if not AuthManager.ensure_authenticated():
                print("❌ API认证失败")
                return False

            print("✅ API认证成功")
            self.client = ZKMallClient()

            # 检查数据库连接
            try:
                self.db_manager = DatabaseManager()
                with self.db_manager.get_connection() as conn:
                    with conn.cursor() as cursor:
                        cursor.execute("SELECT 1")
                print("✅ 数据库连接成功")
            except Exception as e:
                print(f"❌ 数据库连接失败: {e}")
                return False

            return True

        except Exception as e:
            print(f"❌ 环境检查失败: {e}")
            return False

    def _analyze_api_data_structure(self):
        """分析API数据结构"""
        print("\n2. 📊 API数据结构分析")
        print("-" * 40)

        try:
            # 获取产品数据样本
            products = self.client.get_products(pageSize=3, current=1)
            if not products:
                print("❌ 未能获取产品数据")
                return

            print(f"✅ 获取到 {len(products)} 个产品样本")

            # 分析字段结构
            all_fields = set()
            field_types = {}
            field_samples = {}

            for product in products:
                for field, value in product.items():
                    all_fields.add(field)

                    # 记录字段类型
                    field_type = type(value).__name__
                    if field not in field_types:
                        field_types[field] = set()
                    field_types[field].add(field_type)

                    # 记录样本值
                    if field not in field_samples and value is not None:
                        sample_value = (
                            str(value)[:100] + "..."
                            if len(str(value)) > 100
                            else str(value)
                        )
                        field_samples[field] = sample_value

            # 分类字段
            knowledge_fields = []
            attachment_fields = []
            basic_fields = []

            knowledge_keywords = [
                "introduction",
                "details",
                "param",
                "usage",
                "guide",
                "instruction",
                "problem",
                "specification",
            ]
            attachment_keywords = [
                "img",
                "banner",
                "video",
                "attachment",
                "qualification",
                "other",
            ]

            for field in all_fields:
                field_lower = field.lower()
                if any(keyword in field_lower for keyword in knowledge_keywords):
                    knowledge_fields.append(field)
                elif any(keyword in field_lower for keyword in attachment_keywords):
                    attachment_fields.append(field)
                else:
                    basic_fields.append(field)

            print(f"   总字段数: {len(all_fields)}")
            print(f"   知识内容字段: {len(knowledge_fields)} 个")
            print(f"   附件相关字段: {len(attachment_fields)} 个")
            print(f"   基础信息字段: {len(basic_fields)} 个")

            # 详细分析知识内容字段
            print("\n   📚 知识内容字段详情:")
            for field in sorted(knowledge_fields):
                types = list(field_types.get(field, ["unknown"]))
                sample = field_samples.get(field, "无样本")
                print(f"     {field} ({'/'.join(types)}): {sample}")

            # 详细分析附件字段
            print("\n   📎 附件相关字段详情:")
            for field in sorted(attachment_fields):
                types = list(field_types.get(field, ["unknown"]))
                sample = field_samples.get(field, "无样本")
                print(f"     {field} ({'/'.join(types)}): {sample}")

            self.analysis_results["api_data_structure"] = {
                "total_fields": len(all_fields),
                "all_fields": list(all_fields),
                "knowledge_fields": knowledge_fields,
                "attachment_fields": attachment_fields,
                "basic_fields": basic_fields,
                "field_types": {k: list(v) for k, v in field_types.items()},
                "field_samples": field_samples,
                "sample_products": products,
            }

        except Exception as e:
            print(f"❌ API数据结构分析失败: {e}")
            self.analysis_results["api_data_structure"]["error"] = str(e)

    def _analyze_database_structure(self):
        """分析数据库结构"""
        print("\n3. 🗄️ 数据库结构分析")
        print("-" * 40)

        try:
            with self.db_manager.get_connection() as conn:
                with conn.cursor() as cursor:
                    # 获取products表结构
                    cursor.execute(
                        """
                        SELECT column_name, data_type, is_nullable, column_default
                        FROM information_schema.columns
                        WHERE table_name = 'products' AND table_schema = 'public'
                        ORDER BY ordinal_position
                    """
                    )

                    columns = cursor.fetchall()
                    if not columns:
                        print("❌ 未找到products表")
                        return

                    print(f"✅ products表包含 {len(columns)} 个字段")

                    # 分析字段类型
                    db_fields = {}
                    knowledge_db_fields = []
                    attachment_db_fields = []

                    knowledge_keywords = [
                        "introduction",
                        "details",
                        "param",
                        "usage",
                        "guide",
                        "instruction",
                        "problem",
                        "spec",
                    ]
                    attachment_keywords = [
                        "img",
                        "banner",
                        "video",
                        "attachment",
                        "qualification",
                        "other",
                    ]

                    print("\n   📋 数据库字段详情:")
                    for col_name, data_type, is_nullable, default_val in columns:
                        db_fields[col_name] = {
                            "type": data_type,
                            "nullable": is_nullable == "YES",
                            "default": default_val,
                        }

                        # 分类字段
                        col_lower = col_name.lower()
                        if any(keyword in col_lower for keyword in knowledge_keywords):
                            knowledge_db_fields.append(col_name)
                            print(f"     📚 {col_name} ({data_type})")
                        elif any(
                            keyword in col_lower for keyword in attachment_keywords
                        ):
                            attachment_db_fields.append(col_name)
                            print(f"     📎 {col_name} ({data_type})")
                        else:
                            print(f"     📄 {col_name} ({data_type})")

                    print(f"\n   知识内容相关字段: {len(knowledge_db_fields)} 个")
                    print(f"   附件相关字段: {len(attachment_db_fields)} 个")

                    self.analysis_results["database_structure"] = {
                        "total_columns": len(columns),
                        "all_columns": db_fields,
                        "knowledge_fields": knowledge_db_fields,
                        "attachment_fields": attachment_db_fields,
                    }

        except Exception as e:
            print(f"❌ 数据库结构分析失败: {e}")
            self.analysis_results["database_structure"]["error"] = str(e)

    def _analyze_field_mappings(self):
        """分析字段映射"""
        print("\n4. 🔗 字段映射分析")
        print("-" * 40)

        try:
            # 获取当前的字段映射配置
            product_mapping = self.normalizer.FIELD_MAPPINGS.get("product", {})

            api_fields = self.analysis_results.get("api_data_structure", {}).get(
                "all_fields", []
            )
            db_fields = list(
                self.analysis_results.get("database_structure", {})
                .get("all_columns", {})
                .keys()
            )

            print(f"   API字段数: {len(api_fields)}")
            print(f"   数据库字段数: {len(db_fields)}")
            print(f"   配置的映射数: {len(product_mapping)}")

            # 分析映射覆盖情况
            mapped_api_fields = set(product_mapping.keys())
            unmapped_api_fields = set(api_fields) - mapped_api_fields

            mapped_db_fields = set(product_mapping.values())
            unmapped_db_fields = set(db_fields) - mapped_db_fields

            # 检查错误映射
            invalid_mappings = []
            for api_field, db_field in product_mapping.items():
                if api_field not in api_fields:
                    invalid_mappings.append(f"API字段不存在: {api_field}")
                if db_field not in db_fields:
                    invalid_mappings.append(f"数据库字段不存在: {db_field}")

            print(f"\n   📊 映射覆盖情况:")
            print(
                f"     已映射API字段: {len(mapped_api_fields)}/{len(api_fields)} ({len(mapped_api_fields)/len(api_fields)*100:.1f}%)"
            )
            print(
                f"     已映射数据库字段: {len(mapped_db_fields)}/{len(db_fields)} ({len(mapped_db_fields)/len(db_fields)*100:.1f}%)"
            )

            if unmapped_api_fields:
                print(f"\n   ❌ 未映射的API字段 ({len(unmapped_api_fields)}个):")
                for field in sorted(unmapped_api_fields):
                    print(f"     - {field}")

            if unmapped_db_fields:
                print(f"\n   ⚠️ 未使用的数据库字段 ({len(unmapped_db_fields)}个):")
                for field in sorted(unmapped_db_fields):
                    print(f"     - {field}")

            if invalid_mappings:
                print(f"\n   ❌ 无效映射 ({len(invalid_mappings)}个):")
                for mapping in invalid_mappings:
                    print(f"     - {mapping}")

            self.analysis_results["field_mapping_analysis"] = {
                "total_mappings": len(product_mapping),
                "mapped_api_fields": list(mapped_api_fields),
                "unmapped_api_fields": list(unmapped_api_fields),
                "mapped_db_fields": list(mapped_db_fields),
                "unmapped_db_fields": list(unmapped_db_fields),
                "invalid_mappings": invalid_mappings,
                "current_mappings": product_mapping,
            }

        except Exception as e:
            print(f"❌ 字段映射分析失败: {e}")
            self.analysis_results["field_mapping_analysis"]["error"] = str(e)

    def _test_data_flow(self):
        """测试数据流"""
        print("\n5. 🔄 数据流测试")
        print("-" * 40)

        try:
            # 获取一个产品样本进行测试
            sample_products = self.analysis_results.get("api_data_structure", {}).get(
                "sample_products", []
            )
            if not sample_products:
                print("❌ 没有样本产品数据")
                return

            sample_product = sample_products[0]
            print(f"✅ 使用产品样本: {sample_product.get('name', '未知')}")

            # 测试数据标准化
            normalized_data = None
            try:
                normalized_data = self.normalizer.normalize([sample_product], "product")
                if normalized_data:
                    normalized_product = normalized_data[0]
                    print(f"✅ 数据标准化成功，输出字段数: {len(normalized_product)}")

                    # 检查关键字段是否正确映射
                    key_checks = {
                        "name": sample_product.get("name"),
                        "introduction": sample_product.get("introduction"),
                        "details": sample_product.get("details"),
                        "parameterInfo": sample_product.get("parameterInfo"),
                        "useTo": sample_product.get("useTo"),
                    }

                    mapping_success = 0
                    for api_field, api_value in key_checks.items():
                        if api_value:
                            # 查找对应的数据库字段
                            db_field = self.normalizer.FIELD_MAPPINGS.get(
                                "product", {}
                            ).get(api_field)
                            if db_field and db_field in normalized_product:
                                mapping_success += 1
                                print(f"     ✅ {api_field} -> {db_field}: 映射成功")
                            else:
                                print(f"     ❌ {api_field}: 映射失败")

                    print(
                        f"   关键字段映射成功率: {mapping_success}/{len([v for v in key_checks.values() if v])} ({mapping_success/max(1, len([v for v in key_checks.values() if v]))*100:.1f}%)"
                    )

                else:
                    print("❌ 数据标准化失败")

            except Exception as e:
                print(f"❌ 数据标准化测试失败: {e}")

            self.analysis_results["data_flow_test"] = {
                "normalization_success": normalized_data is not None
                and len(normalized_data) > 0,
                "sample_product_fields": len(sample_product),
                "normalized_fields": len(normalized_data[0]) if normalized_data else 0,
            }

        except Exception as e:
            print(f"❌ 数据流测试失败: {e}")
            self.analysis_results["data_flow_test"]["error"] = str(e)

    def _identify_issues(self):
        """识别问题"""
        print("\n6. 🔍 问题识别")
        print("-" * 40)

        issues = []

        # 检查API字段映射问题
        mapping_analysis = self.analysis_results.get("field_mapping_analysis", {})
        unmapped_api_fields = mapping_analysis.get("unmapped_api_fields", [])

        if unmapped_api_fields:
            knowledge_unmapped = [
                f
                for f in unmapped_api_fields
                if any(
                    k in f.lower()
                    for k in ["introduction", "details", "param", "usage", "guide"]
                )
            ]
            if knowledge_unmapped:
                issues.append(
                    {
                        "type": "critical",
                        "category": "字段映射",
                        "description": f'重要知识内容字段未映射: {", ".join(knowledge_unmapped)}',
                        "impact": "产品知识内容无法正确存储",
                    }
                )

        # 检查数据库字段利用率
        unmapped_db_fields = mapping_analysis.get("unmapped_db_fields", [])
        if len(unmapped_db_fields) > 10:
            issues.append(
                {
                    "type": "warning",
                    "category": "数据库设计",
                    "description": f"大量数据库字段未使用: {len(unmapped_db_fields)}个",
                    "impact": "数据库设计可能需要优化",
                }
            )

        # 检查数据流问题
        data_flow = self.analysis_results.get("data_flow_test", {})
        if not data_flow.get("normalization_success", False):
            issues.append(
                {
                    "type": "critical",
                    "category": "数据处理",
                    "description": "数据标准化失败",
                    "impact": "API数据无法正确转换为数据库格式",
                }
            )

        print(f"   发现 {len(issues)} 个问题:")
        for i, issue in enumerate(issues, 1):
            icon = "🔴" if issue["type"] == "critical" else "🟡"
            print(f"   {i}. {icon} [{issue['category']}] {issue['description']}")
            print(f"      影响: {issue['impact']}")

        self.analysis_results["identified_issues"] = issues

    def _generate_fix_recommendations(self):
        """生成修复建议"""
        print("\n7. 💡 修复建议")
        print("-" * 40)

        recommendations = []

        # 基于识别的问题生成建议
        issues = self.analysis_results.get("identified_issues", [])

        for issue in issues:
            if "知识内容字段未映射" in issue["description"]:
                recommendations.append(
                    {
                        "priority": "high",
                        "action": "修复字段映射",
                        "description": "更新ApiResponseNormalizer中的product字段映射",
                        "steps": [
                            "1. 分析未映射的API字段",
                            "2. 确认对应的数据库字段",
                            "3. 更新FIELD_MAPPINGS配置",
                            "4. 测试数据标准化功能",
                        ],
                    }
                )

            if "数据标准化失败" in issue["description"]:
                recommendations.append(
                    {
                        "priority": "high",
                        "action": "修复数据标准化器",
                        "description": "检查并修复normalize方法的逻辑",
                        "steps": [
                            "1. 调试normalize方法",
                            "2. 检查字段映射逻辑",
                            "3. 处理数据类型转换",
                            "4. 添加错误处理",
                        ],
                    }
                )

        # 添加通用改进建议
        recommendations.append(
            {
                "priority": "medium",
                "action": "完善测试覆盖",
                "description": "添加全面的数据处理测试",
                "steps": [
                    "1. 创建单元测试",
                    "2. 添加集成测试",
                    "3. 设置持续监控",
                    "4. 建立数据质量检查",
                ],
            }
        )

        print(f"   生成 {len(recommendations)} 项建议:")
        for i, rec in enumerate(recommendations, 1):
            priority_icon = (
                "🔴"
                if rec["priority"] == "high"
                else "🟡" if rec["priority"] == "medium" else "🟢"
            )
            print(f"   {i}. {priority_icon} {rec['action']}: {rec['description']}")

        self.analysis_results["fix_recommendations"] = recommendations

    def _print_comprehensive_report(self):
        """输出综合报告"""
        print("\n" + "=" * 80)
        print("📊 全面系统分析报告摘要")
        print("=" * 80)

        # API数据结构摘要
        api_structure = self.analysis_results.get("api_data_structure", {})
        print(f"🔌 API数据结构: {api_structure.get('total_fields', 0)} 个字段")
        print(f"   - 知识内容字段: {len(api_structure.get('knowledge_fields', []))} 个")
        print(
            f"   - 附件相关字段: {len(api_structure.get('attachment_fields', []))} 个"
        )

        # 数据库结构摘要
        db_structure = self.analysis_results.get("database_structure", {})
        print(f"🗄️ 数据库结构: {db_structure.get('total_columns', 0)} 个字段")
        print(f"   - 知识内容字段: {len(db_structure.get('knowledge_fields', []))} 个")
        print(f"   - 附件相关字段: {len(db_structure.get('attachment_fields', []))} 个")

        # 字段映射摘要
        mapping_analysis = self.analysis_results.get("field_mapping_analysis", {})
        total_mappings = mapping_analysis.get("total_mappings", 0)
        unmapped_api = len(mapping_analysis.get("unmapped_api_fields", []))
        print(f"🔗 字段映射: {total_mappings} 个映射配置")
        print(f"   - 未映射API字段: {unmapped_api} 个")

        # 问题摘要
        issues = self.analysis_results.get("identified_issues", [])
        critical_issues = len([i for i in issues if i.get("type") == "critical"])
        print(f"🔍 发现问题: {len(issues)} 个 (严重: {critical_issues})")

        # 建议摘要
        recommendations = self.analysis_results.get("fix_recommendations", [])
        high_priority = len([r for r in recommendations if r.get("priority") == "high"])
        print(f"💡 修复建议: {len(recommendations)} 项 (高优先级: {high_priority})")

        print("=" * 80)


def main():
    """主函数"""
    analyzer = ComprehensiveSystemAnalyzer()
    results = analyzer.run_comprehensive_analysis()

    # 保存分析结果
    with open("comprehensive_system_analysis.json", "w", encoding="utf-8") as f:
        json.dump(results, f, ensure_ascii=False, indent=2, default=str)

    print(f"\n📊 完整分析报告已保存到: comprehensive_system_analysis.json")


if __name__ == "__main__":
    main()
