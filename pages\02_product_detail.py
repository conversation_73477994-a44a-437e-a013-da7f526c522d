import streamlit as st
import logging
from typing import Optional, Dict, Any, List
from utils.session import SessionManager
from utils.api_client import ZKMallClient, get_api_client
from utils.api_response_normalizer import normalize_api_response
from utils.logging_config import get_logger

# 配置日志
logger = get_logger()


def main():
    """产品详情页面主函数"""
    # 页面配置
    st.set_page_config(
        page_title="产品详情",
        page_icon="📋",
        layout="wide",
        initial_sidebar_state="expanded",
    )

    # 初始化session
    initialize_session()

    # 检查产品选择
    selected_product = SessionManager.get("selected_product")
    if not selected_product:
        handle_no_product_selected()
        return

    # 渲染页面
    render_header()
    render_sidebar()
    render_content(selected_product)
    render_footer()


def initialize_session():
    """初始化页面session状态"""
    defaults = {
        "selected_product": None,
        "show_specifications": True,
        "show_images": True,
        "show_cases": True,
        "show_solutions": True,
        "show_information": True,
        "show_distribution_orders": True,
    }

    for key, value in defaults.items():
        if key not in st.session_state:
            SessionManager.set(key, value)


def handle_no_product_selected():
    """处理未选择产品的情况"""
    st.warning("⚠️ 未选择产品，请返回产品列表选择")

    col1, col2 = st.columns([1, 1])

    with col1:
        if st.button("🔙 返回产品列表", type="primary"):
            st.switch_page("pages/01_product_management.py")

    with col2:
        if st.button("🏠 返回首页"):
            st.switch_page("main.py")


def render_header():
    """渲染页面头部"""
    st.title("📋 产品详情")
    st.markdown("查看产品的详细信息和规格参数")


def render_sidebar():
    """渲染侧边栏"""
    with st.sidebar:
        st.subheader("🎛️ 显示选项")

        # 显示控制
        show_specifications = st.checkbox(
            "显示规格参数", value=SessionManager.get("show_specifications", True)
        )
        SessionManager.set("show_specifications", show_specifications)

        show_images = st.checkbox(
            "显示产品图片", value=SessionManager.get("show_images", True)
        )
        SessionManager.set("show_images", show_images)

        show_cases = st.checkbox(
            "显示关联案例", value=SessionManager.get("show_cases", True)
        )
        SessionManager.set("show_cases", show_cases)

        show_solutions = st.checkbox(
            "显示关联方案", value=SessionManager.get("show_solutions", True)
        )
        SessionManager.set("show_solutions", show_solutions)

        show_information = st.checkbox(
            "显示关联资讯", value=SessionManager.get("show_information", True)
        )
        SessionManager.set("show_information", show_information)

        show_distribution_orders = st.checkbox(
            "显示关联配单", value=SessionManager.get("show_distribution_orders", True)
        )
        SessionManager.set("show_distribution_orders", show_distribution_orders)

        st.markdown("---")

        # 操作按钮
        if st.button("🔙 返回产品列表", type="primary"):
            st.switch_page("pages/01_product_management.py")

        if st.button("🔄 刷新数据"):
            st.cache_data.clear()
            st.rerun()


def render_content(product: Dict[str, Any]):
    """渲染主要内容"""
    # 产品基本信息
    show_basic_info(product)

    # 产品规格参数
    if SessionManager.get("show_specifications", True):
        show_specifications(product)

    # 产品图片
    if SessionManager.get("show_images", True):
        show_product_images(product)

    # 关联案例
    if SessionManager.get("show_cases", True):
        show_related_cases(product)

    # 关联方案
    if SessionManager.get("show_solutions", True):
        show_related_solutions(product)

    # 关联资讯
    if SessionManager.get("show_information", True):
        show_related_information(product)

    # 关联配单
    if SessionManager.get("show_distribution_orders", True):
        show_related_distribution_orders(product)


def show_basic_info(product: Dict[str, Any]):
    """显示产品基本信息"""
    st.subheader("📦 基本信息")

    # 创建两列布局
    col1, col2 = st.columns([2, 1])

    with col1:
        st.markdown(f"**产品名称：** {product.get('productName', 'N/A')}")

        # 从paramInfoList中提取型号信息
        model = ""
        param_info_list = product.get("paramInfoList", [])
        for param in param_info_list:
            if param.get("params") == "型号":
                model = param.get("content", "")
                break

        st.markdown(f"**产品型号：** {model or 'N/A'}")
        st.markdown(f"**产品品牌：** {product.get('brandName', 'N/A')}")
        st.markdown(f"**产品分类：** {product.get('categoryName', 'N/A')}")
        st.markdown(f"**产品ID：** {product.get('productId', 'N/A')}")

        # 价格信息
        price = product.get("price", 0)
        if price and price > 0:
            st.markdown(f"**产品价格：** ¥{price:.2f}")
        else:
            st.markdown("**产品价格：** 请咨询")

        # 状态信息
        status = product.get("status", 0)
        status_text = "🟢 上架" if status == 1 else "🔴 下架"
        st.markdown(f"**状态：** {status_text}")

        # 时间信息
        create_time = product.get("createTime", "")
        if create_time:
            st.markdown(f"**创建时间：** {create_time[:19]}")

        update_time = product.get("updateTime", "")
        if update_time:
            st.markdown(f"**更新时间：** {update_time[:19]}")

    with col2:
        # 产品缩略图
        thumbnail = (
            product.get("thumbnail") or product.get("image") or product.get("imageUrl")
        )
        if thumbnail:
            try:
                st.image(thumbnail, caption="产品缩略图", use_column_width=True)
            except Exception as e:
                logger.warning(f"无法加载产品图片: {e}")
                st.info("📷 图片加载失败")
        else:
            st.info("📷 暂无产品图片")


def show_specifications(product: Dict[str, Any]):
    """显示产品规格参数"""
    st.markdown("---")
    st.subheader("🔧 规格参数")

    # 首先尝试从paramInfoList获取规格参数
    param_info_list = product.get("paramInfoList", [])

    if param_info_list:
        # 如果有paramInfoList，显示参数信息
        col1, col2 = st.columns(2)

        # 分两列显示参数
        mid = len(param_info_list) // 2

        with col1:
            for param in param_info_list[:mid]:
                param_name = param.get("params", "")
                param_content = param.get("content", "")
                if param_name and param_content:
                    st.markdown(f"**{param_name}：** {param_content}")

        with col2:
            for param in param_info_list[mid:]:
                param_name = param.get("params", "")
                param_content = param.get("content", "")
                if param_name and param_content:
                    st.markdown(f"**{param_name}：** {param_content}")
    else:
        # 获取规格参数
        specifications = (
            product.get("specifications")
            or product.get("specs")
            or product.get("parameters")
        )

        if specifications:
            if isinstance(specifications, dict):
                # 如果是字典格式
                col1, col2 = st.columns(2)
                items = list(specifications.items())
                mid = len(items) // 2

                with col1:
                    for key, value in items[:mid]:
                        st.markdown(f"**{key}：** {value}")

                with col2:
                    for key, value in items[mid:]:
                        st.markdown(f"**{key}：** {value}")
            else:
                # 如果是字符串格式
                st.markdown(specifications)
        else:
            # 显示产品描述
            description = (
                product.get("description")
                or product.get("desc")
                or product.get("content")
            )
            if description:
                st.markdown("**产品描述：**")
                st.markdown(description)
            else:
                st.info("暂无规格参数信息")


def show_product_images(product: Dict[str, Any]):
    """显示产品图片"""
    st.markdown("---")
    st.subheader("🖼️ 产品图片")

    # 获取图片列表
    images = []

    # 尝试不同的图片字段
    image_fields = ["images", "imageList", "gallery", "photos"]
    for field in image_fields:
        if product.get(field):
            if isinstance(product[field], list):
                images.extend(product[field])
            else:
                images.append(product[field])

    # 添加主图
    main_image = (
        product.get("image") or product.get("imageUrl") or product.get("thumbnail")
    )
    if main_image and main_image not in images:
        images.insert(0, main_image)

    if images:
        # 显示图片网格
        cols = st.columns(min(len(images), 3))
        for i, image_url in enumerate(images[:6]):  # 最多显示6张图片
            try:
                with cols[i % 3]:
                    st.image(
                        image_url, caption=f"产品图片 {i+1}", use_column_width=True
                    )
            except Exception as e:
                logger.warning(f"无法加载图片 {image_url}: {e}")
                with cols[i % 3]:
                    st.info(f"📷 图片 {i+1} 加载失败")
    else:
        st.info("暂无产品图片")


def show_related_cases(product: Dict[str, Any]):
    """显示关联案例"""
    st.markdown("---")
    st.subheader("📋 关联案例")

    # 加载相关案例 - 使用正确的字段名：productId
    cases = load_related_cases(product.get("productId"))

    if cases:
        for case in cases[:3]:  # 最多显示3个案例
            with st.expander(f"📋 {case.get('name', '案例')}"):
                col1, col2 = st.columns([2, 1])

                with col1:
                    st.markdown(f"**案例名称：** {case.get('name', 'N/A')}")
                    st.markdown(f"**客户：** {case.get('customer', 'N/A')}")
                    st.markdown(f"**行业：** {case.get('industry', 'N/A')}")

                    description = case.get("description") or case.get("summary")
                    if description:
                        st.markdown(f"**案例描述：** {description[:200]}...")

                with col2:
                    if st.button(f"查看案例详情", key=f"case_{case.get('id')}"):
                        SessionManager.set("selected_case", case)
                        st.switch_page("pages/03_case_detail.py")
    else:
        st.info("暂无关联案例")


@st.cache_data(ttl=300)
def load_related_cases(product_id: str) -> list:
    """加载产品相关案例"""
    if not product_id:
        return []

    try:
        client = ZKMallClient()

        # 调用API获取相关案例 - 注意：get_cases返回的是案例列表
        cases = client.get_cases(
            product_id=int(product_id) if str(product_id).isdigit() else None
        )

        # 检查返回的数据类型
        if isinstance(cases, list):
            logger.info(f"成功加载 {len(cases)} 个关联案例")
            return cases
        elif isinstance(cases, dict):
            # 如果返回的是字典格式（兼容性处理）
            if cases.get("success") or cases.get("code") == 200:
                case_list = cases.get("data", []) or cases.get("list", [])
                logger.info(f"成功加载 {len(case_list)} 个关联案例")
                return case_list
            else:
                logger.warning(f"案例数据加载失败: {cases}")
                return []
        else:
            logger.warning(f"案例数据格式异常: {type(cases)}")
            return []

    except Exception as e:
        logger.error(f"加载关联案例失败: {e}")
        return []


def show_related_solutions(product: Dict[str, Any]):
    """显示关联方案"""
    st.markdown("---")
    st.subheader("🔧 关联方案")

    # 加载相关方案
    product_id = product.get("productId")
    if product_id:
        solutions = load_related_solutions(int(product_id))
    else:
        solutions = []

    if solutions:
        for solution in solutions[:3]:  # 最多显示3个方案
            with st.expander(f"🔧 {solution.get('name', '方案')}"):
                col1, col2 = st.columns([2, 1])

                with col1:
                    st.markdown(f"**方案名称：** {solution.get('name', 'N/A')}")
                    st.markdown(f"**分类：** {solution.get('categoryName', 'N/A')}")
                    st.markdown(
                        f"**场景分类：** {solution.get('categorySceneName', 'N/A')}"
                    )

                    introduction = solution.get("introduction") or solution.get(
                        "content"
                    )
                    if introduction:
                        st.markdown(f"**方案简介：** {introduction[:200]}...")

                    # 显示方案产品列表
                    product_list = solution.get("list", []) or solution.get(
                        "programmeDetails", []
                    )
                    if product_list:
                        st.markdown("**包含产品：**")
                        for prod in product_list[:3]:  # 最多显示3个产品
                            product_name = prod.get("name", "") or prod.get(
                                "productName", ""
                            )
                            if product_name:
                                st.markdown(f"- {product_name}")

                with col2:
                    # 显示方案缩略图
                    thumbnail = solution.get("smallImg") or solution.get("banner")
                    if thumbnail:
                        try:
                            st.image(
                                thumbnail, caption="方案图片", use_column_width=True
                            )
                        except Exception as e:
                            logger.warning(f"无法加载方案图片: {e}")
                            st.info("📷 图片加载失败")

                    if st.button(f"查看方案详情", key=f"solution_{solution.get('id')}"):
                        SessionManager.set("selected_solution", solution)
                        st.switch_page("pages/04_solution_management.py")
    else:
        st.info("暂无关联方案")


def show_related_information(product: Dict[str, Any]):
    """显示关联资讯"""
    st.markdown("---")
    st.subheader("📰 关联资讯")

    # 加载相关资讯
    product_id = product.get("productId")
    if product_id:
        information_list = load_related_information(int(product_id))
    else:
        information_list = []

    if information_list:
        for info in information_list[:3]:  # 最多显示3条资讯
            with st.expander(f"📰 {info.get('title', '资讯')}"):
                col1, col2 = st.columns([2, 1])

                with col1:
                    st.markdown(f"**标题：** {info.get('title', 'N/A')}")
                    st.markdown(f"**分类：** {info.get('categoryName', 'N/A')}")
                    st.markdown(f"**浏览量：** {info.get('watch', 0)}")
                    st.markdown(f"**点赞数：** {info.get('likeCount', 0)}")

                    # 创建时间
                    create_time = info.get("createTime", "")
                    if create_time:
                        st.markdown(f"**发布时间：** {create_time[:19]}")

                    details = info.get("details") or info.get("content")
                    if details:
                        # 移除HTML标签并截取前200字符
                        import re

                        clean_details = re.sub(r"<[^>]+>", "", details)
                        st.markdown(f"**内容摘要：** {clean_details[:200]}...")

                with col2:
                    # 显示资讯缩略图
                    thumbnail = (
                        info.get("smallImg")
                        or info.get("picVideo")
                        or info.get("images")
                    )
                    if thumbnail:
                        try:
                            st.image(
                                thumbnail, caption="资讯图片", use_column_width=True
                            )
                        except Exception as e:
                            logger.warning(f"无法加载资讯图片: {e}")
                            st.info("📷 图片加载失败")

                    if st.button(f"查看资讯详情", key=f"info_{info.get('id')}"):
                        SessionManager.set("selected_information", info)
                        st.switch_page("pages/05_news_management.py")
    else:
        st.info("暂无关联资讯")


def show_related_distribution_orders(product: Dict[str, Any]):
    """显示关联配单"""
    st.markdown("---")
    st.subheader("📋 关联配单")

    # 加载相关配单
    product_id = product.get("productId")
    if product_id:
        distribution_orders = load_related_distribution_orders(int(product_id))
    else:
        distribution_orders = []

    if distribution_orders:
        for order in distribution_orders[:3]:  # 最多显示3个配单
            with st.expander(f"📋 {order.get('name', '配单')}"):
                col1, col2 = st.columns([2, 1])

                with col1:
                    st.markdown(f"**配单名称：** {order.get('name', 'N/A')}")
                    st.markdown(f"**一级分类：** {order.get('firCategoryName', 'N/A')}")
                    st.markdown(f"**二级分类：** {order.get('secCategoryName', 'N/A')}")
                    st.markdown(f"**公司：** {order.get('companyName', 'N/A')}")
                    st.markdown(f"**点赞数：** {order.get('likeCount', 0)}")
                    st.markdown(f"**收藏数：** {order.get('favoriteCount', 0)}")

                    # 配单类型
                    source_type = order.get("sourceType", 0)
                    type_text = "产品配单" if source_type == 0 else "方案配单"
                    st.markdown(f"**配单类型：** {type_text}")

                    # 是否有过期产品
                    has_expire = order.get("hasExpire", False)
                    expire_text = "🔴 有过期产品" if has_expire else "🟢 产品正常"
                    st.markdown(f"**状态：** {expire_text}")

                with col2:
                    # 显示价格隐藏状态
                    hide_price = order.get("hidePrice", 0)
                    price_text = "🔒 价格隐藏" if hide_price == 1 else "👁️ 价格可见"
                    st.markdown(f"**价格显示：** {price_text}")

                    if st.button(f"查看配单详情", key=f"order_{order.get('id')}"):
                        SessionManager.set("selected_distribution_order", order)
                        st.switch_page("pages/06_order_management.py")
    else:
        st.info("暂无关联配单")


def load_related_solutions(product_id: int) -> List[Dict[str, Any]]:
    """加载关联方案"""
    try:
        client = ZKMallClient()
        solutions = client.get_related_solutions(product_id)

        # 使用响应标准化器标准化数据
        normalized_solutions = normalize_api_response(solutions, "programme")

        return normalized_solutions
    except Exception as e:
        logger.error(f"加载产品 {product_id} 关联方案失败: {e}")
        return []


def load_related_information(product_id: int) -> List[Dict[str, Any]]:
    """加载关联资讯"""
    try:
        client = ZKMallClient()
        information = client.get_related_information(product_id)

        # 使用响应标准化器标准化数据
        normalized_information = normalize_api_response(information, "information")

        return normalized_information
    except Exception as e:
        logger.error(f"加载产品 {product_id} 关联资讯失败: {e}")
        return []


def load_related_distribution_orders(product_id: int) -> List[Dict[str, Any]]:
    """加载关联配单"""
    try:
        client = ZKMallClient()
        orders = client.get_related_distribution_orders(product_id)

        # 使用响应标准化器标准化数据
        normalized_orders = normalize_api_response(orders, "distribution_order")

        return normalized_orders
    except Exception as e:
        logger.error(f"加载产品 {product_id} 关联配单失败: {e}")
        return []


def render_footer():
    """渲染页面脚部"""
    st.markdown("---")
    st.markdown("© 2024 云商系统 - 产品详情模块")


if __name__ == "__main__":
    main()
