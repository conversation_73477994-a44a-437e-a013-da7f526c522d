import redis
import json
import pickle
import threading
from typing import Any, Optional, Union
import os
import logging

logger = logging.getLogger(__name__)


class CacheManager:
    """缓存管理器（线程安全的单例模式）"""

    _instance: Optional["CacheManager"] = None
    _lock = threading.Lock()
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        # 双重检查锁定，确保只初始化一次
        if not self._initialized:
            with self._lock:
                if not self._initialized:
                    self._initialize_redis()
                    self._initialized = True

    def _initialize_redis(self) -> None:
        """初始化Redis连接"""
        if hasattr(self, 'redis_client'):
            return  # 已经初始化，直接返回
            
        redis_url = os.getenv("REDIS_URL", "redis://106.63.8.99:6379/0")

        try:
            self.redis_client = redis.from_url(redis_url, decode_responses=False)
            self.redis_client.ping()  # 测试连接
            logger.info("Redis连接初始化成功")
        except Exception as e:
            logger.warning(f"Redis连接失败，使用内存缓存: {e}")
            self.redis_client = None
            self.memory_cache = {}

    def get(self, key: str) -> Any:
        """获取缓存值"""
        if self.redis_client:
            try:
                value = self.redis_client.get(key)
                if value:
                    return pickle.loads(value)
            except Exception as e:
                logger.error(f"获取Redis缓存失败 {key}: {e}")
        else:
            # 使用内存缓存
            return getattr(self, 'memory_cache', {}).get(key)

        return None

    def set(self, key: str, value: Any, ttl: int = 3600) -> bool:
        """设置缓存值"""
        if self.redis_client:
            try:
                serialized_value = pickle.dumps(value)
                return self.redis_client.setex(key, ttl, serialized_value)
            except Exception as e:
                logger.error(f"设置Redis缓存失败 {key}: {e}")
                return False
        else:
            # 使用内存缓存
            if not hasattr(self, 'memory_cache'):
                self.memory_cache = {}
            self.memory_cache[key] = value
            return True

    def delete(self, key: str) -> bool:
        """删除缓存"""
        if self.redis_client:
            try:
                return bool(self.redis_client.delete(key))
            except Exception as e:
                logger.error(f"删除Redis缓存失败 {key}: {e}")
                return False
        else:
            # 使用内存缓存
            memory_cache = getattr(self, 'memory_cache', {})
            if key in memory_cache:
                del memory_cache[key]
                return True
            return False

    def clear_pattern(self, pattern: str) -> int:
        """清理匹配模式的缓存"""
        if self.redis_client:
            try:
                keys = self.redis_client.keys(pattern)
                if keys:
                    return self.redis_client.delete(*keys)
                return 0
            except Exception as e:
                logger.error(f"清理Redis缓存失败 {pattern}: {e}")
                return 0
        else:
            # 使用内存缓存
            memory_cache = getattr(self, 'memory_cache', {})
            count = 0
            keys_to_delete = []

            # 简单模式匹配
            for k in memory_cache.keys():
                if pattern.replace("*", "") in k:
                    keys_to_delete.append(k)

            for k in keys_to_delete:
                del memory_cache[k]
                count += 1

            return count

    def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        if self.redis_client:
            try:
                return bool(self.redis_client.exists(key))
            except Exception as e:
                logger.error(f"检查Redis缓存失败 {key}: {e}")
                return False
        else:
            # 使用内存缓存
            memory_cache = getattr(self, 'memory_cache', {})
            return key in memory_cache


# 全局缓存管理器实例
cache_manager = CacheManager()


def get_cache():
    """获取缓存管理器"""
    return cache_manager
