"""
Enhanced Attachment Processor - Fixes URL Parameter Extraction Issues

This module addresses the core data extraction problems where:
1. Attachment URLs contain file= parameters that aren't being extracted
2. Category data (firCategoryId/secCategoryId) is present but not properly mapped
3. Tag and label information needs better processing

Created: 2025-01-27
Purpose: Fix interface data retrieval issues identified in conversation summary
"""

import os
import re
import logging
from typing import Dict, List, Any, Optional, Tuple, Union
from urllib.parse import urlparse, parse_qs, unquote
from pathlib import Path
import hashlib
import json

logger = logging.getLogger(__name__)


class EnhancedAttachmentProcessor:
    """Enhanced processor for attachment URLs and metadata extraction"""

    def __init__(self):
        """Initialize the enhanced attachment processor"""
        # URL parameter patterns for file extraction
        self.file_param_patterns = [
            r"[?&]file=([^&]+)",
            r"[?&]filename=([^&]+)",
            r"[?&]name=([^&]+)",
        ]

        # Category field mappings (from API logs)
        self.category_mappings = {
            "firCategoryId": "primary_category_id",
            "firCategoryName": "primary_category_name",
            "secCategoryId": "secondary_category_id",
            "secCategoryName": "secondary_category_name",
        }

        # Attachment field mappings by module
        self.attachment_fields = {
            "products": ["other", "qualifications", "smallImg", "banner"],
            "cases": ["img", "banner", "smallImg", "video"],
            "programmes": ["other", "video", "smallImg", "banner"],
            "information": ["otherUrl", "videoUrl", "picVideo", "smallImg", "images"],
            "distribution_orders": ["other"],
        }

        logger.info("Enhanced Attachment Processor initialized")

    def extract_filename_from_url(self, url: str) -> Optional[str]:
        """
        Extract filename from URL, handling file= parameters

        Args:
            url: URL that may contain file parameters

        Returns:
            Extracted filename or None
        """
        if not url or not isinstance(url, str):
            return None

        try:
            # First try URL parameters using patterns
            for pattern in self.file_param_patterns:
                match = re.search(pattern, url)
                if match:
                    filename = unquote(match.group(1))
                    if filename and len(filename.strip()) > 0:
                        logger.debug(f"Extracted filename from parameter: {filename}")
                        return filename.strip()

            # Fallback to parse_qs for more complex parameters
            parsed_url = urlparse(url)
            if parsed_url.query:
                params = parse_qs(parsed_url.query)
                for param_name in ["file", "filename", "name"]:
                    if param_name in params and params[param_name]:
                        filename = params[param_name][0]
                        if filename and len(filename.strip()) > 0:
                            logger.debug(
                                f"Extracted filename from query params: {filename}"
                            )
                            return unquote(filename.strip())

            # Last resort: extract from path
            path_filename = os.path.basename(unquote(parsed_url.path))
            if path_filename and path_filename != "/" and "." in path_filename:
                logger.debug(f"Extracted filename from path: {path_filename}")
                return path_filename

            return None

        except Exception as e:
            logger.warning(f"Failed to extract filename from URL {url}: {e}")
            return None

    def process_attachment_url(
        self, url: str, field_name: str = None
    ) -> Dict[str, Any]:
        """
        Process attachment URL and extract metadata

        Args:
            url: Raw attachment URL
            field_name: Field name for context

        Returns:
            Processed attachment info
        """
        if not url or not isinstance(url, str):
            return {}

        try:
            # Clean and validate URL
            cleaned_url = url.strip()
            if not cleaned_url.startswith(("http://", "https://")):
                logger.warning(f"Invalid URL format: {cleaned_url}")
                return {}

            # Extract filename from URL parameters
            extracted_filename = self.extract_filename_from_url(cleaned_url)

            # Get file extension
            file_extension = None
            if extracted_filename:
                file_extension = Path(extracted_filename).suffix.lower()

            # Determine file type
            file_type = self._determine_file_type(file_extension, cleaned_url)

            # Generate safe filename
            safe_filename = self._generate_safe_filename(
                extracted_filename, cleaned_url, field_name
            )

            result = {
                "original_url": cleaned_url,
                "extracted_filename": extracted_filename,
                "safe_filename": safe_filename,
                "file_extension": file_extension,
                "file_type": file_type,
                "field_name": field_name,
                "url_hash": hashlib.md5(cleaned_url.encode()).hexdigest()[:8],
            }

            logger.info(
                f"Processed attachment URL - Field: {field_name}, "
                f"Original: {extracted_filename}, Safe: {safe_filename}"
            )

            return result

        except Exception as e:
            logger.error(f"Failed to process attachment URL {url}: {e}")
            return {}

    def process_category_data(self, item_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process category data from API response

        Args:
            item_data: Raw item data from API

        Returns:
            Processed category data
        """
        category_data = {}

        try:
            # Process firCategory and secCategory fields
            for api_field, mapped_field in self.category_mappings.items():
                if api_field in item_data:
                    value = item_data[api_field]
                    if value is not None:
                        category_data[mapped_field] = value
                        logger.debug(
                            f"Mapped category field: {api_field} -> {mapped_field} = {value}"
                        )

            # Additional category processing
            if "categoryId" in item_data:
                category_data["category_id"] = item_data["categoryId"]
            if "categoryName" in item_data:
                category_data["category_name"] = item_data["categoryName"]

            return category_data

        except Exception as e:
            logger.error(f"Failed to process category data: {e}")
            return {}

    def process_item_attachments(
        self, item_data: Dict[str, Any], module: str
    ) -> Dict[str, Any]:
        """
        Process all attachments for a single item

        Args:
            item_data: Raw item data from API
            module: Business module (products, cases, etc.)

        Returns:
            Processed attachment data
        """
        if module not in self.attachment_fields:
            logger.warning(f"Unknown module: {module}")
            return {}

        processed_attachments = {}
        item_id = item_data.get("id", "unknown")

        try:
            # Process each attachment field
            for field_name in self.attachment_fields[module]:
                if field_name in item_data:
                    field_value = item_data[field_name]

                    if isinstance(field_value, str) and field_value.strip():
                        # Single URL
                        attachment_info = self.process_attachment_url(
                            field_value, field_name
                        )
                        if attachment_info:
                            processed_attachments[field_name] = attachment_info

                    elif isinstance(field_value, list):
                        # Multiple URLs
                        attachment_list = []
                        for i, url in enumerate(field_value):
                            if isinstance(url, str) and url.strip():
                                attachment_info = self.process_attachment_url(
                                    url, f"{field_name}_{i+1}"
                                )
                                if attachment_info:
                                    attachment_list.append(attachment_info)

                        if attachment_list:
                            processed_attachments[field_name] = attachment_list

            logger.info(
                f"Processed {len(processed_attachments)} attachment fields for "
                f"{module} ID {item_id}"
            )

            return processed_attachments

        except Exception as e:
            logger.error(
                f"Failed to process item attachments for {module} ID {item_id}: {e}"
            )
            return {}

    def enhance_api_response(
        self, api_response: Any, module: str
    ) -> List[Dict[str, Any]]:
        """
        Enhance API response with proper attachment and category processing

        Args:
            api_response: Raw API response
            module: Business module

        Returns:
            Enhanced response with processed attachments and categories
        """
        try:
            # Extract data array from response
            data_list = self._extract_data_list(api_response)
            if not data_list:
                logger.warning("No data found in API response")
                return []

            enhanced_items = []

            for item in data_list:
                if not isinstance(item, dict):
                    continue

                # Start with original item data
                enhanced_item = item.copy()

                # Process attachments
                attachment_data = self.process_item_attachments(item, module)
                if attachment_data:
                    enhanced_item["processed_attachments"] = attachment_data

                # Process categories
                category_data = self.process_category_data(item)
                if category_data:
                    enhanced_item.update(category_data)

                # Add processing metadata
                enhanced_item["_processing_info"] = {
                    "module": module,
                    "processed_at": (
                        logger.handlers[0].formatter.formatTime(
                            logging.LogRecord("", 0, "", 0, "", (), None)
                        )
                        if logger.handlers
                        else None
                    ),
                    "attachment_fields_found": len(attachment_data),
                    "category_fields_mapped": len(category_data),
                }

                enhanced_items.append(enhanced_item)

            logger.info(f"Enhanced {len(enhanced_items)} items for module {module}")
            return enhanced_items

        except Exception as e:
            logger.error(f"Failed to enhance API response for module {module}: {e}")
            return []

    def _extract_data_list(self, api_response: Any) -> List[Dict[str, Any]]:
        """Extract data list from various API response formats"""
        try:
            if isinstance(api_response, list):
                return api_response
            elif isinstance(api_response, dict):
                # Check common response formats
                if "data" in api_response:
                    data = api_response["data"]
                    if isinstance(data, list):
                        return data
                    elif isinstance(data, dict) and "rows" in data:
                        return data["rows"]
                elif "rows" in api_response:
                    return api_response["rows"]
                elif "items" in api_response:
                    return api_response["items"]
                else:
                    # Single item response
                    return [api_response]

            return []

        except Exception as e:
            logger.error(f"Failed to extract data list: {e}")
            return []

    def _determine_file_type(self, file_extension: str, url: str) -> str:
        """Determine file type from extension and URL context"""
        if not file_extension:
            return "unknown"

        type_mappings = {
            "image": [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg"],
            "video": [".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".m4v"],
            "document": [".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx"],
            "archive": [".zip", ".rar", ".7z", ".tar", ".gz"],
        }

        for file_type, extensions in type_mappings.items():
            if file_extension in extensions:
                return file_type

        return "other"

    def _generate_safe_filename(
        self, extracted_filename: str, url: str, field_name: str
    ) -> str:
        """Generate safe filename for storage"""
        try:
            if extracted_filename:
                # Clean the extracted filename
                safe_name = re.sub(r"[^\w\-_.]", "_", extracted_filename)
            else:
                # Generate from URL hash
                url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
                safe_name = f"attachment_{url_hash}"

            # Add field prefix if provided
            if field_name:
                name_parts = safe_name.rsplit(".", 1)
                if len(name_parts) == 2:
                    safe_name = f"{field_name}_{name_parts[0]}.{name_parts[1]}"
                else:
                    safe_name = f"{field_name}_{safe_name}"

            return safe_name

        except Exception as e:
            logger.warning(f"Failed to generate safe filename: {e}")
            return f"attachment_{hashlib.md5(url.encode()).hexdigest()[:8]}"


# Global instance for easy access
enhanced_processor = EnhancedAttachmentProcessor()


def process_api_response_with_attachments(
    api_response: Any, module: str
) -> List[Dict[str, Any]]:
    """
    Global function to process API response with enhanced attachment handling

    Args:
        api_response: Raw API response
        module: Business module name

    Returns:
        Enhanced response with processed attachments and categories
    """
    return enhanced_processor.enhance_api_response(api_response, module)


def extract_filename_from_attachment_url(url: str) -> Optional[str]:
    """
    Global function to extract filename from attachment URL

    Args:
        url: Attachment URL with potential file= parameter

    Returns:
        Extracted filename or None
    """
    return enhanced_processor.extract_filename_from_url(url)


if __name__ == "__main__":
    # Test the enhanced processor
    test_urls = [
        "https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/2b0dc48-5a5d-428b-842c-43b0c7d64e5e.png?file=屏幕快照 2022-12-16 下午6.08.28.png",
        "https://example.com/path/file.jpg?file=test_image.jpg&other=param",
        "https://example.com/files/document.pdf",
    ]

    processor = EnhancedAttachmentProcessor()

    for url in test_urls:
        result = processor.process_attachment_url(url, "test_field")
        print(f"URL: {url}")
        print(f"Result: {json.dumps(result, indent=2, ensure_ascii=False)}")
        print("-" * 80)
