#!/usr/bin/env python3
"""
完整产品数据解析入库测试
测试从API获取数据到数据库存储的完整流程
"""

import os
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 加载环境变量
from config import load_env_file

load_env_file()

from utils.api_client import ZKMallClient
from utils.auth import AuthManager
from utils.logging_config import get_logger
from services.complete_product_storage import CompleteProductStorage

logger = get_logger()


def test_complete_product_storage():
    """测试完整产品数据解析入库"""
    try:
        print("🚀 开始完整产品数据解析入库测试")

        # 1. 登录认证
        print("\n🔐 步骤1: 用户登录认证")
        success, user_info = AuthManager.login("18929343717", "Zk@123456")

        if not success:
            print("❌ 登录失败")
            return

        print(f"✅ 登录成功")

        # 2. 初始化服务
        print("\n🔧 步骤2: 初始化服务")
        api_client = ZKMallClient()
        storage_service = CompleteProductStorage()
        print("✅ 服务初始化成功")

        # 3. 获取产品数据
        print("\n📦 步骤3: 获取产品数据")
        products = api_client.get_products(pageSize=10, current=1)

        if not products:
            print("❌ 未获取到产品数据")
            return

        print(f"✅ 获取到 {len(products)} 个产品")

        # 4. 显示产品数据样例
        print("\n📋 步骤4: 产品数据样例分析")
        if products:
            first_product = products[0]
            print(f"第一个产品数据键: {list(first_product.keys())}")
            print(f"产品ID: {first_product.get('id')}")
            print(f"产品名称: {first_product.get('name')}")
            print(f"产品型号: {first_product.get('spec')}")
            print(f"产品类别: {first_product.get('category_name')}")
            print(f"是否推荐: {first_product.get('is_suggest')}")
            print(f"是否热门: {first_product.get('is_hot')}")
            print(f"是否新品: {first_product.get('is_new')}")

        # 5. 解析并存储产品数据
        print("\n💾 步骤5: 解析并存储产品数据")
        storage_result = storage_service.store_products(products)

        print(f"存储结果:")
        print(f"  - 总数: {storage_result['total_count']}")
        print(f"  - 成功: {storage_result['success_count']}")
        print(f"  - 失败: {storage_result['error_count']}")
        print(f"  - 成功率: {storage_result['success_rate']:.2%}")

        if storage_result["errors"]:
            print(f"  - 错误信息:")
            for error in storage_result["errors"][:3]:  # 只显示前3个错误
                print(f"    * {error}")

        # 6. 获取存储统计
        print("\n📊 步骤6: 获取存储统计")
        stats = storage_service.get_storage_stats()

        if stats:
            print(f"数据库统计:")
            print(f"  - 总产品数: {stats.get('total_count', 0)}")
            print(f"  - 推荐产品: {stats.get('suggest_count', 0)}")
            print(f"  - 热门产品: {stats.get('hot_count', 0)}")
            print(f"  - 新品数量: {stats.get('new_count', 0)}")

            status_stats = stats.get("status_stats", {})
            if status_stats:
                print(f"  - 状态分布:")
                for status, count in status_stats.items():
                    print(f"    * {status}: {count}")

        # 7. 验证数据完整性
        print("\n🔍 步骤7: 验证数据完整性")
        verify_data_integrity(storage_service)

        print("\n🎉 完整产品数据解析入库测试完成！")

    except Exception as e:
        logger.error(f"完整产品数据解析入库测试失败: {e}", exc_info=True)
        print(f"❌ 测试失败: {e}")


def verify_data_integrity(storage_service: CompleteProductStorage):
    """验证数据完整性"""
    try:
        from utils.database import DatabaseManager

        db_manager = DatabaseManager()

        with db_manager.get_connection() as conn:
            with conn.cursor() as cursor:
                # 检查关键字段的完整性
                print("验证关键字段完整性:")

                # 检查产品ID
                cursor.execute(
                    "SELECT COUNT(*) FROM products_complete WHERE product_id IS NOT NULL"
                )
                result = cursor.fetchone()
                product_id_count = result[0] if result else 0
                print(f"  ✅ 有产品ID的记录: {product_id_count}")

                # 检查产品名称
                cursor.execute(
                    "SELECT COUNT(*) FROM products_complete WHERE name IS NOT NULL AND name != ''"
                )
                name_count = cursor.fetchone()[0]
                print(f"  ✅ 有产品名称的记录: {name_count}")

                # 检查产品型号
                cursor.execute(
                    "SELECT COUNT(*) FROM products_complete WHERE spec IS NOT NULL AND spec != ''"
                )
                spec_count = cursor.fetchone()[0]
                print(f"  ✅ 有产品型号的记录: {spec_count}")

                # 检查产品类别
                cursor.execute(
                    "SELECT COUNT(*) FROM products_complete WHERE category_name IS NOT NULL AND category_name != ''"
                )
                category_count = cursor.fetchone()[0]
                print(f"  ✅ 有产品类别的记录: {category_count}")

                # 检查推荐产品
                cursor.execute(
                    "SELECT COUNT(*) FROM products_complete WHERE is_suggest = '1'"
                )
                suggest_count = cursor.fetchone()[0]
                print(f"  ✅ 推荐产品数量: {suggest_count}")

                # 检查热门产品
                cursor.execute(
                    "SELECT COUNT(*) FROM products_complete WHERE is_hot = '1'"
                )
                hot_count = cursor.fetchone()[0]
                print(f"  ✅ 热门产品数量: {hot_count}")

                # 检查新品
                cursor.execute(
                    "SELECT COUNT(*) FROM products_complete WHERE is_new = 1"
                )
                new_count = cursor.fetchone()[0]
                print(f"  ✅ 新品数量: {new_count}")

                # 检查参数信息
                cursor.execute(
                    "SELECT COUNT(*) FROM products_complete WHERE param_info_list IS NOT NULL"
                )
                param_count = cursor.fetchone()[0]
                print(f"  ✅ 有参数信息的记录: {param_count}")

                # 检查原始数据
                cursor.execute(
                    "SELECT COUNT(*) FROM products_complete WHERE raw_data IS NOT NULL"
                )
                raw_data_count = cursor.fetchone()[0]
                print(f"  ✅ 有原始数据的记录: {raw_data_count}")

                # 显示样例数据
                print("\n📋 样例数据:")
                cursor.execute(
                    """
                    SELECT product_id, name, spec, category_name, is_suggest, is_hot, is_new 
                    FROM products_complete 
                    LIMIT 3
                """
                )

                samples = cursor.fetchall()
                for i, sample in enumerate(samples, 1):
                    (
                        product_id,
                        name,
                        spec,
                        category_name,
                        is_suggest,
                        is_hot,
                        is_new,
                    ) = sample
                    print(
                        f"  产品{i}: ID={product_id}, 名称={name}, 型号={spec}, 类别={category_name}"
                    )
                    print(f"         推荐={is_suggest}, 热门={is_hot}, 新品={is_new}")

    except Exception as e:
        logger.error(f"验证数据完整性失败: {e}", exc_info=True)
        print(f"❌ 数据完整性验证失败: {e}")


if __name__ == "__main__":
    test_complete_product_storage()
