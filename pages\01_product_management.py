import streamlit as st
import logging
from typing import Optional, Dict, Any, List
import pandas as pd
from utils.session import SessionManager
from utils.api_client import ZKMallClient
from utils.logging_config import get_logger

# 配置日志
logger = get_logger()


def main():
    """产品管理页面主函数"""
    # 页面配置
    st.set_page_config(
        page_title="产品管理",
        page_icon="📦",
        layout="wide",
        initial_sidebar_state="expanded",
    )

    # 初始化session
    initialize_session()

    # 渲染页面
    render_header()
    render_sidebar()
    render_content()
    render_footer()


def initialize_session():
    """初始化页面session状态"""
    # 清理可能的URL参数污染
    try:
        # 检查并清理query_params
        if hasattr(st, "query_params"):
            for key in st.query_params.keys():
                if key in ["current_page", "page_size"]:
                    logger.warning(f"清理URL参数: {key}={st.query_params[key]}")
                    del st.query_params[key]
    except Exception as e:
        logger.debug(f"URL参数清理异常(可忽略): {e}")

    defaults = {
        "current_page": 1,
        "page_size": 20,
        "search_term": "",
        "selected_category": "",
        "selected_brand": "",
        "products_data": None,
        "total_count": 0,
    }

    for key, value in defaults.items():
        if key not in st.session_state:
            SessionManager.set(key, value)
        else:
            # 验证现有值的类型，特别是数字类型的session值
            existing_value = st.session_state[key]
            if key in ["current_page", "page_size"]:
                try:
                    if isinstance(existing_value, (int, float)):
                        # 值正确，保持不变
                        pass
                    elif isinstance(existing_value, str) and existing_value.isdigit():
                        # 字符串数字，转换为整数
                        SessionManager.set(key, int(existing_value))
                    else:
                        # 无效值，重置为默认值
                        logger.warning(
                            f"重置无效session值: {key}={existing_value} -> {value}"
                        )
                        SessionManager.set(key, value)
                except (ValueError, TypeError) as e:
                    logger.error(
                        f"Session值验证失败: {key}={existing_value}, 重置为{value}"
                    )
                    SessionManager.set(key, value)


def render_header():
    """渲染页面头部"""
    st.title("📦 产品管理")
    st.markdown("管理和查看云商平台的产品信息")


def render_sidebar():
    """渲染侧边栏"""
    with st.sidebar:
        st.subheader("🔍 搜索和筛选")

        # 搜索框
        search_term = st.text_input(
            "产品名称或关键字",
            value=SessionManager.get("search_term", ""),
            placeholder="输入产品名称或关键字搜索...",
        )

        # 分类筛选
        categories = load_categories()
        selected_category = st.selectbox(
            "产品分类", options=["全部"] + categories, index=0
        )

        # 品牌筛选
        brands = load_brands()
        selected_brand = st.selectbox("产品品牌", options=["全部"] + brands, index=0)

        # 搜索按钮
        if st.button("🔍 搜索", type="primary"):
            SessionManager.set("search_term", search_term)
            SessionManager.set(
                "selected_category",
                selected_category if selected_category != "全部" else "",
            )
            SessionManager.set(
                "selected_brand", selected_brand if selected_brand != "全部" else ""
            )
            SessionManager.set("current_page", 1)  # 重置页码
            st.rerun()

        # 重置按钮
        if st.button("🔄 重置"):
            SessionManager.set("search_term", "")
            SessionManager.set("selected_category", "")
            SessionManager.set("selected_brand", "")
            SessionManager.set("current_page", 1)
            st.rerun()


def render_content():
    """渲染主要内容"""
    # 显示搜索条件
    show_search_conditions()

    # 加载产品数据
    with st.spinner("正在加载产品数据..."):
        products_data, total_count = load_products_data()

    if products_data:
        # 显示产品列表
        show_products_list(products_data)

        # 分页控件
        show_pagination(total_count)
    else:
        st.info("没有找到符合条件的产品")


def show_search_conditions():
    """显示当前搜索条件"""
    search_term = SessionManager.get("search_term", "")
    selected_category = SessionManager.get("selected_category", "")
    selected_brand = SessionManager.get("selected_brand", "")

    conditions = []
    if search_term:
        conditions.append(f"关键字: {search_term}")
    if selected_category:
        conditions.append(f"分类: {selected_category}")
    if selected_brand:
        conditions.append(f"品牌: {selected_brand}")

    if conditions:
        st.info(f"当前搜索条件: {' | '.join(conditions)}")


@st.cache_data(ttl=300)
def load_categories() -> List[str]:
    """加载产品分类列表"""
    try:
        client = ZKMallClient()
        categories = client.get_categories()

        # 确保categories是列表类型
        if isinstance(categories, list):
            return [
                cat.get("name", "")
                for cat in categories
                if isinstance(cat, dict) and cat.get("name")
            ]
        else:
            logger.warning(f"分类数据格式异常: {type(categories)}")
            return []
    except Exception as e:
        logger.error(f"加载分类失败: {e}")
        return []


@st.cache_data(ttl=300)
def load_brands() -> List[str]:
    """加载品牌列表"""
    try:
        client = ZKMallClient()
        brands = client.get_brands()

        # 确保brands是列表类型
        if isinstance(brands, list):
            return [
                brand.get("name", "")
                for brand in brands
                if isinstance(brand, dict) and brand.get("name")
            ]
        else:
            logger.warning(f"品牌数据格式异常: {type(brands)}")
            return []
    except Exception as e:
        logger.error(f"加载品牌失败: {e}")
        return []


def load_products_data():
    """加载产品数据"""
    try:
        client = ZKMallClient()

        # 获取搜索参数
        search_term = SessionManager.get("search_term", "")
        selected_category = SessionManager.get("selected_category", "")
        selected_brand = SessionManager.get("selected_brand", "")

        # 安全地获取和转换页码和页大小
        try:
            current_page_raw = SessionManager.get("current_page", 1)
            if isinstance(current_page_raw, (int, float)):
                current_page = int(current_page_raw)
            elif isinstance(current_page_raw, str) and current_page_raw.isdigit():
                current_page = int(current_page_raw)
            else:
                logger.warning(f"页码值异常: {current_page_raw}, 使用默认值1")
                current_page = 1
                SessionManager.set("current_page", 1)  # 重置为正确值
        except (ValueError, TypeError) as e:
            logger.error(f"页码转换失败: {e}, 使用默认值1")
            current_page = 1
            SessionManager.set("current_page", 1)

        try:
            page_size_raw = SessionManager.get("page_size", 20)
            if isinstance(page_size_raw, (int, float)):
                page_size = int(page_size_raw)
            elif isinstance(page_size_raw, str) and page_size_raw.isdigit():
                page_size = int(page_size_raw)
            else:
                logger.warning(f"页大小值异常: {page_size_raw}, 使用默认值20")
                page_size = 20
                SessionManager.set("page_size", 20)  # 重置为正确值
        except (ValueError, TypeError) as e:
            logger.error(f"页大小转换失败: {e}, 使用默认值20")
            page_size = 20
            SessionManager.set("page_size", 20)

        # 构建搜索参数
        params = {"current": current_page, "pageSize": page_size}

        if search_term:
            params["productName"] = search_term
        if selected_category:
            params["categoryName"] = selected_category
        if selected_brand:
            params["brandName"] = selected_brand

        # 获取产品数据
        response = client.get_products_with_count(**params)

        # 处理不同的响应格式
        if response:
            # 新格式：直接包含products和pagination字段
            if "products" in response and "pagination" in response:
                products_data = response.get("products", [])
                pagination = response.get("pagination", {})
                total_count = pagination.get("total", response.get("total", 0))

            # 旧格式：包含data字段
            elif "data" in response:
                data = response["data"]
                products_data = data.get("records", data.get("rows", []))
                total_count = data.get("total", 0)

            # 其他格式：尝试直接提取
            else:
                products_data = response.get("rows", response.get("records", []))
                total_count = response.get("total", 0)

            # 数据预处理 - 确保所有产品数据安全
            safe_products_data = []
            for product in products_data:
                if product and isinstance(product, dict):
                    # 确保paramInfoList字段安全
                    if "paramInfoList" in product and product["paramInfoList"] is None:
                        product["paramInfoList"] = []
                    # 确保其他可能为None的列表字段也安全
                    list_fields = [
                        "labelList",
                        "specList",
                        "instructionsList",
                        "commonProblemList",
                    ]
                    for field in list_fields:
                        if field in product and product[field] is None:
                            product[field] = []
                    safe_products_data.append(product)

            # 记录成功获取的数据
            logger.info(
                f"成功获取产品数据: {len(safe_products_data)}条，总计: {total_count}条"
            )

            # 缓存数据
            SessionManager.set("products_data", safe_products_data)
            SessionManager.set("total_count", total_count)

            return safe_products_data, total_count
        else:
            logger.warning(f"产品数据响应格式异常: {response}")
            return [], 0

    except Exception as e:
        logger.error(f"加载产品数据失败: {e}")
        st.error(f"加载产品数据失败: {str(e)}")
        return [], 0


def show_products_list(products_data: List[Dict]):
    """显示产品列表"""
    if not products_data:
        st.info("暂无产品数据")
        return

    # 转换为DataFrame以便显示
    df_data = []
    for product in products_data:
        # 使用完整字段解析器提取产品型号和所有字段
        from services.complete_product_parser import CompleteProductParser

        parser = CompleteProductParser()
        parsed_product = parser.parse_product(product)

        # 获取解析后的型号信息
        model = (
            parsed_product.get("extracted_model", "")
            or parsed_product.get("spec", "")
            or f"ID-{parsed_product.get('product_id', 'Unknown')}"
        )

        # 同时存储完整的产品数据到数据库
        try:
            from services.product_storage_service import ProductStorageService

            storage_service = ProductStorageService()
            storage_service.store_products_batch([product])
        except Exception as e:
            logger.warning(f"存储产品数据到数据库失败: {e}")

        df_data.append(
            {
                "ID": parsed_product.get("product_id", ""),
                "产品名称": parsed_product.get("name", ""),
                "产品型号": model,
                "品牌": parsed_product.get("brand_name", ""),
                "分类": parsed_product.get("category_name", ""),
                "价格": (
                    f"¥{parsed_product.get('price', 0):.2f}"
                    if parsed_product.get("price")
                    else "价格未设定"
                ),
                "状态": "正常" if parsed_product.get("status") == "0" else "停用",
                "创建时间": (
                    str(parsed_product.get("create_time", ""))[:19]
                    if parsed_product.get("create_time")
                    else ""
                ),
                "解析字段数": len(
                    [k for k, v in parsed_product.items() if v is not None and v != ""]
                ),
                "未知字段": "是" if parsed_product.get("unknown_fields") else "否",
            }
        )

    if not df_data:
        st.info("暂无产品数据")
        return

    df = pd.DataFrame(df_data)

    # 显示表格
    st.dataframe(df, use_container_width=True)

    # 显示详细信息按钮
    col1, col2, col3 = st.columns([1, 1, 1])

    with col1:
        if st.button("📊 导出数据"):
            export_products_data(df)

    with col2:
        if st.button("🔄 刷新数据"):
            st.cache_data.clear()
            st.rerun()

    with col3:
        selected_product = st.selectbox(
            "选择产品查看详情",
            options=[""]
            + [
                f"{p.get('name', p.get('productName', ''))} ({p.get('productId', '')})"
                for p in products_data
            ],
            key="selected_product_detail",
        )

        if selected_product and selected_product != "":
            # 提取产品ID
            product_id = selected_product.split("(")[-1].replace(")", "")
            if product_id:
                SessionManager.set("selected_product_id", product_id)
                st.switch_page("pages/02_product_detail.py")


def show_pagination(total_count: int):
    """显示分页控件"""
    # 安全地获取和转换页码和页大小
    try:
        current_page_raw = SessionManager.get("current_page", 1)
        if isinstance(current_page_raw, (int, float)):
            current_page = int(current_page_raw)
        elif isinstance(current_page_raw, str) and current_page_raw.isdigit():
            current_page = int(current_page_raw)
        else:
            logger.warning(f"分页-页码值异常: {current_page_raw}, 使用默认值1")
            current_page = 1
            SessionManager.set("current_page", 1)
    except (ValueError, TypeError) as e:
        logger.error(f"分页-页码转换失败: {e}, 使用默认值1")
        current_page = 1
        SessionManager.set("current_page", 1)

    try:
        page_size_raw = SessionManager.get("page_size", 20)
        if isinstance(page_size_raw, (int, float)):
            page_size = int(page_size_raw)
        elif isinstance(page_size_raw, str) and page_size_raw.isdigit():
            page_size = int(page_size_raw)
        else:
            logger.warning(f"分页-页大小值异常: {page_size_raw}, 使用默认值20")
            page_size = 20
            SessionManager.set("page_size", 20)
    except (ValueError, TypeError) as e:
        logger.error(f"分页-页大小转换失败: {e}, 使用默认值20")
        page_size = 20
        SessionManager.set("page_size", 20)

    total_pages = (total_count + page_size - 1) // page_size

    if total_pages <= 1:
        return

    st.subheader("分页")

    col1, col2, col3, col4, col5 = st.columns([1, 1, 2, 1, 1])

    with col1:
        if st.button("⏮️ 首页", disabled=(current_page <= 1)):
            SessionManager.set("current_page", 1)
            st.rerun()

    with col2:
        if st.button("⏪ 上一页", disabled=(current_page <= 1)):
            SessionManager.set("current_page", current_page - 1)
            st.rerun()

    with col3:
        st.write(f"第 {current_page} / {total_pages} 页 (共 {total_count} 条记录)")

    with col4:
        if st.button("⏩ 下一页", disabled=(current_page >= total_pages)):
            SessionManager.set("current_page", current_page + 1)
            st.rerun()

    with col5:
        if st.button("⏭️ 末页", disabled=(current_page >= total_pages)):
            SessionManager.set("current_page", total_pages)
            st.rerun()


def export_products_data(df: pd.DataFrame):
    """导出产品数据"""
    try:
        # 将DataFrame转换为CSV
        csv = df.to_csv(index=False, encoding="utf-8-sig")

        # 提供下载按钮
        st.download_button(
            label="下载CSV文件",
            data=csv,
            file_name=f"产品数据_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.csv",
            mime="text/csv",
        )

        st.success("数据导出成功！")
    except Exception as e:
        logger.error(f"导出数据失败: {e}")
        st.error(f"导出数据失败: {str(e)}")


def render_footer():
    """渲染页面脚部"""
    st.markdown("---")
    st.markdown("© 2024 云商系统 - 产品管理模块")


if __name__ == "__main__":
    main()
