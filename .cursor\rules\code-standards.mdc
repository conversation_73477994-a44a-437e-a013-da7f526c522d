---
description: 
globs: 
alwaysApply: true
---
# 代码开发规范

## 📋 概述

本文档定义了云商系统项目的代码开发规范，包括命名约定、代码组织、注释规范等。所有开发人员必须遵循这些规范，确保代码质量和一致性。

## 🔤 命名约定

### 1. 文件命名
- 使用英文小写字母，单词间用下划线连接（snake_case）
- 模块名应当简洁且能表达其功能，如 `product_service.py`
- 避免使用中文或特殊字符作为文件名

### 2. 变量命名
- 使用描述性名称，避免缩写（除非是广泛接受的缩写）
- 使用小写字母和下划线（snake_case）
- 常量使用大写字母和下划线，如 `MAX_RETRY_COUNT`
- 布尔变量应当以 `is_`、`has_`、`can_` 等前缀开头

### 3. 函数命名
- 使用动词或动词短语，如 `get_product()`、`validate_user()`
- 使用小写字母和下划线（snake_case）
- 私有函数以单下划线开头，如 `_validate_input()`

### 4. 类命名
- 使用大驼峰命名法（CamelCase），如 `ProductService`
- 类名应当是名词或名词短语

## 📝 代码组织

### 1. 模块结构
- 每个模块应当有明确的单一职责
- 按以下顺序组织导入语句：
  1. 标准库导入
  2. 第三方库导入
  3. 本地应用/库导入
- 在导入语句后添加一个空行

### 2. 函数和类
- 函数应当短小精悍，一个函数只做一件事
- 避免过长函数，建议不超过50行
- 类应当遵循单一职责原则
- 相关的函数和类应当放在同一个模块中

### 3. 注释规范
- 使用文档字符串（docstring）为模块、类和函数提供文档
- 函数文档应当包括功能描述、参数说明和返回值说明
- 复杂逻辑应当添加行内注释
- 注释应当解释"为什么"而不是"是什么"

```python
def get_product_by_id(product_id: int) -> Optional[Dict[str, Any]]:
    """
    根据ID获取产品详情
    
    Args:
        product_id: 产品ID
        
    Returns:
        产品详情字典，如果未找到则返回None
    """
    # 实现代码...
```

## 🧪 测试规范

### 1. 测试覆盖
- 所有核心功能必须有单元测试
- 测试覆盖率应达到80%以上
- 边界条件和异常情况必须测试

### 2. 测试命名
- 测试文件名应当以 `test_` 开头
- 测试函数名应当以 `test_` 开头，并描述测试内容
- 测试类名应当以 `Test` 开头

```python
# 测试示例
def test_get_product_returns_none_for_invalid_id():
    result = get_product_by_id(-1)
    assert result is None
```

## 🛡️ 错误处理

### 1. 异常处理
- 使用具体的异常类型而不是捕获所有异常
- 记录异常信息，包括堆栈跟踪
- 不要在异常处理中使用空的 `except` 语句
- 使用 `finally` 子句确保资源被正确释放

```python
try:
    result = api_client.get_data()
except ConnectionError as e:
    logger.error(f"API连接失败: {e}")
    # 适当的错误处理
except ValueError as e:
    logger.error(f"数据格式错误: {e}")
    # 适当的错误处理
finally:
    # 清理资源
```

### 2. 日志记录
- 使用不同的日志级别（DEBUG, INFO, WARNING, ERROR, CRITICAL）
- 日志消息应当包含上下文信息
- 敏感信息不应当记录在日志中

## 🚀 性能优化

### 1. 数据库操作
- 减少数据库查询次数
- 使用批量操作而不是循环单条操作
- 合理使用索引
- 避免在循环中进行数据库操作

### 2. 缓存策略
- 合理使用 `@st.cache_data` 和 `@st.cache_resource` 装饰器
- 为缓存设置适当的TTL（生存时间）
- 在数据更新时主动清理相关缓存

## 🔒 安全最佳实践

### 1. 输入验证
- 所有用户输入必须经过验证
- 使用参数化查询防止SQL注入
- 避免直接拼接SQL语句

### 2. 敏感信息
- 不要在代码中硬编码敏感信息（密码、API密钥等）
- 使用环境变量或配置文件存储敏感信息
- 敏感信息应当加密存储

## ✅ 代码审查清单

在提交代码前，请检查以下项目：

- [ ] 代码是否遵循命名约定
- [ ] 是否有适当的注释和文档
- [ ] 是否有单元测试
- [ ] 是否处理了异常情况
- [ ] 是否有潜在的安全问题
- [ ] 是否有性能优化的空间
- [ ] 是否遵循了DRY（Don't Repeat Yourself）原则

---

**文档版本**: v1.0  
**最后更新**: 2024-12-19  
**维护者**: 开发团队

