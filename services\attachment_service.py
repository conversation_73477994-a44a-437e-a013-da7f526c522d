import os
import requests
import logging
import hashlib
import mimetypes
from typing import Dict, List, Any, Optional, Tuple
from urllib.parse import urlparse, unquote
from pathlib import Path
import time
from datetime import datetime
import psycopg2
import psycopg2.extras
import json
from utils.db_base import DatabaseService

logger = logging.getLogger(__name__)


class AttachmentService(DatabaseService):
    """附件管理服务 - 处理文件下载、存储和管理"""

    def __init__(
        self,
        download_base_path: str = "data/attachments",
        db_config: Optional[Dict] = None,
    ):
        """
        初始化附件服务

        Args:
            download_base_path: 附件下载基础路径
            db_config: 数据库配置（已弃用，现在使用连接池）
        """
        # 初始化父类DatabaseService
        super().__init__("AttachmentService")

        self.download_base_path = Path(download_base_path)
        self.download_base_path.mkdir(parents=True, exist_ok=True)

        # 支持的文件类型
        self.supported_types = {
            "image": [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg"],
            "video": [".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".m4v"],
            "document": [".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx"],
            "archive": [".zip", ".rar", ".7z", ".tar", ".gz"],
            "other": [],  # 其他类型
        }

        # 请求配置
        self.session = requests.Session()
        self.session.headers.update({"User-Agent": "YunShang-AttachmentService/1.0"})
        self.timeout = 30
        self.max_retries = 3

        logger.info(f"附件服务初始化完成 - 存储路径: {self.download_base_path}")

    def _save_attachment_record(self, attachment_info: Dict[str, Any]) -> Optional[int]:
        """
        保存附件记录到数据库

        Args:
            attachment_info: 附件信息

        Returns:
            附件记录ID
        """
        try:
            # 检查是否已存在相同的附件记录
            existing = self.execute_query(
                """
                SELECT id FROM attachments 
                WHERE original_url = %s AND category = %s AND item_id = %s AND field_name = %s
                """,
                (
                    attachment_info["url"],
                    attachment_info.get("category"),
                    attachment_info.get("item_id"),
                    attachment_info["field_name"],
                ),
                fetch_one=True,
            )

            if existing:
                # 更新现有记录
                self.execute_update(
                    """
                    UPDATE attachments SET 
                        local_path = %s,
                        filename = %s,
                        file_type = %s,
                        file_size = %s,
                        content_type = %s,
                        downloaded_at = %s,
                        is_active = %s,
                        update_time = CURRENT_TIMESTAMP
                    WHERE id = %s
                    """,
                    (
                        attachment_info.get("local_path"),
                        attachment_info.get("filename"),
                        attachment_info.get("file_type"),
                        attachment_info.get("file_size"),
                        attachment_info.get("content_type"),
                        attachment_info.get("downloaded_at"),
                        True,
                        existing["id"],
                    ),
                )
                attachment_id = existing["id"]
                logger.info(f"更新附件记录: {attachment_id}")
            else:
                # 插入新记录
                attachment_id = self.execute_update(
                    """
                    INSERT INTO attachments (
                        original_url, local_path, filename, file_type, file_size,
                        content_type, field_name, category, item_id, downloaded_at, is_active
                    ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    RETURNING id
                    """,
                    (
                        attachment_info["url"],
                        attachment_info.get("local_path"),
                        attachment_info.get("filename"),
                        attachment_info.get("file_type"),
                        attachment_info.get("file_size"),
                        attachment_info.get("content_type"),
                        attachment_info["field_name"],
                        attachment_info.get("category"),
                        attachment_info.get("item_id"),
                        attachment_info.get("downloaded_at"),
                        True,
                    ),
                    return_id=True,
                )
                logger.info(f"创建附件记录: {attachment_id}")

            return attachment_id

        except Exception as e:
            logger.error(f"保存附件记录失败: {e}")
            return None

    def _log_attachment_operation(
        self,
        attachment_id: int,
        operation: str,
        status: str,
        error_message: str = None,
        details: Dict = None,
    ):
        """
        记录附件操作日志

        Args:
            attachment_id: 附件ID
            operation: 操作类型
            status: 状态
            error_message: 错误信息
            details: 详细信息
        """
        try:
            self.execute_update(
                """
                INSERT INTO attachment_logs (attachment_id, operation, status, error_message, details)
                VALUES (%s, %s, %s, %s, %s)
                """,
                (
                    attachment_id,
                    operation,
                    status,
                    error_message,
                    json.dumps(details) if details else None,
                ),
            )
        except Exception as e:
            logger.error(f"记录附件操作日志失败: {e}")

    def get_local_attachments(
        self, category: str, item_id: int, field_name: str = None
    ) -> List[Dict[str, Any]]:
        """
        获取本地附件记录

        Args:
            category: 分类
            item_id: 项目ID
            field_name: 字段名（可选）

        Returns:
            本地附件列表
        """
        try:
            if field_name:
                return self.execute_query(
                    """
                    SELECT * FROM attachments 
                    WHERE category = %s AND item_id = %s AND field_name = %s AND is_active = %s
                    ORDER BY create_time
                    """,
                    (category, item_id, field_name, True),
                )
            else:
                return self.execute_query(
                    """
                    SELECT * FROM attachments 
                    WHERE category = %s AND item_id = %s AND is_active = %s
                    ORDER BY field_name, create_time
                    """,
                    (category, item_id, True),
                )
        except Exception as e:
            logger.error(f"获取本地附件记录失败: {e}")
            return []

    def get_file_type(self, url: str, filename: str = None) -> str:
        """
        获取文件类型

        Args:
            url: 文件URL
            filename: 文件名

        Returns:
            文件类型 (image/video/document/archive/other)
        """
        # 优先使用filename，其次使用URL
        check_name = filename or url

        # 提取文件扩展名
        ext = Path(check_name).suffix.lower()

        # 判断文件类型
        for file_type, extensions in self.supported_types.items():
            if ext in extensions:
                return file_type

        return "other"

    def generate_filename(self, url: str, content_type: str = None) -> Tuple[str, str]:
        """
        生成本地文件名

        Args:
            url: 原始URL
            content_type: MIME类型

        Returns:
            (文件名, 文件扩展名)
        """
        parsed_url = urlparse(url)
        original_filename = os.path.basename(unquote(parsed_url.path))

        # 如果URL没有文件名，根据content_type生成
        if not original_filename or "." not in original_filename:
            if content_type:
                ext = mimetypes.guess_extension(content_type)
                if ext:
                    original_filename = f"attachment{ext}"
                else:
                    original_filename = "attachment.bin"
            else:
                original_filename = "attachment.bin"

        # 生成安全的文件名
        timestamp = int(time.time())
        name_part = Path(original_filename).stem
        ext_part = Path(original_filename).suffix

        # 使用URL hash确保唯一性
        url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
        safe_filename = f"{name_part}_{timestamp}_{url_hash}{ext_part}"

        return safe_filename, ext_part

    def download_file(
        self, url: str, category: str, item_id: int, field_name: str
    ) -> Optional[Dict[str, Any]]:
        """
        下载单个文件

        Args:
            url: 文件URL
            category: 分类 (product/case/programme/information/distribution)
            item_id: 项目ID
            field_name: 字段名

        Returns:
            下载结果信息
        """
        if not url or not url.strip():
            logger.warning("URL为空，跳过下载")
            return None

        try:
            logger.info(f"开始下载附件: {url}")

            # 创建分类目录
            category_path = self.download_base_path / category / str(item_id)
            category_path.mkdir(parents=True, exist_ok=True)

            for attempt in range(self.max_retries):
                try:
                    # 发送HEAD请求获取文件信息
                    head_response = self.session.head(
                        url, timeout=self.timeout, allow_redirects=True
                    )
                    content_type = head_response.headers.get("content-type", "").split(
                        ";"
                    )[0]
                    content_length = head_response.headers.get("content-length")

                    # 检查文件大小（限制100MB）
                    if content_length and int(content_length) > 100 * 1024 * 1024:
                        logger.warning(f"文件过大，跳过下载: {url}")
                        return None

                    # 生成本地文件名
                    filename, ext = self.generate_filename(url, content_type)
                    local_path = category_path / filename

                    # 如果文件已存在，检查是否需要重新下载
                    if local_path.exists():
                        logger.info(f"文件已存在: {local_path}")
                        file_stats = local_path.stat()
                        return {
                            "success": True,
                            "url": url,
                            "local_path": str(
                                local_path.relative_to(self.download_base_path)
                            ),
                            "filename": filename,
                            "file_type": self.get_file_type(url, filename),
                            "file_size": file_stats.st_size,
                            "content_type": content_type,
                            "field_name": field_name,
                            "downloaded_at": datetime.fromtimestamp(
                                file_stats.st_mtime
                            ),
                            "is_duplicate": True,
                        }

                    # 下载文件
                    response = self.session.get(url, timeout=self.timeout, stream=True)
                    response.raise_for_status()

                    # 保存文件
                    with open(local_path, "wb") as f:
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                f.write(chunk)

                    # 验证文件
                    if not local_path.exists() or local_path.stat().st_size == 0:
                        logger.error(f"文件下载失败或文件为空: {local_path}")
                        if local_path.exists():
                            local_path.unlink()
                        return None

                    file_stats = local_path.stat()
                    result = {
                        "success": True,
                        "url": url,
                        "local_path": str(
                            local_path.relative_to(self.download_base_path)
                        ),
                        "filename": filename,
                        "file_type": self.get_file_type(url, filename),
                        "file_size": file_stats.st_size,
                        "content_type": content_type,
                        "field_name": field_name,
                        "downloaded_at": datetime.now(),
                        "is_duplicate": False,
                    }

                    logger.info(
                        f"文件下载成功: {filename} ({file_stats.st_size} bytes)"
                    )
                    return result

                except requests.exceptions.RequestException as e:
                    logger.warning(f"下载尝试 {attempt + 1} 失败: {e}")
                    if attempt == self.max_retries - 1:
                        logger.error(f"文件下载最终失败: {url}")
                        return {
                            "success": False,
                            "url": url,
                            "error": str(e),
                            "field_name": field_name,
                        }
                    time.sleep(2**attempt)  # 指数退避

        except Exception as e:
            logger.error(f"文件下载异常: {url} - {e}")
            return {
                "success": False,
                "url": url,
                "error": str(e),
                "field_name": field_name,
            }

    def download_multiple_files(
        self, urls: List[str], category: str, item_id: int, field_name: str
    ) -> List[Dict[str, Any]]:
        """
        批量下载文件

        Args:
            urls: URL列表
            category: 分类
            item_id: 项目ID
            field_name: 字段名

        Returns:
            下载结果列表
        """
        results = []

        if not urls:
            return results

        # 处理字符串形式的URL列表（逗号分隔）
        if isinstance(urls, str):
            urls = [url.strip() for url in urls.split(",") if url.strip()]

        logger.info(f"开始批量下载附件: {len(urls)} 个文件")

        for i, url in enumerate(urls):
            logger.info(f"下载进度: {i+1}/{len(urls)} - {url}")
            result = self.download_file(url, category, item_id, f"{field_name}_{i+1}")
            if result:
                results.append(result)

        successful_downloads = sum(1 for r in results if r.get("success"))
        logger.info(f"批量下载完成: 成功 {successful_downloads}/{len(urls)}")

        return results

    def process_item_attachments(
        self, item_data: Dict[str, Any], category: str
    ) -> Dict[str, List[Dict[str, Any]]]:
        """
        处理单个项目的所有附件

        Args:
            item_data: 项目数据
            category: 分类

        Returns:
            按字段分组的下载结果
        """
        item_id = item_data.get("id")
        if not item_id:
            logger.warning("项目ID为空，跳过附件处理")
            return {}

        results = {}

        # 定义各分类的附件字段映射
        attachment_fields = {
            "product": ["other", "qualifications", "smallImg", "banner"],
            "case": ["img", "banner", "smallImg", "video"],
            "programme": ["other", "video", "smallImg", "banner"],
            "information": ["otherUrl", "videoUrl", "picVideo", "smallImg", "images"],
            "distribution": ["other"],
        }

        fields = attachment_fields.get(category, [])

        for field in fields:
            field_value = item_data.get(field)
            if field_value:
                logger.info(f"处理 {category} ID:{item_id} 的字段 {field}")

                # 处理单个URL或多个URL
                if "," in str(field_value):
                    # 多个URL
                    field_results = self.download_multiple_files(
                        field_value, category, item_id, field
                    )
                else:
                    # 单个URL
                    field_result = self.download_file(
                        field_value, category, item_id, field
                    )
                    field_results = [field_result] if field_result else []

                if field_results:
                    results[field] = field_results

        return results

    def get_attachment_statistics(self) -> Dict[str, Any]:
        """
        获取附件统计信息

        Returns:
            统计信息字典
        """
        stats = {"total_files": 0, "total_size": 0, "by_category": {}, "by_type": {}}

        try:
            for category_path in self.download_base_path.iterdir():
                if category_path.is_dir():
                    category_stats = {"files": 0, "size": 0}

                    for root, dirs, files in os.walk(category_path):
                        for file in files:
                            file_path = Path(root) / file
                            file_size = file_path.stat().st_size

                            category_stats["files"] += 1
                            category_stats["size"] += file_size

                            # 统计文件类型
                            file_type = self.get_file_type("", file)
                            if file_type not in stats["by_type"]:
                                stats["by_type"][file_type] = {"files": 0, "size": 0}
                            stats["by_type"][file_type]["files"] += 1
                            stats["by_type"][file_type]["size"] += file_size

                    stats["by_category"][category_path.name] = category_stats
                    stats["total_files"] += category_stats["files"]
                    stats["total_size"] += category_stats["size"]

        except Exception as e:
            logger.error(f"获取附件统计信息失败: {e}")

        return stats

    def cleanup_orphaned_files(self, valid_paths: List[str]) -> int:
        """
        清理孤立文件

        Args:
            valid_paths: 有效的文件路径列表

        Returns:
            清理的文件数量
        """
        cleaned_count = 0

        try:
            valid_paths_set = set(valid_paths)

            for root, dirs, files in os.walk(self.download_base_path):
                for file in files:
                    file_path = Path(root) / file
                    relative_path = str(file_path.relative_to(self.download_base_path))

                    if relative_path not in valid_paths_set:
                        file_path.unlink()
                        cleaned_count += 1
                        logger.info(f"删除孤立文件: {relative_path}")

        except Exception as e:
            logger.error(f"清理孤立文件失败: {e}")

        return cleaned_count
