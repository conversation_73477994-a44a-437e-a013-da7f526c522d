#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Markdown图片路径替换脚本
功能：将所有*.md文件中的 (images/ 替换为 (https://zktecowebui.com/images/
"""

import os
import glob
import re
from typing import List


def replace_image_paths_in_file(file_path: str) -> bool:
    """
    替换单个Markdown文件中的图片路径
    返回是否成功处理
    """
    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 替换 (images/ 为 (https://zktecowebui.com/images/
        # 使用正则表达式确保只替换图片路径中的部分
        content = re.sub(r'\(images/', r'(https://zktecowebui.com/images/', content)
        
        # 如果内容有变化，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            # 统计替换次数
            replacement_count = original_content.count('(images/') - content.count('(images/')
            print(f"✓ 已处理: {file_path} (替换了 {replacement_count} 个图片路径)")
            return True
        else:
            print(f"○ 无需更改: {file_path}")
            return False
            
    except Exception as e:
        print(f"✗ 处理失败 {file_path}: {str(e)}")
        return False


def find_markdown_files(root_dir: str) -> List[str]:
    """
    递归查找所有Markdown文件
    """
    # 使用glob递归查找所有.md文件
    pattern = os.path.join(root_dir, '**', '*.md')
    markdown_files = glob.glob(pattern, recursive=True)
    return markdown_files


def preview_replacements(file_path: str) -> tuple:
    """
    预览文件将要进行的替换
    返回 (是否有替换, 替换前示例, 替换后示例, 替换次数)
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否有需要替换的内容
        matches = re.findall(r'\(images/[^)]+\)', content)
        if not matches:
            return False, "", "", 0
        
        # 计算替换次数
        replacement_count = len(matches)
        
        # 获取前几个示例
        examples_before = matches[:3]  # 最多显示3个示例
        examples_after = [match.replace('(images/', '(https://zktecowebui.com/images/') for match in examples_before]
        
        return True, examples_before, examples_after, replacement_count
        
    except Exception as e:
        return False, f"错误: {str(e)}", "", 0


def main():
    """
    主函数
    """
    # 设置目标目录
    target_dir = 'output_new'
    
    if not os.path.exists(target_dir):
        print(f"错误：目录 {target_dir} 不存在！")
        return
    
    print("Markdown图片路径替换脚本")
    print("="*60)
    print("功能说明：")
    print("将所有*.md文件中的图片路径进行替换：")
    print("从：(images/filename.jpg)")
    print("到：(https://zktecowebui.com/images/filename.jpg)")
    print("="*60)
    print(f"目标目录: {target_dir}")
    
    # 查找所有Markdown文件
    markdown_files = find_markdown_files(target_dir)
    
    if not markdown_files:
        print("未找到任何Markdown文件！")
        return
    
    print(f"找到 {len(markdown_files)} 个Markdown文件")
    
    # 询问是否预览更改
    choice = input("\n请选择操作：\n1. 直接处理所有文件\n2. 预览前几个需要更改的文件\n3. 退出\n请输入选择 (1/2/3): ").strip()
    
    if choice == '3':
        print("已退出。")
        return
    elif choice == '2':
        # 预览模式
        print("\n正在查找需要更改的文件...")
        preview_count = 0
        for file_path in markdown_files:
            has_replacements, examples_before, examples_after, count = preview_replacements(file_path)
            if has_replacements:
                preview_count += 1
                print(f"\n预览文件 {preview_count}: {file_path}")
                print(f"需要替换 {count} 个图片路径")
                print("示例替换：")
                for i, (before, after) in enumerate(zip(examples_before, examples_after)):
                    print(f"  {i+1}. {before} -> {after}")
                
                if preview_count >= 5:  # 最多预览5个文件
                    print("\n...")
                    break
        
        if preview_count == 0:
            print("没有找到需要替换的文件。")
            return
        
        confirm = input(f"\n总共找到 {preview_count}+ 个文件需要处理。是否继续处理所有文件？(y/n): ").strip().lower()
        if confirm == 'y':
            choice = '1'
        else:
            print("已取消处理。")
            return
    
    if choice == '1':
        print("\n开始处理文件...")
        print("-" * 60)
        
        # 处理统计
        processed_count = 0
        success_count = 0
        total_replacements = 0
        
        # 处理每个文件
        for file_path in markdown_files:
            processed_count += 1
            
            # 先统计替换次数
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            file_replacements = content.count('(images/')
            
            if replace_image_paths_in_file(file_path):
                success_count += 1
                total_replacements += file_replacements
        
        print("-" * 60)
        print(f"处理完成！")
        print(f"总文件数: {processed_count}")
        print(f"成功处理: {success_count}")
        print(f"无需更改: {processed_count - success_count}")
        print(f"总计替换: {total_replacements} 个图片路径")
    else:
        print("无效选择，已退出。")


if __name__ == "__main__":
    main()