#!/usr/bin/env python3
"""
测试字段映射
验证重要字段的映射是否正确
"""

import os
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 加载环境变量
from config import load_env_file

load_env_file()

from utils.api_client import ZKMallClient
from utils.auth import AuthManager
from utils.logging_config import get_logger
from services.complete_product_parser import CompleteProductParser

logger = get_logger()


def test_field_mapping():
    """测试字段映射"""
    try:
        print("🚀 开始测试字段映射")

        # 1. 登录认证
        print("\n🔐 步骤1: 用户登录认证")
        success, user_info = AuthManager.login("18929343717", "Zk@123456")

        if not success:
            print("❌ 登录失败")
            return

        print(f"✅ 登录成功")

        # 2. 初始化服务
        print("\n🔧 步骤2: 初始化解析器")
        api_client = ZKMallClient()
        parser = CompleteProductParser()
        print("✅ 解析器初始化成功")

        # 3. 获取产品数据
        print("\n📦 步骤3: 获取产品数据")
        products = api_client.get_products(pageSize=1, current=1)

        if not products:
            print("❌ 未获取到产品数据")
            return

        print(f"✅ 获取到 {len(products)} 个产品")

        # 4. 分析第一个产品的字段映射
        product = products[0]
        print(f"\n📋 产品: {product.get('name', 'Unknown')}")

        # 重要字段的原始字段名和期望的解析字段名
        important_field_mapping = {
            "useTo": "use_to",  # 应用于
            "newParam": "new_param",  # 设备参数介绍功能介绍新的
            "paramInfo": "param_info",  # 参数信息
            "paramInfoList": "param_info_list",  # 参数信息列表
            "accessoryList": "accessory_list",  # 配件列表
            "category": "category_id",  # 分类ID
            "categoryName": "category_name",  # 分类名称
            "commonProblem": "common_problem",  # 常见问题
            "isHot": "is_hot",  # 是否热门
            "isNew": "is_new",  # 是否新品
            "label": "label_id",  # 标签ID
            "labelList": "label_list",  # 标签列表
            "labelName": "label_name",  # 标签名称
            "updateTime": "update_time",  # 更新时间
            "videoExplanation": "video_explanation",  # 说明视频
            "videoInstallation": "video_installation",  # 安装视频
            "videoTroubleshooting": "video_troubleshooting",  # 故障排除视频
        }

        print("\n🔍 标准化后数据中的字段值:")
        for original_field, expected_parsed_field in important_field_mapping.items():
            # 使用标准化后的字段名查找
            standardized_value = product.get(expected_parsed_field)
            if standardized_value is not None:
                if (
                    isinstance(standardized_value, str)
                    and len(standardized_value) > 100
                ):
                    display_value = standardized_value[:100] + "..."
                elif isinstance(standardized_value, (list, dict)):
                    display_value = f"{type(standardized_value).__name__} with {len(standardized_value)} items"
                else:
                    display_value = standardized_value
                print(f"  ✅ {expected_parsed_field}: {display_value}")
            else:
                print(f"  ❌ {expected_parsed_field}: None/不存在")

        # 5. 解析产品并检查映射结果
        print("\n🔄 解析产品...")
        parsed_product = parser.parse_product(product)

        print("\n🔍 解析后的字段值:")
        for original_field, expected_parsed_field in important_field_mapping.items():
            parsed_value = parsed_product.get(expected_parsed_field)
            if parsed_value is not None:
                if isinstance(parsed_value, str) and len(parsed_value) > 100:
                    display_value = parsed_value[:100] + "..."
                elif isinstance(parsed_value, (list, dict)):
                    display_value = (
                        f"{type(parsed_value).__name__} with {len(parsed_value)} items"
                    )
                else:
                    display_value = parsed_value
                print(f"  ✅ {expected_parsed_field}: {display_value}")
            else:
                print(f"  ❌ {expected_parsed_field}: None/不存在")

        # 6. 检查解析器的字段映射配置
        print("\n🔧 检查解析器的字段映射配置:")
        # 获取解析器的字段映射
        field_mapping = getattr(parser, "field_mapping", {})

        for original_field, expected_parsed_field in important_field_mapping.items():
            if original_field in field_mapping:
                mapped_field, data_type = field_mapping[original_field]
                if mapped_field == expected_parsed_field:
                    print(f"  ✅ {original_field} -> {mapped_field} ({data_type})")
                else:
                    print(
                        f"  ⚠️  {original_field} -> {mapped_field} (期望: {expected_parsed_field})"
                    )
            else:
                print(f"  ❌ {original_field}: 未在字段映射中找到")

        # 7. 显示所有原始字段
        print(f"\n📋 原始数据所有字段 ({len(product)} 个):")
        for i, key in enumerate(sorted(product.keys()), 1):
            value = product[key]
            if isinstance(value, str) and len(value) > 50:
                display_value = value[:50] + "..."
            elif isinstance(value, (list, dict)):
                display_value = f"{type(value).__name__}({len(value)})"
            else:
                display_value = value
            print(f"  {i:2d}. {key}: {display_value}")

        print("\n🎉 字段映射测试完成！")

    except Exception as e:
        logger.error(f"字段映射测试失败: {e}", exc_info=True)
        print(f"❌ 测试失败: {e}")


if __name__ == "__main__":
    test_field_mapping()
