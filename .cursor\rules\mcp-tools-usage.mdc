---
description: 
globs: 
alwaysApply: true
---
# MCP工具使用规范

## 📋 概述

本项目强制要求优先使用MCP（Model Context Protocol）工具来提升开发效率和代码质量。MCP工具提供了丰富的功能，包括浏览器自动化、UI组件生成、项目管理、反馈收集等。

**核心原则**: 
- 优先使用MCP工具而非传统方法
- 充分利用MCP工具的自动化能力
- 遵循MCP工具的最佳实践

## 🛠️ 可用MCP工具分类

### 1. 🌐 浏览器自动化工具 (Playwright)

#### 核心功能
- **页面导航**: `mcp_playwright_playwright_navigate`
- **元素交互**: `mcp_playwright_playwright_click`, `mcp_playwright_playwright_fill`
- **截图功能**: `mcp_playwright_playwright_screenshot`
- **脚本执行**: `mcp_playwright_playwright_evaluate`
- **HTTP请求**: `mcp_playwright_playwright_get/post/put/patch/delete`

#### 使用场景
```python
# 自动化测试云商接口
await mcp_playwright_playwright_navigate({
    "url": "https://zkmall.zktecoiot.com",
    "browserType": "chromium",
    "headless": false
})

# 截图记录页面状态
await mcp_playwright_playwright_screenshot({
    "name": "login_page",
    "savePng": true,
    "fullPage": true
})

# API测试
await mcp_playwright_playwright_post({
    "url": "https://zkmall.zktecoiot.com/api/login",
    "value": json.dumps({"username": "test", "password": "test"}),
    "headers": {"Content-Type": "application/json"}
})
```

#### 代码生成功能
- **开始录制**: `mcp_playwright_start_codegen_session`
- **结束录制**: `mcp_playwright_end_codegen_session`
- **会话管理**: `mcp_playwright_get_codegen_session`

### 2. 🎨 UI组件生成工具 (21st DevMagic)

#### 组件构建器
- **工具**: `mcp_21st-devmagic_21st_magic_component_builder`
- **用途**: 根据用户需求自动生成React/Streamlit组件
- **触发词**: `/ui`, `/21`, `/21st`

```python
# 生成产品展示组件
component = mcp_21st-devmagic_21st_magic_component_builder({
    "searchQuery": "product card",
    "message": "创建一个产品卡片组件，显示产品图片、名称、价格和描述",
    "absolutePathToCurrentFile": "/d:/syncdb-nnnnn/yunshang/components/product.py",
    "absolutePathToProjectDirectory": "/d:/syncdb-nnnnn/yunshang",
    "standaloneRequestQuery": "产品卡片组件，包含图片、标题、价格、描述字段"
})
```

#### 组件优化器
- **工具**: `mcp_21st-devmagic_21st_magic_component_refiner`
- **用途**: 优化现有UI组件的设计和功能

#### Logo搜索
- **工具**: `mcp_21st-devmagic_logo_search`
- **格式**: TSX, JSX, SVG
- **用法**: `/logo GitHub` 或 `["discord", "github", "slack"]`

### 3. 📊 项目管理工具 (CodeLF)

#### 项目信息管理
- **获取项目信息**: `mcp_codelf_get-project-info`
- **更新项目信息**: `mcp_codelf_update-project-info`
- **初始化CodeLF**: `mcp_codelf_init-codelf`

```python
# 获取项目完整信息
project_info = mcp_codelf_get-project-info({
    "rootPath": "/d:/syncdb-nnnnn/yunshang"
})

# 代码修改后更新项目文档
mcp_codelf_update-project-info({
    "rootPath": "/d:/syncdb-nnnnn/yunshang"
})
```

#### 使用规范
- 每次重大代码修改后必须调用更新项目信息
- 定期使用获取项目信息来了解项目状态
- 新项目必须先初始化CodeLF目录

### 4. 💭 思考工具 (Think Tool)

#### 核心功能
- **记录思考**: `mcp_think-tool_think`
- **获取思考**: `mcp_think-tool_get_thoughts`
- **清理思考**: `mcp_think-tool_clear_thoughts`
- **思考统计**: `mcp_think-tool_get_thought_stats`

#### 使用场景
```python
# 复杂问题分析
mcp_think-tool_think({
    "thought": "分析云商API集成的技术难点：1. 认证机制复杂 2. 数据量大需要分页 3. 错误处理机制"
})

# 设计决策记录
mcp_think-tool_think({
    "thought": "选择PostgreSQL作为主数据库的原因：支持JSON字段、ACID事务、丰富的索引类型"
})
```

### 5. 📝 反馈收集工具 (MCP Feedback Enhanced)

#### 交互式反馈
- **工具**: `mcp_mcp-feedback-enhanced_interactive_feedback`
- **功能**: 收集用户反馈，支持文字和图片
- **超时设置**: 默认600秒（10分钟）

```python
# 收集开发完成反馈
feedback = mcp_mcp-feedback-enhanced_interactive_feedback({
    "project_directory": "/d:/syncdb-nnnnn/yunshang",
    "summary": "产品管理模块开发完成，请测试功能并提供反馈",
    "timeout": 600
})
```

#### 系统信息获取
- **工具**: `mcp_mcp-feedback-enhanced_get_system_info`
- **用途**: 获取系统环境信息用于调试

### 6. 🧠 深度推理工具 (DeepSeek Claude)

#### 高级推理
- **工具**: `mcp_deepseek-claude_reason`
- **功能**: 使用DeepSeek R1引擎进行复杂推理
- **输出**: 包含`<ant_thinking>`标签的推理过程

```python
# 复杂架构决策推理
reasoning = mcp_deepseek-claude_reason({
    "query": {
        "context": "云商系统需要处理大量产品数据和用户并发访问",
        "question": "如何设计缓存策略来优化系统性能？"
    }
})
```

### 7. 📚 文档检索工具 (Context7)

#### 库文档检索
- **解析库ID**: `mcp_context7_resolve-library-id`
- **获取文档**: `mcp_context7_get-library-docs`

```python
# 获取Streamlit文档
library_id = mcp_context7_resolve-library-id({
    "libraryName": "streamlit"
})

docs = mcp_context7_get-library-docs({
    "context7CompatibleLibraryID": library_id,
    "tokens": 10000,
    "topic": "caching"
})
```

### 8. 🐙 GitHub集成工具

#### 仓库管理
- **创建仓库**: `mcp_github_create_repository`
- **fork仓库**: `mcp_github_fork_repository`
- **文件操作**: `mcp_github_create_or_update_file`

#### Issue和PR管理
- **创建Issue**: `mcp_github_create_issue`
- **创建PR**: `mcp_github_create_pull_request`
- **添加评论**: `mcp_github_add_issue_comment`

#### 代码搜索
- **搜索代码**: `mcp_github_search_code`
- **搜索仓库**: `mcp_github_search_repositories`

## 🎯 MCP工具使用最佳实践

### 1. 开发流程中的MCP使用

#### 需求分析阶段
```python
# 1. 使用思考工具分析需求
mcp_think-tool_think({
    "thought": "分析用户需求：需要一个产品展示页面，包含搜索、筛选、分页功能"
})

# 2. 获取相关库文档
streamlit_docs = mcp_context7_get-library-docs({
    "context7CompatibleLibraryID": "/streamlit/streamlit",
    "topic": "pagination"
})
```

#### 设计阶段
```python
# 1. 使用深度推理工具进行架构设计
architecture = mcp_deepseek-claude_reason({
    "query": {
        "context": "Streamlit应用需要展示云商产品数据",
        "question": "如何设计组件架构来实现最佳的用户体验？"
    }
})

# 2. 生成UI组件
product_component = mcp_21st-devmagic_21st_magic_component_builder({
    "searchQuery": "data table",
    "message": "创建一个产品数据表格组件"
})
```

#### 开发阶段
```python
# 1. 更新项目信息
mcp_codelf_update-project-info({
    "rootPath": "/d:/syncdb-nnnnn/yunshang"
})

# 2. 使用浏览器自动化测试
mcp_playwright_playwright_navigate({
    "url": "http://localhost:8501"
})

mcp_playwright_playwright_screenshot({
    "name": "development_progress",
    "savePng": true
})
```

#### 测试阶段
```python
# 1. 自动化API测试
mcp_playwright_playwright_post({
    "url": "https://zkmall.zktecoiot.com/api/products",
    "value": json.dumps({"page": 1, "size": 20})
})

# 2. 收集测试反馈
mcp_mcp-feedback-enhanced_interactive_feedback({
    "summary": "产品模块测试完成，请验收功能"
})
```

### 2. 代码质量保障

#### 组件开发规范
```python
# 使用MCP工具生成的组件必须符合以下标准：
# 1. 遵循Streamlit开发规范
# 2. 包含完整的类型提示
# 3. 具备错误处理机制
# 4. 支持缓存优化

@st.cache_data
def load_products_with_mcp():
    """使用MCP工具优化的产品加载函数"""
    # 记录思考过程
    mcp_think-tool_think({
        "thought": "产品数据加载需要考虑缓存策略和错误处理"
    })
    
    try:
        # 实际数据加载逻辑
        return fetch_products_from_api()
    except Exception as e:
        st.error(f"产品数据加载失败: {e}")
        return []
```

#### GitHub集成规范
```python
# 每个功能完成后必须：
# 1. 创建对应的GitHub Issue记录
mcp_github_create_issue({
    "owner": "your-org",
    "repo": "yunshang",
    "title": "产品管理模块开发完成",
    "body": "完成产品列表、详情、搜索功能的开发"
})

# 2. 创建Pull Request
mcp_github_create_pull_request({
    "owner": "your-org",
    "repo": "yunshang",
    "title": "feat: 产品管理模块",
    "head": "feature/product-management",
    "base": "main",
    "body": "实现产品管理相关功能"
})
```

### 3. 性能优化指南

#### 使用MCP工具进行性能监控
```python
# 1. 浏览器性能测试
mcp_playwright_playwright_evaluate({
    "script": """
        // 测量页面加载时间
        const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
        console.log('页面加载时间:', loadTime);
        return loadTime;
    """
})

# 2. 组件性能优化
optimized_component = mcp_21st-devmagic_21st_magic_component_refiner({
    "absolutePathToRefiningFile": "/d:/syncdb-nnnnn/yunshang/components/product.py",
    "context": "优化产品列表组件的加载性能，减少渲染时间",
    "userMessage": "请优化产品列表的性能，支持虚拟滚动"
})
```

## 🚨 强制要求

### 1. MCP工具优先级
- **必须**: 优先使用MCP工具完成任务
- **禁止**: 在有对应MCP工具的情况下使用传统方法
- **要求**: 每个开发任务都要考虑相关的MCP工具

### 2. 文档更新要求
- 每次使用MCP工具后必须更新项目文档
- 使用`mcp_codelf_update-project-info`保持文档同步
- 记录MCP工具使用的最佳实践

### 3. 质量检查清单
- [ ] 是否使用了合适的MCP工具？
- [ ] 是否记录了思考过程？
- [ ] 是否收集了用户反馈？
- [ ] 是否更新了项目信息？
- [ ] 是否进行了自动化测试？

## 📚 参考资源

### 重要文档引用
- [数据模型设计](mdc:docs/data-models-design.md) - 数据库设计参考
- [系统架构设计](mdc:docs/system-architecture.md) - 架构设计参考  
- [开发计划](mdc:docs/development-plan.md) - 开发流程参考
- [Streamlit开发规范](mdc:.cursor/rules/streamlit-dev-standards.mdc) - 前端开发规范

### MCP工具链接
- Playwright自动化: 用于浏览器测试和截图
- 21st DevMagic: 用于UI组件生成和优化
- CodeLF: 用于项目信息管理
- DeepSeek推理: 用于复杂问题分析
- GitHub集成: 用于代码管理和协作

---

**重要提醒**: 本规范是强制性的，所有开发人员必须严格遵循。MCP工具的使用将显著提升开发效率和代码质量，是项目成功的关键因素。

