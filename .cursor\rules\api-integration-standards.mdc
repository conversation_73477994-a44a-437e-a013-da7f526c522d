---
description: 
globs: 
alwaysApply: true
---
# API集成标准规范

## 📋 概述

本文档基于云商系统历史API异常分析，制定严格的API集成标准。重点解决认证失败、字段映射错误、接口兼容性等关键问题，确保API集成的稳定性和可靠性。

## 🚨 历史API异常分析

### 发现的关键问题
1. **配单数据API认证失败**：大量401错误，认证机制不稳定
2. **FastGPT接口从静态到动态**：接口变更导致的兼容性问题
3. **字段映射不一致**：productId vs id, productName vs name
4. **用户信息API端点错误**：错误端点导致类型转换异常
5. **API响应处理不规范**：缺乏统一的响应验证机制

## 🔑 认证机制标准

### 1. 统一认证管理器
```python
from typing import Dict, Any, Optional
import time
import logging
from dataclasses import dataclass
from enum import Enum

class AuthType(Enum):
    BEARER_TOKEN = "bearer"
    API_KEY = "api_key"
    BASIC_AUTH = "basic"
    OAUTH2 = "oauth2"

@dataclass
class AuthConfig:
    auth_type: AuthType
    credentials: Dict[str, str]
    token_refresh_url: Optional[str] = None
    token_expiry: Optional[int] = None

class AuthenticationManager:
    """统一认证管理器"""
    
    def __init__(self):
        self.max_retries = 3
        self.retry_delay = 2
        self.timeout = 30
        self.token_cache = {}
        self.logger = logging.getLogger(__name__)
    
    def authenticate(self, auth_config: AuthConfig) -> Dict[str, Any]:
        """
        执行认证操作
        
        Args:
            auth_config: 认证配置
            
        Returns:
            认证结果，包含token和过期时间
            
        Raises:
            AuthenticationError: 认证失败
            ValidationError: 参数验证失败
        """
        # 验证认证配置
        self._validate_auth_config(auth_config)
        
        # 检查缓存的token
        cached_token = self._get_cached_token(auth_config)
        if cached_token and not self._is_token_expired(cached_token):
            return cached_token
        
        # 执行认证
        for attempt in range(self.max_retries):
            try:
                result = self._perform_authentication(auth_config)
                self._cache_token(auth_config, result)
                return result
                
            except (ConnectionError, TimeoutError) as e:
                if attempt == self.max_retries - 1:
                    raise AuthenticationError(f"认证连接失败，已重试{self.max_retries}次: {e}")
                
                wait_time = self.retry_delay * (2 ** attempt)
                self.logger.warning(f"认证尝试 {attempt + 1} 失败，{wait_time}秒后重试: {e}")
                time.sleep(wait_time)
                
            except AuthenticationError:
                # 认证错误不重试
                raise
                
            except Exception as e:
                self.logger.error(f"认证过程中发生未知错误: {e}")
                raise AuthenticationError(f"认证失败: {e}")
    
    def _validate_auth_config(self, config: AuthConfig) -> None:
        """验证认证配置"""
        if not config.credentials:
            raise ValidationError("认证凭据不能为空")
        
        required_fields = {
            AuthType.BEARER_TOKEN: ['token'],
            AuthType.API_KEY: ['api_key'],
            AuthType.BASIC_AUTH: ['username', 'password'],
            AuthType.OAUTH2: ['client_id', 'client_secret']
        }
        
        required = required_fields.get(config.auth_type, [])
        missing = [field for field in required if not config.credentials.get(field)]
        
        if missing:
            raise ValidationError(f"缺少必需的认证字段: {missing}")
    
    def _perform_authentication(self, config: AuthConfig) -> Dict[str, Any]:
        """执行具体的认证逻辑"""
        if config.auth_type == AuthType.BEARER_TOKEN:
            return self._bearer_token_auth(config)
        elif config.auth_type == AuthType.API_KEY:
            return self._api_key_auth(config)
        elif config.auth_type == AuthType.BASIC_AUTH:
            return self._basic_auth(config)
        elif config.auth_type == AuthType.OAUTH2:
            return self._oauth2_auth(config)
        else:
            raise AuthenticationError(f"不支持的认证类型: {config.auth_type}")
```

### 2. 云商API专用认证
```python
class ZKMallAuthenticator(AuthenticationManager):
    """云商API专用认证器"""
    
    def __init__(self):
        super().__init__()
        self.base_url = os.getenv('ZKMALL_API_BASE_URL')
        self.login_endpoint = '/api/login'
        
    def authenticate_zkmall(self, username: str, password: str) -> Dict[str, Any]:
        """
        云商系统认证
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            认证结果，包含token和用户信息
        """
        if not username or not password:
            raise ValidationError("用户名和密码不能为空")
        
        auth_config = AuthConfig(
            auth_type=AuthType.BASIC_AUTH,
            credentials={
                'username': username,
                'password': password
            }
        )
        
        try:
            result = self.authenticate(auth_config)
            
            # 验证云商API特有的响应格式
            if not result.get('success'):
                raise AuthenticationError(result.get('message', '云商认证失败'))
            
            # 提取token和用户信息
            token = result.get('data', {}).get('token')
            user_info = result.get('data', {}).get('userInfo', {})
            
            if not token:
                raise AuthenticationError("认证成功但未获取到token")
            
            return {
                'token': token,
                'user_info': user_info,
                'expires_at': time.time() + result.get('data', {}).get('expiresIn', 3600)
            }
            
        except Exception as e:
            self.logger.error(f"云商认证失败: {e}")
            raise
```

## 🔄 字段映射标准化

### 1. 统一字段映射管理器
```python
from typing import Dict, Any, List, Optional
import json

class FieldMappingManager:
    """统一字段映射管理器"""
    
    def __init__(self):
        self.mappings = {
            # 产品相关字段映射
            'product': {
                'api_to_internal': {
                    'productId': 'id',
                    'productName': 'name',
                    'productCode': 'code',
                    'productCategory': 'category',
                    'paramInfoList': 'specifications',
                    'productImages': 'images',
                    'productPrice': 'price',
                    'productDescription': 'description'
                },
                'internal_to_api': {
                    'id': 'productId',
                    'name': 'productName',
                    'code': 'productCode',
                    'category': 'productCategory',
                    'specifications': 'paramInfoList',
                    'images': 'productImages',
                    'price': 'productPrice',
                    'description': 'productDescription'
                }
            },
            
            # 用户相关字段映射
            'user': {
                'api_to_internal': {
                    'userId': 'id',
                    'userName': 'username',
                    'userEmail': 'email',
                    'userPhone': 'phone',
                    'emailVerified': 'email_verified',
                    'phoneVerified': 'phone_verified',
                    'userStatus': 'status',
                    'createTime': 'created_at',
                    'updateTime': 'updated_at'
                },
                'internal_to_api': {
                    'id': 'userId',
                    'username': 'userName',
                    'email': 'userEmail',
                    'phone': 'userPhone',
                    'email_verified': 'emailVerified',
                    'phone_verified': 'phoneVerified',
                    'status': 'userStatus',
                    'created_at': 'createTime',
                    'updated_at': 'updateTime'
                }
            },
            
            # 配单相关字段映射
            'distribution': {
                'api_to_internal': {
                    'orderId': 'id',
                    'orderNumber': 'number',
                    'orderStatus': 'status',
                    'orderItems': 'items',
                    'totalAmount': 'total_amount',
                    'orderDate': 'created_at'
                },
                'internal_to_api': {
                    'id': 'orderId',
                    'number': 'orderNumber',
                    'status': 'orderStatus',
                    'items': 'orderItems',
                    'total_amount': 'totalAmount',
                    'created_at': 'orderDate'
                }
            }
        }
    
    def map_fields(self, data: Any, entity_type: str, direction: str) -> Any:
        """
        执行字段映射
        
        Args:
            data: 要映射的数据
            entity_type: 实体类型 (product, user, distribution等)
            direction: 映射方向 (api_to_internal 或 internal_to_api)
            
        Returns:
            映射后的数据
        """
        if not data:
            return data
        
        mapping = self.mappings.get(entity_type, {}).get(direction, {})
        if not mapping:
            self.logger.warning(f"未找到 {entity_type} 的 {direction} 映射配置")
            return data
        
        if isinstance(data, dict):
            return self._map_dict_fields(data, mapping)
        elif isinstance(data, list):
            return [self._map_dict_fields(item, mapping) if isinstance(item, dict) else item for item in data]
        else:
            return data
    
    def _map_dict_fields(self, data: Dict[str, Any], mapping: Dict[str, str]) -> Dict[str, Any]:
        """映射字典字段"""
        result = {}
        
        for key, value in data.items():
            # 获取映射后的键名
            mapped_key = mapping.get(key, key)
            
            # 递归处理嵌套对象
            if isinstance(value, dict):
                result[mapped_key] = self._map_dict_fields(value, mapping)
            elif isinstance(value, list) and value and isinstance(value[0], dict):
                result[mapped_key] = [self._map_dict_fields(item, mapping) for item in value]
            else:
                result[mapped_key] = value
        
        return result
    
    def validate_required_fields(self, data: Dict[str, Any], entity_type: str, required_fields: List[str]) -> None:
        """
        验证必需字段
        
        Args:
            data: 要验证的数据
            entity_type: 实体类型
            required_fields: 必需字段列表
            
        Raises:
            ValidationError: 当缺少必需字段时
        """
        missing_fields = []
        
        for field in required_fields:
            if field not in data or data[field] is None:
                missing_fields.append(field)
        
        if missing_fields:
            raise ValidationError(f"{entity_type} 缺少必需字段: {missing_fields}")
```

## 🌐 API客户端标准

### 1. 统一API客户端基类
```python
import requests
from typing import Dict, Any, Optional, Union
from urllib.parse import urljoin
import time

class BaseAPIClient:
    """API客户端基类"""
    
    def __init__(self, base_url: str, timeout: int = 30):
        self.base_url = base_url.rstrip('/')
        self.timeout = timeout
        self.session = requests.Session()
        self.field_mapper = FieldMappingManager()
        self.authenticator = None
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 设置默认请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'YunShang-System/1.0'
        })
    
    def set_authenticator(self, authenticator: AuthenticationManager) -> None:
        """设置认证器"""
        self.authenticator = authenticator
    
    def _ensure_authenticated(self) -> None:
        """确保已认证"""
        if not self.authenticator:
            raise AuthenticationError("未设置认证器")
        
        # 这里应该检查token是否有效，如果无效则重新认证
        # 具体实现取决于认证方式
    
    def _make_request(
        self,
        method: str,
        endpoint: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        require_auth: bool = True
    ) -> Dict[str, Any]:
        """
        执行HTTP请求
        
        Args:
            method: HTTP方法
            endpoint: API端点
            params: URL参数
            data: 请求体数据
            headers: 额外的请求头
            require_auth: 是否需要认证
            
        Returns:
            API响应数据
            
        Raises:
            APIError: API调用失败
            AuthenticationError: 认证失败
        """
        if require_auth:
            self._ensure_authenticated()
        
        url = urljoin(self.base_url, endpoint.lstrip('/'))
        
        # 合并请求头
        request_headers = self.session.headers.copy()
        if headers:
            request_headers.update(headers)
        
        try:
            self.logger.debug(f"API请求: {method} {url}")
            
            response = self.session.request(
                method=method,
                url=url,
                params=params,
                json=data,
                headers=request_headers,
                timeout=self.timeout
            )
            
            # 处理HTTP状态码
            if response.status_code == 401:
                raise AuthenticationError("API认证失败，请检查认证信息")
            elif response.status_code == 403:
                raise AuthorizationError("API访问被拒绝，权限不足")
            elif response.status_code == 404:
                raise APINotFoundError(f"API端点不存在: {endpoint}")
            elif response.status_code >= 500:
                raise APIServerError(f"API服务器错误: {response.status_code}")
            elif response.status_code >= 400:
                raise APIClientError(f"API客户端错误: {response.status_code}")
            
            # 解析响应
            try:
                result = response.json()
            except ValueError as e:
                raise APIResponseError(f"API响应不是有效的JSON: {e}")
            
            # 验证响应格式
            self._validate_response(result)
            
            return result
            
        except requests.RequestException as e:
            self.logger.error(f"API请求异常: {e}")
            raise APIConnectionError(f"API连接失败: {e}")
    
    def _validate_response(self, response: Dict[str, Any]) -> None:
        """验证API响应格式"""
        # 可以根据具体API的响应格式进行验证
        pass
    
    def get(self, endpoint: str, params: Optional[Dict[str, Any]] = None, **kwargs) -> Dict[str, Any]:
        """GET请求"""
        return self._make_request('GET', endpoint, params=params, **kwargs)
    
    def post(self, endpoint: str, data: Optional[Dict[str, Any]] = None, **kwargs) -> Dict[str, Any]:
        """POST请求"""
        return self._make_request('POST', endpoint, data=data, **kwargs)
    
    def put(self, endpoint: str, data: Optional[Dict[str, Any]] = None, **kwargs) -> Dict[str, Any]:
        """PUT请求"""
        return self._make_request('PUT', endpoint, data=data, **kwargs)
    
    def delete(self, endpoint: str, **kwargs) -> Dict[str, Any]:
        """DELETE请求"""
        return self._make_request('DELETE', endpoint, **kwargs)
```

### 2. 云商API专用客户端
```python
class ZKMallAPIClient(BaseAPIClient):
    """云商API专用客户端"""
    
    def __init__(self):
        base_url = os.getenv('ZKMALL_API_BASE_URL')
        if not base_url:
            raise ConfigurationError("未配置ZKMALL_API_BASE_URL环境变量")
        
        super().__init__(base_url)
        self.set_authenticator(ZKMallAuthenticator())
    
    def authenticate(self, username: str, password: str) -> Dict[str, Any]:
        """云商系统认证"""
        result = self.authenticator.authenticate_zkmall(username, password)
        
        # 设置认证头
        self.session.headers['Authorization'] = f"Bearer {result['token']}"
        
        return result
    
    def get_products(self, page: int = 1, size: int = 20, search: str = "") -> Dict[str, Any]:
        """
        获取产品列表
        
        Args:
            page: 页码
            size: 每页数量
            search: 搜索关键词
            
        Returns:
            产品列表数据
        """
        params = {
            'page': page,
            'size': size
        }
        
        if search:
            params['search'] = search
        
        try:
            response = self.get('/api/products', params=params)
            
            # 字段映射
            if 'data' in response and 'list' in response['data']:
                response['data']['list'] = self.field_mapper.map_fields(
                    response['data']['list'],
                    'product',
                    'api_to_internal'
                )
            
            return response
            
        except Exception as e:
            self.logger.error(f"获取产品列表失败: {e}")
            raise
    
    def get_product_detail(self, product_id: Union[str, int]) -> Dict[str, Any]:
        """
        获取产品详情
        
        Args:
            product_id: 产品ID
            
        Returns:
            产品详情数据
        """
        if not product_id:
            raise ValidationError("产品ID不能为空")
        
        try:
            response = self.get(f'/api/products/{product_id}')
            
            # 字段映射
            if 'data' in response:
                response['data'] = self.field_mapper.map_fields(
                    response['data'],
                    'product',
                    'api_to_internal'
                )
            
            return response
            
        except APINotFoundError:
            raise ProductNotFoundError(f"产品不存在: {product_id}")
        except Exception as e:
            self.logger.error(f"获取产品详情失败: {e}")
            raise
    
    def get_distribution_orders(self, page: int = 1, size: int = 20, status: str = "") -> Dict[str, Any]:
        """
        获取配单列表
        
        Args:
            page: 页码
            size: 每页数量
            status: 订单状态
            
        Returns:
            配单列表数据
        """
        params = {
            'page': page,
            'size': size
        }
        
        if status:
            params['status'] = status
        
        try:
            response = self.get('/api/distributionOrderPlatform/list', params=params)
            
            # 字段映射
            if 'data' in response and 'list' in response['data']:
                response['data']['list'] = self.field_mapper.map_fields(
                    response['data']['list'],
                    'distribution',
                    'api_to_internal'
                )
            
            return response
            
        except Exception as e:
            self.logger.error(f"获取配单列表失败: {e}")
            raise
```

## 🚫 禁止模式

### 1. 禁止的API调用模式
```python
# ❌ 禁止：硬编码API端点
def get_user_info():
    response = requests.get("http://localhost:8080/api/system/user/getInfo")  # 错误的端点
    return response.json()

# ❌ 禁止：不处理异常
def call_api():
    response = requests.get(url)
    return response.json()  # 可能抛出异常

# ❌ 禁止：使用模拟数据
def get_products():
    return {
        "data": {
            "list": [
                {"id": 1, "name": "模拟产品"}  # 模拟数据
            ]
        }
    }

# ❌ 禁止：字段名不一致
def process_product(api_data):
    return {
        "productId": api_data["id"],  # 不一致的字段名
        "name": api_data["productName"]
    }
```

### 2. 必须的API调用模式
```python
# ✅ 正确：使用统一API客户端
def get_user_info():
    client = ZKMallAPIClient()
    try:
        response = client.get('/api/user/info')  # 正确的端点
        return response
    except APIError as e:
        logger.error(f"获取用户信息失败: {e}")
        raise

# ✅ 正确：完整的异常处理
def call_api_safely():
    try:
        client = ZKMallAPIClient()
        response = client.get('/api/endpoint')
        return response
    except AuthenticationError as e:
        logger.error(f"认证失败: {e}")
        raise
    except APIError as e:
        logger.error(f"API调用失败: {e}")
        raise
    except Exception as e:
        logger.error(f"未知错误: {e}")
        raise

# ✅ 正确：使用真实API
def get_products():
    client = ZKMallAPIClient()
    return client.get_products()

# ✅ 正确：使用字段映射
def process_product(api_data):
    mapper = FieldMappingManager()
    return mapper.map_fields(api_data, 'product', 'api_to_internal')
```

## 📋 API集成检查清单

### 开发阶段
- [ ] 是否使用统一的API客户端？
- [ ] 是否正确处理认证？
- [ ] 是否使用字段映射？
- [ ] 是否处理所有可能的异常？
- [ ] 是否使用真实API而非模拟数据？

### 测试阶段
- [ ] 是否测试认证失败场景？
- [ ] 是否测试网络连接失败？
- [ ] 是否测试API响应格式错误？
- [ ] 是否测试字段映射正确性？
- [ ] 是否测试超时处理？

### 部署阶段
- [ ] 是否配置正确的API端点？
- [ ] 是否设置适当的超时时间？
- [ ] 是否配置监控和告警？
- [ ] 是否准备API降级方案？

## 🔧 自定义异常类型

```python
class APIError(Exception):
    """API相关异常基类"""
    pass

class AuthenticationError(APIError):
    """认证失败异常"""
    pass

class AuthorizationError(APIError):
    """授权失败异常"""
    pass

class APIConnectionError(APIError):
    """API连接异常"""
    pass

class APIResponseError(APIError):
    """API响应格式异常"""
    pass

class APINotFoundError(APIError):
    """API端点不存在异常"""
    pass

class APIServerError(APIError):
    """API服务器错误异常"""
    pass

class APIClientError(APIError):
    """API客户端错误异常"""
    pass

class ValidationError(Exception):
    """数据验证异常"""
    pass

class ProductNotFoundError(Exception):
    """产品不存在异常"""
    pass

class ConfigurationError(Exception):
    """配置错误异常"""
    pass
```

---

**重要提醒**: 本规范是强制性的，所有API集成必须严格遵循此标准。违反此规范的代码将不被接受，需要重新修改后才能合并。

**文档版本**: v1.0  
**最后更新**: 2024-12-19  
**维护者**: 开发团队

