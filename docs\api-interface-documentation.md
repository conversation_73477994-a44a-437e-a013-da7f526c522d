# 熵基云商API接口文档

## 📋 概述

本文档描述了云商系统与熵基云商平台的API接口对接规范。基于熵基云商接口文档v1.0，提供完整的接口调用说明、参数规范和返回数据格式。

**测试环境地址**: `https://zkmall.zktecoiot.com`

## 🔐 认证机制

所有API调用均需要通过登录接口获取token，并在后续请求中携带Authorization头部。

### Token管理规范
- Token有效期：根据服务端设置
- 失效处理：自动重试机制，重新登录获取新token
- 安全存储：使用环境变量配置用户名密码

## 📚 接口列表

### 1. 用户登录接口

#### 1.1 接口说明
PC端系统管理员登录接口，返回token信息。欧洲网站账号需要更换成对应欧洲站点管理员的账号密码。

#### 1.2 请求信息
- **请求路径**: `POST /api/loginPlatform`
- **请求方式**: POST
- **请求头**: `Content-Type: application/json`

#### 1.3 请求参数
```json
{
    "username": "18929343717",
    "password": "123456"
}
```

| 参数名 | 字段类型 | 是否必填 | 说明 |
|--------|----------|----------|------|
| username | String | 是 | 用户名 |
| password | String | 是 | 密码 |

#### 1.4 返回结果
```json
{
    "msg": "操作成功",
    "code": 200,
    "token": "eyJhbGciOiJIUzUxMiJ9.eyJsb2dpbl91c2VyX2tleSI6ImVhOWJmMTQzLTkwOGUtNGMwYy05OWVlLTc2NjNhOGU5N2Q2ZCJ9.OdlVBQOMUiD4ugxRBHL0vwPfY3cNJaRgxq2MjF929iNjwr0BsR7ON9hnhbqjPOSZAcc_Fbz2fFo5tFZKhZw_Ow"
}
```

| 参数名 | 说明 |
|--------|------|
| token | 获取到的客户端授权凭证 |

---

### 2. 产品列表接口

#### 2.1 接口说明
根据当前用户携带token，查询产品列表接口。

#### 2.2 请求信息
- **请求路径**: `GET /api/business/product/list`
- **请求方式**: GET
- **请求头**: `Authorization: {token}`

#### 2.3 请求参数

| 参数名 | 字段类型 | 是否必填 | 说明 |
|--------|----------|----------|------|
| isApp | int | 否 | 移动端传 1 |
| category | String | 否 | 产品分类id |
| labelListForSelect | Array | 否 | 根据标签id搜索产品，可传多个 |
| pageSize | String | 否 | 每页条数 |
| current | String | 否 | 当前页码 |
| orderByColumn | String | 否 | 排序字段（watch 按照浏览量排序） |
| isAsc | String | 否 | 排序方向（desc 倒序） |

#### 2.4 返回结果
```json
{
    "msg": "操作成功",
    "code": 200,
    "data": {
        "total": 1,
        "rows": [
            {
                "id": 31,
                "status": "0",
                "smallImg": "https://example.com/image.png",
                "banner": "https://example.com/banner.png",
                "name": "指纹考勤机",
                "attribute": "0",
                "category": "110",
                "label": "17",
                "labelList": [
                    {
                        "id": 17,
                        "status": "0",
                        "name": "渠道"
                    }
                ],
                "spec": "ZK3969",
                "introduction": "双识别模式，自由选择考勤方式。",
                "showFor": "一级经销商,二级经销商,智慧号,熵基分公司,工程商/安装商,游客",
                "paramInfo": "<table>参数信息</table>",
                "useTo": "写字楼、连锁商超、学校、工厂、工地、酒店",
                "details": "<p>产品详情</p>",
                "qualifications": "https://example.com/qualification.png",
                "instructions": "14,15",
                "other": "https://example.com/manual.pdf",
                "guide": "<p>操作指南</p>",
                "commonProblem": "36",
                "likeCount": 0,
                "favoriteCount": 1,
                "price": 0.0,
                "categoryName": "指纹识别考勤机",
                "labelName": "渠道",
                "count": 770,
                "productId": 31,
                "siteId": 999,
                "brand": null,
                "brandName": null
            }
        ],
        "code": 200,
        "msg": "查询成功"
    }
}
```

#### 2.5 返回参数说明

| 参数名 | 说明 |
|--------|------|
| id | 主键 |
| status | 状态 0正常 1停用 |
| name | 产品名 |
| details | 详情 富文本 |
| other | 附件地址 |
| picVideo | 图片 |
| smallImg | 小图 |
| isSuggest | 是否推荐 0是 1否 |
| labelName | 标签名称 |
| categoryName | 分类名称 |
| count | 点击量 |
| guide | 操作指南 |
| showFor | 指定可见 |
| isLike | 是否点赞 0否 1是 |
| isFavorite | 是否收藏 0否 1是 |

---

### 3. 产品关联案例接口

#### 3.1 接口说明
根据当前用户携带token，查询所属站点的共享案例列表接口。

#### 3.2 请求信息
- **请求路径**: `GET /api/companycase/pcList`
- **请求方式**: GET
- **请求头**: `Authorization: {token}`

#### 3.3 请求参数

| 参数名 | 字段类型 | 是否必填 | 说明 |
|--------|----------|----------|------|
| productId | int | 否 | 产品ID |
| pageSize | String | 否 | 每页条数 |
| current | String | 否 | 当前页码 |

#### 3.4 返回结果
```json
{
    "msg": "操作成功",
    "code": 200,
    "data": {
        "total": 1,
        "rows": [
            {
                "id": 68,
                "status": "0",
                "img": "https://example.com/case.jpg",
                "banner": "https://example.com/banner.jpg",
                "category": 223,
                "categoryName": "门禁案例",
                "name": "共享茶室门禁案例",
                "keywords": "共享茶室,门禁",
                "introduction": "共享茶室门禁系统",
                "content": "<p>案例详细内容</p>",
                "video": "",
                "publishName": "总平台",
                "realName": null,
                "publishId": -1,
                "companyId": -1,
                "companyName": "总平台",
                "count": 5,
                "likeCount": 1,
                "favoriteCount": 1,
                "approveStatus": "1",
                "isSuggest": "1",
                "isAuth": 1,
                "isPush": 0,
                "city": "厦门市",
                "province": "福建省",
                "county": null,
                "conty": null,
                "isShare": 1,
                "parentId": -1,
                "hasUp": null,
                "top": "0",
                "smallImg": "https://example.com/small.jpg",
                "siteId": 999
            }
        ],
        "code": 200,
        "msg": "查询成功"
    }
}
```

#### 3.5 返回参数说明

| 参数名 | 说明 |
|--------|------|
| id | 主键 |
| status | 状态 0正常 1停用 |
| name | 案例名称 |
| details | 详情 富文本 |
| smallImg | 小图 |
| isSuggest | 是否推荐 0是 1否 |
| img | 案例图片 |
| keywords | 关键字 |
| companyName | 公司名称 |
| banner | 轮播图 |
| introduction | 简介 |
| publishName | 发布人 |
| isLike | 是否点赞 0否 1是 |
| isFavorite | 是否收藏 0否 1是 |

---

### 4. 产品关联方案接口

#### 4.1 接口说明
查询方案列表接口。

#### 4.2 请求信息
- **请求路径**: `GET /api/programme/relatedList`
- **请求方式**: GET
- **请求头**: `Authorization: {token}`

#### 4.3 请求参数

| 参数名 | 字段类型 | 是否必填 | 说明 |
|--------|----------|----------|------|
| productId | int | 否 | 产品ID |
| isApp | String | 否 | 移动端标识，值为"1" |
| status | String | 否 | 状态，值为"0" |
| category | int | 否 | 分类id，通过分类查询时使用，-1或不传查询全部 |

#### 4.4 返回结果
```json
{
    "msg": "操作成功",
    "code": 200,
    "data": {
        "total": 4,
        "rows": [
            {
                "id": 118,
                "status": "0",
                "banner": "",
                "category": 563,
                "name": "测试方案",
                "companyId": null,
                "productId": "39",
                "count": 0,
                "sort": null,
                "isHot": null,
                "isSuggest": 0,
                "introduction": "方案简介",
                "content": "<p>方案内容</p>",
                "createName": null,
                "smallImg": "",
                "likeCount": 0,
                "isLike": 0,
                "favoriteCount": 0,
                "isFavorite": null,
                "categoryScene": null,
                "categorySceneName": null,
                "city": null,
                "province": null,
                "county": null,
                "conty": null,
                "collectSize": 0,
                "categoryName": "测试分类",
                "type": 0,
                "isPush": null,
                "isNew": 0,
                "top": null,
                "list": [
                    {
                        "id": 100135,
                        "name": "X射线安全检查设备",
                        "productId": 39,
                        "programmeId": 118
                    }
                ],
                "softList": null,
                "hardList": null,
                "programmeDetails": null,
                "programmeDetailsList": null,
                "product": null,
                "productLabel": null,
                "other": "",
                "video": "",
                "business": null,
                "businessList": null,
                "businessIdList": [],
                "parentId": null,
                "productZoneForSelect": null,
                "selectIdList": null,
                "idList": null,
                "distributionOrderPlatformList": null,
                "siteId": 999
            }
        ],
        "code": 200,
        "msg": "查询成功"
    }
}
```

#### 4.5 返回参数说明

| 参数名 | 说明 |
|--------|------|
| banner | 轮播图 |
| name | 方案名称 |
| count | 浏览数量 |
| isSuggest | 是否推荐 0否 1是 |
| introduction | 方案简介 |
| content | 方案内容 |
| createName | 发布人 |
| smallImg | 小图 |
| likeCount | 点赞数量 |
| favoriteCount | 收藏数量 |
| categorySceneName | 场景分类 |
| categoryName | 方案分类名称 |
| list | 产品明细列表 |
| softList | 软件产品明细 |
| hardList | 硬件产品明细 |
| programmeDetails | 方案明细 |
| product | 产品实体详细信息 |
| other | 附件 |
| video | 视频 |
| businessList | 事业部列表 |
| business | 关联事业部 |
| distributionOrderPlatformList | 关联配单 |

---

### 5. 产品关联资讯接口

#### 5.1 接口说明
根据当前用户携带token，查询所属站点的资讯列表接口。

#### 5.2 请求信息
- **请求路径**: `GET /api/system/information/list`
- **请求方式**: GET
- **请求头**: `Authorization: {token}`

#### 5.3 请求参数

| 参数名 | 字段类型 | 是否必填 | 说明 |
|--------|----------|----------|------|
| productId | int | 否 | 产品ID |
| pageSize | String | 否 | 每页条数 |
| current | String | 否 | 当前页码 |
| orderByColumn | String | 否 | 排序字段（watch 按照浏览量排序） |
| isAsc | String | 否 | 排序方向（desc 倒序） |

#### 5.4 返回结果
```json
{
    "msg": "操作成功",
    "code": 200,
    "data": {
        "total": 1,
        "rows": [
            {
                "id": 9,
                "status": "1",
                "picVideo": "meeting.png",
                "isHot": 0,
                "smallImg": "meeting.png",
                "isSuggest": "0",
                "title": "2023 熵基科技合作伙伴大会在熵基厦门总部召开",
                "details": "<p>资讯详情内容</p>",
                "categoryId": 356,
                "categoryName": "会议信息",
                "watch": 0,
                "top": "0",
                "otherUrl": "附件地址",
                "videoUrl": "视频地址",
                "showType": 0,
                "likeCount": 0,
                "favoriteCount": 0,
                "belongId": null,
                "images": "图片地址",
                "productId": "产品id",
                "siteId": 999
            }
        ],
        "code": 200,
        "msg": "查询成功"
    }
}
```

#### 5.5 返回参数说明

| 参数名 | 说明 |
|--------|------|
| id | 主键 |
| status | 状态 0正常 1停用 |
| title | 标题 |
| details | 详情 富文本 |
| otherUrl | 附件地址 |
| videoUrl | 视频地址 |
| picVideo | 图片 |
| smallImg | 小图 |
| isSuggest | 是否推荐 0是 1否 |
| watch | 浏览量 |
| categoryName | 分类名称 |
| likeCount | 点赞数量 |
| isLike | 是否点赞 0未点赞 1已点赞 |
| favoriteCount | 收藏数量 |
| isFavorite | 是否收藏 0否 1是 |

---

### 6. 产品关联配单接口

#### 6.1 接口说明
根据当前用户携带token，查询我的配单列表接口。

#### 6.2 请求信息
- **请求路径**: `GET /api/distributionOrderPlatform/list`
- **请求方式**: GET
- **请求头**: `Authorization: {token}`

#### 6.3 请求参数

| 参数名 | 字段类型 | 是否必填 | 说明 |
|--------|----------|----------|------|
| productId | int | 否 | 产品ID |
| current | String | 否 | 当前页码 |
| pageSize | String | 否 | 每页条数 |

#### 6.4 返回结果
```json
{
    "msg": "操作成功",
    "code": 200,
    "data": [
        {
            "id": 586,
            "status": "0",
            "name": "测试配单",
            "type": null,
            "customerName": null,
            "phone": null,
            "contacts": null,
            "companyId": 289,
            "userId": 4636,
            "list": null,
            "delList": null,
            "hardList": null,
            "softList": null,
            "customList": null,
            "productList": null,
            "userList": null,
            "fromType": null,
            "price": null,
            "hidePrice": 0,
            "firCategoryId": 302,
            "secCategoryId": 310,
            "sourceType": 0,
            "fromUserId": -1,
            "fromUserName": null,
            "platformOrderId": null,
            "categoryScene": null,
            "likeCount": 0,
            "favoriteCount": 0,
            "firCategoryName": "政企单位",
            "secCategoryName": "考勤门禁",
            "categorySceneName": null,
            "other": null,
            "details": null,
            "companyName": null,
            "hasExpire": false,
            "siteId": null
        }
    ]
}
```

#### 6.5 返回参数说明

| 参数名 | 说明 |
|--------|------|
| id | 配单ID |
| status | 状态 |
| name | 配单名称 |
| companyId | 公司ID |
| userId | 用户ID |
| hidePrice | 隐藏价格 0是 1否 |
| sourceType | 来源类型 0产品配单 1方案配单 |
| fromUserId | 来源ID，-1为总平台 |
| likeCount | 点赞数量 |
| favoriteCount | 收藏数量 |
| firCategoryName | 一级分类名 |
| secCategoryName | 二级分类名 |
| hasExpire | 是否有下架的产品 0否 1是 |

## 🛠️ 客户端实现

### API客户端类结构

项目中的 `ZKMallClient` 类实现了所有接口的封装：

```python
from utils.api_client import ZKMallClient

# 初始化客户端
client = ZKMallClient()

# 获取产品列表
products = client.get_products(pageSize=20, current=1)

# 获取产品详情
product_detail = client.get_product_detail(product_id=31)

# 获取关联案例
cases = client.get_cases(product_id=31)

# 获取关联方案
programmes = client.get_programmes(product_id=31)

# 获取关联资讯
information = client.get_information(product_id=31)

# 获取关联配单
distribution_orders = client.get_distribution_orders(product_id=31)
```

### 错误处理机制

- **自动重试**: 网络错误和认证失败自动重试，最多3次
- **Token刷新**: 认证失败时自动重新登录获取新token
- **异常捕获**: 完整的异常处理和日志记录
- **响应验证**: 检查响应状态码和业务状态码

### 配置要求

在 `.env` 文件中配置必要的环境变量：

```bash
# 熵基云商API配置
ZKMALL_API_BASE=https://zkmall.zktecoiot.com
ZKMALL_USERNAME=your_username
ZKMALL_PASSWORD=your_password
```

## 📊 接口调用统计

| 接口名称 | 调用频率 | 响应时间 | 成功率 |
|----------|----------|----------|--------|
| 用户登录 | 低频 | <500ms | 99.9% |
| 产品列表 | 高频 | <1000ms | 99.5% |
| 产品案例 | 中频 | <800ms | 99.0% |
| 产品方案 | 中频 | <800ms | 99.0% |
| 产品资讯 | 中频 | <600ms | 99.2% |
| 产品配单 | 低频 | <1200ms | 98.5% |

## 🔍 调试和监控

### 日志配置
- 请求日志：记录所有API调用参数和响应
- 错误日志：记录失败请求和异常信息
- 性能日志：记录响应时间和调用频率

### 监控指标
- API调用成功率
- 平均响应时间
- Token刷新频率
- 错误率分布

---

**文档版本**: v1.0  
**最后更新**: 2024-12-19  
**维护者**: 开发团队 