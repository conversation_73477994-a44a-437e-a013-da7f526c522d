#!/usr/bin/env python3
"""
文档处理服务

该服务负责：
1. 检测和处理产品相关文档（PDF、Word等）
2. 使用mineru将PDF转换为Markdown格式
3. 优化转换后的Markdown内容格式
4. 处理图片标注和AI多模态分析
5. 按二级标题分段并添加标题标识

严格遵循开发规范，使用真实API接口，禁止模拟数据。
"""

import json
import logging
import os
import re
import shutil
import subprocess
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any

import requests

logger = logging.getLogger(__name__)

# 可选依赖导入
try:
    from docx import Document

    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False
    logger.warning("python-docx未安装，Word文档处理功能不可用")

try:
    from docx2pdf import convert as docx2pdf_convert

    DOCX2PDF_AVAILABLE = True
except ImportError:
    docx2pdf_convert = None
    DOCX2PDF_AVAILABLE = False
    logger.warning("docx2pdf未安装，Word转PDF功能不可用")


class DocumentProcessor:
    """文档处理器"""

    def __init__(self):
        """初始化处理器"""
        self.temp_dir = Path("temp_docs")
        self.output_dir = Path("processed_docs")
        self.mineru_output_dir = Path("mineru_output")

        # 创建必要的目录
        for dir_path in [self.temp_dir, self.output_dir, self.mineru_output_dir]:
            dir_path.mkdir(exist_ok=True)

        # AI多模态服务配置
        self.ai_service_url = os.getenv("AI_MULTIMODAL_SERVICE_URL", "")
        self.ai_service_key = os.getenv("AI_MULTIMODAL_SERVICE_KEY", "")

        # 初始化AI标注服务（可选）
        try:
            from services.ai_annotation_service import AIAnnotationService

            self.ai_annotation_service = AIAnnotationService()
            logger.info("AI标注服务初始化成功")
        except Exception as e:
            logger.warning(f"AI标注服务初始化失败: {e}")
            self.ai_annotation_service = None

        logger.info("文档处理器初始化成功")

    def process_product_documents(
        self, product_model: str, document_paths: List[str]
    ) -> List[Dict[str, Any]]:
        """
        处理产品相关文档

        Args:
            product_model: 产品型号
            document_paths: 文档路径列表

        Returns:
            处理结果列表
        """
        try:
            logger.info(
                f"开始处理产品 '{product_model}' 的 {len(document_paths)} 个文档"
            )

            results = []

            for doc_path in document_paths:
                try:
                    result = self._process_single_document(product_model, doc_path)
                    if result:
                        results.append(result)

                except Exception as e:
                    logger.error(f"处理文档 '{doc_path}' 失败: {e}")
                    results.append(
                        {"file_path": doc_path, "status": "error", "error": str(e)}
                    )

            logger.info(
                f"产品 '{product_model}' 文档处理完成，成功处理 {len([r for r in results if r.get('status') == 'success'])} 个文档"
            )
            return results

        except Exception as e:
            logger.error(f"处理产品 '{product_model}' 文档失败: {e}")
            return []

    def _process_single_document(
        self, product_model: str, doc_path: str
    ) -> Optional[Dict[str, Any]]:
        """处理单个文档"""
        try:
            doc_file_path = Path(doc_path)
            if not doc_file_path.exists():
                logger.warning(f"文档文件不存在: {doc_file_path}")
                return None

            file_extension = doc_file_path.suffix.lower()
            file_name = doc_file_path.stem

            logger.info(f"处理文档: {doc_file_path.name} (类型: {file_extension})")

            # 根据文件类型选择处理方式
            if file_extension == ".pdf":
                return self._process_pdf_document(product_model, doc_file_path)
            elif file_extension in [".doc", ".docx"]:
                return self._process_word_document(product_model, doc_file_path)
            elif file_extension in [".txt", ".md"]:
                return self._process_text_document(product_model, doc_file_path)
            else:
                logger.warning(f"不支持的文档类型: {file_extension}")
                return None

        except Exception as e:
            logger.error(f"处理文档 '{doc_path}' 失败: {e}")
            return None

    def _process_pdf_document(
        self, product_model: str, pdf_path: Path
    ) -> Dict[str, Any]:
        """处理PDF文档"""
        try:
            logger.info(f"使用mineru处理PDF文档: {pdf_path.name}")

            # 使用mineru转换PDF
            markdown_content = self._convert_pdf_with_mineru(pdf_path)

            if not markdown_content:
                return {
                    "file_path": str(pdf_path),
                    "status": "error",
                    "error": "PDF转换失败",
                }

            # 优化Markdown格式
            optimized_content = self._optimize_markdown_format(
                markdown_content, pdf_path.stem
            )

            # 按二级标题分段
            segments = self._segment_by_h2_headers(optimized_content, pdf_path.stem)

            # 处理图片标注
            processed_segments = self._process_image_annotations(
                segments, product_model
            )

            # 保存处理结果
            output_file = self.output_dir / f"{product_model}_{pdf_path.stem}.md"
            with open(output_file, "w", encoding="utf-8") as f:
                f.write("\n\n".join(processed_segments))

            return {
                "file_path": str(pdf_path),
                "output_path": str(output_file),
                "status": "success",
                "segments_count": len(processed_segments),
                "content_preview": (
                    optimized_content[:200] + "..."
                    if len(optimized_content) > 200
                    else optimized_content
                ),
            }

        except Exception as e:
            logger.error(f"处理PDF文档 '{pdf_path}' 失败: {e}")
            return {"file_path": str(pdf_path), "status": "error", "error": str(e)}

    def _process_word_document(
        self, product_model: str, word_path: Path
    ) -> Dict[str, Any]:
        """处理Word文档"""
        try:
            logger.info(f"处理Word文档: {word_path.name}")

            # 先转换为PDF
            pdf_path = self._convert_word_to_pdf(word_path)

            if not pdf_path:
                return {
                    "file_path": str(word_path),
                    "status": "error",
                    "error": "Word转PDF失败",
                }

            # 然后按PDF流程处理
            result = self._process_pdf_document(product_model, pdf_path)

            # 更新原始文件路径
            result["original_file_path"] = str(word_path)
            result["converted_pdf_path"] = str(pdf_path)

            return result

        except Exception as e:
            logger.error(f"处理Word文档 '{word_path}' 失败: {e}")
            return {"file_path": str(word_path), "status": "error", "error": str(e)}

    def _process_text_document(
        self, product_model: str, text_path: Path
    ) -> Dict[str, Any]:
        """处理文本文档"""
        try:
            logger.info(f"处理文本文档: {text_path.name}")

            # 读取文本内容
            with open(text_path, "r", encoding="utf-8") as f:
                content = f.read()

            # 如果是Markdown，直接优化格式
            if text_path.suffix.lower() == ".md":
                optimized_content = self._optimize_markdown_format(
                    content, text_path.stem
                )
            else:
                # 普通文本转换为Markdown格式
                optimized_content = f"# {text_path.stem}\n\n{content}"

            # 按二级标题分段
            segments = self._segment_by_h2_headers(optimized_content, text_path.stem)

            # 保存处理结果
            output_file = self.output_dir / f"{product_model}_{text_path.stem}.md"
            with open(output_file, "w", encoding="utf-8") as f:
                f.write("\n\n".join(segments))

            return {
                "file_path": str(text_path),
                "output_path": str(output_file),
                "status": "success",
                "segments_count": len(segments),
                "content_preview": (
                    content[:200] + "..." if len(content) > 200 else content
                ),
            }

        except Exception as e:
            logger.error(f"处理文本文档 '{text_path}' 失败: {e}")
            return {"file_path": str(text_path), "status": "error", "error": str(e)}

    def _convert_word_to_pdf(self, word_path: Path) -> Optional[Path]:
        """将Word文档转换为PDF"""
        try:
            if not DOCX2PDF_AVAILABLE:
                logger.error("docx2pdf未安装，无法转换Word文档")
                return None

            pdf_path = self.temp_dir / f"{word_path.stem}.pdf"

            # 使用docx2pdf转换
            if docx2pdf_convert:
                docx2pdf_convert(str(word_path), str(pdf_path))
            else:
                raise ImportError("docx2pdf未安装")

            if pdf_path.exists():
                logger.info(f"Word转PDF成功: {word_path.name} -> {pdf_path.name}")
                return pdf_path
            else:
                logger.error(f"Word转PDF失败: {word_path.name}")
                return None

        except Exception as e:
            logger.error(f"Word转PDF异常: {e}")
            return None

    def _convert_pdf_with_mineru(self, pdf_path: Path) -> str:
        """使用mineru转换PDF为Markdown"""
        try:
            # 检查mineru是否可用
            if not self._check_mineru_availability():
                logger.error("mineru不可用，无法转换PDF")
                return ""

            # 设置输出目录
            output_dir = self.mineru_output_dir / pdf_path.stem
            output_dir.mkdir(exist_ok=True)

            # 调用mineru转换
            success = self._call_mineru_parse(pdf_path, output_dir)

            if not success:
                logger.error(f"mineru转换失败: {pdf_path.name}")
                return ""

            # 查找生成的Markdown文件
            content = self._extract_markdown_from_mineru_output(
                output_dir, pdf_path.stem
            )

            if content:
                logger.info(f"mineru转换成功: {pdf_path.name}")
                return content
            else:
                logger.error(f"mineru转换后未找到有效内容: {pdf_path.name}")
                return ""

        except Exception as e:
            logger.error(f"mineru转换PDF失败: {e}")
            return ""

    def _check_mineru_availability(self) -> bool:
        """检查mineru是否可用"""
        try:
            # 尝试导入mineru相关模块
            from backup_20250621_145301.baickmarkdofile.demo import parse_doc

            return True
        except ImportError as e:
            logger.warning(f"mineru模块导入失败: {e}")
            try:
                # 尝试直接导入mineru
                import mineru

                return True
            except ImportError:
                logger.warning("mineru未安装或不可用")
                return False

    def _call_mineru_parse(self, pdf_path: Path, output_dir: Path) -> bool:
        """调用mineru解析PDF"""
        try:
            from backup_20250621_145301.baickmarkdofile.demo import parse_doc

            # 调用mineru转换
            parse_doc(
                path_list=[pdf_path],
                output_dir=str(output_dir),
                lang="ch",
                backend="pipeline",
                method="auto",
            )

            return True

        except Exception as e:
            logger.error(f"调用mineru解析失败: {e}")
            return False

    def _extract_markdown_from_mineru_output(
        self, output_dir: Path, file_stem: str
    ) -> str:
        """从mineru输出中提取Markdown内容"""
        try:
            # 查找生成的Markdown文件
            # mineru通常在子目录中生成文件
            md_files = []

            # 搜索模式：output_dir/file_stem/pipeline/*.md 或 output_dir/file_stem/vlm/*.md
            for subdir in ["pipeline", "vlm"]:
                subdir_path = output_dir / file_stem / subdir
                if subdir_path.exists():
                    md_files.extend(list(subdir_path.glob("*.md")))

            # 如果没找到，直接在输出目录搜索
            if not md_files:
                md_files = list(output_dir.rglob("*.md"))

            if not md_files:
                logger.warning(f"未找到Markdown文件: {output_dir}")
                return ""

            # 选择主要的Markdown文件（通常是与原文件名相同的）
            main_md_file = None
            for md_file in md_files:
                if md_file.stem == file_stem:
                    main_md_file = md_file
                    break

            # 如果没找到同名文件，取第一个
            if not main_md_file:
                main_md_file = md_files[0]

            # 读取内容
            with open(main_md_file, "r", encoding="utf-8") as f:
                content = f.read()

            logger.info(
                f"提取Markdown内容成功: {main_md_file.name} ({len(content)} 字符)"
            )
            return content

        except Exception as e:
            logger.error(f"提取Markdown内容失败: {e}")
            return ""

    def _optimize_markdown_format(self, content: str, doc_name: str) -> str:
        """优化Markdown格式，确保标题层级正确"""
        try:
            logger.info(f"开始优化Markdown格式: {doc_name}")

            # 1. 处理标题层级（基于backup代码的逻辑）
            content = self._process_heading_levels(content)

            # 2. 优化图片表达式
            content = self._process_image_expressions(content)

            # 3. 清理格式
            content = self._clean_markdown_format(content)

            # 4. 优化表格格式
            content = self._optimize_table_format(content)

            # 5. 处理列表格式
            content = self._optimize_list_format(content)

            # 6. 在文档开头添加文档信息
            header = f"# {doc_name} 文档\n\n**文档处理时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n---\n\n"

            logger.info(f"Markdown格式优化完成: {doc_name}")
            return header + content

        except Exception as e:
            logger.error(f"Markdown格式优化失败: {e}")
            return content

    def _segment_by_h2_headers(self, content: str, doc_name: str) -> List[str]:
        """按二级标题分段，并添加标题标识"""
        try:
            lines = content.split("\n")
            segments = []
            current_segment = []
            current_h1 = ""
            current_h2 = ""

            for line in lines:
                line_stripped = line.strip()

                # 检测一级标题
                if line_stripped.startswith("# ") and not line_stripped.startswith(
                    "## "
                ):
                    current_h1 = line_stripped[2:].strip()
                    current_segment.append(line)

                # 检测二级标题 - 分段点
                elif line_stripped.startswith("## "):
                    # 保存当前段落
                    if current_segment:
                        segment_content = "\n".join(current_segment)
                        if segment_content.strip():
                            segments.append(segment_content)
                        current_segment = []

                    # 开始新段落
                    current_h2 = line_stripped[3:].strip()

                    # 构建新段落标题：文档名+一级标题+二级标题
                    segment_title = self._build_segment_title(
                        doc_name, current_h1, current_h2
                    )
                    current_segment.append(f"# {segment_title}")
                    current_segment.append("")
                    current_segment.append(line)  # 原始二级标题

                else:
                    current_segment.append(line)

            # 添加最后一个段落
            if current_segment:
                segment_content = "\n".join(current_segment)
                if segment_content.strip():
                    segments.append(segment_content)

            logger.info(
                f"文档 '{doc_name}' 按二级标题分段完成，共 {len(segments)} 个段落"
            )
            return segments

        except Exception as e:
            logger.error(f"按二级标题分段失败: {e}")
            return [content]

    def _build_segment_title(self, doc_name: str, h1_title: str, h2_title: str) -> str:
        """构建段落标题：文档名+一级标题+二级标题"""
        try:
            title_parts = []

            # 清理文档名
            clean_doc_name = doc_name.replace("_", " ").replace("-", " ")
            title_parts.append(clean_doc_name)

            # 添加一级标题
            if h1_title and h1_title != clean_doc_name:
                title_parts.append(h1_title)

            # 添加二级标题
            if h2_title:
                title_parts.append(h2_title)

            return " - ".join(title_parts)

        except Exception as e:
            logger.error(f"构建段落标题失败: {e}")
            return f"{doc_name} - {h2_title}"

    def _process_image_annotations(
        self, segments: List[str], product_model: str
    ) -> List[str]:
        """处理图片标注，使用AI多模态分析"""
        try:
            if not self.ai_service_url or not self.ai_service_key:
                logger.warning("未配置AI多模态服务，跳过图片标注")
                return segments

            processed_segments = []

            for i, segment in enumerate(segments):
                try:
                    # 查找图片标记
                    if "*[图片说明待AI标注]*" in segment:
                        # 提取图片路径
                        img_matches = re.findall(r"!\[.*?\]\((.*?)\)", segment)

                        for img_path in img_matches:
                            # 调用AI服务标注图片
                            annotation = self._annotate_image_with_ai(
                                img_path, product_model, segment
                            )

                            if annotation:
                                # 替换占位符
                                segment = segment.replace(
                                    "*[图片说明待AI标注]*",
                                    f"**图片说明**: {annotation}",
                                    1,
                                )

                    processed_segments.append(segment)

                except Exception as e:
                    logger.error(f"处理段落 {i} 图片标注失败: {e}")
                    processed_segments.append(segment)

            return processed_segments

        except Exception as e:
            logger.error(f"处理图片标注失败: {e}")
            return segments

    def _annotate_image_with_ai(
        self, img_path: str, product_model: str, context: str
    ) -> str:
        """使用AI多模态服务标注图片"""
        try:
            # 检查图片文件是否存在
            if not os.path.exists(img_path):
                logger.warning(f"图片文件不存在: {img_path}")
                return ""

            # 准备请求数据
            with open(img_path, "rb") as f:
                img_data = f.read()

            # 构建提示词
            prompt = f"""
            请分析这张图片，这是关于产品型号 "{product_model}" 的技术文档图片。

            文档上下文：
            {context[:500]}...

            请提供详细的图片说明，包括：
            1. 图片内容描述
            2. 技术要点说明
            3. 与产品的关联性

            请用中文回答，保持专业和准确。
            """

            # 调用AI服务
            response = requests.post(
                self.ai_service_url,
                headers={
                    "Authorization": f"Bearer {self.ai_service_key}",
                    "Content-Type": "application/json",
                },
                json={
                    "model": "gpt-4-vision-preview",
                    "messages": [
                        {
                            "role": "user",
                            "content": [
                                {"type": "text", "text": prompt},
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": f"data:image/jpeg;base64,{img_data.hex()}"
                                    },
                                },
                            ],
                        }
                    ],
                    "max_tokens": 500,
                },
                timeout=30,
            )

            if response.status_code == 200:
                result = response.json()
                annotation = (
                    result.get("choices", [{}])[0].get("message", {}).get("content", "")
                )
                logger.info(f"AI图片标注成功: {img_path}")
                return annotation
            else:
                logger.error(f"AI服务请求失败: {response.status_code}")
                return ""

        except Exception as e:
            logger.error(f"AI图片标注失败: {e}")
            return ""

    def get_document_types_for_display(
        self, product_model: str
    ) -> List[Dict[str, Any]]:
        """获取产品的文档类型供用户选择展示"""
        try:
            # 搜索产品相关文档
            doc_search_paths = [
                f"documents/{product_model}",
                f"docs/{product_model}",
                f"attachments/products/{product_model}",
                f"attachments/documents/{product_model}",
                "documents/products",
                "docs/products",
                "attachments/products",
                "attachments/documents",
            ]

            document_types = {}

            for search_path in doc_search_paths:
                if os.path.exists(search_path):
                    for root, dirs, files in os.walk(search_path):
                        for file in files:
                            if self._is_document_file(file):
                                file_path = os.path.join(root, file)

                                # 检查文件名是否与产品型号相关
                                if self._is_product_related_document(
                                    file, product_model
                                ):
                                    doc_type = self._classify_document_type(file_path)
                                    priority = self._get_document_priority(doc_type)

                                    if doc_type not in document_types:
                                        document_types[doc_type] = []

                                    document_types[doc_type].append(
                                        {
                                            "file_name": file,
                                            "file_path": file_path,
                                            "file_size": os.path.getsize(file_path),
                                            "file_type": os.path.splitext(file)[
                                                1
                                            ].lower(),
                                            "last_modified": datetime.fromtimestamp(
                                                os.path.getmtime(file_path)
                                            ).isoformat(),
                                            "priority": priority,
                                            "relevance_score": self._calculate_relevance_score(
                                                file, product_model
                                            ),
                                        }
                                    )

            # 转换为列表格式并按优先级排序
            result = []
            for doc_type, files in document_types.items():
                # 按相关性得分排序文件
                files.sort(key=lambda x: x["relevance_score"], reverse=True)

                result.append(
                    {
                        "document_type": doc_type,
                        "file_count": len(files),
                        "files": files,
                        "priority": self._get_document_priority(doc_type),
                    }
                )

            # 按优先级排序文档类型
            result.sort(key=lambda x: x["priority"])

            logger.info(f"产品 '{product_model}' 找到 {len(result)} 种文档类型")
            return result

        except Exception as e:
            logger.error(f"获取产品 '{product_model}' 文档类型失败: {e}")
            return []

    def _is_document_file(self, filename: str) -> bool:
        """判断是否为文档文件"""
        doc_extensions = [".pdf", ".doc", ".docx", ".txt", ".md", ".html"]
        return any(filename.lower().endswith(ext) for ext in doc_extensions)

    def _classify_document_type(self, file_path: str) -> str:
        """分类文档类型"""
        filename = os.path.basename(file_path).lower()

        if any(keyword in filename for keyword in ["安装", "install", "部署", "setup"]):
            return "安装文档"
        elif any(
            keyword in filename for keyword in ["调试", "debug", "配置", "config"]
        ):
            return "调试文档"
        elif any(keyword in filename for keyword in ["操作", "manual", "使用", "user"]):
            return "操作文档"
        elif any(keyword in filename for keyword in ["维护", "maintain", "保养"]):
            return "维护文档"
        elif any(
            keyword in filename for keyword in ["规格", "spec", "参数", "parameter"]
        ):
            return "规格文档"
        elif any(keyword in filename for keyword in ["快速", "quick", "入门", "start"]):
            return "快速入门"
        elif any(keyword in filename for keyword in ["故障", "trouble", "问题", "faq"]):
            return "故障排除"
        elif any(
            keyword in filename for keyword in ["升级", "update", "固件", "firmware"]
        ):
            return "升级文档"
        else:
            return "其他文档"

    def _is_product_related_document(self, filename: str, product_model: str) -> bool:
        """检查文件名是否与产品型号相关"""
        try:
            filename_lower = filename.lower()
            product_model_lower = product_model.lower()

            # 直接匹配产品型号
            if product_model_lower in filename_lower:
                return True

            # 移除特殊字符后匹配
            clean_model = re.sub(r"[^a-zA-Z0-9]", "", product_model_lower)
            clean_filename = re.sub(r"[^a-zA-Z0-9]", "", filename_lower)

            if clean_model in clean_filename:
                return True

            # 提取型号中的数字部分进行匹配
            model_numbers = re.findall(r"\d+", product_model)
            for number in model_numbers:
                if len(number) >= 3 and number in filename_lower:  # 至少3位数字
                    return True

            # 如果没有产品型号信息，则认为是通用文档
            if not product_model or product_model.strip() == "":
                return True

            return False

        except Exception as e:
            logger.error(f"检查文档相关性失败: {e}")
            return True  # 出错时默认包含

    def _calculate_relevance_score(self, filename: str, product_model: str) -> float:
        """计算文档与产品的相关性得分"""
        try:
            score = 0.0
            filename_lower = filename.lower()
            product_model_lower = product_model.lower()

            # 完全匹配产品型号 +10分
            if product_model_lower in filename_lower:
                score += 10.0

            # 部分匹配产品型号 +5分
            clean_model = re.sub(r"[^a-zA-Z0-9]", "", product_model_lower)
            clean_filename = re.sub(r"[^a-zA-Z0-9]", "", filename_lower)
            if clean_model in clean_filename:
                score += 5.0

            # 数字匹配 +3分
            model_numbers = re.findall(r"\d+", product_model)
            for number in model_numbers:
                if len(number) >= 3 and number in filename_lower:
                    score += 3.0

            # 文档类型权重
            doc_type_weights = {
                "操作": 2.0,
                "manual": 2.0,
                "使用": 2.0,
                "安装": 1.5,
                "install": 1.5,
                "规格": 1.5,
                "spec": 1.5,
                "调试": 1.0,
                "config": 1.0,
                "维护": 0.8,
                "maintain": 0.8,
            }

            for keyword, weight in doc_type_weights.items():
                if keyword in filename_lower:
                    score += weight

            # 文件扩展名权重
            if filename_lower.endswith(".pdf"):
                score += 1.0
            elif filename_lower.endswith((".doc", ".docx")):
                score += 0.8
            elif filename_lower.endswith(".md"):
                score += 0.6

            return score

        except Exception as e:
            logger.error(f"计算相关性得分失败: {e}")
            return 0.0

    def _get_document_priority(self, doc_type: str) -> int:
        """获取文档类型的优先级（数字越小优先级越高）"""
        priority_map = {
            "操作文档": 1,
            "快速入门": 2,
            "安装文档": 3,
            "规格文档": 4,
            "调试文档": 5,
            "故障排除": 6,
            "升级文档": 7,
            "维护文档": 8,
            "其他文档": 9,
        }

        return priority_map.get(doc_type, 10)

    def scan_all_product_documents(self) -> Dict[str, List[Dict[str, Any]]]:
        """扫描项目中所有可能的产品文档"""
        try:
            logger.info("开始扫描项目中的所有产品文档")

            # 定义搜索路径
            search_paths = [
                "documents",
                "docs",
                "attachments",
                "files",
                "resources",
                "assets",
                "data",
            ]

            all_documents = {}

            for base_path in search_paths:
                if os.path.exists(base_path):
                    logger.info(f"扫描路径: {base_path}")
                    self._scan_directory_for_documents(base_path, all_documents)

            # 按文档类型分组
            grouped_documents = {}
            for file_path, doc_info in all_documents.items():
                doc_type = doc_info["document_type"]
                if doc_type not in grouped_documents:
                    grouped_documents[doc_type] = []
                grouped_documents[doc_type].append(doc_info)

            # 按优先级排序
            for doc_type in grouped_documents:
                grouped_documents[doc_type].sort(
                    key=lambda x: x["relevance_score"], reverse=True
                )

            logger.info(
                f"扫描完成，找到 {len(all_documents)} 个文档，{len(grouped_documents)} 种类型"
            )
            return grouped_documents

        except Exception as e:
            logger.error(f"扫描产品文档失败: {e}")
            return {}

    def _scan_directory_for_documents(
        self, directory: str, documents_dict: Dict[str, Dict[str, Any]]
    ):
        """递归扫描目录中的文档"""
        try:
            for root, dirs, files in os.walk(directory):
                # 跳过虚拟环境目录
                if "venv" in root or "__pycache__" in root or ".git" in root:
                    continue

                for file in files:
                    if self._is_document_file(file):
                        file_path = os.path.join(root, file)

                        # 尝试从路径推断产品型号
                        inferred_model = self._infer_product_model_from_path(file_path)

                        doc_info = {
                            "file_name": file,
                            "file_path": file_path,
                            "file_size": os.path.getsize(file_path),
                            "file_type": os.path.splitext(file)[1].lower(),
                            "document_type": self._classify_document_type(file_path),
                            "inferred_product_model": inferred_model,
                            "last_modified": datetime.fromtimestamp(
                                os.path.getmtime(file_path)
                            ).isoformat(),
                            "relevance_score": self._calculate_general_relevance_score(
                                file
                            ),
                            "directory": os.path.dirname(file_path),
                        }

                        documents_dict[file_path] = doc_info

        except Exception as e:
            logger.error(f"扫描目录 '{directory}' 失败: {e}")

    def _infer_product_model_from_path(self, file_path: str) -> str:
        """从文件路径推断产品型号"""
        try:
            # 从文件名中提取可能的型号
            filename = os.path.basename(file_path)

            # 常见的产品型号模式
            model_patterns = [
                r"[A-Z]{2,4}[-_]?\d{3,6}[A-Z]?",  # 如 ZK-3969, MB460
                r"\d{4,6}[A-Z]?",  # 如 3969A, 460
                r"[A-Z]+\d+[A-Z]*",  # 如 MB460, ZK3969
            ]

            for pattern in model_patterns:
                matches = re.findall(pattern, filename.upper())
                if matches:
                    return matches[0]

            # 从目录名中提取
            path_parts = file_path.split(os.sep)
            for part in path_parts:
                for pattern in model_patterns:
                    matches = re.findall(pattern, part.upper())
                    if matches:
                        return matches[0]

            return ""

        except Exception as e:
            logger.error(f"推断产品型号失败: {e}")
            return ""

    def _calculate_general_relevance_score(self, filename: str) -> float:
        """计算文档的通用相关性得分"""
        try:
            score = 0.0
            filename_lower = filename.lower()

            # 文档类型权重
            type_weights = {
                "manual": 5.0,
                "操作": 5.0,
                "使用": 5.0,
                "install": 4.0,
                "安装": 4.0,
                "部署": 4.0,
                "spec": 3.5,
                "规格": 3.5,
                "参数": 3.5,
                "config": 3.0,
                "配置": 3.0,
                "调试": 3.0,
                "quick": 2.5,
                "快速": 2.5,
                "入门": 2.5,
                "trouble": 2.0,
                "故障": 2.0,
                "问题": 2.0,
                "maintain": 1.5,
                "维护": 1.5,
                "保养": 1.5,
            }

            for keyword, weight in type_weights.items():
                if keyword in filename_lower:
                    score += weight

            # 文件格式权重
            if filename_lower.endswith(".pdf"):
                score += 2.0
            elif filename_lower.endswith((".doc", ".docx")):
                score += 1.5
            elif filename_lower.endswith(".md"):
                score += 1.0

            # 文件名长度权重（适中的长度通常更有意义）
            name_length = len(os.path.splitext(filename)[0])
            if 10 <= name_length <= 50:
                score += 1.0
            elif name_length > 50:
                score -= 0.5

            return score

        except Exception as e:
            logger.error(f"计算通用相关性得分失败: {e}")
            return 0.0

    def get_documents_by_product_model(
        self, product_model: str
    ) -> List[Dict[str, Any]]:
        """根据产品型号获取相关文档"""
        try:
            all_docs = self.scan_all_product_documents()
            related_docs = []

            for doc_type, docs in all_docs.items():
                for doc in docs:
                    if self._is_product_related_document(
                        doc["file_name"], product_model
                    ):
                        doc["relevance_score"] = self._calculate_relevance_score(
                            doc["file_name"], product_model
                        )
                        related_docs.append(doc)

            # 按相关性得分排序
            related_docs.sort(key=lambda x: x["relevance_score"], reverse=True)

            logger.info(f"为产品 '{product_model}' 找到 {len(related_docs)} 个相关文档")
            return related_docs

        except Exception as e:
            logger.error(f"获取产品 '{product_model}' 相关文档失败: {e}")
            return []

    def batch_convert_pdfs_with_mineru(
        self, pdf_paths: List[str], output_base_dir: Optional[str] = None
    ) -> Dict[str, Any]:
        """批量使用mineru转换PDF文档"""
        try:
            logger.info(f"开始批量转换 {len(pdf_paths)} 个PDF文档")

            if output_base_dir is None:
                output_base_dir = str(self.mineru_output_dir)

            results = {
                "total": len(pdf_paths),
                "success": 0,
                "failed": 0,
                "details": [],
                "errors": [],
            }

            # 检查mineru可用性
            if not self._check_mineru_availability():
                error_msg = "mineru不可用，无法进行批量转换"
                logger.error(error_msg)
                results["errors"].append(error_msg)
                return results

            # 批量处理
            for pdf_path in pdf_paths:
                try:
                    pdf_file = Path(pdf_path)
                    if not pdf_file.exists():
                        error_msg = f"PDF文件不存在: {pdf_path}"
                        logger.warning(error_msg)
                        results["failed"] += 1
                        results["errors"].append(error_msg)
                        continue

                    # 转换单个PDF
                    markdown_content = self._convert_pdf_with_mineru(pdf_file)

                    if markdown_content:
                        # 保存转换结果
                        output_file = (
                            Path(output_base_dir) / f"{pdf_file.stem}_converted.md"
                        )
                        output_file.parent.mkdir(parents=True, exist_ok=True)

                        with open(output_file, "w", encoding="utf-8") as f:
                            f.write(markdown_content)

                        results["success"] += 1
                        results["details"].append(
                            {
                                "pdf_path": pdf_path,
                                "output_path": str(output_file),
                                "status": "success",
                                "content_length": len(markdown_content),
                            }
                        )

                        logger.info(f"PDF转换成功: {pdf_file.name}")
                    else:
                        results["failed"] += 1
                        error_msg = f"PDF转换失败: {pdf_file.name}"
                        results["errors"].append(error_msg)
                        logger.error(error_msg)

                except Exception as e:
                    results["failed"] += 1
                    error_msg = f"处理PDF文件 '{pdf_path}' 时出错: {str(e)}"
                    results["errors"].append(error_msg)
                    logger.error(error_msg)

            logger.info(
                f"批量转换完成: 成功 {results['success']}, 失败 {results['failed']}"
            )
            return results

        except Exception as e:
            logger.error(f"批量转换PDF失败: {e}")
            return {"error": str(e)}

    def process_documents_with_mineru_integration(
        self, product_model: str, document_paths: List[str]
    ) -> Dict[str, Any]:
        """集成mineru的完整文档处理流程"""
        try:
            logger.info(
                f"开始为产品 '{product_model}' 处理 {len(document_paths)} 个文档"
            )

            results = {
                "product_model": product_model,
                "total_documents": len(document_paths),
                "processed": 0,
                "success": 0,
                "failed": 0,
                "pdf_converted": 0,
                "word_converted": 0,
                "text_processed": 0,
                "details": [],
                "errors": [],
            }

            for doc_path in document_paths:
                try:
                    doc_file = Path(doc_path)
                    if not doc_file.exists():
                        results["failed"] += 1
                        results["errors"].append(f"文档不存在: {doc_path}")
                        continue

                    results["processed"] += 1
                    file_extension = doc_file.suffix.lower()

                    # 根据文件类型处理
                    if file_extension == ".pdf":
                        result = self._process_pdf_with_mineru_integration(
                            product_model, doc_file
                        )
                        if result.get("status") == "success":
                            results["pdf_converted"] += 1
                            results["success"] += 1
                        else:
                            results["failed"] += 1

                    elif file_extension in [".doc", ".docx"]:
                        result = self._process_word_with_mineru_integration(
                            product_model, doc_file
                        )
                        if result.get("status") == "success":
                            results["word_converted"] += 1
                            results["success"] += 1
                        else:
                            results["failed"] += 1

                    elif file_extension in [".txt", ".md"]:
                        result = self._process_text_document(product_model, doc_file)
                        if result.get("status") == "success":
                            results["text_processed"] += 1
                            results["success"] += 1
                        else:
                            results["failed"] += 1
                    else:
                        results["failed"] += 1
                        result = {
                            "status": "error",
                            "error": f"不支持的文件类型: {file_extension}",
                        }

                    results["details"].append(result)

                except Exception as e:
                    results["failed"] += 1
                    error_msg = f"处理文档 '{doc_path}' 失败: {str(e)}"
                    results["errors"].append(error_msg)
                    logger.error(error_msg)

            logger.info(
                f"产品 '{product_model}' 文档处理完成: 成功 {results['success']}, 失败 {results['failed']}"
            )
            return results

        except Exception as e:
            logger.error(f"集成文档处理失败: {e}")
            return {"error": str(e)}

    def _process_pdf_with_mineru_integration(
        self, product_model: str, pdf_path: Path
    ) -> Dict[str, Any]:
        """使用mineru集成处理PDF文档"""
        try:
            # 使用mineru转换
            markdown_content = self._convert_pdf_with_mineru(pdf_path)

            if not markdown_content:
                return {
                    "file_path": str(pdf_path),
                    "status": "error",
                    "error": "mineru转换失败",
                }

            # 优化格式
            optimized_content = self._optimize_markdown_format(
                markdown_content, pdf_path.stem
            )

            # 分段处理
            segments = self._segment_by_h2_headers(optimized_content, pdf_path.stem)

            # 保存结果
            output_file = self.output_dir / f"{product_model}_{pdf_path.stem}_mineru.md"
            with open(output_file, "w", encoding="utf-8") as f:
                f.write("\n\n".join(segments))

            return {
                "file_path": str(pdf_path),
                "output_path": str(output_file),
                "status": "success",
                "conversion_method": "mineru",
                "segments_count": len(segments),
                "content_length": len(optimized_content),
            }

        except Exception as e:
            logger.error(f"mineru集成处理PDF失败: {e}")
            return {"file_path": str(pdf_path), "status": "error", "error": str(e)}

    def _process_word_with_mineru_integration(
        self, product_model: str, word_path: Path
    ) -> Dict[str, Any]:
        """使用mineru集成处理Word文档"""
        try:
            # 先转换为PDF
            pdf_path = self._convert_word_to_pdf(word_path)

            if not pdf_path:
                return {
                    "file_path": str(word_path),
                    "status": "error",
                    "error": "Word转PDF失败",
                }

            # 然后使用mineru处理PDF
            result = self._process_pdf_with_mineru_integration(product_model, pdf_path)

            # 更新结果信息
            result["original_file_path"] = str(word_path)
            result["converted_pdf_path"] = str(pdf_path)
            result["conversion_method"] = "word_to_pdf_to_mineru"

            return result

        except Exception as e:
            logger.error(f"Word文档mineru集成处理失败: {e}")
            return {"file_path": str(word_path), "status": "error", "error": str(e)}

    def process_word_document_complete_flow(
        self, word_path: str, product_model: str = "", output_dir: str = ""
    ) -> Dict[str, Any]:
        """
        完整的Word文档处理流程

        流程：Word → PDF → mineru转换 → 格式优化 → 分段处理 → AI标注

        Args:
            word_path: Word文档路径
            product_model: 产品型号
            output_dir: 输出目录

        Returns:
            处理结果详情
        """
        try:
            logger.info(f"开始完整Word文档处理流程: {word_path}")

            word_file = Path(word_path)
            if not word_file.exists():
                return {"status": "error", "error": f"Word文档不存在: {word_path}"}

            # 设置输出目录
            if not output_dir:
                output_dir = str(self.output_dir)

            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)

            result = {
                "original_file": str(word_file),
                "product_model": product_model,
                "status": "processing",
                "steps": [],
                "outputs": {},
                "errors": [],
            }

            # 步骤1: Word转PDF
            logger.info("步骤1: Word转PDF")
            pdf_path = self._convert_word_to_pdf(word_file)

            if not pdf_path:
                result["status"] = "error"
                result["errors"].append("Word转PDF失败")
                return result

            result["steps"].append("word_to_pdf")
            result["outputs"]["pdf_file"] = str(pdf_path)
            logger.info(f"Word转PDF成功: {pdf_path}")

            # 步骤2: PDF转Markdown (使用mineru)
            logger.info("步骤2: PDF转Markdown (mineru)")
            markdown_content = self._convert_pdf_with_mineru(pdf_path)

            if not markdown_content:
                result["status"] = "error"
                result["errors"].append("PDF转Markdown失败")
                return result

            result["steps"].append("pdf_to_markdown")
            result["outputs"]["raw_markdown_length"] = len(markdown_content)
            logger.info(f"PDF转Markdown成功: {len(markdown_content)} 字符")

            # 步骤3: 格式优化
            logger.info("步骤3: Markdown格式优化")
            optimized_content = self._optimize_markdown_format(
                markdown_content, word_file.stem
            )

            result["steps"].append("format_optimization")
            result["outputs"]["optimized_markdown_length"] = len(optimized_content)

            # 步骤4: 分段处理
            logger.info("步骤4: 按二级标题分段")
            segments = self._segment_by_h2_headers(optimized_content, word_file.stem)

            result["steps"].append("segmentation")
            result["outputs"]["segments_count"] = len(segments)

            # 步骤5: AI图片标注（如果有AI服务配置）
            logger.info("步骤5: AI图片标注")
            if (
                hasattr(self, "ai_annotation_service")
                and self.ai_annotation_service is not None
            ):
                try:
                    # 查找文档中的图片
                    image_paths = self._extract_image_paths_from_content(
                        optimized_content
                    )

                    if image_paths:
                        annotated_segments = self._process_segments_with_ai_annotation(
                            segments, image_paths, product_model
                        )
                        segments = annotated_segments
                        result["steps"].append("ai_annotation")
                        result["outputs"]["annotated_images"] = len(image_paths)
                    else:
                        result["outputs"]["annotated_images"] = 0

                except Exception as e:
                    logger.warning(f"AI图片标注失败: {e}")
                    result["errors"].append(f"AI图片标注失败: {str(e)}")
            else:
                result["outputs"]["annotated_images"] = 0

            # 步骤6: 保存结果
            logger.info("步骤6: 保存处理结果")

            # 保存完整的优化后内容
            full_output_file = output_path / f"{word_file.stem}_processed.md"
            with open(full_output_file, "w", encoding="utf-8") as f:
                f.write(optimized_content)

            result["outputs"]["full_markdown_file"] = str(full_output_file)

            # 保存分段内容
            segments_dir = output_path / f"{word_file.stem}_segments"
            segments_dir.mkdir(exist_ok=True)

            segment_files = []
            for i, segment in enumerate(segments):
                segment_file = segments_dir / f"segment_{i+1:03d}.md"
                with open(segment_file, "w", encoding="utf-8") as f:
                    f.write(segment)
                segment_files.append(str(segment_file))

            result["outputs"]["segment_files"] = segment_files
            result["outputs"]["segments_directory"] = str(segments_dir)

            # 生成处理报告
            report = self._generate_processing_report(result, word_file.stem)
            report_file = output_path / f"{word_file.stem}_report.md"
            with open(report_file, "w", encoding="utf-8") as f:
                f.write(report)

            result["outputs"]["report_file"] = str(report_file)
            result["steps"].append("save_results")
            result["status"] = "success"

            logger.info(f"Word文档完整处理流程完成: {word_path}")
            return result

        except Exception as e:
            logger.error(f"Word文档完整处理流程失败: {e}")
            return {"status": "error", "error": str(e), "original_file": word_path}

    def _extract_image_paths_from_content(self, content: str) -> List[str]:
        """从Markdown内容中提取图片路径"""
        try:
            # 匹配Markdown图片语法: ![alt](path)
            image_pattern = r"!\[.*?\]\(([^)]+)\)"
            matches = re.findall(image_pattern, content)

            # 过滤出实际存在的图片文件
            existing_images = []
            for img_path in matches:
                # 处理相对路径
                if not os.path.isabs(img_path):
                    # 尝试在常见目录中查找
                    search_dirs = ["images", "attachments", "assets", "."]
                    for search_dir in search_dirs:
                        full_path = os.path.join(search_dir, img_path)
                        if os.path.exists(full_path):
                            existing_images.append(full_path)
                            break
                elif os.path.exists(img_path):
                    existing_images.append(img_path)

            logger.info(f"从内容中提取到 {len(existing_images)} 个有效图片路径")
            return existing_images

        except Exception as e:
            logger.error(f"提取图片路径失败: {e}")
            return []

    def _process_segments_with_ai_annotation(
        self, segments: List[str], image_paths: List[str], product_model: str
    ) -> List[str]:
        """使用AI标注处理分段内容"""
        try:
            if (
                not hasattr(self, "ai_annotation_service")
                or self.ai_annotation_service is None
            ):
                return segments

            processed_segments = []

            for segment in segments:
                # 查找段落中的图片
                segment_images = []
                for img_path in image_paths:
                    img_name = os.path.basename(img_path)
                    if img_name in segment or img_path in segment:
                        segment_images.append(img_path)

                # 如果段落包含图片，进行AI标注
                if segment_images:
                    for img_path in segment_images:
                        try:
                            success, annotation, _ = (
                                self.ai_annotation_service.annotate_image(
                                    image_path=img_path,
                                    product_model=product_model,
                                    context=segment[:500],  # 提供上下文
                                    annotation_type="technical",
                                )
                            )

                            if success and annotation:
                                # 在图片后添加AI标注
                                img_name = os.path.basename(img_path)
                                img_pattern = (
                                    rf"!\[.*?\]\([^)]*{re.escape(img_name)}[^)]*\)"
                                )

                                def add_annotation(match):
                                    return (
                                        match.group(0)
                                        + f"\n\n**AI图片分析**: {annotation}\n"
                                    )

                                segment = re.sub(img_pattern, add_annotation, segment)

                        except Exception as e:
                            logger.error(f"处理图片 {img_path} 的AI标注失败: {e}")

                processed_segments.append(segment)

            logger.info(f"AI标注处理完成，处理了 {len(image_paths)} 张图片")
            return processed_segments

        except Exception as e:
            logger.error(f"AI标注处理失败: {e}")
            return segments

    def _generate_processing_report(self, result: Dict[str, Any], doc_name: str) -> str:
        """生成处理报告"""
        try:
            report_lines = [
                f"# {doc_name} 文档处理报告",
                "",
                f"**处理时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
                f"**原始文件**: {result.get('original_file', 'N/A')}",
                f"**产品型号**: {result.get('product_model', 'N/A')}",
                f"**处理状态**: {result.get('status', 'N/A')}",
                "",
                "## 处理步骤",
                "",
            ]

            steps = result.get("steps", [])
            step_names = {
                "word_to_pdf": "Word转PDF",
                "pdf_to_markdown": "PDF转Markdown (mineru)",
                "format_optimization": "Markdown格式优化",
                "segmentation": "按二级标题分段",
                "ai_annotation": "AI图片标注",
                "save_results": "保存结果",
            }

            for i, step in enumerate(steps, 1):
                step_name = step_names.get(step, step)
                report_lines.append(f"{i}. ✅ {step_name}")

            report_lines.extend(["", "## 输出结果", ""])

            outputs = result.get("outputs", {})
            if "pdf_file" in outputs:
                report_lines.append(f"- **PDF文件**: {outputs['pdf_file']}")

            if "raw_markdown_length" in outputs:
                report_lines.append(
                    f"- **原始Markdown长度**: {outputs['raw_markdown_length']} 字符"
                )

            if "optimized_markdown_length" in outputs:
                report_lines.append(
                    f"- **优化后Markdown长度**: {outputs['optimized_markdown_length']} 字符"
                )

            if "segments_count" in outputs:
                report_lines.append(f"- **分段数量**: {outputs['segments_count']} 个")

            if "annotated_images" in outputs:
                report_lines.append(
                    f"- **AI标注图片**: {outputs['annotated_images']} 张"
                )

            if "full_markdown_file" in outputs:
                report_lines.append(
                    f"- **完整Markdown文件**: {outputs['full_markdown_file']}"
                )

            if "segments_directory" in outputs:
                report_lines.append(
                    f"- **分段文件目录**: {outputs['segments_directory']}"
                )

            # 添加错误信息
            errors = result.get("errors", [])
            if errors:
                report_lines.extend(["", "## 错误信息", ""])
                for error in errors:
                    report_lines.append(f"- ⚠️ {error}")

            report_lines.extend(["", "---", "*此报告由文档处理系统自动生成*"])

            return "\n".join(report_lines)

        except Exception as e:
            logger.error(f"生成处理报告失败: {e}")
            return f"# 处理报告生成失败\n\n错误: {str(e)}"

    def _process_heading_levels(self, content: str) -> str:
        """处理标题级别，基于backup代码的逻辑"""
        try:
            lines = content.split("\n")
            processed_lines = []

            for line in lines:
                # 检查是否为一级标题
                if line.startswith("# "):
                    heading_text = line[2:].strip()

                    # 计算数字点的层级数量
                    dot_match = re.match(r"^(\d+(?:\.\d+)*)", heading_text)
                    if dot_match:
                        dot_sequence = dot_match.group(1)
                        dot_count = dot_sequence.count(".")

                        if dot_count >= 3:
                            # 四级及以上：# *******文字 -> #### *******文字
                            processed_lines.append(f"#### {heading_text}")
                        elif dot_count == 2:
                            # 三级：# 2.1.1文字 -> ### 2.1.1文字
                            processed_lines.append(f"### {heading_text}")
                        elif dot_count == 1:
                            # 二级：# 2.1文字 -> ## 2.1文字
                            processed_lines.append(f"## {heading_text}")
                        else:
                            # 一级：# 2 文字 -> 保留一级标题（只有数字，后面跟空格）
                            if re.match(r"^\d+\s", heading_text):
                                processed_lines.append(line)
                            else:
                                # 单独的数字但后面没有空格，保留一级标题
                                processed_lines.append(line)
                    else:
                        # 其他格式的一级标题保留
                        processed_lines.append(line)
                else:
                    processed_lines.append(line)

            return "\n".join(processed_lines)

        except Exception as e:
            logger.error(f"处理标题层级失败: {e}")
            return content

    def _process_image_expressions(self, content: str) -> str:
        """优化图片表达式，从 ![](images/xxx.jpg)\n图 x-x xxx 格式改为 ![图 x-x xxx](images/xxx.jpg)"""
        try:
            # 匹配模式：![](images/文件名.jpg) 后跟可能的空行和图片描述
            pattern = r"!\[\]\((images/[^)]+\.(?:jpg|jpeg|png|gif|bmp|webp))\)[ \t]*\r?\n(图\s*\d+(?:[-–]\d+)?\s+[^\r\n]*)"

            def replace_image(match):
                image_path = match.group(1)
                caption = match.group(2).strip()
                return f"![{caption}]({image_path})"

            # 执行替换
            processed_content = re.sub(
                pattern, replace_image, content, flags=re.MULTILINE
            )

            return processed_content

        except Exception as e:
            logger.error(f"处理图片表达式失败: {e}")
            return content

    def _clean_markdown_format(self, content: str) -> str:
        """清理Markdown格式"""
        try:
            # 清理多余的空行
            content = re.sub(r"\n{3,}", "\n\n", content)

            # 确保标题前后有适当的空行
            content = re.sub(r"(\n)(#{1,6}\s+[^\n]+)(\n)", r"\1\n\2\n\3", content)

            # 清理行尾空格
            lines = content.split("\n")
            cleaned_lines = [line.rstrip() for line in lines]
            content = "\n".join(cleaned_lines)

            # 确保文档以换行符结尾
            if not content.endswith("\n"):
                content += "\n"

            return content

        except Exception as e:
            logger.error(f"清理Markdown格式失败: {e}")
            return content

    def _optimize_table_format(self, content: str) -> str:
        """优化表格格式"""
        try:
            lines = content.split("\n")
            optimized_lines = []
            in_table = False

            for line in lines:
                # 检测表格行
                if "|" in line and line.strip():
                    # 简单的表格格式化
                    cells = [cell.strip() for cell in line.split("|")]
                    if len(cells) >= 3:  # 至少有开始、内容、结束的|
                        formatted_line = "| " + " | ".join(cells[1:-1]) + " |"
                        optimized_lines.append(formatted_line)
                        in_table = True
                    else:
                        optimized_lines.append(line)
                else:
                    if in_table:
                        # 表格结束，添加空行
                        optimized_lines.append("")
                        in_table = False
                    optimized_lines.append(line)

            return "\n".join(optimized_lines)

        except Exception as e:
            logger.error(f"优化表格格式失败: {e}")
            return content

    def _optimize_list_format(self, content: str) -> str:
        """优化列表格式"""
        try:
            lines = content.split("\n")
            optimized_lines = []

            for line in lines:
                stripped = line.strip()

                # 处理无序列表
                if stripped.startswith("*") or stripped.startswith("-"):
                    # 确保列表项格式正确
                    if not stripped.startswith("* ") and not stripped.startswith("- "):
                        if stripped.startswith("*"):
                            line = line.replace("*", "* ", 1)
                        elif stripped.startswith("-"):
                            line = line.replace("-", "- ", 1)

                # 处理有序列表
                elif re.match(r"^\d+\.", stripped):
                    # 确保有序列表格式正确
                    if not re.match(r"^\d+\.\s", stripped):
                        line = re.sub(r"^(\d+\.)([^\s])", r"\1 \2", line)

                optimized_lines.append(line)

            return "\n".join(optimized_lines)

        except Exception as e:
            logger.error(f"优化列表格式失败: {e}")
            return content
