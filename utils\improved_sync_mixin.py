
# 改进的同步服务补丁
# 解决数据提取问题

from utils.data_cleaner import clean_product_data, clean_integer_field, clean_text_field
import logging

logger = logging.getLogger(__name__)

class ImprovedSyncMixin:
    """改进的同步混合类"""
    
    def _safe_extract_data(self, api_response, data_type="unknown"):
        """
        安全地提取API响应数据
        """
        try:
            if not api_response:
                logger.warning(f"API响应为空: {data_type}")
                return []
            
            # 处理不同的响应格式
            if isinstance(api_response, list):
                return api_response
            
            if isinstance(api_response, dict):
                # 尝试不同的数据字段
                for field in ['data', 'rows', 'list', 'items', 'content']:
                    if field in api_response:
                        data = api_response[field]
                        if isinstance(data, list):
                            return data
                        elif isinstance(data, dict) and 'rows' in data:
                            return data['rows'] or []
                
                # 如果没有找到列表数据，返回单个对象
                return [api_response] if api_response else []
            
            logger.warning(f"未知的响应格式: {type(api_response)}")
            return []
            
        except Exception as e:
            logger.error(f"提取数据失败 ({data_type}): {e}")
            return []
    
    def _process_product_batch(self, products):
        """
        批量处理产品数据
        """
        processed_products = []
        
        for product in products:
            try:
                # 使用数据清洗函数
                cleaned_product = clean_product_data(product)
                
                if cleaned_product.get('id'):
                    processed_products.append(cleaned_product)
                else:
                    logger.warning(f"产品数据缺少ID，跳过: {product}")
                    
            except Exception as e:
                logger.error(f"处理产品数据失败: {e}")
                continue
        
        return processed_products
    
    def _extract_brands_from_products(self, products):
        """
        从产品数据中提取品牌信息
        """
        brands = {}
        
        for product in products:
            brand_id = product.get('brandId')
            brand_name = product.get('brandName')
            
            if brand_id and brand_id not in brands:
                brands[brand_id] = {
                    'id': brand_id,
                    'name': brand_name or f'品牌_{brand_id}',
                    'status': '1',
                    'site_id': product.get('siteId', 999),
                    'create_time': product.get('createTime'),
                    'update_time': product.get('updateTime')
                }
        
        return list(brands.values())
    
    def _extract_categories_from_products(self, products):
        """
        从产品数据中提取分类信息
        """
        categories = {}
        
        for product in products:
            category_id = product.get('categoryId')
            category_name = product.get('categoryName')
            
            if category_id:
                # 处理逗号分隔的分类ID
                if isinstance(category_id, str) and ',' in category_id:
                    category_ids = category_id.split(',')
                    category_names = category_name.split(',') if category_name else []
                    
                    for i, cat_id in enumerate(category_ids):
                        cat_id = cat_id.strip()
                        cat_name = category_names[i].strip() if i < len(category_names) else f'分类_{cat_id}'
                        
                        if cat_id and cat_id not in categories:
                            categories[cat_id] = {
                                'id': cat_id,
                                'name': cat_name,
                                'status': '1',
                                'site_id': product.get('siteId', 999)
                            }
                else:
                    if category_id not in categories:
                        categories[category_id] = {
                            'id': category_id,
                            'name': category_name or f'分类_{category_id}',
                            'status': '1',
                            'site_id': product.get('siteId', 999)
                        }
        
        return list(categories.values())

# 使用说明：
# 在SyncService中混入这个类：
# class SyncService(ImprovedSyncMixin):
#     ...
