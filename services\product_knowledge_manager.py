#!/usr/bin/env python3
"""
产品知识库管理服务

该服务负责：
1. 根据产品型号和类别管理知识库
2. 整理产品介绍、特性、文档等信息
3. 生成优化的知识块，包含索引、摘要和标签
4. 与FastGPT知识库集成

严格遵循开发规范，使用真实API接口，禁止模拟数据。
"""

import json
import logging
import os
import re
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any

from config import config
from utils.database import DatabaseManager
from services.fastgpt_knowledge_service import FastGPTKnowledgeService
from services.document_processor import DocumentProcessor
from utils.api_client import ZKMallClient

logger = logging.getLogger(__name__)


class ProductKnowledgeManager:
    """产品知识库管理器"""

    def __init__(self):
        """初始化管理器"""
        self.db_manager = DatabaseManager()
        self.fastgpt_service = FastGPTKnowledgeService()
        self.doc_processor = DocumentProcessor()
        self.api_client = ZKMallClient()

        # 产品类别与知识库ID的映射配置
        self.category_knowledge_mapping = self._load_category_mapping()

        logger.info("产品知识库管理器初始化成功")

    def _load_category_mapping(self) -> Dict[str, str]:
        """加载产品类别与知识库ID的映射配置"""
        try:
            # 从环境变量或配置文件加载映射关系
            mapping_config = {
                # 门禁类产品
                "门禁": os.getenv("FASTGPT_ACCESS_CONTROL_DATASET_ID", ""),
                "考勤": os.getenv("FASTGPT_ATTENDANCE_DATASET_ID", ""),
                "人脸识别": os.getenv("FASTGPT_FACE_RECOGNITION_DATASET_ID", ""),
                # 安检类产品
                "安检": os.getenv("FASTGPT_SECURITY_CHECK_DATASET_ID", ""),
                "X光机": os.getenv("FASTGPT_XRAY_DATASET_ID", ""),
                "金属探测": os.getenv("FASTGPT_METAL_DETECTOR_DATASET_ID", ""),
                # 智能硬件
                "智能锁": os.getenv("FASTGPT_SMART_LOCK_DATASET_ID", ""),
                "摄像头": os.getenv("FASTGPT_CAMERA_DATASET_ID", ""),
                "传感器": os.getenv("FASTGPT_SENSOR_DATASET_ID", ""),
                # 默认分类
                "default": os.getenv("FASTGPT_DEFAULT_PRODUCT_DATASET_ID", ""),
            }

            # 过滤掉空值
            valid_mapping = {k: v for k, v in mapping_config.items() if v}

            if not valid_mapping:
                logger.warning("未配置产品类别知识库映射，将使用默认知识库")

            return valid_mapping

        except Exception as e:
            logger.error(f"加载产品类别映射配置失败: {e}")
            return {}

    def get_dataset_id_by_category(self, category_name: str) -> str:
        """根据产品类别获取对应的知识库ID"""
        try:
            # 直接匹配
            if category_name in self.category_knowledge_mapping:
                return self.category_knowledge_mapping[category_name]

            # 模糊匹配
            for key, dataset_id in self.category_knowledge_mapping.items():
                if key in category_name or category_name in key:
                    logger.info(
                        f"模糊匹配类别 '{category_name}' -> '{key}' -> {dataset_id}"
                    )
                    return dataset_id

            # 使用默认知识库
            default_id = self.category_knowledge_mapping.get("default", "")
            if default_id:
                logger.info(f"使用默认知识库处理类别: {category_name}")
                return default_id

            logger.warning(f"未找到类别 '{category_name}' 对应的知识库ID")
            return ""

        except Exception as e:
            logger.error(f"获取类别 '{category_name}' 知识库ID失败: {e}")
            return ""

    def create_product_knowledge_block(
        self, product_model: str, product_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        为产品型号创建知识块

        Args:
            product_model: 产品型号
            product_data: 产品完整数据

        Returns:
            知识块数据
        """
        try:
            logger.info(f"开始为产品型号 '{product_model}' 创建知识块")

            # 1. 提取产品基本信息
            basic_info = self._extract_product_basic_info(product_data)

            # 2. 生成产品介绍和特性
            introduction = self._generate_product_introduction(product_data)

            # 3. 收集相关文档
            documents = self._collect_product_documents(product_model)

            # 4. 生成索引和标签
            indexes, tags = self._generate_indexes_and_tags(product_data, documents)

            # 5. 生成摘要
            summary = self._generate_product_summary(product_data, introduction)

            # 6. 构建完整的知识块
            knowledge_block = {
                "product_model": product_model,
                "title": f"{basic_info.get('name', product_model)} 产品知识库",
                "summary": summary,
                "introduction": introduction,
                "basic_info": basic_info,
                "documents": documents,
                "indexes": indexes,
                "tags": tags,
                "created_at": datetime.now().isoformat(),
                "category": basic_info.get("category_name", ""),
                "dataset_id": self.get_dataset_id_by_category(
                    basic_info.get("category_name", "")
                ),
            }

            logger.info(f"产品型号 '{product_model}' 知识块创建成功")
            return knowledge_block

        except Exception as e:
            logger.error(f"创建产品型号 '{product_model}' 知识块失败: {e}")
            return {}

    def _extract_product_basic_info(
        self, product_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """提取产品基本信息"""
        try:
            basic_info = {
                "id": product_data.get("id"),
                "name": product_data.get("name", ""),
                "category_name": product_data.get("categoryName", ""),
                "brand": product_data.get("brand", ""),
                "model": product_data.get("model", ""),
                "status": product_data.get("status", ""),
                "price": product_data.get("price", 0),
                "description": product_data.get("description", ""),
                "specifications": product_data.get("specifications", ""),
            }

            return basic_info

        except Exception as e:
            logger.error(f"提取产品基本信息失败: {e}")
            return {}

    def _generate_product_introduction(self, product_data: Dict[str, Any]) -> str:
        """生成产品介绍和特性描述"""
        try:
            sections = []

            # 产品概述
            name = product_data.get("name", "")
            if name:
                sections.append(
                    f"## 产品概述\n\n{name}是一款专业的{product_data.get('categoryName', '')}产品。"
                )

            # 产品介绍
            introduction = product_data.get("introduction", "")
            if introduction:
                sections.append(f"## 产品介绍\n\n{introduction}")

            # 产品详情
            details = product_data.get("details", "")
            if details:
                # 清理HTML标签
                clean_details = re.sub(r"<[^>]+>", "", details)
                sections.append(f"## 产品详情\n\n{clean_details}")

            # 技术参数
            param_info = product_data.get("parameterInfo", "") or product_data.get(
                "newParam", ""
            )
            if param_info:
                if isinstance(param_info, str) and param_info.startswith("{"):
                    try:
                        param_data = json.loads(param_info)
                        param_text = self._format_parameters(param_data)
                        sections.append(f"## 技术参数\n\n{param_text}")
                    except:
                        clean_param = re.sub(r"<[^>]+>", "", param_info)
                        sections.append(f"## 技术参数\n\n{clean_param}")
                else:
                    clean_param = re.sub(r"<[^>]+>", "", str(param_info))
                    sections.append(f"## 技术参数\n\n{clean_param}")

            # 适用场景
            usage = product_data.get("usage", "") or product_data.get("useTo", "")
            if usage:
                sections.append(f"## 适用场景\n\n{usage}")

            # 产品特性
            features = self._extract_product_features(product_data)
            if features:
                sections.append(f"## 产品特性\n\n{features}")

            return "\n\n".join(sections)

        except Exception as e:
            logger.error(f"生成产品介绍失败: {e}")
            return ""

    def _format_parameters(self, param_data: Any) -> str:
        """格式化技术参数"""
        try:
            if isinstance(param_data, dict):
                lines = []
                for key, value in param_data.items():
                    if isinstance(value, (dict, list)):
                        lines.append(
                            f"**{key}**: {json.dumps(value, ensure_ascii=False)}"
                        )
                    else:
                        lines.append(f"**{key}**: {value}")
                return "\n".join(lines)
            elif isinstance(param_data, list):
                lines = []
                for i, item in enumerate(param_data, 1):
                    if isinstance(item, dict):
                        lines.append(f"{i}. {json.dumps(item, ensure_ascii=False)}")
                    else:
                        lines.append(f"{i}. {item}")
                return "\n".join(lines)
            else:
                return str(param_data)

        except Exception as e:
            logger.error(f"格式化技术参数失败: {e}")
            return str(param_data)

    def _extract_product_features(self, product_data: Dict[str, Any]) -> str:
        """提取产品特性"""
        try:
            features = []

            # 从各个字段提取特性信息
            feature_fields = [
                "features",
                "advantages",
                "highlights",
                "keyFeatures",
                "productFeatures",
            ]

            for field in feature_fields:
                if field in product_data and product_data[field]:
                    value = product_data[field]
                    if isinstance(value, str):
                        features.append(value)
                    elif isinstance(value, list):
                        features.extend([str(item) for item in value])

            # 从规格中提取特性
            specs = product_data.get("specifications", "")
            if specs and "特性" in specs:
                features.append(specs)

            return "\n".join([f"- {feature}" for feature in features if feature])

        except Exception as e:
            logger.error(f"提取产品特性失败: {e}")
            return ""

    def _collect_product_documents(self, product_model: str) -> List[Dict[str, Any]]:
        """收集产品相关文档"""
        try:
            documents = []

            # 定义文档搜索路径
            doc_search_paths = [
                f"documents/{product_model}",
                f"docs/{product_model}",
                f"attachments/{product_model}",
                "documents/products",
                "docs/products",
            ]

            # 搜索文档文件
            for search_path in doc_search_paths:
                if os.path.exists(search_path):
                    for root, dirs, files in os.walk(search_path):
                        for file in files:
                            if self._is_document_file(file):
                                file_path = os.path.join(root, file)
                                doc_info = self._analyze_document(
                                    file_path, product_model
                                )
                                if doc_info:
                                    documents.append(doc_info)

            logger.info(
                f"为产品型号 '{product_model}' 找到 {len(documents)} 个相关文档"
            )
            return documents

        except Exception as e:
            logger.error(f"收集产品 '{product_model}' 文档失败: {e}")
            return []

    def _is_document_file(self, filename: str) -> bool:
        """判断是否为文档文件"""
        doc_extensions = [".pdf", ".doc", ".docx", ".txt", ".md", ".html"]
        return any(filename.lower().endswith(ext) for ext in doc_extensions)

    def _analyze_document(
        self, file_path: str, product_model: str
    ) -> Optional[Dict[str, Any]]:
        """分析文档文件"""
        try:
            file_info = {
                "file_path": file_path,
                "file_name": os.path.basename(file_path),
                "file_size": os.path.getsize(file_path),
                "file_type": os.path.splitext(file_path)[1].lower(),
                "product_model": product_model,
                "document_type": self._classify_document_type(file_path),
                "processed": False,
                "markdown_content": "",
            }

            return file_info

        except Exception as e:
            logger.error(f"分析文档 '{file_path}' 失败: {e}")
            return None

    def _classify_document_type(self, file_path: str) -> str:
        """分类文档类型"""
        filename = os.path.basename(file_path).lower()

        if any(keyword in filename for keyword in ["安装", "install", "部署", "setup"]):
            return "安装文档"
        elif any(
            keyword in filename for keyword in ["调试", "debug", "配置", "config"]
        ):
            return "调试文档"
        elif any(keyword in filename for keyword in ["操作", "manual", "使用", "user"]):
            return "操作文档"
        elif any(keyword in filename for keyword in ["维护", "maintain", "保养"]):
            return "维护文档"
        elif any(
            keyword in filename for keyword in ["规格", "spec", "参数", "parameter"]
        ):
            return "规格文档"
        else:
            return "其他文档"

    def _generate_indexes_and_tags(
        self, product_data: Dict[str, Any], documents: List[Dict[str, Any]]
    ) -> Tuple[List[str], List[str]]:
        """生成索引和标签以提升召回率"""
        try:
            indexes = []
            tags = []

            # 基础索引
            name = product_data.get("name", "")
            model = product_data.get("model", "")
            category = product_data.get("categoryName", "")

            if name:
                indexes.extend([name, name.replace(" ", ""), name.upper()])
            if model:
                indexes.extend([model, model.replace(" ", ""), model.upper()])
            if category:
                indexes.extend([category])

            # 从产品描述中提取关键词
            description = (
                product_data.get("description", "")
                + " "
                + product_data.get("introduction", "")
            )
            keywords = self._extract_keywords(description)
            indexes.extend(keywords)

            # 生成标签
            tags.append(category if category else "产品")
            tags.append("硬件设备")

            # 根据产品特性生成标签
            if "门禁" in name or "门禁" in category:
                tags.extend(["门禁系统", "访问控制", "安防设备"])
            if "考勤" in name or "考勤" in category:
                tags.extend(["考勤系统", "人事管理", "时间管理"])
            if "人脸" in name:
                tags.extend(["人脸识别", "生物识别", "AI技术"])
            if "安检" in name or "安检" in category:
                tags.extend(["安检设备", "安全检查", "公共安全"])

            # 从文档类型生成标签
            doc_types = [doc.get("document_type", "") for doc in documents]
            for doc_type in set(doc_types):
                if doc_type:
                    tags.append(doc_type)

            # 去重并过滤
            indexes = list(set([idx for idx in indexes if idx and len(idx) > 1]))
            tags = list(set([tag for tag in tags if tag and len(tag) > 1]))

            return indexes, tags

        except Exception as e:
            logger.error(f"生成索引和标签失败: {e}")
            return [], []

    def _extract_keywords(self, text: str) -> List[str]:
        """从文本中提取关键词"""
        try:
            if not text:
                return []

            # 清理HTML标签
            clean_text = re.sub(r"<[^>]+>", "", text)

            # 提取中文关键词（简单实现）
            keywords = []

            # 常见技术关键词
            tech_keywords = [
                "识别",
                "检测",
                "控制",
                "管理",
                "系统",
                "设备",
                "终端",
                "读卡器",
                "传感器",
                "摄像头",
                "显示屏",
                "触摸屏",
                "指纹",
                "人脸",
                "虹膜",
                "RFID",
                "NFC",
                "蓝牙",
                "WiFi",
                "以太网",
                "TCP/IP",
                "HTTP",
                "数据库",
                "云端",
                "本地",
                "离线",
                "在线",
                "实时",
                "同步",
            ]

            for keyword in tech_keywords:
                if keyword in clean_text:
                    keywords.append(keyword)

            # 提取数字+单位的组合
            number_patterns = re.findall(r"\d+[A-Za-z中文]+", clean_text)
            keywords.extend(number_patterns)

            return keywords[:20]  # 限制关键词数量

        except Exception as e:
            logger.error(f"提取关键词失败: {e}")
            return []

    def _generate_product_summary(
        self, product_data: Dict[str, Any], introduction: str
    ) -> str:
        """生成产品摘要"""
        try:
            name = product_data.get("name", "")
            category = product_data.get("categoryName", "")

            # 提取介绍的前200字符作为基础摘要
            base_summary = introduction[:200] if introduction else ""
            base_summary = re.sub(r"#+\s*", "", base_summary)  # 移除markdown标题符号
            base_summary = base_summary.replace("\n", " ").strip()

            # 构建结构化摘要
            summary_parts = []

            if name and category:
                summary_parts.append(f"{name}是一款专业的{category}产品。")

            if base_summary:
                summary_parts.append(base_summary)

            # 添加主要特性
            key_features = []
            if "人脸" in name:
                key_features.append("支持人脸识别技术")
            if "门禁" in name or "门禁" in category:
                key_features.append("提供访问控制功能")
            if "考勤" in name or "考勤" in category:
                key_features.append("具备考勤管理能力")

            if key_features:
                summary_parts.append("主要特性包括：" + "、".join(key_features) + "。")

            summary = " ".join(summary_parts)

            # 限制摘要长度
            if len(summary) > 300:
                summary = summary[:297] + "..."

            return summary

        except Exception as e:
            logger.error(f"生成产品摘要失败: {e}")
            return ""

    def sync_product_to_fastgpt(
        self, product_model: str, knowledge_block: Dict[str, Any]
    ) -> Tuple[bool, str, Dict[str, Any]]:
        """将产品知识块同步到FastGPT"""
        try:
            dataset_id = knowledge_block.get("dataset_id", "")
            if not dataset_id:
                return False, "未配置知识库ID", {}

            # 构建FastGPT数据格式
            title = knowledge_block.get("title", f"{product_model} 产品知识库")
            content = self._build_fastgpt_content(knowledge_block)

            # 上传到FastGPT
            success, data_id, response = self.fastgpt_service.upload_text_data(
                dataset_id=dataset_id,
                content=content,
                title=title,
                source=f"产品型号: {product_model}",
                metadata={
                    "product_model": product_model,
                    "category": knowledge_block.get("category", ""),
                    "tags": knowledge_block.get("tags", []),
                    "indexes": knowledge_block.get("indexes", []),
                },
            )

            if success:
                logger.info(
                    f"产品 '{product_model}' 知识块同步到FastGPT成功，数据ID: {data_id}"
                )
            else:
                logger.error(
                    f"产品 '{product_model}' 知识块同步到FastGPT失败: {response}"
                )

            return success, data_id, response

        except Exception as e:
            logger.error(f"同步产品 '{product_model}' 到FastGPT失败: {e}")
            return False, "", {"error": str(e)}

    def _build_fastgpt_content(self, knowledge_block: Dict[str, Any]) -> str:
        """构建FastGPT内容格式"""
        try:
            content_parts = []

            # 标题和摘要
            title = knowledge_block.get("title", "")
            summary = knowledge_block.get("summary", "")

            if title:
                content_parts.append(f"# {title}")
            if summary:
                content_parts.append(f"## 产品摘要\n{summary}")

            # 产品介绍
            introduction = knowledge_block.get("introduction", "")
            if introduction:
                content_parts.append(introduction)

            # 基本信息
            basic_info = knowledge_block.get("basic_info", {})
            if basic_info:
                info_lines = []
                for key, value in basic_info.items():
                    if value and key not in ["id"]:
                        info_lines.append(f"- **{key}**: {value}")

                if info_lines:
                    content_parts.append("## 基本信息\n" + "\n".join(info_lines))

            # 相关文档
            documents = knowledge_block.get("documents", [])
            if documents:
                doc_lines = []
                for doc in documents:
                    doc_name = doc.get("file_name", "")
                    doc_type = doc.get("document_type", "")
                    if doc_name:
                        doc_lines.append(f"- {doc_name} ({doc_type})")

                if doc_lines:
                    content_parts.append("## 相关文档\n" + "\n".join(doc_lines))

            # 标签和索引
            tags = knowledge_block.get("tags", [])
            indexes = knowledge_block.get("indexes", [])

            if tags:
                content_parts.append(f"## 标签\n{', '.join(tags)}")

            if indexes:
                content_parts.append(
                    f"## 关键词索引\n{', '.join(indexes[:10])}"
                )  # 限制显示数量

            return "\n\n".join(content_parts)

        except Exception as e:
            logger.error(f"构建FastGPT内容失败: {e}")
            return ""

    def process_all_products(self, batch_size: int = 10) -> Dict[str, Any]:
        """处理所有产品的知识库"""
        try:
            logger.info("开始处理所有产品的知识库")

            results = {
                "total": 0,
                "success": 0,
                "failed": 0,
                "details": [],
                "errors": [],
            }

            # 获取所有产品数据
            products = self._get_all_products()
            results["total"] = len(products)

            logger.info(f"找到 {len(products)} 个产品需要处理")

            # 批量处理
            for i in range(0, len(products), batch_size):
                batch = products[i : i + batch_size]
                batch_results = self._process_product_batch(batch)

                results["success"] += batch_results["success"]
                results["failed"] += batch_results["failed"]
                results["details"].extend(batch_results["details"])
                results["errors"].extend(batch_results["errors"])

                logger.info(
                    f"已处理 {min(i + batch_size, len(products))}/{len(products)} 个产品"
                )

            logger.info(
                f"产品知识库处理完成: 成功 {results['success']}, 失败 {results['failed']}"
            )
            return results

        except Exception as e:
            logger.error(f"处理所有产品知识库失败: {e}")
            return {"error": str(e)}

    def _get_all_products(self) -> List[Dict[str, Any]]:
        """获取所有产品数据"""
        try:
            # 从API获取产品列表
            products = []
            page = 1
            page_size = 100

            while True:
                api_products = self.api_client.get_products(
                    pageSize=page_size, current=page
                )

                if not api_products:
                    break

                products.extend(api_products)

                if len(api_products) < page_size:
                    break

                page += 1

            logger.info(f"从API获取到 {len(products)} 个产品")
            return products

        except Exception as e:
            logger.error(f"获取产品数据失败: {e}")
            return []

    def _process_product_batch(self, products: List[Dict[str, Any]]) -> Dict[str, Any]:
        """处理产品批次"""
        results = {"success": 0, "failed": 0, "details": [], "errors": []}

        for product in products:
            try:
                product_model = product.get("model", "") or product.get("name", "")
                if not product_model:
                    results["failed"] += 1
                    results["errors"].append("产品型号为空")
                    continue

                # 创建知识块
                knowledge_block = self.create_product_knowledge_block(
                    product_model, product
                )

                if not knowledge_block:
                    results["failed"] += 1
                    results["errors"].append(f"创建产品 '{product_model}' 知识块失败")
                    continue

                # 同步到FastGPT
                success, data_id, response = self.sync_product_to_fastgpt(
                    product_model, knowledge_block
                )

                if success:
                    results["success"] += 1
                    results["details"].append(
                        {
                            "product_model": product_model,
                            "status": "success",
                            "data_id": data_id,
                            "dataset_id": knowledge_block.get("dataset_id", ""),
                        }
                    )
                else:
                    results["failed"] += 1
                    results["errors"].append(
                        f"同步产品 '{product_model}' 失败: {response}"
                    )

            except Exception as e:
                results["failed"] += 1
                results["errors"].append(f"处理产品失败: {str(e)}")

        return results
