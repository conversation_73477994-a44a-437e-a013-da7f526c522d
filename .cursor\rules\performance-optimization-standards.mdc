---
description: 
globs: 
alwaysApply: true
---
# 性能优化标准规范

## 📋 概述

本文档基于云商系统历史性能异常分析，制定严格的性能优化标准。重点解决函数挂起、页面卡死、资源泄漏等关键问题，确保系统性能的稳定性和可扩展性。

## 🚨 历史性能异常分析

### 发现的关键问题
1. **getCPUUsage函数永远挂起**：Promise未调用resolve导致无限等待
2. **管理员登录页面卡死**：验证权限过程中无限等待状态
3. **数据库连接池管理不当**：连接泄漏和超时处理问题
4. **Streamlit缓存TTL过短**：频繁重新计算影响性能
5. **批量操作缺乏优化**：附件处理等操作缺乏批量优化

## ⏱️ 超时和异步处理标准

### 1. 统一超时管理器
```python
import asyncio
import time
import threading
from typing import Any, Callable, Optional, Union
from concurrent.futures import ThreadPoolExecutor, TimeoutError as FutureTimeoutError

class TimeoutManager:
    """统一超时管理器"""
    
    def __init__(self):
        self.default_timeout = 30  # 默认30秒超时
        self.max_timeout = 300     # 最大5分钟超时
        self.logger = logging.getLogger(__name__)
    
    def with_timeout(self, func: Callable, timeout: Optional[int] = None, *args, **kwargs) -> Any:
        """
        为函数添加超时控制
        
        Args:
            func: 要执行的函数
            timeout: 超时时间（秒）
            *args, **kwargs: 函数参数
            
        Returns:
            函数执行结果
            
        Raises:
            TimeoutError: 函数执行超时
        """
        timeout = timeout or self.default_timeout
        timeout = min(timeout, self.max_timeout)
        
        if asyncio.iscoroutinefunction(func):
            return self._async_with_timeout(func, timeout, *args, **kwargs)
        else:
            return self._sync_with_timeout(func, timeout, *args, **kwargs)
    
    def _sync_with_timeout(self, func: Callable, timeout: int, *args, **kwargs) -> Any:
        """同步函数超时控制"""
        with ThreadPoolExecutor(max_workers=1) as executor:
            future = executor.submit(func, *args, **kwargs)
            try:
                return future.result(timeout=timeout)
            except FutureTimeoutError:
                self.logger.error(f"函数 {func.__name__} 执行超时 ({timeout}秒)")
                raise TimeoutError(f"函数执行超时: {func.__name__}")
    
    async def _async_with_timeout(self, func: Callable, timeout: int, *args, **kwargs) -> Any:
        """异步函数超时控制"""
        try:
            return await asyncio.wait_for(func(*args, **kwargs), timeout=timeout)
        except asyncio.TimeoutError:
            self.logger.error(f"异步函数 {func.__name__} 执行超时 ({timeout}秒)")
            raise TimeoutError(f"异步函数执行超时: {func.__name__}")

# 修复getCPUUsage函数挂起问题
class SystemMetrics:
    """系统指标监控"""
    
    def __init__(self):
        self.timeout_manager = TimeoutManager()
    
    def get_cpu_usage(self, timeout: int = 10) -> float:
        """
        获取CPU使用率（修复版本）
        
        Args:
            timeout: 超时时间
            
        Returns:
            CPU使用率百分比
            
        Raises:
            TimeoutError: 获取超时
            SystemError: 系统错误
        """
        try:
            def _get_cpu_usage() -> float:
                import psutil
                # 确保有返回值，避免挂起
                cpu_percent = psutil.cpu_percent(interval=1)
                if cpu_percent is None:
                    raise SystemError("无法获取CPU使用率")
                return cpu_percent
            
            return self.timeout_manager.with_timeout(_get_cpu_usage, timeout)
            
        except TimeoutError:
            self.logger.error("获取CPU使用率超时")
            raise
        except Exception as e:
            self.logger.error(f"获取CPU使用率失败: {e}")
            raise SystemError(f"CPU监控失败: {e}")
```

### 2. 异步操作标准模式
```python
import asyncio
from typing import List, Dict, Any

class AsyncOperationManager:
    """异步操作管理器"""
    
    def __init__(self):
        self.max_concurrent = 10  # 最大并发数
        self.timeout_manager = TimeoutManager()
    
    async def batch_process(
        self,
        items: List[Any],
        process_func: Callable,
        batch_size: int = 10,
        timeout: int = 60
    ) -> List[Any]:
        """
        批量异步处理
        
        Args:
            items: 要处理的项目列表
            process_func: 处理函数
            batch_size: 批处理大小
            timeout: 超时时间
            
        Returns:
            处理结果列表
        """
        results = []
        
        for i in range(0, len(items), batch_size):
            batch = items[i:i + batch_size]
            
            # 创建异步任务
            tasks = [
                self._safe_process_item(process_func, item, timeout)
                for item in batch
            ]
            
            # 等待批次完成
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果和异常
            for result in batch_results:
                if isinstance(result, Exception):
                    self.logger.error(f"批处理项目失败: {result}")
                    results.append(None)
                else:
                    results.append(result)
        
        return results
    
    async def _safe_process_item(self, func: Callable, item: Any, timeout: int) -> Any:
        """安全处理单个项目"""
        try:
            if asyncio.iscoroutinefunction(func):
                return await self.timeout_manager._async_with_timeout(func, timeout, item)
            else:
                return await asyncio.get_event_loop().run_in_executor(
                    None, lambda: self.timeout_manager.with_timeout(func, timeout, item)
                )
        except Exception as e:
            self.logger.error(f"处理项目失败: {e}")
            raise
```

## 🔗 数据库连接池优化

### 1. 连接池管理器
```python
import psycopg2
from psycopg2 import pool
import threading
import time
from contextlib import contextmanager

class DatabaseConnectionPool:
    """数据库连接池管理器"""
    
    def __init__(self, database_url: str):
        self.database_url = database_url
        self.min_connections = 5
        self.max_connections = 20
        self.connection_timeout = 30
        self.idle_timeout = 300  # 5分钟空闲超时
        self.pool = None
        self.lock = threading.Lock()
        self.connection_stats = {
            'active': 0,
            'idle': 0,
            'total_created': 0,
            'total_closed': 0
        }
        self.logger = logging.getLogger(__name__)
        self._initialize_pool()
    
    def _initialize_pool(self):
        """初始化连接池"""
        try:
            self.pool = psycopg2.pool.ThreadedConnectionPool(
                minconn=self.min_connections,
                maxconn=self.max_connections,
                dsn=self.database_url,
                connection_factory=None
            )
            self.logger.info(f"数据库连接池初始化成功: {self.min_connections}-{self.max_connections}")
        except Exception as e:
            self.logger.error(f"连接池初始化失败: {e}")
            raise
    
    @contextmanager
    def get_connection(self, timeout: Optional[int] = None):
        """
        获取数据库连接（上下文管理器）
        
        Args:
            timeout: 获取连接的超时时间
            
        Yields:
            数据库连接对象
        """
        connection = None
        timeout = timeout or self.connection_timeout
        start_time = time.time()
        
        try:
            # 获取连接
            while time.time() - start_time < timeout:
                try:
                    with self.lock:
                        connection = self.pool.getconn()
                        if connection:
                            self.connection_stats['active'] += 1
                            break
                except pool.PoolError:
                    time.sleep(0.1)  # 等待100ms后重试
            
            if not connection:
                raise TimeoutError(f"获取数据库连接超时 ({timeout}秒)")
            
            # 检查连接有效性
            self._validate_connection(connection)
            
            yield connection
            
        except Exception as e:
            self.logger.error(f"数据库连接错误: {e}")
            if connection:
                try:
                    connection.rollback()
                except:
                    pass
            raise
        finally:
            # 归还连接
            if connection:
                try:
                    with self.lock:
                        self.pool.putconn(connection)
                        self.connection_stats['active'] -= 1
                except Exception as e:
                    self.logger.error(f"归还连接失败: {e}")
    
    def _validate_connection(self, connection):
        """验证连接有效性"""
        try:
            with connection.cursor() as cursor:
                cursor.execute("SELECT 1")
                cursor.fetchone()
        except Exception as e:
            self.logger.error(f"连接验证失败: {e}")
            raise
    
    def get_pool_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        with self.lock:
            stats = self.connection_stats.copy()
            if self.pool:
                stats.update({
                    'pool_size': len(self.pool._pool),
                    'available': len([conn for conn in self.pool._pool if conn]),
                    'min_connections': self.min_connections,
                    'max_connections': self.max_connections
                })
            return stats
    
    def cleanup_idle_connections(self):
        """清理空闲连接"""
        try:
            # 这里可以实现空闲连接清理逻辑
            # PostgreSQL连接池会自动管理，但可以添加额外的清理逻辑
            pass
        except Exception as e:
            self.logger.error(f"清理空闲连接失败: {e}")
    
    def close_all_connections(self):
        """关闭所有连接"""
        try:
            if self.pool:
                self.pool.closeall()
                self.logger.info("所有数据库连接已关闭")
        except Exception as e:
            self.logger.error(f"关闭连接池失败: {e}")
```

## 💾 Streamlit缓存优化

### 1. 智能缓存管理
```python
import streamlit as st
import hashlib
import time
from typing import Any, Callable, Optional, Dict

class StreamlitCacheManager:
    """Streamlit缓存管理器"""
    
    def __init__(self):
        self.cache_stats = {}
        self.logger = logging.getLogger(__name__)
    
    @staticmethod
    def smart_cache_data(
        ttl: Optional[int] = None,
        max_entries: Optional[int] = None,
        show_spinner: bool = True,
        hash_funcs: Optional[Dict[Any, Callable]] = None
    ):
        """
        智能数据缓存装饰器
        
        Args:
            ttl: 缓存生存时间（秒），默认根据数据类型智能设置
            max_entries: 最大缓存条目数
            show_spinner: 是否显示加载指示器
            hash_funcs: 自定义哈希函数
        """
        def decorator(func: Callable) -> Callable:
            # 根据函数名称和用途智能设置TTL
            if ttl is None:
                smart_ttl = StreamlitCacheManager._get_smart_ttl(func.__name__)
            else:
                smart_ttl = ttl
            
            @st.cache_data(
                ttl=smart_ttl,
                max_entries=max_entries,
                show_spinner=show_spinner,
                hash_funcs=hash_funcs
            )
            def wrapper(*args, **kwargs):
                return func(*args, **kwargs)
            
            return wrapper
        return decorator
    
    @staticmethod
    def _get_smart_ttl(func_name: str) -> int:
        """根据函数名称智能设置TTL"""
        ttl_mapping = {
            # 用户相关数据 - 较短TTL
            'get_user': 300,        # 5分钟
            'load_user': 300,
            'authenticate': 600,    # 10分钟
            
            # 产品数据 - 中等TTL
            'get_products': 900,    # 15分钟
            'load_products': 900,
            'search_products': 600, # 10分钟
            
            # 配置数据 - 较长TTL
            'get_config': 3600,     # 1小时
            'load_config': 3600,
            'get_settings': 1800,   # 30分钟
            
            # 统计数据 - 中等TTL
            'get_stats': 600,       # 10分钟
            'calculate_metrics': 900, # 15分钟
            
            # 静态数据 - 长TTL
            'get_categories': 3600,  # 1小时
            'load_constants': 7200,  # 2小时
        }
        
        # 模糊匹配
        for pattern, ttl in ttl_mapping.items():
            if pattern in func_name.lower():
                return ttl
        
        # 默认TTL
        return 600  # 10分钟
    
    @staticmethod
    def cache_resource_with_health_check(
        validate: Optional[Callable] = None,
        hash_funcs: Optional[Dict[Any, Callable]] = None
    ):
        """
        带健康检查的资源缓存
        
        Args:
            validate: 资源验证函数
            hash_funcs: 自定义哈希函数
        """
        def decorator(func: Callable) -> Callable:
            @st.cache_resource(hash_funcs=hash_funcs)
            def wrapper(*args, **kwargs):
                resource = func(*args, **kwargs)
                
                # 健康检查
                if validate and not validate(resource):
                    st.cache_resource.clear()
                    resource = func(*args, **kwargs)
                
                return resource
            
            return wrapper
        return decorator
```

### 2. 缓存监控和清理
```python
class CacheMonitor:
    """缓存监控器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            # Streamlit没有直接的缓存统计API，这里模拟实现
            stats = {
                'data_cache_size': len(st.session_state.get('_cache_data', {})),
                'resource_cache_size': len(st.session_state.get('_cache_resource', {})),
                'memory_usage': self._estimate_cache_memory(),
                'hit_rate': self._calculate_hit_rate(),
                'last_cleared': st.session_state.get('_cache_last_cleared', 'Never')
            }
            return stats
        except Exception as e:
            self.logger.error(f"获取缓存统计失败: {e}")
            return {}
    
    def _estimate_cache_memory(self) -> str:
        """估算缓存内存使用"""
        try:
            import sys
            total_size = 0
            for key, value in st.session_state.items():
                if key.startswith('_cache'):
                    total_size += sys.getsizeof(value)
            
            # 转换为人类可读格式
            for unit in ['B', 'KB', 'MB', 'GB']:
                if total_size < 1024:
                    return f"{total_size:.2f} {unit}"
                total_size /= 1024
            return f"{total_size:.2f} TB"
        except:
            return "Unknown"
    
    def _calculate_hit_rate(self) -> float:
        """计算缓存命中率"""
        # 这里需要实现缓存命中率计算逻辑
        # Streamlit没有内置的命中率统计，需要自己实现
        return 0.0
    
    def smart_cache_cleanup(self, force: bool = False):
        """智能缓存清理"""
        try:
            current_time = time.time()
            last_cleanup = st.session_state.get('_cache_last_cleanup', 0)
            
            # 每小时自动清理一次，或强制清理
            if force or (current_time - last_cleanup) > 3600:
                st.cache_data.clear()
                st.session_state['_cache_last_cleanup'] = current_time
                st.session_state['_cache_last_cleared'] = time.strftime('%Y-%m-%d %H:%M:%S')
                self.logger.info("缓存已清理")
                return True
            
            return False
        except Exception as e:
            self.logger.error(f"缓存清理失败: {e}")
            return False
    
    def display_cache_metrics(self):
        """显示缓存指标"""
        stats = self.get_cache_stats()
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("数据缓存条目", stats.get('data_cache_size', 0))
        
        with col2:
            st.metric("资源缓存条目", stats.get('resource_cache_size', 0))
        
        with col3:
            st.metric("内存使用", stats.get('memory_usage', 'Unknown'))
        
        with col4:
            st.metric("命中率", f"{stats.get('hit_rate', 0):.1%}")
        
        if st.button("清理缓存"):
            if self.smart_cache_cleanup(force=True):
                st.success("缓存已清理")
                st.rerun()
```

## 🚀 批量操作优化

### 1. 批量处理框架
```python
from typing import List, Any, Callable, Optional
import asyncio
from concurrent.futures import ThreadPoolExecutor
import time

class BatchProcessor:
    """批量处理器"""
    
    def __init__(self, batch_size: int = 100, max_workers: int = 5):
        self.batch_size = batch_size
        self.max_workers = max_workers
        self.logger = logging.getLogger(__name__)
    
    def process_in_batches(
        self,
        items: List[Any],
        process_func: Callable,
        progress_callback: Optional[Callable] = None
    ) -> List[Any]:
        """
        批量处理项目
        
        Args:
            items: 要处理的项目列表
            process_func: 处理函数
            progress_callback: 进度回调函数
            
        Returns:
            处理结果列表
        """
        results = []
        total_batches = (len(items) + self.batch_size - 1) // self.batch_size
        
        for batch_index in range(total_batches):
            start_idx = batch_index * self.batch_size
            end_idx = min(start_idx + self.batch_size, len(items))
            batch = items[start_idx:end_idx]
            
            # 处理批次
            batch_results = self._process_batch(batch, process_func)
            results.extend(batch_results)
            
            # 进度回调
            if progress_callback:
                progress = (batch_index + 1) / total_batches
                progress_callback(progress, batch_index + 1, total_batches)
        
        return results
    
    def _process_batch(self, batch: List[Any], process_func: Callable) -> List[Any]:
        """处理单个批次"""
        try:
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                futures = [executor.submit(process_func, item) for item in batch]
                results = []
                
                for future in futures:
                    try:
                        result = future.result(timeout=30)  # 30秒超时
                        results.append(result)
                    except Exception as e:
                        self.logger.error(f"批处理项目失败: {e}")
                        results.append(None)
                
                return results
        except Exception as e:
            self.logger.error(f"批处理失败: {e}")
            return [None] * len(batch)

# 附件处理优化示例
class AttachmentProcessor:
    """附件处理器"""
    
    def __init__(self):
        self.batch_processor = BatchProcessor(batch_size=50, max_workers=3)
        self.logger = logging.getLogger(__name__)
    
    def process_attachments_batch(
        self,
        attachments: List[Dict[str, Any]],
        progress_bar: Optional[Any] = None
    ) -> List[Dict[str, Any]]:
        """
        批量处理附件
        
        Args:
            attachments: 附件列表
            progress_bar: Streamlit进度条
            
        Returns:
            处理结果列表
        """
        def progress_callback(progress: float, current: int, total: int):
            if progress_bar:
                progress_bar.progress(progress)
                st.write(f"处理进度: {current}/{total} 批次")
        
        return self.batch_processor.process_in_batches(
            attachments,
            self._process_single_attachment,
            progress_callback
        )
    
    def _process_single_attachment(self, attachment: Dict[str, Any]) -> Dict[str, Any]:
        """处理单个附件"""
        try:
            # 模拟附件处理逻辑
            time.sleep(0.1)  # 模拟处理时间
            
            result = {
                'id': attachment.get('id'),
                'status': 'processed',
                'processed_at': time.time(),
                'size': attachment.get('size', 0)
            }
            
            return result
        except Exception as e:
            self.logger.error(f"处理附件失败: {e}")
            return {
                'id': attachment.get('id'),
                'status': 'failed',
                'error': str(e)
            }
```

## 📊 性能监控系统

### 1. 性能指标收集器
```python
import time
import psutil
import threading
from collections import deque
from typing import Dict, Any, List

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, history_size: int = 100):
        self.history_size = history_size
        self.metrics_history = {
            'cpu_usage': deque(maxlen=history_size),
            'memory_usage': deque(maxlen=history_size),
            'response_times': deque(maxlen=history_size),
            'error_rates': deque(maxlen=history_size)
        }
        self.lock = threading.Lock()
        self.logger = logging.getLogger(__name__)
        self.timeout_manager = TimeoutManager()
    
    def collect_system_metrics(self) -> Dict[str, float]:
        """收集系统指标"""
        try:
            # 使用超时控制避免挂起
            cpu_usage = self.timeout_manager.with_timeout(
                psutil.cpu_percent, 5, interval=1
            )
            
            memory = psutil.virtual_memory()
            memory_usage = memory.percent
            
            disk = psutil.disk_usage('/')
            disk_usage = disk.percent
            
            metrics = {
                'cpu_usage': cpu_usage,
                'memory_usage': memory_usage,
                'disk_usage': disk_usage,
                'timestamp': time.time()
            }
            
            # 记录历史数据
            with self.lock:
                self.metrics_history['cpu_usage'].append(cpu_usage)
                self.metrics_history['memory_usage'].append(memory_usage)
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"收集系统指标失败: {e}")
            return {
                'cpu_usage': 0.0,
                'memory_usage': 0.0,
                'disk_usage': 0.0,
                'timestamp': time.time(),
                'error': str(e)
            }
    
    def record_response_time(self, operation: str, duration: float):
        """记录响应时间"""
        with self.lock:
            self.metrics_history['response_times'].append({
                'operation': operation,
                'duration': duration,
                'timestamp': time.time()
            })
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        with self.lock:
            cpu_history = list(self.metrics_history['cpu_usage'])
            memory_history = list(self.metrics_history['memory_usage'])
            response_times = list(self.metrics_history['response_times'])
        
        summary = {
            'cpu': {
                'current': cpu_history[-1] if cpu_history else 0,
                'average': sum(cpu_history) / len(cpu_history) if cpu_history else 0,
                'max': max(cpu_history) if cpu_history else 0
            },
            'memory': {
                'current': memory_history[-1] if memory_history else 0,
                'average': sum(memory_history) / len(memory_history) if memory_history else 0,
                'max': max(memory_history) if memory_history else 0
            },
            'response_times': {
                'count': len(response_times),
                'average': sum(rt['duration'] for rt in response_times) / len(response_times) if response_times else 0,
                'max': max(rt['duration'] for rt in response_times) if response_times else 0
            }
        }
        
        return summary

# 性能装饰器
def monitor_performance(operation_name: str):
    """性能监控装饰器"""
    def decorator(func: Callable) -> Callable:
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                duration = time.time() - start_time
                # 记录性能指标
                if hasattr(st.session_state, 'performance_monitor'):
                    st.session_state.performance_monitor.record_response_time(
                        operation_name, duration
                    )
        return wrapper
    return decorator
```

## 🚫 禁止模式

### 1. 禁止的性能反模式
```python
# ❌ 禁止：无超时控制的操作
def get_cpu_usage():
    return psutil.cpu_percent(interval=None)  # 可能挂起

# ❌ 禁止：不释放数据库连接
def query_database():
    conn = psycopg2.connect(DATABASE_URL)
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM products")
    return cursor.fetchall()  # 连接未关闭

# ❌ 禁止：过短的缓存TTL
@st.cache_data(ttl=1)  # 1秒TTL太短
def load_products():
    return expensive_operation()

# ❌ 禁止：逐项处理大量数据
def process_all_items(items):
    results = []
    for item in items:  # 应该使用批处理
        result = expensive_process(item)
        results.append(result)
    return results
```

### 2. 必须的性能模式
```python
# ✅ 正确：带超时控制
def get_cpu_usage_safe():
    timeout_manager = TimeoutManager()
    return timeout_manager.with_timeout(psutil.cpu_percent, 10, interval=1)

# ✅ 正确：使用连接池
def query_database_safe():
    with db_pool.get_connection() as conn:
        with conn.cursor() as cursor:
            cursor.execute("SELECT * FROM products")
            return cursor.fetchall()

# ✅ 正确：智能缓存TTL
@StreamlitCacheManager.smart_cache_data()
def load_products():
    return expensive_operation()

# ✅ 正确：批量处理
def process_all_items_batch(items):
    processor = BatchProcessor()
    return processor.process_in_batches(items, expensive_process)
```

## 📋 性能优化检查清单

### 开发阶段
- [ ] 是否为所有耗时操作添加超时控制？
- [ ] 是否使用连接池管理数据库连接？
- [ ] 是否合理设置缓存TTL？
- [ ] 是否使用批处理优化大量数据操作？
- [ ] 是否添加性能监控？

### 测试阶段
- [ ] 是否测试超时场景？
- [ ] 是否测试高并发下的性能？
- [ ] 是否测试内存泄漏？
- [ ] 是否测试缓存效果？
- [ ] 是否测试批处理性能？

### 部署阶段
- [ ] 是否配置性能监控？
- [ ] 是否设置性能告警？
- [ ] 是否优化资源配置？
- [ ] 是否准备性能降级方案？

---

**重要提醒**: 本规范是强制性的，所有性能相关代码必须严格遵循此标准。违反此规范的代码将不被接受，需要重新修改后才能合并。

**文档版本**: v1.0  
**最后更新**: 2024-12-19  
**维护者**: 开发团队

