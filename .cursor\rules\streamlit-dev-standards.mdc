---
description: 
globs: 
alwaysApply: true
---
# Streamlit 开发规范

## 🎯 项目结构规范

### 标准目录结构
```
yunshang/
├── .streamlit/              # Streamlit配置文件
│   ├── config.toml         # 应用配置
│   └── secrets.toml        # 密钥配置（不提交）
├── pages/                  # 多页面应用页面
│   ├── 01_🏠_主页.py
│   ├── 02_📊_数据分析.py
│   └── 03_⚙️_设置.py
├── components/             # 可复用组件
│   ├── __init__.py
│   ├── charts.py          # 图表组件
│   ├── forms.py           # 表单组件
│   └── widgets.py         # 自定义小部件
├── utils/                 # 工具函数
│   ├── __init__.py
│   ├── data_processing.py # 数据处理
│   ├── auth.py           # 认证相关
│   └── helpers.py        # 辅助函数
├── data/                 # 数据文件
│   ├── raw/              # 原始数据
│   ├── processed/        # 处理后数据
│   └── cache/            # 缓存数据
├── tests/                # 测试文件
│   ├── test_components.py
│   ├── test_utils.py
│   └── test_integration.py
├── docs/                 # 文档
│   ├── README.md
│   ├── API.md
│   └── deployment.md
├── requirements.txt      # 依赖管理
├── main.py              # 主应用入口
└── config.py            # 配置管理
```

## 📝 代码规范

### Python代码风格
- 严格遵循 PEP 8 规范
- 使用 black 进行代码格式化
- 使用 flake8 进行代码检查
- 使用 mypy 进行类型检查

### Streamlit特定规范

#### 页面结构标准模板
```python
import streamlit as st
from typing import Optional, Dict, Any
import logging

# 配置日志
logger = logging.getLogger(__name__)

def main():
    """页面主函数"""
    # 页面配置
    st.set_page_config(
        page_title="页面标题",
        page_icon="🎯",
        layout="wide",  # 或 "centered"
        initial_sidebar_state="expanded"
    )
    
    # 页面头部
    render_header()
    
    # 主要内容
    render_content()
    
    # 页面脚部
    render_footer()

def render_header():
    """渲染页面头部"""
    st.title("页面标题")
    st.markdown("页面描述")

def render_content():
    """渲染主要内容"""
    pass

def render_footer():
    """渲染页面脚部"""
    st.markdown("---")
    st.markdown("© 2024 项目名称")

if __name__ == "__main__":
    main()
```

#### 组件开发规范
```python
import streamlit as st
from typing import Optional, Dict, Any, Callable

def create_data_table(
    data: Any,
    title: str,
    key: Optional[str] = None,
    on_select: Optional[Callable] = None,
    **kwargs
) -> Any:
    """
    创建数据表格组件
    
    Args:
        data: 表格数据
        title: 表格标题
        key: 组件唯一标识
        on_select: 选择回调函数
        **kwargs: 其他参数
        
    Returns:
        选中的数据或操作结果
    """
    with st.container():
        st.subheader(title)
        
        # 组件逻辑
        result = st.dataframe(
            data,
            key=key,
            on_select=on_select,
            **kwargs
        )
        
        return result
```

## 🔄 状态管理规范

### Session State 使用规范
```python
import streamlit as st
from typing import Any, Optional

class SessionManager:
    """Session状态管理器"""
    
    @staticmethod
    def get(key: str, default: Any = None) -> Any:
        """获取session值"""
        return st.session_state.get(key, default)
    
    @staticmethod
    def set(key: str, value: Any) -> None:
        """设置session值"""
        st.session_state[key] = value
    
    @staticmethod
    def delete(key: str) -> None:
        """删除session值"""
        if key in st.session_state:
            del st.session_state[key]
    
    @staticmethod
    def clear() -> None:
        """清空所有session"""
        st.session_state.clear()

# 使用示例
def initialize_session():
    """初始化session状态"""
    defaults = {
        'user_authenticated': False,
        'current_page': 'home',
        'data_cache': {},
        'user_preferences': {}
    }
    
    for key, value in defaults.items():
        if key not in st.session_state:
            SessionManager.set(key, value)
```

## 💾 数据处理规范

### 缓存使用规范
```python
import streamlit as st
import pandas as pd
from typing import Union, Optional
import logging

logger = logging.getLogger(__name__)

@st.cache_data(ttl=3600, show_spinner=True)
def load_data(file_path: str) -> pd.DataFrame:
    """
    加载数据文件
    
    Args:
        file_path: 文件路径
        
    Returns:
        DataFrame: 加载的数据
    """
    try:
        if file_path.endswith('.csv'):
            return pd.read_csv(file_path)
        elif file_path.endswith('.xlsx'):
            return pd.read_excel(file_path)
        else:
            raise ValueError(f"不支持的文件格式: {file_path}")
    except Exception as e:
        logger.error(f"数据加载失败: {e}")
        st.error(f"数据加载失败: {e}")
        return pd.DataFrame()

@st.cache_resource
def init_model():
    """初始化模型（资源型缓存）"""
    # 初始化机器学习模型或数据库连接
    pass

# 缓存清理
def clear_cache():
    """清理缓存"""
    st.cache_data.clear()
    st.cache_resource.clear()
```

### 数据验证规范
```python
import pandas as pd
from typing import List, Dict, Any
import streamlit as st

def validate_dataframe(
    df: pd.DataFrame,
    required_columns: List[str],
    min_rows: int = 1
) -> Dict[str, Any]:
    """
    验证DataFrame格式
    
    Args:
        df: 待验证的DataFrame
        required_columns: 必需的列
        min_rows: 最小行数
        
    Returns:
        验证结果字典
    """
    validation_result = {
        'valid': True,
        'errors': [],
        'warnings': []
    }
    
    # 检查必需列
    missing_columns = set(required_columns) - set(df.columns)
    if missing_columns:
        validation_result['valid'] = False
        validation_result['errors'].append(f"缺少必需列: {missing_columns}")
    
    # 检查行数
    if len(df) < min_rows:
        validation_result['valid'] = False
        validation_result['errors'].append(f"数据行数不足，需要至少{min_rows}行")
    
    return validation_result
```

## 🎨 UI/UX 规范

### 布局规范
```python
import streamlit as st

def create_responsive_layout():
    """创建响应式布局"""
    
    # 侧边栏
    with st.sidebar:
        st.title("导航菜单")
        page = st.selectbox("选择页面", ["首页", "数据分析", "设置"])
    
    # 主内容区
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col1:
        st.subheader("左侧栏")
        # 左侧内容
    
    with col2:
        st.subheader("主内容")
        # 主要内容
    
    with col3:
        st.subheader("右侧栏")
        # 右侧内容
```

### 用户体验规范
- 加载状态：使用 `st.spinner()` 显示加载状态
- 错误处理：使用 `st.error()` 显示错误信息
- 成功反馈：使用 `st.success()` 显示成功信息
- 警告提示：使用 `st.warning()` 显示警告信息
- 信息提示：使用 `st.info()` 显示信息提示

```python
def handle_user_feedback():
    """用户反馈处理示例"""
    
    with st.spinner("正在处理..."):
        try:
            # 处理逻辑
            result = process_data()
            st.success("处理成功！")
            return result
        except Exception as e:
            st.error(f"处理失败: {str(e)}")
            logger.error(f"处理失败: {e}")
            return None
```

## 🧪 测试规范

### 单元测试
```python
import pytest
import streamlit as st
from streamlit.testing.v1 import AppTest

def test_main_page():
    """测试主页面"""
    at = AppTest.from_file("main.py")
    at.run()
    
    # 检查页面标题
    assert not at.exception
    assert len(at.title) > 0

def test_data_processing():
    """测试数据处理函数"""
    from utils.data_processing import validate_dataframe
    import pandas as pd
    
    # 测试数据
    df = pd.DataFrame({'A': [1, 2, 3], 'B': ['a', 'b', 'c']})
    result = validate_dataframe(df, ['A', 'B'])
    
    assert result['valid'] is True
    assert len(result['errors']) == 0
```

### 集成测试
```python
def test_full_workflow():
    """测试完整工作流程"""
    at = AppTest.from_file("main.py")
    at.run()
    
    # 模拟用户操作
    at.text_input("input_field").set_value("test_value")
    at.button("submit_button").click()
    at.run()
    
    # 验证结果
    assert not at.exception
    assert "成功" in at.success[0].value
```

## 🚀 部署规范

### 配置文件
```toml
# .streamlit/config.toml
[global]
developmentMode = false

[server]
port = 8501
enableCORS = false
enableXsrfProtection = true

[browser]
gatherUsageStats = false

[theme]
primaryColor = "#FF6B6B"
backgroundColor = "#FFFFFF"
secondaryBackgroundColor = "#F0F2F6"
textColor = "#262730"
```

### 环境变量
```python
# config.py
import os
from typing import Optional

class Config:
    """应用配置类"""
    
    # 数据库配置
    DATABASE_URL: str = os.getenv('DATABASE_URL', '***********************************************/product')
    
    # API配置
    API_KEY: Optional[str] = os.getenv('API_KEY')
    API_BASE_URL: str = os.getenv('API_BASE_URL', 'https://api.example.com')
    
    # 应用配置
    DEBUG: bool = os.getenv('DEBUG', 'False').lower() == 'true'
    LOG_LEVEL: str = os.getenv('LOG_LEVEL', 'INFO')
    
    @classmethod
    def validate(cls) -> None:
        """验证配置"""
        if not cls.API_KEY:
            raise ValueError("API_KEY environment variable is required")
```

## 🔒 安全规范

### 敏感信息处理
- 使用 `secrets.toml` 存储敏感信息
- 不要在代码中硬编码密钥
- 使用环境变量管理配置

```python
import streamlit as st

def get_secret(key: str, default: str = None) -> str:
    """安全获取密钥"""
    try:
        return st.secrets[key]
    except KeyError:
        if default is not None:
            return default
        raise ValueError(f"Secret '{key}' not found")
```

### 输入验证
```python
import re
from typing import Optional

def validate_email(email: str) -> bool:
    """验证邮箱格式"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def sanitize_input(input_str: str) -> str:
    """清理用户输入"""
    # 移除潜在的危险字符
    return re.sub(r'[<>"\']', '', input_str.strip())
```

## ⚡ 性能优化规范

### 缓存策略
- 数据加载：使用 `@st.cache_data`
- 模型/资源：使用 `@st.cache_resource`
- 合理设置TTL时间
- 及时清理不必要的缓存

### 代码优化
```python
# 避免在循环中使用Streamlit组件
# ❌ 错误示例
for item in items:
    st.write(item)

# ✅ 正确示例
output = []
for item in items:
    output.append(str(item))
st.write('\n'.join(output))
```

## 📚 文档规范

### 代码注释
- 函数必须有docstring
- 复杂逻辑必须有行内注释
- 使用类型提示

### README.md 模板
```markdown
# 项目名称

## 项目描述
简要描述项目功能和用途

## 安装和运行
\`\`\`bash
pip install -r requirements.txt
streamlit run main.py
\`\`\`

## 项目结构
描述主要文件和目录的作用

## 配置说明
说明环境变量和配置文件

## API文档
如果有API，提供详细文档

## 部署指南
生产环境部署步骤
```

## 🔧 开发工具配置

### VS Code 配置
```json
{
    "python.linting.enabled": true,
    "python.linting.flake8Enabled": true,
    "python.formatting.provider": "black",
    "python.formatting.blackArgs": ["--line-length=88"],
    "editor.formatOnSave": true
}
```

### Git 提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建或工具相关
```

## ✅ 质量检查清单

### 开发阶段
- [ ] 代码符合PEP 8规范
- [ ] 函数有完整的docstring
- [ ] 使用了类型提示
- [ ] 敏感信息未硬编码
- [ ] 异常处理完善

### 测试阶段
- [ ] 单元测试覆盖率 > 80%
- [ ] 集成测试通过
- [ ] 性能测试通过
- [ ] 安全性检查通过

### 部署阶段
- [ ] 配置文件正确
- [ ] 环境变量设置
- [ ] 依赖项完整
- [ ] 文档更新完整

---
**注意**: 本规范是活文档，需要根据项目发展持续更新和完善。所有团队成员都应严格遵守此规范，确保代码质量和项目的可维护性。

