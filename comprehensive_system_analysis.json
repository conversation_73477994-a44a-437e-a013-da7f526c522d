{"api_data_structure": {"total_fields": 54, "all_fields": ["id", "update_by", "allProductGenListLearning", "showForCompanyFilter", "use_to", "label_list", "params", "productId", "video_explanation", "showForCompanyList", "common_problem", "size_img", "video_installation", "update_time", "allProductGenListInstall", "banner", "count", "productGenListHandle", "param_info", "show_for_company_name", "param_info_list", "new_param", "video_troubleshooting", "attribute", "status", "show_time", "guide", "small_img", "accessory", "productGenListFault", "instructions", "productGenListLearning", "details", "introduction", "category_name", "allProductGenListHandle", "allProductGenListFault", "unit", "show_for", "create_by", "name", "category_id", "other", "create_time", "label_name", "specList", "label_id", "qualifications", "price", "productGenListInstall", "is_new", "thirdTypeSearch", "spec", "show_for_company"], "knowledge_fields": ["params", "common_problem", "param_info", "param_info_list", "new_param", "guide", "instructions", "details", "introduction"], "attachment_fields": ["video_explanation", "size_img", "video_installation", "banner", "video_troubleshooting", "small_img", "other", "qualifications"], "basic_fields": ["id", "update_by", "allProductGenListLearning", "showForCompanyFilter", "use_to", "label_list", "productId", "showForCompanyList", "update_time", "allProductGenListInstall", "count", "productGenListHandle", "show_for_company_name", "attribute", "status", "show_time", "accessory", "productGenListFault", "productGenListLearning", "category_name", "allProductGenListHandle", "allProductGenListFault", "unit", "show_for", "create_by", "name", "category_id", "create_time", "label_name", "specList", "label_id", "price", "productGenListInstall", "is_new", "thirdTypeSearch", "spec", "show_for_company"], "field_types": {"create_by": ["NoneType"], "create_time": ["str"], "update_by": ["NoneType"], "update_time": ["str"], "params": ["dict"], "id": ["int"], "status": ["str"], "small_img": ["str"], "banner": ["str"], "name": ["str"], "attribute": ["str"], "category_id": ["str"], "label_id": ["str"], "spec": ["str"], "introduction": ["str"], "show_for": ["NoneType"], "param_info": ["str"], "new_param": ["str"], "use_to": ["str"], "details": ["str"], "qualifications": ["NoneType"], "instructions": ["str", "NoneType"], "show_time": ["str"], "other": ["str"], "guide": ["str"], "common_problem": ["str", "NoneType"], "price": ["float"], "is_new": ["int"], "category_name": ["str"], "label_list": ["list"], "label_name": ["str"], "video_explanation": ["NoneType"], "video_installation": ["NoneType"], "video_troubleshooting": ["NoneType"], "show_for_company": ["NoneType"], "count": ["int"], "size_img": ["NoneType"], "specList": ["list"], "productId": ["int"], "accessory": ["NoneType"], "show_for_company_name": ["NoneType"], "unit": ["int"], "productGenListHandle": ["list"], "productGenListInstall": ["list"], "productGenListFault": ["list"], "productGenListLearning": ["list"], "allProductGenListHandle": ["list"], "allProductGenListInstall": ["list"], "allProductGenListFault": ["list"], "allProductGenListLearning": ["list"], "showForCompanyFilter": ["bool"], "thirdTypeSearch": ["bool"], "showForCompanyList": ["list"], "param_info_list": ["list"]}, "field_samples": {"create_time": "2022-08-29 08:44:21", "update_time": "2025-06-06 10:24:14", "params": "{}", "id": "31", "status": "0", "small_img": "https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/appl...", "banner": "https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/appl...", "name": "指纹考勤机", "attribute": "0", "category_id": "110", "label_id": "17", "spec": "ZK3969", "introduction": "ZK3969采用了ZKFace7.0面部识别算法及ZKFinger10.0指纹算法，能够快速准确识别您的面部、指纹，双识别模式，自由选择考勤方式。", "param_info": "<table><tbody><tr><td><p><font>用户数</font></p></td><td><p><font>200</font><font>人</font></p></td></tr...", "new_param": "[{\"content\":\"200\",\"id\":\"008554f4-d8b6-45ab-bb88-3120f0d793e6\",\"params\":\"用户数\"},{\"content\":\"400\",\"id\":...", "use_to": "写字楼、连锁商超、学校、工厂、工地、酒店", "details": "<p><img src=\"https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.c...", "instructions": "14", "show_time": "2022-08-29T08:44:09.778Z", "other": "https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/appl...", "guide": "<p><br/></p>", "common_problem": "36", "price": "0.0", "is_new": "1", "category_name": "指纹识别考勤机", "label_list": "[{'createBy': '', 'createTime': '2022-08-27 13:33:25', 'updateBy': '', 'updateTime': '2022-08-27 13:...", "label_name": "渠道", "count": "4145", "specList": "[]", "productId": "31", "unit": "2", "productGenListHandle": "[]", "productGenListInstall": "[]", "productGenListFault": "[]", "productGenListLearning": "[]", "allProductGenListHandle": "[]", "allProductGenListInstall": "[]", "allProductGenListFault": "[]", "allProductGenListLearning": "[]", "showForCompanyFilter": "False", "thirdTypeSearch": "False", "showForCompanyList": "[]", "param_info_list": "[{'id': '008554f4-d8b6-45ab-bb88-3120f0d793e6', 'params': '用户数', 'content': '200'}, {'id': 'f811ec24..."}, "sample_products": [{"create_by": null, "create_time": "2022-08-29 08:44:21", "update_by": null, "update_time": "2025-06-06 10:24:14", "params": {}, "id": 31, "status": "0", "small_img": "https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/82b0dc48-5a5d-428b-842c-43b0c7d64e5e.png?file=屏幕快照 2022-12-16 下午6.08.28.png", "banner": "https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/2f186b73-2b84-4d51-be92-dbdb117f234e.png?file=屏幕快照 2022-12-16 下午6.08.25.png,https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/91720920-f402-4c46-8eba-b62a8526a2ec.png?file=屏幕快照 2022-12-16 下午6.08.28.png,https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/531167ff-f3d6-4988-9f2e-46f19d813903.png?file=屏幕快照 2022-12-16 下午6.08.32.png", "name": "指纹考勤机", "attribute": "0", "category_id": "110", "label_id": "17", "spec": "ZK3969", "introduction": "ZK3969采用了ZKFace7.0面部识别算法及ZKFinger10.0指纹算法，能够快速准确识别您的面部、指纹，双识别模式，自由选择考勤方式。", "show_for": null, "param_info": "<table><tbody><tr><td><p><font>用户数</font></p></td><td><p><font>200</font><font>人</font></p></td></tr><tr><td><p><font>指纹容量</font></p></td><td><p><font>400</font><font>枚</font></p></td></tr><tr><td><p><font>面部容量</font></p></td><td><p><font>200张</font></p></td></tr><tr><td><p><font>记录容量</font></p></td><td><p><font>5万</font><font>条</font></p></td></tr><tr><td><p><font>显示屏</font></p></td><td><p><font>2.</font><font>8</font><font>寸高清彩屏</font></p></td></tr><tr><td><p><font>算法版本</font></p></td><td><p><font>ZKFinger&nbsp;</font><font>10.0</font><font>最新高速双引擎指纹识别算法</font></p></td></tr><tr><td><p><font>通讯方式</font></p></td><td><p><font>TCP/IP、USB-host</font></p></td></tr><tr><td><p><font>U盘功能</font></p></td><td><p><font>支持</font><font>U盘高速上传下载</font></p></td></tr><tr><td><p><font>其它功能</font></p></td><td><p><font>T9输入法，</font><font>简单门禁，</font><font>定时响铃</font><font>，记录查询</font></p></td></tr><tr><td><p><font>使用温度</font></p></td><td><p><font>0℃～45℃</font></p></td></tr><tr><td><p><font>使用湿度</font></p></td><td><p><font>20%～80%</font></p></td></tr><tr><td><p><font>电源规格</font></p></td><td><p><font>DC&nbsp;5V&nbsp;1A</font></p></td></tr><tr><td><p><font>尺寸</font></p></td><td><p><font>162*153*30 mm</font></p></td></tr></tbody></table>", "new_param": "[{\"content\":\"200\",\"id\":\"008554f4-d8b6-45ab-bb88-3120f0d793e6\",\"params\":\"用户数\"},{\"content\":\"400\",\"id\":\"f811ec24-3ffa-4f0d-bb71-8139aa9ac585\",\"params\":\"指纹容量\"},{\"content\":\"200\",\"id\":\"f9741238-a471-4e97-8050-3f9633b669e8\",\"params\":\"面部容量\"},{\"content\":\"5万条\",\"id\":\"da48713e-4b57-481b-896b-26cde86e0450\",\"params\":\"记录容量\"},{\"content\":\"2.8寸高清彩屏\",\"id\":\"cbe07f9b-5b6d-44b7-8ecd-c6c060277b93\",\"params\":\"显示屏\"},{\"content\":\"ZKFinger 10.0最新高速双引擎指纹识别算法\",\"id\":\"486adc50-11ab-4742-89ba-ee8497605166\",\"params\":\"算法版本\"},{\"content\":\"TCP/IP、USB-host\",\"id\":\"adae0100-8d96-4712-a72c-13a0ca82b88b\",\"params\":\"通讯方式\"},{\"content\":\"支持U盘高速上传下载\",\"id\":\"22933647-508d-40b3-8eb1-5a7117450103\",\"params\":\"U盘功能\"},{\"content\":\"T9输入法，简单门禁，定时响铃，记录查询\",\"id\":\"7aa3ff8b-7c46-49ee-8bfc-d0c0254614f1\",\"params\":\"其它功能\"},{\"content\":\"0℃～45℃\",\"id\":\"bfe52f08-1faa-4230-a1a6-cb96749bb483\",\"params\":\"使用温度\"},{\"content\":\"20%～80%\",\"id\":\"a552dbc7-43ce-4c2a-a20f-7ba7f07b713b\",\"params\":\"使用湿度\"},{\"content\":\"DC 5V 1A\",\"id\":\"efdf688a-5e88-4308-8885-11830beb68de\",\"params\":\"电源规格\"},{\"content\":\"162*153*30 mm\",\"id\":\"cc4cdf67-f932-4648-89af-17643af75b9c\",\"params\":\"尺寸\"}]", "use_to": "写字楼、连锁商超、学校、工厂、工地、酒店", "details": "<p><img src=\"https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/cdb92413-6c83-4f16-a650-5a97f3ca266f.png?file=【宣传彩页】熵基彩屏指纹考勤终端ZK3969_00.png\" style=\"max-width:100%;\" contenteditable=\"false\"/><br/></p>", "qualifications": null, "instructions": "14", "show_time": "2022-08-29T08:44:09.778Z", "other": "https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/b8279695-0220-409e-bb54-e0b4e509a9d2.pdf?file=【宣传彩页】熵基彩屏指纹考勤终端ZK3969.pdf", "guide": "<p><br/></p>", "common_problem": "36", "price": 0.0, "is_new": 1, "category_name": "指纹识别考勤机", "label_list": [{"createBy": "", "createTime": "2022-08-27 13:33:25", "updateBy": "", "updateTime": "2022-08-27 13:34:18", "remark": null, "params": {}, "isApp": null, "id": 17, "status": "0", "name": "渠道", "showDay": null}], "label_name": "渠道", "video_explanation": null, "video_installation": null, "video_troubleshooting": null, "show_for_company": null, "count": 4145, "size_img": null, "specList": [], "productId": 31, "accessory": null, "show_for_company_name": null, "unit": 2, "productGenListHandle": [], "productGenListInstall": [], "productGenListFault": [], "productGenListLearning": [], "allProductGenListHandle": [], "allProductGenListInstall": [], "allProductGenListFault": [], "allProductGenListLearning": [], "showForCompanyFilter": false, "thirdTypeSearch": false, "showForCompanyList": [], "param_info_list": [{"id": "008554f4-d8b6-45ab-bb88-3120f0d793e6", "params": "用户数", "content": "200"}, {"id": "f811ec24-3ffa-4f0d-bb71-8139aa9ac585", "params": "指纹容量", "content": "400"}, {"id": "f9741238-a471-4e97-8050-3f9633b669e8", "params": "面部容量", "content": "200"}, {"id": "da48713e-4b57-481b-896b-26cde86e0450", "params": "记录容量", "content": "5万条"}, {"id": "cbe07f9b-5b6d-44b7-8ecd-c6c060277b93", "params": "显示屏", "content": "2.8寸高清彩屏"}, {"id": "486adc50-11ab-4742-89ba-ee8497605166", "params": "算法版本", "content": "ZKFinger 10.0最新高速双引擎指纹识别算法"}, {"id": "adae0100-8d96-4712-a72c-13a0ca82b88b", "params": "通讯方式", "content": "TCP/IP、USB-host"}, {"id": "22933647-508d-40b3-8eb1-5a7117450103", "params": "U盘功能", "content": "支持U盘高速上传下载"}, {"id": "7aa3ff8b-7c46-49ee-8bfc-d0c0254614f1", "params": "其它功能", "content": "T9输入法，简单门禁，定时响铃，记录查询"}, {"id": "bfe52f08-1faa-4230-a1a6-cb96749bb483", "params": "使用温度", "content": "0℃～45℃"}, {"id": "a552dbc7-43ce-4c2a-a20f-7ba7f07b713b", "params": "使用湿度", "content": "20%～80%"}, {"id": "efdf688a-5e88-4308-8885-11830beb68de", "params": "电源规格", "content": "DC 5V 1A"}, {"id": "cc4cdf67-f932-4648-89af-17643af75b9c", "params": "尺寸", "content": "162*153*30 mm"}]}, {"create_by": null, "create_time": "2022-08-29 08:47:33", "update_by": null, "update_time": "2024-09-10 15:57:18", "params": {}, "id": 32, "status": "1", "small_img": "https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/756480d0-acca-4fa1-8f3f-6b5ce37aa937.jpg?file=附件一：产品主图_01.jpg", "banner": "https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/290b951b-fbbb-4231-b81a-96e94ef5dfb7.jpg?file=附件一：产品主图_01.jpg", "name": "电动车检测摄像机", "attribute": "0", "category_id": "186", "label_id": "18", "spec": "SJ-W952ES21D-A", "introduction": "采用深度学习算法，提供精准的电动车侦测, 支持电动车遗留侦测/警戒等功能。", "param_info": "<p>摄像机<br/></p><table><tbody><tr><td>型号</td><td>SJ-W952ES21D-A</td></tr><tr><td>图象传感器</td><td>200万像素逐行扫描超低照度CMOS</td></tr><tr><td>图像分辨率</td><td>2MP(1920*1080@25fps)</td></tr><tr><td>日夜转换模式</td><td>自动/彩色/黑白/外部触发</td></tr><tr><td>宽动态</td><td>WDR(关闭/数字宽动态)</td></tr><tr><td>3D降噪</td><td>关闭/低/中/较高/高</td></tr><tr><td>去雾</td><td>关闭/低/中/高</td></tr><tr><td>镜像</td><td>关闭/水平镜像/垂直镜像/180°旋转/走廊模式（90°旋转、270°旋转）</td></tr><tr><td>背光模式</td><td>关闭/强光抑制/背光补偿</td></tr><tr><td>色彩模式</td><td>标准/艳丽/自然<br/></td></tr></tbody></table><p>电动车检测<br/></p><table>\n<colgroup><col>\n<col>\n</colgroup><tbody><tr>\n<td>电动车检测大小要求</td><td>像素≥50X50</td>\n</tr>\n<tr>\n<td>实测检测情况</td><td>目前市面上大部分不同款式的电动车都可以检测出来</td>\n</tr>\n<tr>\n<td></td><td>人坐在电动车上、或者人推着电动车进电梯都可以检测出来</td>\n</tr>\n<tr>\n<td>实测漏检和误检</td><td>漏检：冬天电动车挡风布大面积遮挡电动车，会出现一定漏检</td>\n</tr>\n<tr>\n<td></td><td>误检：个别车轮和车身较为高大的婴儿车，会出现一定误检</td>\n</tr></tbody></table><p>音视频编码</p><table>\n<colgroup><col>\n<col>\n</colgroup><tbody><tr>\n<td>视频编码标准</td><td>H.265/H.264/MJPEG(子码流)</td>\n</tr>\n<tr>\n<td>编码特性</td><td>NTSC&nbsp;：主码流：1920x1080&nbsp;@30fps，子码流：D1@30fps</td>\n</tr>\n<tr>\n<td></td><td>PAL&nbsp;&nbsp;：主码流：1920x1080&nbsp;@25fps，子码流：D1@25fps</td>\n</tr>\n<tr>\n<td>最大图像分辨率</td><td>1920x1080</td>\n</tr>\n<tr>\n<td>视频码率</td><td>160Kbps-8Mbps</td>\n</tr>\n<tr>\n<td>视频类型</td><td>复合流/视频流</td>\n</tr>\n<tr>\n<td>音频编码标准</td><td>G.711u/64Kbps</td>\n</tr>\n<tr>\n<td>感兴趣区域（ROI）</td><td>支持4个区域，可设置多级等级</td>\n</tr></tbody></table><p>智能分析<br/></p><table><tbody><tr><td>电动车检测</td><td>可检测自行车和电动车，可设置4个检测区域，可用报表统计数据</td></tr></tbody></table><p>网络功能<br/></p><table><tbody><tr><td>网络协议</td><td>TCP/IP，UDP，HTTP，HTTPS，DHCP，RTSP，DDNS，NTP，PPPOE，UPNP，SMTP ,&nbsp;</td></tr><tr><td>接口协议</td><td>ONVIF、GB28181（可选）</td></tr><tr><td>同时预览视频数</td><td>5个</td></tr><tr><td>安全模式</td><td>授权的用户名跟密码，HTTPS加密，AES连接加密，RTSP验证</td></tr><tr><td>移动侦测</td><td>设置检测区域，可设置多级灵敏度</td></tr><tr><td>遮挡报警</td><td>支持1个区域设置</td></tr><tr><td>隐私遮蔽</td><td>支持5个区域遮挡块</td></tr><tr><td>事件联动</td><td>联动抓拍，TF卡录像，发送Email，报警输出联动，联动云台</td></tr><tr><td>手机同步</td><td>支持手机APP（IOS/Android）</td></tr><tr><td>设备异常检测</td><td>支持网线断、IP冲突、非法访问报警</td></tr><tr><td>字符叠加（OSD）</td><td>标题，时间和日期叠加，OSD 颜色可选</td></tr><tr><td>系统升级</td><td>支持工具升级，支持WEB端升级</td></tr><tr><td>手机监管</td><td>通过扫描二维码添加绑定梯控机，进行手机远程监管，报警推送</td></tr></tbody></table><p>接口<br/></p><table><tbody><tr><td>网络接口</td><td>RJ45 10M/100M网络自适应</td></tr><tr><td>音频</td><td>线路输入/线路输出</td></tr><tr><td>报警输入</td><td>1路输入（扩展板）</td></tr><tr><td>报警输出</td><td>2路输出（扩展板）</td></tr><tr><td>复位按钮</td><td>支持（扩展板）</td></tr><tr><td>RS485接口</td><td>支持（扩展板）</td></tr><tr><td>TF卡</td><td>支持最大128G ，Micro SD(SDHC /SDXC)卡本地存储（扩展板）</td></tr><tr><td>电源</td><td>DC 12V±10%</td></tr><tr><td>功耗</td><td>＜3W</td></tr><tr><td>毛重</td><td>0.3 kg</td></tr><tr><td>外形尺寸</td><td>107*107mm</td></tr></tbody></table>", "details": "<p><img src=\"https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/15c9ae33-dd86-4878-b8cc-cbd355c1172b.png?file=【宣传长图】熵基电动车检测摄像机 SJ-W952ES21D-A.png\" style=\"max-width:100%;\" contenteditable=\"false\"/></p>", "qualifications": null, "instructions": null, "show_time": "2022-08-29T08:47:23.622Z", "other": "https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/a4cca52d-58bc-4451-98ab-81b7cfdd6215.pdf?file=【宣传彩页】SJ-W952ES21D-A熵基电动车检测摄像机 .pdf,https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/74cc34e4-cbd5-433d-9784-0c968c983888.png?file=【宣传长图】SJ-W952ES21D-A熵基电动车检测摄像机 .png,https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/zkteco/applications/sj/0547c64e-4a98-4a58-9803-859d2cdd5878.pdf?file=【用户手册】熵基电动车检测摄像机 SJ-W952ES21D-A.pdf", "common_problem": null, "price": 0.0, "is_new": 1, "category_name": "摄像机", "label_list": [{"createBy": "", "createTime": "2022-08-27 13:33:32", "updateBy": "", "updateTime": null, "remark": null, "params": {}, "isApp": null, "id": 18, "status": "0", "name": "项目", "showDay": null}], "label_name": "项目", "video_explanation": null, "video_installation": null, "video_troubleshooting": null, "show_for_company": null, "count": 203, "size_img": null, "specList": [], "productId": 32, "accessory": null, "show_for_company_name": null, "unit": 2, "productGenListHandle": [], "productGenListInstall": [], "productGenListFault": [], "productGenListLearning": [], "allProductGenListHandle": [], "allProductGenListInstall": [], "allProductGenListFault": [], "allProductGenListLearning": [], "showForCompanyFilter": false, "thirdTypeSearch": false, "showForCompanyList": []}, {"create_by": null, "create_time": "2022-08-29 08:57:48", "update_by": null, "update_time": "2025-05-28 14:21:49", "params": {}, "id": 33, "status": "0", "small_img": "https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/4c480140-9f4f-4c26-9743-3d61c6e344a0.png?file=产品图_29.png", "banner": "https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/f0fcc78a-70d2-46ee-99fd-cd6f1af84283.png?file=产品图_29.png", "name": "X射线安全检查设备", "attribute": "0", "category_id": "167", "label_id": "22", "spec": "LD5030", "introduction": "X射线安全检查设备LD5030是一种通道尺寸为500mm*300mm的安检设备，利用X射线对物品包裹透射成像技术，快速有效地检查出各种可疑危险物品，这是一款外观时尚、设计紧凑的小型安检机、重量轻便，成像清晰，穿透力强，配置19寸液晶显示器，高质量的图像能满足更严格的判图标准。广泛应用于公共服务部门、银行、酒店、大型会议、场馆、景区等安全检查场所。", "show_for": null, "param_info": "<table><tbody><tr><td><p>产品型号</p></td><td><p>LD5030</p></td></tr><tr><td><p>线分辨力</p></td><td><p>40AWG（Ф0.0787mm）</p></td></tr><tr><td><p>空间分辨力</p></td><td><p>水平≤ Ф1.0mm &nbsp;垂直≤ Ф1.0mm</p></td></tr><tr><td><p>穿透分辨力</p></td><td><p>26AWG（Ф0.404mm）</p></td></tr><tr><td><p>穿透力（钢板厚度）</p></td><td><p>≥10mm</p></td></tr><tr><td><p>管电压</p></td><td><p>80~100KV</p></td></tr><tr><td><p>单次检测剂量</p></td><td><p>0.5μ Gy</p></td></tr><tr><td><p>图像处理</p></td><td><p>超级增强、灰度、反色、高/低穿、无机剔除、有机剔除、吸收率加减、动态灰度扫描、P1P2P3功能组合键</p></td></tr><tr><td><p>通道尺寸</p></td><td><p>500mm*300mm</p></td></tr><tr><td><p>传送速度</p></td><td><p>≥0.22m/s</p></td></tr><tr><td><p>最大负荷</p></td><td><p>≥150kg（均匀分布）</p></td></tr><tr><td><p>重量</p></td><td><p>300KG</p></td></tr><tr><td><p>电源</p></td><td><p>AC220V(-15%～+10%)，50Hz±3Hz</p></td></tr><tr><td><p>功耗</p></td><td><p>1.0KW（Max）</p></td></tr><tr><td><p>噪声级别</p></td><td><p>＜56dB(A)</p></td></tr><tr><td><p>显示器</p></td><td><p>21.5英寸彩显1920*1080</p></td></tr><tr><td><p>选区与放大</p></td><td><p>1-64倍放大，支持连续放大</p></td></tr><tr><td><p>存储能力</p></td><td><p>约100000幅图片</p></td></tr><tr><td><p>选配功能</p></td><td><p>单屏操作台/视频监控功能/人包关联/智能识别等</p></td></tr><tr><td><p>图像回拉</p></td><td><p>100幅已检图像回放</p></td></tr><tr><td><p>周围剂量当量率</p></td><td><p>≤0.1μ Gy/h（距离外壳10cm），工作人员位置≤0.05μ Gy/h，</p><p>符合所有国内及国际健康安全标准；</p></td></tr><tr><td><p>胶卷安全</p></td><td><p>符合ASA/ISO1600标准胶卷安全</p></td></tr></tbody></table>", "new_param": "[{\"content\":\"LD5030\",\"id\":\"43ef3a58-a357-41c5-85fa-3548b873673e\",\"params\":\"产品型号\"},{\"content\":\"40AWG（Ф0.0787mm）\",\"id\":\"9c1155bc-7a43-4111-afce-2024c7dbbbc0\",\"params\":\"线分辨力\"},{\"content\":\"水平≤ Ф1.0mm  垂直≤ Ф1.0mm\",\"id\":\"c50c0f97-bbeb-4570-bb70-9c3e496906e0\",\"params\":\"空间分辨力\"},{\"content\":\"26AWG（Ф0.404mm）\",\"id\":\"0c02db23-f19d-4cc9-9b07-7d448838076a\",\"params\":\"穿透分辨力\"},{\"content\":\"≥10mm\",\"id\":\"ec5b2791-9f11-471c-88f7-04716fb94ac3\",\"params\":\"穿透力（钢板厚度）\"},{\"content\":\"80~100KV\",\"id\":\"8f57cde6-86d3-478a-a041-ff8d0e2d0fa9\",\"params\":\"管电压\"},{\"content\":\"0.5μ Gy\",\"id\":\"b89b20cc-e166-4cfb-911d-59e49a85344a\",\"params\":\"单次检测剂量\"},{\"content\":\"超级增强、灰度、反色、高/低穿、无机剔除、有机剔除、吸收率加减、动态灰度扫描、P1P2P3功能组合键\",\"id\":\"05fc8853-e077-4d70-a77f-ff9b15ee353f\",\"params\":\"图像处理\"},{\"content\":\"500mm*300mm\",\"id\":\"a300949c-90e8-4ffd-8db0-9e60fbca7b6e\",\"params\":\"通道尺寸\"},{\"content\":\"≥0.22m/s\",\"id\":\"e410224b-f06a-44f9-8329-311c9a870edb\",\"params\":\"传送速度\"},{\"content\":\"≥150kg（均匀分布）\",\"id\":\"decb55bb-2bd7-488c-a12d-32a354b750b2\",\"params\":\"最大负荷\"},{\"content\":\"300KG\",\"id\":\"82d1b6f4-916b-4af9-bcdb-65c3d862fa94\",\"params\":\"重量\"},{\"content\":\"AC220V(-15%～+10%)，50Hz±3Hz\",\"id\":\"4aad4743-884d-4910-9119-479ae579b284\",\"params\":\"电源\"},{\"content\":\"1.0KW（Max）\",\"id\":\"84ba0c40-d0aa-41f9-a65d-912ad780cbdd\",\"params\":\"功耗\"},{\"content\":\"＜56dB(A)\",\"id\":\"c041e1c4-8c6a-4a32-ad27-29391162580e\",\"params\":\"噪声级别\"},{\"content\":\"21.5英寸彩显1920*1080\",\"id\":\"538c3a92-0573-4a94-ab43-1eefc16d5920\",\"params\":\"显示器\"},{\"content\":\"1-64倍放大，支持连续放大\",\"id\":\"1e335629-49b7-44c7-84fd-7a52ef3c380c\",\"params\":\"选区与放大\"},{\"content\":\"约100000幅图片\",\"id\":\"b4b3a44a-f54d-4f3e-88f8-fc1db7ae054e\",\"params\":\"存储能力\"},{\"content\":\"单屏操作台/视频监控功能/人包关联/智能识别等\",\"id\":\"7030fc45-7818-4a3b-9e22-60f5d572697e\",\"params\":\"选配功能\"},{\"content\":\"100幅已检图像回放\",\"id\":\"f0ad4535-9587-4ba3-9046-3dc02fa1b7cc\",\"params\":\"图像回拉\"},{\"content\":\"≤0.1μGy/h（距离外壳10cm），工作人员位置≤0.05μGy/h，符合所有国内及国际健康安全标准\",\"id\":\"e80b4beb-f71a-4ccf-a61c-f4e8084508a6\",\"params\":\"周围剂量当量率\"},{\"content\":\"符合ASA/ISO1600标准胶卷安全\",\"id\":\"a3123d11-7309-4449-8caa-6576e68671fd\",\"params\":\"胶卷安全\"}]", "use_to": "公共服务部门、银行、酒店、大型会议、体育馆、博物馆、图书馆、科技馆、学校、景区、工厂园区、大厦等安全检查场所。", "details": "<p><img src=\"https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/zkteco/applications/sj/5427bcdd-c7da-40e5-8112-43c1513cc778.jpg?file=【彩页】X射线安全检查设备 LD5030_00.jpg\" style=\"max-width:100%;\" contenteditable=\"false\"/><img src=\"https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/zkteco/applications/sj/a05c91a8-fcad-4230-ba75-5f0ed8f52903.jpg?file=【彩页】X射线安全检查设备 LD5030_01.jpg\" style=\"max-width:100%;\" contenteditable=\"false\"/></p><p><br/></p>", "qualifications": null, "instructions": null, "show_time": "2022-08-29T08:57:44.500Z", "other": "https://armatura-minervaiot-china-stage-cn-north-1-public.s3.cn-north-1.amazonaws.com.cn/zkteco/applications/sj/9265f30d-8312-4457-8f87-2a726e8c3609.png?file=【产品图片】LD5030.png,https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/zkteco/applications/sj/41195396-d21f-4ee8-a650-36c1828ee2b4.pdf?file=【彩页】X射线安全检查设备 LD5030.pdf,https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/zkteco/applications/sj/63853c27-08c3-490e-bff6-05554c9b34be.pdf?file=【用户手册】 X射线安全检查设备LD5030.pdf", "price": 0.0, "is_new": 1, "category_name": "安检机", "label_list": [{"createBy": "", "createTime": "2022-08-27 13:34:40", "updateBy": "", "updateTime": null, "remark": null, "params": {}, "isApp": null, "id": 22, "status": "0", "name": "安检", "showDay": null}], "label_name": "安检", "video_explanation": null, "show_for_company": null, "count": 4278, "size_img": null, "specList": [], "productId": 33, "accessory": null, "show_for_company_name": null, "unit": 2, "productGenListHandle": [], "productGenListInstall": [], "productGenListFault": [], "productGenListLearning": [], "allProductGenListHandle": [{"id": 698, "genType": 0, "genUrl": "", "name": "ZK-D2180安检门操作使用介绍", "count": 32, "docType": null, "status": 0, "createTime": "2025-05-14 16:34:09", "updateTime": null, "genBelongType": 0, "productId": null, "categoryId": null, "genUrlList": null, "deleteIdList": null, "productIdList": null, "categoryIdList": null, "productList": null, "categoryNameList": null, "details": null, "subTitle": null, "type": 503, "videoUrl": "https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/zkteco/applications/sj/ea334ba2-421a-44a1-a222-5b3b1f90eeb3.mp4?file=安检门操作使用介绍.mp4", "belongId": null, "typeName": null, "idList": null, "showFor": null, "isApp": null, "startTime": null, "endTime": null}, {"id": 699, "genType": 0, "genUrl": "", "name": "ZK-D6180安装介绍（横梁款）", "count": 33, "docType": null, "status": 0, "createTime": "2025-05-15 16:01:59", "updateTime": "2025-05-15 16:03:16", "genBelongType": 0, "productId": null, "categoryId": null, "genUrlList": null, "deleteIdList": null, "productIdList": null, "categoryIdList": null, "productList": null, "categoryNameList": null, "details": null, "subTitle": null, "type": 503, "videoUrl": "https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/zkteco/applications/sj/28b47cf8-5bbf-4697-89eb-680d723c953d.mp4?file=ZK-D6180安装介绍（横梁款）.mp4", "belongId": null, "typeName": null, "idList": null, "showFor": null, "isApp": null, "startTime": null, "endTime": null}, {"id": 700, "genType": 0, "genUrl": "", "name": "智慧安检管理平台", "count": 26, "docType": null, "status": 0, "createTime": "2025-05-15 16:12:05", "updateTime": null, "genBelongType": 0, "productId": null, "categoryId": null, "genUrlList": null, "deleteIdList": null, "productIdList": null, "categoryIdList": null, "productList": null, "categoryNameList": null, "details": null, "subTitle": null, "type": 503, "videoUrl": "https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/zkteco/applications/sj/f7bd93b6-4190-496c-9341-a574298e7c12.mp4?file=智慧安检管理平台.mp4", "belongId": null, "typeName": null, "idList": null, "showFor": null, "isApp": null, "startTime": null, "endTime": null}, {"id": 701, "genType": 0, "genUrl": "", "name": "ZKX5030安检机介绍", "count": 21, "docType": null, "status": 0, "createTime": "2025-05-15 16:52:26", "updateTime": null, "genBelongType": 0, "productId": null, "categoryId": null, "genUrlList": null, "deleteIdList": null, "productIdList": null, "categoryIdList": null, "productList": null, "categoryNameList": null, "details": null, "subTitle": null, "type": 503, "videoUrl": "https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/zkteco/applications/sj/9bac38e7-ff7f-4a6d-a193-4e2f043572c3.mp4?file=ZKX5030安检机介绍1.mp4", "belongId": null, "typeName": null, "idList": null, "showFor": null, "isApp": null, "startTime": null, "endTime": null}, {"id": 702, "genType": 0, "genUrl": "", "name": "ZKX6550安检机安装", "count": 60, "docType": null, "status": 0, "createTime": "2025-05-15 18:02:44", "updateTime": null, "genBelongType": 0, "productId": null, "categoryId": null, "genUrlList": null, "deleteIdList": null, "productIdList": null, "categoryIdList": null, "productList": null, "categoryNameList": null, "details": null, "subTitle": null, "type": 503, "videoUrl": "https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/zkteco/applications/sj/873afa47-299c-4083-8ea1-1a3bd06cb767.mp4?file=ZKX6550安检机安装视频.mp4", "belongId": null, "typeName": null, "idList": null, "showFor": null, "isApp": null, "startTime": null, "endTime": null}, {"id": 703, "genType": 0, "genUrl": "", "name": "ZKX系列安检机变频器设置操作视频", "count": 22, "docType": null, "status": 0, "createTime": "2025-05-15 18:05:01", "updateTime": null, "genBelongType": 0, "productId": null, "categoryId": null, "genUrlList": null, "deleteIdList": null, "productIdList": null, "categoryIdList": null, "productList": null, "categoryNameList": null, "details": null, "subTitle": null, "type": 503, "videoUrl": "https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/zkteco/applications/sj/435b51f8-2b35-480c-80d5-ae380e810976.mp4?file=ZKX系列安检机变频器设置操作视频.mp4", "belongId": null, "typeName": null, "idList": null, "showFor": null, "isApp": null, "startTime": null, "endTime": null}], "allProductGenListInstall": [{"id": 695, "genType": 1, "genUrl": "https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/zkteco/applications/sj/83d773b0-ec94-4c66-9ee2-9f66aa0f521b.zip?file=ZKX6550操作台安装.zip", "name": "ZKX6550操作台安装", "count": 122, "docType": 3, "status": 0, "createTime": "2025-03-14 17:42:18", "updateTime": null, "genBelongType": 0, "productId": null, "categoryId": null, "genUrlList": null, "deleteIdList": null, "productIdList": null, "categoryIdList": null, "productList": null, "categoryNameList": null, "details": null, "subTitle": "ZKX6550操作台安装", "type": 503, "videoUrl": "https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/zkteco/applications/sj/fd3ab623-efbe-471c-b165-e6d3fe0fa018.mp4?file=ZKX6550操作台安装.mp4", "belongId": null, "typeName": null, "idList": null, "showFor": null, "isApp": null, "startTime": null, "endTime": null}, {"id": 704, "genType": 1, "genUrl": "", "name": "ZKX6550操作台安装流程", "count": 45, "docType": null, "status": 0, "createTime": "2025-05-15 18:17:29", "updateTime": null, "genBelongType": 0, "productId": null, "categoryId": null, "genUrlList": null, "deleteIdList": null, "productIdList": null, "categoryIdList": null, "productList": null, "categoryNameList": null, "details": null, "subTitle": null, "type": 503, "videoUrl": "https://minervaiot-storage-service-china-prod.s3.cn-north-1.amazonaws.com.cn/public/zkteco/applications/sj/cf0cdcca-7144-4dff-a0a1-739a6b8041b4.mp4?file=ZKX6550操作台安装流程.mp4", "belongId": null, "typeName": null, "idList": null, "showFor": null, "isApp": null, "startTime": null, "endTime": null}], "allProductGenListFault": [], "allProductGenListLearning": [], "showForCompanyFilter": false, "thirdTypeSearch": false, "showForCompanyList": [], "param_info_list": [{"id": "43ef3a58-a357-41c5-85fa-3548b873673e", "params": "产品型号", "content": "LD5030"}, {"id": "9c1155bc-7a43-4111-afce-2024c7dbbbc0", "params": "线分辨力", "content": "40AWG（Ф0.0787mm）"}, {"id": "c50c0f97-bbeb-4570-bb70-9c3e496906e0", "params": "空间分辨力", "content": "水平≤ Ф1.0mm  垂直≤ Ф1.0mm"}, {"id": "0c02db23-f19d-4cc9-9b07-7d448838076a", "params": "穿透分辨力", "content": "26AWG（Ф0.404mm）"}, {"id": "ec5b2791-9f11-471c-88f7-04716fb94ac3", "params": "穿透力（钢板厚度）", "content": "≥10mm"}, {"id": "8f57cde6-86d3-478a-a041-ff8d0e2d0fa9", "params": "管电压", "content": "80~100KV"}, {"id": "b89b20cc-e166-4cfb-911d-59e49a85344a", "params": "单次检测剂量", "content": "0.5μ Gy"}, {"id": "05fc8853-e077-4d70-a77f-ff9b15ee353f", "params": "图像处理", "content": "超级增强、灰度、反色、高/低穿、无机剔除、有机剔除、吸收率加减、动态灰度扫描、P1P2P3功能组合键"}, {"id": "a300949c-90e8-4ffd-8db0-9e60fbca7b6e", "params": "通道尺寸", "content": "500mm*300mm"}, {"id": "e410224b-f06a-44f9-8329-311c9a870edb", "params": "传送速度", "content": "≥0.22m/s"}, {"id": "decb55bb-2bd7-488c-a12d-32a354b750b2", "params": "最大负荷", "content": "≥150kg（均匀分布）"}, {"id": "82d1b6f4-916b-4af9-bcdb-65c3d862fa94", "params": "重量", "content": "300KG"}, {"id": "4aad4743-884d-4910-9119-479ae579b284", "params": "电源", "content": "AC220V(-15%～+10%)，50Hz±3Hz"}, {"id": "84ba0c40-d0aa-41f9-a65d-912ad780cbdd", "params": "功耗", "content": "1.0KW（Max）"}, {"id": "c041e1c4-8c6a-4a32-ad27-29391162580e", "params": "噪声级别", "content": "＜56dB(A)"}, {"id": "538c3a92-0573-4a94-ab43-1eefc16d5920", "params": "显示器", "content": "21.5英寸彩显1920*1080"}, {"id": "1e335629-49b7-44c7-84fd-7a52ef3c380c", "params": "选区与放大", "content": "1-64倍放大，支持连续放大"}, {"id": "b4b3a44a-f54d-4f3e-88f8-fc1db7ae054e", "params": "存储能力", "content": "约100000幅图片"}, {"id": "7030fc45-7818-4a3b-9e22-60f5d572697e", "params": "选配功能", "content": "单屏操作台/视频监控功能/人包关联/智能识别等"}, {"id": "f0ad4535-9587-4ba3-9046-3dc02fa1b7cc", "params": "图像回拉", "content": "100幅已检图像回放"}, {"id": "e80b4beb-f71a-4ccf-a61c-f4e8084508a6", "params": "周围剂量当量率", "content": "≤0.1μGy/h（距离外壳10cm），工作人员位置≤0.05μGy/h，符合所有国内及国际健康安全标准"}, {"id": "a3123d11-7309-4449-8caa-6576e68671fd", "params": "胶卷安全", "content": "符合ASA/ISO1600标准胶卷安全"}]}]}, "database_structure": {"total_columns": 48, "all_columns": {"column_name": {"type": "data_type", "nullable": false, "default": "column_default"}}, "knowledge_fields": [], "attachment_fields": []}, "field_mapping_analysis": {"total_mappings": 68, "mapped_api_fields": ["id", "operationGuide", "commonIssues", "likeCount", "instructionIds", "questionList", "paramInfoList", "brandId", "attachments", "createTime", "newParam", "video", "siteId", "updateTime", "banner", "isSuggest", "count", "labelList", "label", "isHot", "commonProblem", "productIdList", "showTime", "status", "videoInstallation", "useTo", "guide", "sizeImg", "accessory", "categoryName", "parameterInfo", "instructions", "viewCount", "unitName", "details", "showForCompany", "createBy", "updateBy", "paramInfo", "companyId", "usage", "showForCompanyName", "videoUrl", "accessoryList", "introduction", "brandName", "isNew", "showFor", "productGenMap", "videoTroubleshooting", "hardList", "softList", "unit", "sort", "createdAt", "name", "other", "category", "qualificationUrl", "favoriteCount", "productName", "smallImg", "qualifications", "videoExplanation", "price", "labelName", "spec", "updatedAt"], "unmapped_api_fields": ["update_by", "allProductGenListLearning", "showForCompanyFilter", "use_to", "label_list", "params", "productId", "video_explanation", "showForCompanyList", "common_problem", "size_img", "video_installation", "update_time", "allProductGenListInstall", "productGenListHandle", "param_info", "show_for_company_name", "param_info_list", "new_param", "video_troubleshooting", "attribute", "show_time", "small_img", "productGenListFault", "productGenListLearning", "category_name", "allProductGenListHandle", "allProductGenListFault", "show_for", "create_by", "category_id", "create_time", "label_name", "specList", "label_id", "productGenListInstall", "thirdTypeSearch", "is_new", "show_for_company"], "mapped_db_fields": ["id", "update_by", "created_at", "use_to", "like_count", "label_list", "favorite_count", "updated_at", "product_id_list", "product_gen_map", "video_explanation", "is_hot", "accessory_list", "common_problem", "size_img", "video_installation", "update_time", "banner", "brand_id", "hard_list", "count", "param_info", "show_for_company_name", "param_info_list", "unit_name", "new_param", "video_troubleshooting", "status", "show_time", "guide", "small_img", "accessory", "instructions", "details", "category_name", "introduction", "sort", "unit", "soft_list", "show_for", "is_suggest", "is_new", "name", "brand_name", "category_id", "other", "video_url", "label_name", "company_id", "create_time", "create_by", "label_id", "qualifications", "price", "question_list", "site_id", "spec", "show_for_company"], "unmapped_db_fields": ["column_name"], "invalid_mappings": ["数据库字段不存在: id", "数据库字段不存在: name", "API字段不存在: productName", "数据库字段不存在: name", "API字段不存在: category", "数据库字段不存在: category_id", "API字段不存在: categoryName", "数据库字段不存在: category_name", "API字段不存在: label", "数据库字段不存在: label_id", "API字段不存在: labelList", "数据库字段不存在: label_list", "API字段不存在: labelName", "数据库字段不存在: label_name", "API字段不存在: smallImg", "数据库字段不存在: small_img", "数据库字段不存在: banner", "数据库字段不存在: spec", "数据库字段不存在: introduction", "数据库字段不存在: details", "API字段不存在: paramInfo", "数据库字段不存在: param_info", "API字段不存在: parameterInfo", "数据库字段不存在: param_info", "API字段不存在: newParam", "数据库字段不存在: new_param", "API字段不存在: paramInfoList", "数据库字段不存在: param_info_list", "API字段不存在: useTo", "数据库字段不存在: use_to", "API字段不存在: usage", "数据库字段不存在: use_to", "API字段不存在: showFor", "数据库字段不存在: show_for", "API字段不存在: commonProblem", "数据库字段不存在: common_problem", "API字段不存在: commonIssues", "数据库字段不存在: common_problem", "数据库字段不存在: instructions", "API字段不存在: instructionIds", "数据库字段不存在: instructions", "数据库字段不存在: other", "API字段不存在: attachments", "数据库字段不存在: other", "数据库字段不存在: guide", "API字段不存在: operationGuide", "数据库字段不存在: guide", "数据库字段不存在: qualifications", "API字段不存在: qualificationUrl", "数据库字段不存在: qualifications", "API字段不存在: video", "数据库字段不存在: video_url", "API字段不存在: videoUrl", "数据库字段不存在: video_url", "API字段不存在: videoExplanation", "数据库字段不存在: video_explanation", "API字段不存在: videoInstallation", "数据库字段不存在: video_installation", "API字段不存在: videoTroubleshooting", "数据库字段不存在: video_troubleshooting", "数据库字段不存在: accessory", "API字段不存在: accessoryList", "数据库字段不存在: accessory_list", "API字段不存在: showForCompany", "数据库字段不存在: show_for_company", "API字段不存在: showForCompanyName", "数据库字段不存在: show_for_company_name", "API字段不存在: sizeImg", "数据库字段不存在: size_img", "数据库字段不存在: unit", "API字段不存在: unitName", "数据库字段不存在: unit_name", "API字段不存在: productGenMap", "数据库字段不存在: product_gen_map", "API字段不存在: questionList", "数据库字段不存在: question_list", "API字段不存在: hardList", "数据库字段不存在: hard_list", "API字段不存在: softList", "数据库字段不存在: soft_list", "API字段不存在: productIdList", "数据库字段不存在: product_id_list", "数据库字段不存在: count", "API字段不存在: viewCount", "数据库字段不存在: count", "API字段不存在: likeCount", "数据库字段不存在: like_count", "API字段不存在: favoriteCount", "数据库字段不存在: favorite_count", "API字段不存在: isSuggest", "数据库字段不存在: is_suggest", "API字段不存在: isHot", "数据库字段不存在: is_hot", "API字段不存在: isNew", "数据库字段不存在: is_new", "数据库字段不存在: status", "数据库字段不存在: price", "API字段不存在: siteId", "数据库字段不存在: site_id", "API字段不存在: companyId", "数据库字段不存在: company_id", "API字段不存在: brandId", "数据库字段不存在: brand_id", "API字段不存在: brandName", "数据库字段不存在: brand_name", "API字段不存在: showTime", "数据库字段不存在: show_time", "API字段不存在: sort", "数据库字段不存在: sort", "API字段不存在: createTime", "数据库字段不存在: create_time", "API字段不存在: createdAt", "数据库字段不存在: created_at", "API字段不存在: updateTime", "数据库字段不存在: update_time", "API字段不存在: updatedAt", "数据库字段不存在: updated_at", "API字段不存在: createBy", "数据库字段不存在: create_by", "API字段不存在: updateBy", "数据库字段不存在: update_by"], "current_mappings": {"id": "id", "name": "name", "productName": "name", "category": "category_id", "categoryName": "category_name", "label": "label_id", "labelList": "label_list", "labelName": "label_name", "smallImg": "small_img", "banner": "banner", "spec": "spec", "introduction": "introduction", "details": "details", "paramInfo": "param_info", "parameterInfo": "param_info", "newParam": "new_param", "paramInfoList": "param_info_list", "useTo": "use_to", "usage": "use_to", "showFor": "show_for", "commonProblem": "common_problem", "commonIssues": "common_problem", "instructions": "instructions", "instructionIds": "instructions", "other": "other", "attachments": "other", "guide": "guide", "operationGuide": "guide", "qualifications": "qualifications", "qualificationUrl": "qualifications", "video": "video_url", "videoUrl": "video_url", "videoExplanation": "video_explanation", "videoInstallation": "video_installation", "videoTroubleshooting": "video_troubleshooting", "accessory": "accessory", "accessoryList": "accessory_list", "showForCompany": "show_for_company", "showForCompanyName": "show_for_company_name", "sizeImg": "size_img", "unit": "unit", "unitName": "unit_name", "productGenMap": "product_gen_map", "questionList": "question_list", "hardList": "hard_list", "softList": "soft_list", "productIdList": "product_id_list", "count": "count", "viewCount": "count", "likeCount": "like_count", "favoriteCount": "favorite_count", "isSuggest": "is_suggest", "isHot": "is_hot", "isNew": "is_new", "status": "status", "price": "price", "siteId": "site_id", "companyId": "company_id", "brandId": "brand_id", "brandName": "brand_name", "showTime": "show_time", "sort": "sort", "createTime": "create_time", "createdAt": "created_at", "updateTime": "update_time", "updatedAt": "updated_at", "createBy": "create_by", "updateBy": "update_by"}}, "data_flow_test": {"normalization_success": true, "sample_product_fields": 54, "normalized_fields": 43}, "identified_issues": [{"type": "critical", "category": "字段映射", "description": "重要知识内容字段未映射: params, param_info, param_info_list, new_param", "impact": "产品知识内容无法正确存储"}], "fix_recommendations": [{"priority": "high", "action": "修复字段映射", "description": "更新ApiResponseNormalizer中的product字段映射", "steps": ["1. 分析未映射的API字段", "2. 确认对应的数据库字段", "3. 更新FIELD_MAPPINGS配置", "4. 测试数据标准化功能"]}, {"priority": "medium", "action": "完善测试覆盖", "description": "添加全面的数据处理测试", "steps": ["1. 创建单元测试", "2. 添加集成测试", "3. 设置持续监控", "4. 建立数据质量检查"]}]}